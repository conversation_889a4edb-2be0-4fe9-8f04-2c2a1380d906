﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.BitConverter">
      <summary>Convierte los tipos de datos base en una matriz de bytes y una matriz de bytes en tipos de datos base.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.BitConverter.DoubleToInt64Bits(System.Double)">
      <summary>Convierte el número de punto flotante de precisión doble especificado en un entero de 64 bits con signo.</summary>
      <returns>Entero de 64 bits con signo cuyo valor es equivalente a <paramref name="value" />.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Boolean)">
      <summary>Devuelve el valor booleano especificado como una matriz de bytes.</summary>
      <returns>Matriz de bytes cuya longitud es 1.</returns>
      <param name="value">Valor booleano. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Char)">
      <summary>Devuelve el valor de carácter Unicode especificado como una matriz de bytes.</summary>
      <returns>Matriz de bytes cuya longitud es 2.</returns>
      <param name="value">Carácter que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Double)">
      <summary>Devuelve el valor de punto flotante de doble precisión especificado como una matriz de bytes.</summary>
      <returns>Matriz de bytes cuya longitud es 8.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int16)">
      <summary>Devuelve el valor entero con signo de 16 bytes especificado como una matriz de bytes.</summary>
      <returns>Matriz de bytes cuya longitud es 2.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int32)">
      <summary>Devuelve el valor entero con signo de 32 bytes especificado como una matriz de bytes.</summary>
      <returns>Matriz de bytes cuya longitud es 4.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int64)">
      <summary>Devuelve el valor entero con signo de 64 bytes especificado como una matriz de bytes.</summary>
      <returns>Matriz de bytes cuya longitud es 8.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Single)">
      <summary>Devuelve el valor de punto flotante de precisión sencilla especificado como una matriz de bytes.</summary>
      <returns>Matriz de bytes cuya longitud es 4.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt16)">
      <summary>Devuelve el valor entero sin signo de 16 bits especificado como una matriz de bytes.</summary>
      <returns>Matriz de bytes cuya longitud es 2.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt32)">
      <summary>Devuelve el valor entero sin signo de 32 bits especificado como una matriz de bytes.</summary>
      <returns>Matriz de bytes cuya longitud es 4.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt64)">
      <summary>Devuelve el valor entero sin signo de 64 bits especificado como una matriz de bytes.</summary>
      <returns>Matriz de bytes cuya longitud es 8.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.Int64BitsToDouble(System.Int64)">
      <summary>Convierte el entero de 64 bits con signo especificado en un número de punto flotante de precisión doble.</summary>
      <returns>Número de punto flotante de precisión doble cuyo valor es equivalente al valor de <paramref name="value" />.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.BitConverter.IsLittleEndian">
      <summary>Indica el orden de bytes en el que se almacenan los datos en la arquitectura de este equipo, es decir, si la arquitectura es Big-endian o Little-endian.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToBoolean(System.Byte[],System.Int32)">
      <summary>Devuelve un valor booleano convertido a partir de un byte en la posición especificada de una matriz de bytes.</summary>
      <returns>Es true si el byte de <paramref name="value" /> situado en <paramref name="startIndex" /> es distinto de cero; de lo contrario, es false.</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToChar(System.Byte[],System.Int32)">
      <summary>Devuelve un carácter Unicode convertido a partir de dos bytes en la posición especificada de una matriz de bytes.</summary>
      <returns>Carácter formado por dos bytes que comienzan en <paramref name="startIndex" />.</returns>
      <param name="value">Matriz. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="startIndex" /> es igual a la longitud de <paramref name="value" /> menos 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToDouble(System.Byte[],System.Int32)">
      <summary>Devuelve un número de punto flotante de precisión doble convertido a partir de ocho bytes en la posición especificada de una matriz de bytes.</summary>
      <returns>Número de punto flotante de precisión doble formado por ocho bytes que comienzan en <paramref name="startIndex" />.</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="startIndex" /> es mayor o igual que la longitud de <paramref name="value" /> menos 7 y es menor o igual que la longitud de <paramref name="value" /> menos 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt16(System.Byte[],System.Int32)">
      <summary>Devuelve un entero de 16 bits con signo convertido a partir de dos bytes en la posición especificada de una matriz de bytes.</summary>
      <returns>Entero de 16 bits con signo formado por dos bytes que comienzan en <paramref name="startIndex" />.</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="startIndex" /> es igual a la longitud de <paramref name="value" /> menos 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt32(System.Byte[],System.Int32)">
      <summary>Devuelve un entero de 32 bits con signo convertido a partir de cuatro bytes en la posición especificada de una matriz de bytes.</summary>
      <returns>Entero de 32 bits con signo formado por cuatro bytes que comienzan en <paramref name="startIndex" />.</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="startIndex" /> es mayor o igual que la longitud de <paramref name="value" /> menos 3 y es menor o igual que la longitud de <paramref name="value" /> menos 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt64(System.Byte[],System.Int32)">
      <summary>Devuelve un entero de 64 bits con signo convertido a partir de ocho bytes en la posición especificada de una matriz de bytes.</summary>
      <returns>Entero de 64 bits con signo formado por ocho bytes que comienzan en <paramref name="startIndex" />.</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="startIndex" /> es mayor o igual que la longitud de <paramref name="value" /> menos 7 y es menor o igual que la longitud de <paramref name="value" /> menos 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToSingle(System.Byte[],System.Int32)">
      <summary>Devuelve un número de punto flotante de precisión sencilla convertido a partir de cuatro bytes en la posición especificada de una matriz de bytes.</summary>
      <returns>Número de punto flotante de precisión sencilla formado por cuatro bytes que comienzan en <paramref name="startIndex" />.</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="startIndex" /> es mayor o igual que la longitud de <paramref name="value" /> menos 3 y es menor o igual que la longitud de <paramref name="value" /> menos 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[])">
      <summary>Convierte el valor numérico de cada elemento de una matriz especificada de bytes en su representación de cadena hexadecimal equivalente.</summary>
      <returns>Cadena de pares hexadecimales separados por guiones, donde cada par representa el elemento correspondiente en <paramref name="value" />; por ejemplo, "7F-2C-4A-00".</returns>
      <param name="value">Matriz de bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32)">
      <summary>Convierte el valor numérico de cada elemento de una submatriz especificada de bytes en su representación de cadena hexadecimal equivalente.</summary>
      <returns>Cadena de pares hexadecimales separados por guiones, donde cada par representa el elemento correspondiente en una submatriz de <paramref name="value" />; por ejemplo, "7F-2C-4A-00".</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32,System.Int32)">
      <summary>Convierte el valor numérico de cada elemento de una submatriz especificada de bytes en su representación de cadena hexadecimal equivalente.</summary>
      <returns>Cadena de pares hexadecimales separados por guiones, donde cada par representa el elemento correspondiente en una submatriz de <paramref name="value" />; por ejemplo, "7F-2C-4A-00".</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <param name="length">Número de elementos de matriz de <paramref name="value" /> que se van a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="startIndex" /> o <paramref name="length" /> es menor que cero.O bien<paramref name="startIndex" /> es mayor que cero y es mayor o igual que la longitud de <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentException">La combinación de <paramref name="startIndex" /> y <paramref name="length" /> no especifica ninguna posición dentro de <paramref name="value" />; es decir, el parámetro <paramref name="startIndex" /> es mayor que la longitud de <paramref name="value" /> menos el parámetro <paramref name="length" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt16(System.Byte[],System.Int32)">
      <summary>Devuelve un entero de 16 bits sin signo convertido a partir de dos bytes en la posición especificada de una matriz de bytes.</summary>
      <returns>Entero de 16 bits sin signo formado por dos bytes que comienzan en <paramref name="startIndex" />.</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="startIndex" /> es igual a la longitud de <paramref name="value" /> menos 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt32(System.Byte[],System.Int32)">
      <summary>Devuelve un entero de 32 bits sin signo convertido a partir de cuatro bytes en la posición especificada de una matriz de bytes.</summary>
      <returns>Entero de 32 bits sin signo formado por cuatro bytes que comienzan en <paramref name="startIndex" />.</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="startIndex" /> es mayor o igual que la longitud de <paramref name="value" /> menos 3 y es menor o igual que la longitud de <paramref name="value" /> menos 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt64(System.Byte[],System.Int32)">
      <summary>Devuelve un entero de 64 bits sin signo convertido a partir de ocho bytes en la posición especificada de una matriz de bytes.</summary>
      <returns>Entero de 64 bits sin signo formado por ocho bytes que comienzan en <paramref name="startIndex" />.</returns>
      <param name="value">Matriz de bytes. </param>
      <param name="startIndex">Posición inicial de <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="startIndex" /> es mayor o igual que la longitud de <paramref name="value" /> menos 7 y es menor o igual que la longitud de <paramref name="value" /> menos 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> es menor que cero o mayor que la longitud de <paramref name="value" /> menos 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Convert">
      <summary>Convierte un tipo de datos base en otro tipo de datos base.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type)">
      <summary>Devuelve un objeto del tipo especificado y cuyo valor es equivalente al objeto especificado.</summary>
      <returns>Objeto cuyo tipo es <paramref name="conversionType" /> y cuyo valor es equivalente a <paramref name="value" />.o bienUna referencia nula (Nothing en Visual Basic), si <paramref name="value" /> es null y <paramref name="conversionType" /> no es un tipo de valor. </returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="conversionType">Tipo de objeto que se va a devolver. </param>
      <exception cref="T:System.InvalidCastException">No se admite esta conversión.  o bien<paramref name="value" /> es null y <paramref name="conversionType" /> es un tipo de valor.o bien<paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un formato reconocido por <paramref name="conversionType" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número que está fuera del intervalo de <paramref name="conversionType" />.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="conversionType" /> es null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type,System.IFormatProvider)">
      <summary>Devuelve un objeto del tipo especificado cuyo valor es equivalente al objeto especificado.Un parámetro proporciona información de formato específica de la referencia cultural.</summary>
      <returns>Objeto cuyo tipo es <paramref name="conversionType" /> y cuyo valor es equivalente a <paramref name="value" />.o bien <paramref name="value" /> si el tipo <see cref="T:System.Type" /> de <paramref name="value" /> y <paramref name="conversionType" /> son iguales.o bien Una referencia nula (Nothing en Visual Basic), si <paramref name="value" /> es null y <paramref name="conversionType" /> no es un tipo de valor.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="conversionType">Tipo de objeto que se va a devolver. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.InvalidCastException">No se admite esta conversión. o bien<paramref name="value" /> es null y <paramref name="conversionType" /> es un tipo de valor.o bien<paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato para <paramref name="conversionType" /> que <paramref name="provider" /> reconozca.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número que está fuera del intervalo de <paramref name="conversionType" />.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="conversionType" /> es null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.TypeCode,System.IFormatProvider)">
      <summary>Devuelve un objeto del tipo especificado cuyo valor es equivalente al objeto especificado.Un parámetro proporciona información de formato específica de la referencia cultural.</summary>
      <returns>Objeto cuyo tipo subyacente es <paramref name="typeCode" /> y cuyo valor es equivalente a <paramref name="value" />.o bien Una referencia nula (Nothing en Visual Basic) si <paramref name="value" /> es null y <paramref name="typeCode" /> es <see cref="F:System.TypeCode.Empty" />, <see cref="F:System.TypeCode.String" /> u <see cref="F:System.TypeCode.Object" />.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="typeCode">Tipo de objeto que se va a devolver. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.InvalidCastException">No se admite esta conversión.  o bien<paramref name="value" /> es null y <paramref name="typeCode" /> especifica un tipo de valor.o bien<paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> tiene un formato para el tipo <paramref name="typeCode" /> que no reconoce <paramref name="provider" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número que está fuera del intervalo del tipo <paramref name="typeCode" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeCode" /> no es válido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64CharArray(System.Char[],System.Int32,System.Int32)">
      <summary>Convierte un subconjunto de una matriz de caracteres Unicode, que codifica los datos binarios como dígitos en base 64, en una matriz equivalente de enteros de 8 bits sin signo.Los parámetros especifican el subconjunto de la matriz de entrada y el número de elementos que se convierten.</summary>
      <returns>Matriz de enteros de 8 bits sin signo que equivale a un número <paramref name="length" /> de elementos en la posición <paramref name="offset" /> de <paramref name="inArray" />.</returns>
      <param name="inArray">Matriz de caracteres Unicode. </param>
      <param name="offset">Posición en <paramref name="inArray" />. </param>
      <param name="length">Número de elementos de <paramref name="inArray" /> que se van a convertir. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="inArray" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> o <paramref name="length" /> es menor que 0.o bien <paramref name="offset" /> más <paramref name="length" /> indica una posición de carácter que no se encuentra dentro de <paramref name="inArray" />. </exception>
      <exception cref="T:System.FormatException">La longitud de <paramref name="inArray" />, sin tener en cuenta los espacios en blanco, no es cero ni un múltiplo de 4. o bienEl formato de <paramref name="inArray" /> no es válido.<paramref name="inArray" /> contiene un carácter que no está en base 64, más de dos caracteres de relleno o un carácter entre los caracteres de relleno que no es un espacio en blanco.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64String(System.String)">
      <summary>Convierte la cadena especificada, que codifica los datos binarios como dígitos en base 64, en una matriz equivalente de enteros de 8 bits sin signo.</summary>
      <returns>Una matriz de enteros de 8 bits sin signo equivalente a <paramref name="s" />.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="s" /> es null. </exception>
      <exception cref="T:System.FormatException">La longitud de <paramref name="s" />, sin tener en cuenta los espacios en blanco, no es cero ni un múltiplo de 4. o bienEl formato de <paramref name="s" /> no es válido.<paramref name="s" /> contiene un carácter que no está en base 64, más de dos caracteres de relleno o un carácter entre los caracteres de relleno que no es un espacio en blanco.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.GetTypeCode(System.Object)">
      <summary>Devuelve el <see cref="T:System.TypeCode" /> del objeto especificado.</summary>
      <returns>
        <see cref="T:System.TypeCode" /> para <paramref name="value" /> o <see cref="F:System.TypeCode.Empty" /> si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64CharArray(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Convierte un subconjunto de una matriz de enteros de 8 bits sin signo en un subconjunto equivalente de una matriz de caracteres Unicode codificada con dígitos de base 64.Los parámetros especifican los subconjuntos como posiciones de desplazamiento en las matrices de entrada y salida, así como el número de elementos de la matriz de entrada que se van a convertir.</summary>
      <returns>Entero de 32 bits con signo que contiene el número de bytes de <paramref name="outArray" />.</returns>
      <param name="inArray">Matriz de entrada de enteros de 8 bits sin signo. </param>
      <param name="offsetIn">Posición en <paramref name="inArray" />. </param>
      <param name="length">Número de elementos de <paramref name="inArray" /> que se van a convertir. </param>
      <param name="outArray">Matriz de salida de caracteres Unicode. </param>
      <param name="offsetOut">Posición en <paramref name="outArray" />. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="inArray" /> o <paramref name="outArray" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offsetIn" />, <paramref name="offsetOut" /> o <paramref name="length" /> son valores negativos.o bien La suma de <paramref name="offsetIn" /> y <paramref name="length" /> es mayor que la longitud de <paramref name="inArray" />.o bien La suma de <paramref name="offsetOut" /> y el número de elementos a devolver es mayor que la longitud de <paramref name="outArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[])">
      <summary>Convierte una matriz de enteros de 8 bits sin signo en su representación de cadena equivalente, que se codifica con dígitos de base 64.</summary>
      <returns>Representación de cadena, en base 64, del contenido de <paramref name="inArray" />.</returns>
      <param name="inArray">Matriz de enteros de 8 bits sin signo. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="inArray" /> es null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[],System.Int32,System.Int32)">
      <summary>Convierte un subconjunto de una matriz de enteros de 8 bits sin signo en su representación de cadena equivalente, que se codifica con dígitos de base 64.Los parámetros especifican el subconjunto como una posición de desplazamiento en la matriz de entrada y el número de elementos de la matriz que se van a convertir.</summary>
      <returns>Representación de cadena en base 64 de los elementos <paramref name="length" /> de <paramref name="inArray" />, empezando en la posición <paramref name="offset" />.</returns>
      <param name="inArray">Matriz de enteros de 8 bits sin signo. </param>
      <param name="offset">Posición de desplazamiento en <paramref name="inArray" />. </param>
      <param name="length">Número de elementos de <paramref name="inArray" /> que se van a convertir. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="inArray" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="offset" /> o <paramref name="length" /> es negativo.o bien La suma de <paramref name="offset" /> y <paramref name="length" /> es mayor que la longitud de <paramref name="inArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Boolean)">
      <summary>Devuelve el valor booleano especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Valor booleano que se va a devolver. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Número que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object)">
      <summary>Convierte el valor de un objeto especificado en un valor booleano equivalente.</summary>
      <returns>Valor true o false, que refleja el valor devuelto al invocar el método <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> para el tipo subyacente de <paramref name="value" />.Si el valor de <paramref name="value" /> es null, el método devuelve false.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> es una cadena que no es igual a <see cref="F:System.Boolean.TrueString" /> o <see cref="F:System.Boolean.FalseString" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />.o bienLa conversión de <paramref name="value" /> en un <see cref="T:System.Boolean" /> no se admite.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un valor booleano equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Valor true o false, que refleja el valor devuelto al invocar el método <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> para el tipo subyacente de <paramref name="value" />.Si el valor de <paramref name="value" /> es null, el método devuelve false.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> es una cadena que no es igual a <see cref="F:System.Boolean.TrueString" /> o <see cref="F:System.Boolean.FalseString" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />.o bienLa conversión de <paramref name="value" /> en un <see cref="T:System.Boolean" /> no se admite. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String)">
      <summary>Convierte la representación de cadena especificada de un valor lógico en un valor booleano equivalente.</summary>
      <returns>true si <paramref name="value" /> es igual a <see cref="F:System.Boolean.TrueString" />, o false si <paramref name="value" /> es igual a <see cref="F:System.Boolean.FalseString" /> o null.</returns>
      <param name="value">Cadena que contiene el valor de <see cref="F:System.Boolean.TrueString" /> o de <see cref="F:System.Boolean.FalseString" />. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es igual a <see cref="F:System.Boolean.TrueString" /> ni a <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un valor lógico en un valor booleano equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>true si <paramref name="value" /> es igual a <see cref="F:System.Boolean.TrueString" />, o false si <paramref name="value" /> es igual a <see cref="F:System.Boolean.FalseString" /> o null.</returns>
      <param name="value">Cadena que contiene el valor de <see cref="F:System.Boolean.TrueString" /> o de <see cref="F:System.Boolean.FalseString" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural.Este parámetro se ignora.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es igual a <see cref="F:System.Boolean.TrueString" /> ni a <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en un valor booleano equivalente.</summary>
      <returns>Es true si <paramref name="value" /> es distinto de cero; en caso contrario, es false.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Byte)">
      <summary>Devuelve el entero de 8 bits sin signo especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a devolver. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Char)">
      <summary>Convierte el valor del carácter Unicode especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Carácter Unicode que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número mayor que <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 8 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Byte.MaxValue" /> o menor que <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 8 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Byte.MaxValue" /> o menor que <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que <see cref="F:System.Byte.MinValue" /> o mayor que <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que <see cref="F:System.Byte.MinValue" /> o mayor que <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que <see cref="F:System.Byte.MinValue" /> o mayor que <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object)">
      <summary>Convierte el valor del objeto especificado en un entero de 8 bits sin signo.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un formato de propiedad válido para un valor <see cref="T:System.Byte" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa <see cref="T:System.IConvertible" />. o bienLa conversión de <paramref name="value" /> en un tipo <see cref="T:System.Byte" /> no se admite.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Byte.MinValue" /> o mayor que <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un entero de 8 bits sin signo, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un formato de propiedad válido para un valor <see cref="T:System.Byte" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa <see cref="T:System.IConvertible" />. o bienLa conversión de <paramref name="value" /> en un tipo <see cref="T:System.Byte" /> no se admite.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Byte.MinValue" /> o mayor que <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">El entero de 8 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 8 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión sencilla. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Byte.MaxValue" /> o menor que <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 8 bits sin signo equivalente.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Byte.MinValue" /> o mayor que <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 8 bits sin signo equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Byte.MinValue" /> o mayor que <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.Int32)">
      <summary>Convierte la representación de cadena de un número en una base especificada en un entero de 8 bits sin signo equivalente.</summary>
      <returns>Un entero de 8 bits sin signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="fromBase">Base del número de <paramref name="value" />, que debe ser 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> no es 2, 8, 10 ó 16. o bien<paramref name="value" />, que representa un número sin signo que no está en base 10, con un signo negativo como prefijo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="value" /> es <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carácter que no es un dígito válido en la base especificada por <paramref name="fromBase" />.Si el primer carácter de <paramref name="value" /> no es válido, el mensaje de excepción indica que no hay dígitos para convertir; de lo contrario, el mensaje indica que <paramref name="value" /> contiene caracteres finales no válidos.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, que representa un número sin signo en base 10, con un signo negativo como prefijo.o bien<paramref name="value" /> representa un número menor que <see cref="F:System.Byte.MinValue" /> o mayor que <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en un entero de 8 bits sin signo equivalente.</summary>
      <returns>Un entero de 8 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en el carácter Unicode equivalente.</summary>
      <returns>Un carácter Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un carácter Unicode equivalente.</summary>
      <returns>Un carácter Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un carácter Unicode equivalente.</summary>
      <returns>Un carácter Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que <see cref="F:System.Char.MinValue" /> o mayor que <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un carácter Unicode equivalente.</summary>
      <returns>Un carácter Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que <see cref="F:System.Char.MinValue" /> o mayor que <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object)">
      <summary>Convierte el valor del objeto especificado en un carácter Unicode.</summary>
      <returns>Un carácter Unicode que es equivalente a value o <see cref="F:System.Char.MinValue" /> si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es una cadena nula.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />.o bienLa conversión de <paramref name="value" /> en un <see cref="T:System.Char" /> no se admite. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que <see cref="F:System.Char.MinValue" /> o mayor que <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en su carácter Unicode equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un carácter Unicode que es equivalente a <paramref name="value" /> o <see cref="F:System.Char.MinValue" /> si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es una cadena nula.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienLa conversión de <paramref name="value" /> en un <see cref="T:System.Char" /> no se admite.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que <see cref="F:System.Char.MinValue" /> o mayor que <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un carácter Unicode equivalente.</summary>
      <returns>Un carácter Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String)">
      <summary>Convierte el primer carácter de una cadena especificada en un carácter Unicode.</summary>
      <returns>Un carácter Unicode equivalente al primer y único carácter de <paramref name="value" />.</returns>
      <param name="value">Una cadena de longitud 1. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="value" /> es null. </exception>
      <exception cref="T:System.FormatException">La longitud de <paramref name="value" /> no es 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String,System.IFormatProvider)">
      <summary>Convierte el primer carácter de una cadena especificada en un carácter Unicode, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un carácter Unicode equivalente al primer y único carácter de <paramref name="value" />.</returns>
      <param name="value">Una cadena de longitud 1 o null. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural.Este parámetro se ignora.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="value" /> es null. </exception>
      <exception cref="T:System.FormatException">La longitud de <paramref name="value" /> no es 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en el carácter Unicode equivalente.</summary>
      <returns>Un carácter Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en el carácter Unicode equivalente.</summary>
      <returns>Un carácter Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en el carácter Unicode equivalente.</summary>
      <returns>Un carácter Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object)">
      <summary>Convierte el valor del objeto especificado en un objeto <see cref="T:System.DateTime" />.</summary>
      <returns>Fecha y hora equivalente del valor de <paramref name="value" />, o fecha y hora equivalente de <see cref="F:System.DateTime.MinValue" /> si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un valor de fecha y hora válido.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un objeto <see cref="T:System.DateTime" />, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Fecha y hora equivalente del valor de <paramref name="value" />, o la fecha y hora equivalente de <see cref="F:System.DateTime.MinValue" /> si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un valor de fecha y hora válido.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String)">
      <summary>Convierte la representación de cadena especificada de una fecha y hora en un valor de fecha y hora equivalente.</summary>
      <returns>Fecha y hora equivalente del valor de <paramref name="value" />, o la fecha y hora equivalente de <see cref="F:System.DateTime.MinValue" /> si <paramref name="value" /> es null.</returns>
      <param name="value">Representación en forma de cadena de una fecha y hora.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> es una cadena de fecha y hora con un formato incorrecto. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en una fecha y hora equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Fecha y hora equivalente del valor de <paramref name="value" />, o la fecha y hora equivalente de <see cref="F:System.DateTime.MinValue" /> si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene una fecha y hora que se van a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> es una cadena de fecha y hora con un formato incorrecto. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un número decimal equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en el número decimal equivalente.</summary>
      <returns>Número decimal equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Decimal)">
      <summary>Devuelve el número decimal especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Número decimal. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un número decimal equivalente.</summary>
      <returns>Un número decimal equivalente a <paramref name="value" />. </returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Decimal.MaxValue" /> o menor que <see cref="F:System.Decimal.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un número decimal equivalente.</summary>
      <returns>Un número decimal equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un número decimal equivalente.</summary>
      <returns>Un número decimal equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un número decimal equivalente.</summary>
      <returns>Un número decimal equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object)">
      <summary>Convierte el valor del objeto especificado en un número decimal equivalente.</summary>
      <returns>Un número decimal equivalente a <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un formato adecuado para un tipo <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Decimal.MinValue" /> o mayor que <see cref="F:System.Decimal.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un número decimal equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un número decimal equivalente a <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un formato adecuado para un tipo <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />.o bienNo se admite la conversión. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Decimal.MinValue" /> o mayor que <see cref="F:System.Decimal.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en el número decimal equivalente.</summary>
      <returns>Un número decimal equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en el número decimal equivalente.</summary>
      <returns>Un número decimal equivalente a <paramref name="value" />. </returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Decimal.MaxValue" /> o menor que <see cref="F:System.Decimal.MinValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un número decimal equivalente.</summary>
      <returns>Un número decimal equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene un número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un número con un formato válido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Decimal.MinValue" /> o mayor que <see cref="F:System.Decimal.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un número decimal equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un número decimal equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene un número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un número con un formato válido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Decimal.MinValue" /> o mayor que <see cref="F:System.Decimal.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en un número decimal equivalente.</summary>
      <returns>Número decimal equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en un número decimal equivalente.</summary>
      <returns>Un número decimal equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en un número decimal equivalente.</summary>
      <returns>Un número decimal equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Número de punto flotante de precisión doble equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente a <paramref name="value" />.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Double)">
      <summary>Devuelve el número de punto flotante de precisión doble especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a devolver. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Número de punto flotante de precisión doble equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object)">
      <summary>Convierte el valor del objeto especificado en un número de punto flotante de precisión doble.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un formato adecuado para un tipo <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Double.MinValue" /> o mayor que <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un número de punto flotante de precisión doble, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un formato adecuado para un tipo <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Double.MinValue" /> o mayor que <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente a <paramref name="value" />.</returns>
      <param name="value">Número de punto flotante de precisión sencilla. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un número con un formato válido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Double.MinValue" /> o mayor que <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un número de punto flotante de precisión doble equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un número con un formato válido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Double.MinValue" /> o mayor que <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en un número de punto flotante de precisión doble equivalente.</summary>
      <returns>Un número de punto flotante de precisión doble equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>Un entero de 16 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Char)">
      <summary>Convierte el valor del carácter Unicode especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>Un entero de 16 bits con signo equivalente a <paramref name="value" />. </returns>
      <param name="value">Carácter Unicode que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 16 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int16.MaxValue" /> o menor que <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 16 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int16.MaxValue" /> o menor que <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int16)">
      <summary>Devuelve el entero de 16 bits con signo especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Entero de 16 bits con signo que se va a devolver. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>Entero de 16 bits con signo que equivale a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int16.MaxValue" /> o menor que <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>Un entero de 16 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int16.MaxValue" /> o menor que <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object)">
      <summary>Convierte el valor del objeto especificado en un entero de 16 bits con signo.</summary>
      <returns>Un entero de 16 bits con signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un formato adecuado para un tipo <see cref="T:System.Int16" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int16.MinValue" /> o mayor que <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un entero de 16 bits con signo, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 16 bits con signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un formato adecuado para un tipo <see cref="T:System.Int16" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int16.MinValue" /> o mayor que <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 16 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int16.MaxValue" /> o menor que <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 16 bits con signo equivalente.</summary>
      <returns>Un entero de 16 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int16.MinValue" /> o mayor que <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 16 bits con signo equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 16 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int16.MinValue" /> o mayor que <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.Int32)">
      <summary>Convierte la representación de cadena de un número en una base especificada en un entero de 16 bits con signo equivalente.</summary>
      <returns>Un entero de 16 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="fromBase">Base del número de <paramref name="value" />, que debe ser 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> no es 2, 8, 10 ó 16. o bien<paramref name="value" />, que representa un número con signo que no está en base 10, con un signo negativo como prefijo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="value" /> es <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carácter que no es un dígito válido en la base especificada por <paramref name="fromBase" />.Si el primer carácter de <paramref name="value" /> no es válido, el mensaje de excepción indica que no hay dígitos para convertir; de lo contrario, el mensaje indica que <paramref name="value" /> contiene caracteres finales no válidos.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, que representa un número con signo que no está en base 10, con un signo negativo como prefijo.o bien<paramref name="value" /> representa un número menor que <see cref="F:System.Int16.MinValue" /> o mayor que <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>Un entero de 16 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>Un entero de 16 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en un entero de 16 bits con signo equivalente.</summary>
      <returns>Un entero de 16 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un entero de 32 bits con signo equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en un entero de 32 bits con signo equivalente.</summary>
      <returns>Un entero de 32 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Char)">
      <summary>Convierte el valor del carácter Unicode especificado en un entero de 32 bits con signo equivalente.</summary>
      <returns>Un entero de 32 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Carácter Unicode que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un entero de 32 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 32 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int32.MaxValue" /> o menor que <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un entero de 32 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 32 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int32.MaxValue" /> o menor que <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>Un entero de 32 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int32)">
      <summary>Devuelve el entero de 32 bits con signo especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Entero de 32 bits con signo que se va a devolver. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>Un entero de 32 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int32.MaxValue" /> o menor que <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object)">
      <summary>Convierte el valor del objeto especificado en un entero de 32 bits con signo.</summary>
      <returns>Un entero de 32 bits con signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int32.MinValue" /> o mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un entero de 32 bits con signo, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 32 bits con signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int32.MinValue" /> o mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un entero de 32 bits con signo equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en un entero de 32 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 32 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int32.MaxValue" /> o menor que <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 32 bits con signo equivalente.</summary>
      <returns>Un entero de 32 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int32.MinValue" /> o mayor que <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 32 bits con signo equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 32 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int32.MinValue" /> o mayor que <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.Int32)">
      <summary>Convierte la representación de cadena de un número en una base especificada en un entero de 32 bits con signo equivalente.</summary>
      <returns>Un entero de 32 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="fromBase">Base del número de <paramref name="value" />, que debe ser 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> no es 2, 8, 10 ó 16. o bien<paramref name="value" />, que representa un número con signo que no está en base 10, con un signo negativo como prefijo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="value" /> es <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carácter que no es un dígito válido en la base especificada por <paramref name="fromBase" />.Si el primer carácter de <paramref name="value" /> no es válido, el mensaje de excepción indica que no hay dígitos para convertir; de lo contrario, el mensaje indica que <paramref name="value" /> contiene caracteres finales no válidos.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, que representa un número con signo que no está en base 10, con un signo negativo como prefijo.o bien<paramref name="value" /> representa un número menor que <see cref="F:System.Int32.MinValue" /> o mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en un entero de 32 bits con signo equivalente.</summary>
      <returns>Un entero de 32 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en un entero de 32 bits con signo equivalente.</summary>
      <returns>Un entero de 32 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 32-bit signed integer.</summary>
      <returns>Un entero de 32 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Char)">
      <summary>Convierte el valor del carácter Unicode especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Carácter Unicode que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 64 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int64.MaxValue" /> o menor que <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 64 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int64.MaxValue" /> o menor que <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int64)">
      <summary>Devuelve el entero de 64 bits con signo especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Entero de 64 bits con signo. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object)">
      <summary>Convierte el valor del objeto especificado en un entero de 64 bits con signo.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int64.MinValue" /> o mayor que <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un entero de 64 bits con signo, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />.o bienNo se admite la conversión. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int64.MinValue" /> o mayor que <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 64 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int64.MaxValue" /> o menor que <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 64 bits con signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene un número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int64.MinValue" /> o mayor que <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 64 bits con signo equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 64 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Int64.MinValue" /> o mayor que <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.Int32)">
      <summary>Convierte la representación de cadena de un número en una base especificada en un entero de 64 bits con signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="fromBase">Base del número de <paramref name="value" />, que debe ser 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> no es 2, 8, 10 ó 16. o bien<paramref name="value" />, que representa un número con signo que no está en base 10, con un signo negativo como prefijo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="value" /> es <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carácter que no es un dígito válido en la base especificada por <paramref name="fromBase" />.Si el primer carácter de <paramref name="value" /> no es válido, el mensaje de excepción indica que no hay dígitos para convertir; de lo contrario, el mensaje indica que <paramref name="value" /> contiene caracteres finales no válidos.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, que representa un número con signo que no está en base 10, con un signo negativo como prefijo.o bien<paramref name="value" /> representa un número menor que <see cref="F:System.Int64.MinValue" /> o mayor que <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en un entero de 64 bits con signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Char)">
      <summary>Convierte el valor del carácter Unicode especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Carácter Unicode que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 8 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" /> o menor que <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 8 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" /> o menor que <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" /> o menor que <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" /> o menor que <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" /> o menor que <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object)">
      <summary>Convierte el valor del objeto especificado en un entero de 8 bits con signo.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.SByte.MinValue" /> o mayor que <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un entero de 8 bits con signo, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.SByte.MinValue" /> o mayor que <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.SByte)">
      <summary>Devuelve el entero de 8 bits con signo especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Entero de 8 bits con signo que se va a devolver. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 8 bits con signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" /> o menor que <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 8 bits con signo equivalente.</summary>
      <returns>Entero de 8 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si value es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.SByte.MinValue" /> o mayor que <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 8 bits con signo equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="value" /> es null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.SByte.MinValue" /> o mayor que <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.Int32)">
      <summary>Convierte la representación de cadena de un número en una base especificada en un entero de 8 bits con signo equivalente.</summary>
      <returns>Entero de 8 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="fromBase">Base del número de <paramref name="value" />, que debe ser 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> no es 2, 8, 10 ó 16. o bien<paramref name="value" />, que representa un número con signo que no está en base 10, con un signo negativo como prefijo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="value" /> es <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carácter que no es un dígito válido en la base especificada por <paramref name="fromBase" />.Si el primer carácter de <paramref name="value" /> no es válido, el mensaje de excepción indica que no hay dígitos para convertir; de lo contrario, el mensaje indica que <paramref name="value" /> contiene caracteres finales no válidos.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, que representa un número con signo que no está en base 10, con un signo negativo como prefijo.o bien<paramref name="value" /> representa un número menor que <see cref="F:System.SByte.MinValue" /> o mayor que <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" /> o menor que <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en un entero de 8 bits con signo equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.SByte.MaxValue" /> o menor que <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />.<paramref name="value" /> se redondea utilizando el sistema de redondeo al valor más próximo.Por ejemplo, cuando se redondea a dos decimales, el valor 2,345 se convierte en 2,34 y el valor 2,355 pasa a ser 2,36.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />.<paramref name="value" /> se redondea utilizando el sistema de redondeo al valor más próximo.Por ejemplo, cuando se redondea a dos decimales, el valor 2,345 se convierte en 2,34 y el valor 2,355 pasa a ser 2,36.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object)">
      <summary>Convierte el valor del objeto especificado en un número de punto flotante de precisión sencilla.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Single.MinValue" /> o mayor que <see cref="F:System.Single.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un número de punto flotante de precisión sencilla, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Single.MinValue" /> o mayor que <see cref="F:System.Single.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un entero de 8 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Single)">
      <summary>Devuelve el número de punto flotante de precisión sencilla especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a devolver. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un número con un formato válido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Single.MinValue" /> o mayor que <see cref="F:System.Single.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un número de punto flotante de precisión sencilla equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no es un número con un formato válido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.Single.MinValue" /> o mayor que <see cref="F:System.Single.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en un número de punto flotante de precisión sencilla equivalente.</summary>
      <returns>Un número de punto flotante de precisión sencilla equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean)">
      <summary>Convierte el valor booleano especificado en su representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean,System.IFormatProvider)">
      <summary>Convierte el valor booleano especificado en su representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <param name="provider">Una instancia de un objeto.Este parámetro se ignora.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.IFormatProvider)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.Int32)">
      <summary>Convierte el valor de un entero de 8 bits sin signo en su representación de cadena equivalente en una base especificada.</summary>
      <returns>Representación de cadena de <paramref name="value" /> en la base <paramref name="toBase" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <param name="toBase">Base del valor devuelto, que debe ser 2, 8, 10 ó 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> no es 2, 8, 10 ó 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char)">
      <summary>Convierte el valor del carácter Unicode especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Carácter Unicode que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char,System.IFormatProvider)">
      <summary>Convierte el valor del carácter Unicode especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Carácter Unicode que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural.Este parámetro se ignora.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime)">
      <summary>Convierte el valor de la estructura <see cref="T:System.DateTime" /> especificada en su representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Valor de fecha y hora que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime,System.IFormatProvider)">
      <summary>Convierte el valor de la estructura <see cref="T:System.DateTime" /> especificada en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Valor de fecha y hora que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal)">
      <summary>Convierte el valor de un número decimal especificado en su representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal,System.IFormatProvider)">
      <summary>Convierte el valor del número decimal especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double,System.IFormatProvider)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.IFormatProvider)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.Int32)">
      <summary>Convierte el valor de un entero de 16 bits con signo en su representación de cadena equivalente en una base especificada.</summary>
      <returns>Representación de cadena de <paramref name="value" /> en la base <paramref name="toBase" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <param name="toBase">Base del valor devuelto, que debe ser 2, 8, 10 ó 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> no es 2, 8, 10 ó 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.IFormatProvider)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.Int32)">
      <summary>Convierte el valor de un entero de 32 bits con signo en su representación de cadena equivalente en una base especificada.</summary>
      <returns>Representación de cadena de <paramref name="value" /> en la base <paramref name="toBase" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <param name="toBase">Base del valor devuelto, que debe ser 2, 8, 10 ó 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> no es 2, 8, 10 ó 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.IFormatProvider)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.Int32)">
      <summary>Convierte el valor de un entero de 64 bits con signo en su representación de cadena equivalente en una base especificada.</summary>
      <returns>Representación de cadena de <paramref name="value" /> en la base <paramref name="toBase" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <param name="toBase">Base del valor devuelto, que debe ser 2, 8, 10 ó 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> no es 2, 8, 10 ó 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object)">
      <summary>Convierte el valor del objeto especificado en su representación de cadena equivalente.</summary>
      <returns>La representación de cadena de <paramref name="value" />, o <see cref="F:System.String.Empty" /> si <paramref name="value" /> es un objeto cuyo valor es null.Si el valor de <paramref name="value" /> es null, el método devuelve null.</returns>
      <param name="value">Objeto que proporciona el valor que se va a convertir, o null. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>La representación de cadena de <paramref name="value" />, o <see cref="F:System.String.Empty" /> si <paramref name="value" /> es un objeto cuyo valor es null.Si el valor de <paramref name="value" /> es null, el método devuelve null.</returns>
      <param name="value">Objeto que proporciona el valor que se va a convertir, o null. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte,System.IFormatProvider)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single,System.IFormatProvider)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16,System.IFormatProvider)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32,System.IFormatProvider)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en la representación de cadena equivalente.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64,System.IFormatProvider)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en su representación de cadena equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Representación de cadena de <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 16-bit unsigned integer.</summary>
      <returns>Un entero de 16 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Char)">
      <summary>Convierte el valor del carácter Unicode especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>Entero de 16 bits sin signo que equivale a <paramref name="value" />.</returns>
      <param name="value">Carácter Unicode que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 16 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 16 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>Un entero de 16 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>Un entero de 16 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>Un entero de 16 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object)">
      <summary>Convierte el valor del objeto especificado en un entero de 16 bits sin signo.</summary>
      <returns>Un entero de 16 bits sin signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt16.MinValue" /> o mayor que <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un entero de 16 bits sin signo, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 16 bits sin signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt16.MinValue" /> o mayor que <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>Un entero de 16 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 16 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 16 bits sin signo equivalente.</summary>
      <returns>Un entero de 16 bits sin signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt16.MinValue" /> o mayor que <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 16 bits sin signo equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 16 bits sin signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt16.MinValue" /> o mayor que <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.Int32)">
      <summary>Convierte la representación de cadena de un número en una base especificada en un entero de 16 bits sin signo equivalente.</summary>
      <returns>Un entero de 16 bits sin signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="fromBase">Base del número de <paramref name="value" />, que debe ser 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> no es 2, 8, 10 ó 16. o bien<paramref name="value" />, que representa un número sin signo que no está en base 10, con un signo negativo como prefijo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="value" /> es <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carácter que no es un dígito válido en la base especificada por <paramref name="fromBase" />.Si el primer carácter de <paramref name="value" /> no es válido, el mensaje de excepción indica que no hay dígitos para convertir; de lo contrario, el mensaje indica que <paramref name="value" /> contiene caracteres finales no válidos.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, que representa un número sin signo que no está en base 10, con un signo negativo como prefijo.o bien<paramref name="value" /> representa un número menor que <see cref="F:System.UInt16.MinValue" /> o mayor que <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt16)">
      <summary>Devuelve el entero de 16 bits sin signo especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a devolver. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt32)">
      <summary>Convierte el valor del entero de 32 bits sin signo especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>Un entero de 16 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en un entero de 16 bits sin signo equivalente.</summary>
      <returns>Un entero de 16 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Un entero de 32 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Char)">
      <summary>Convierte el valor del carácter Unicode especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Un entero de 32 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Carácter Unicode que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 32 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 32 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Un entero de 32 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Un entero de 32 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Un entero de 32 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object)">
      <summary>Convierte el valor del objeto especificado en un entero de 32 bits sin signo.</summary>
      <returns>Un entero de 32 bits sin signo equivalente a <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt32.MinValue" /> o mayor que <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un entero de 32 bits sin signo, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 32 bits sin signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt32.MinValue" /> o mayor que <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Un entero de 32 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 32 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Un entero de 32 bits sin signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt32.MinValue" /> o mayor que <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 32 bits sin signo equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 32 bits sin signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt32.MinValue" /> o mayor que <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.Int32)">
      <summary>Convierte la representación de cadena de un número en una base especificada en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Un entero de 32 bits sin signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="fromBase">Base del número de <paramref name="value" />, que debe ser 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> no es 2, 8, 10 ó 16. o bien<paramref name="value" />, que representa un número sin signo que no está en base 10, con un signo negativo como prefijo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="value" /> es <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carácter que no es un dígito válido en la base especificada por <paramref name="fromBase" />.Si el primer carácter de <paramref name="value" /> no es válido, el mensaje de excepción indica que no hay dígitos para convertir; de lo contrario, el mensaje indica que <paramref name="value" /> contiene caracteres finales no válidos.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, que representa un número sin signo que no está en base 10, con un signo negativo como prefijo.o bien<paramref name="value" /> representa un número menor que <see cref="F:System.UInt32.MinValue" /> o mayor que <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Un entero de 32 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt32)">
      <summary>Devuelve el entero de 32 bits sin signo especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a devolver. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt64)">
      <summary>Convierte el valor del entero de 64 bits sin signo especificado en un entero de 32 bits sin signo equivalente.</summary>
      <returns>Un entero de 32 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es mayor que <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Boolean)">
      <summary>Convierte el valor booleano especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>Número 1 si <paramref name="value" /> es true; en caso contrario, 0.</returns>
      <param name="value">Valor booleano que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Byte)">
      <summary>Convierte el valor del entero de 8 bits sin signo especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Char)">
      <summary>Convierte el valor del carácter Unicode especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>Un entero de 64 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Carácter Unicode que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Decimal)">
      <summary>Convierte el valor del número decimal especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 64 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número decimal que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Double)">
      <summary>Convierte el valor del número de punto flotante de precisión doble especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 64 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int16)">
      <summary>Convierte el valor del entero de 16 bits con signo especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>Un entero de 64 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int32)">
      <summary>Convierte el valor del entero de 32 bits con signo especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>Un entero de 64 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int64)">
      <summary>Convierte el valor del entero de 64 bits con signo especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>Un entero de 64 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 64 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object)">
      <summary>Convierte el valor del objeto especificado en un entero de 64 bits sin signo.</summary>
      <returns>Un entero de 64 bits sin signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" /> o null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt64.MinValue" /> o mayor que <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object,System.IFormatProvider)">
      <summary>Convierte el valor del objeto especificado en un entero de 64 bits sin signo, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 64 bits sin signo equivalente a <paramref name="value" />, o cero si <paramref name="value" /> es null.</returns>
      <param name="value">Objeto que implementa la interfaz <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no tiene un formato adecuado.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> no implementa la interfaz <see cref="T:System.IConvertible" />. o bienNo se admite la conversión.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt64.MinValue" /> o mayor que <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.SByte)">
      <summary>Convierte el valor del entero de 8 bits con signo especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>Un entero de 64 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 8 bits con signo que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Single)">
      <summary>Convierte el valor del número de punto flotante de precisión sencilla especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>
        <paramref name="value" /> se redondea al entero de 64 bits sin signo más próximo.Si <paramref name="value" /> está en medio de dos números enteros, se devuelve el número par, es decir, 4,5 se convierte en 4 y 5,5 se convierte en 6.</returns>
      <param name="value">Número de punto flotante de precisión sencilla que se va a convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es menor que cero o mayor que <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 64 bits sin signo equivalente.</summary>
      <returns>Un entero de 64 bits con signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt64.MinValue" /> o mayor que <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.IFormatProvider)">
      <summary>Convierte la representación de cadena especificada de un número en un entero de 64 bits sin signo equivalente, usando la información de formato específica de la referencia cultural indicada.</summary>
      <returns>Un entero de 64 bits sin signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="provider">Objeto que proporciona información de formato específica de la referencia cultural. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> no está compuesto por un signo opcional seguido de una serie de dígitos (del 0 al 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> representa un número menor que <see cref="F:System.UInt64.MinValue" /> o mayor que <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.Int32)">
      <summary>Convierte la representación de cadena de un número en una base especificada en un entero de 64 bits sin signo equivalente.</summary>
      <returns>Un entero de 64 bits sin signo equivalente al número de <paramref name="value" />, o 0 (cero) si <paramref name="value" /> es null.</returns>
      <param name="value">Cadena que contiene el número que se va a convertir. </param>
      <param name="fromBase">Base del número de <paramref name="value" />, que debe ser 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> no es 2, 8, 10 ó 16. o bien<paramref name="value" />, que representa un número sin signo que no está en base 10, con un signo negativo como prefijo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="value" /> es <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carácter que no es un dígito válido en la base especificada por <paramref name="fromBase" />.Si el primer carácter de <paramref name="value" /> no es válido, el mensaje de excepción indica que no hay dígitos para convertir; de lo contrario, el mensaje indica que <paramref name="value" /> contiene caracteres finales no válidos.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, que representa un número sin signo que no está en base 10, con un signo negativo como prefijo.o bien<paramref name="value" /> representa un número menor que <see cref="F:System.UInt64.MinValue" /> o mayor que <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt16)">
      <summary>Convierte el valor del entero de 16 bits sin signo especificado en un entero de 64 bits sin signo equivalente.</summary>
      <returns>Un entero de 64 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 16 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 64-bit unsigned integer.</summary>
      <returns>Un entero de 64 bits sin signo equivalente a <paramref name="value" />.</returns>
      <param name="value">Entero de 32 bits sin signo que se va a convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt64)">
      <summary>Devuelve el entero de 64 bits sin signo especificado; no se efectúa una conversión real.</summary>
      <returns>El parámetro <paramref name="value" /> se devuelve sin cambios.</returns>
      <param name="value">Entero de 64 bits sin signo que se va a devolver. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Environment">
      <summary>Proporciona información acerca del entorno y la plataforma actuales, y la forma de manipularlos.Esta clase no puede heredarse.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.CurrentManagedThreadId">
      <summary>Obtiene un identificador único para el actual subproceso administrado.</summary>
      <returns>Entero que representa un identificador único para este subproceso administrado.</returns>
    </member>
    <member name="M:System.Environment.ExpandEnvironmentVariables(System.String)">
      <summary>Reemplaza el nombre de cada variable de entorno incluida en la cadena especificada por la cadena equivalente del valor de la variable y devuelve la cadena resultante.</summary>
      <returns>Una cadena con todas las variables de entorno reemplazadas por su valor.</returns>
      <param name="name">Cadena que contiene los nombres de cero o más variables de entorno.Todas las variables de entorno se definen con el carácter de signo de porcentaje (%).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.FailFast(System.String)">
      <summary>Finaliza inmediatamente un proceso después de escribir un mensaje en el registro de eventos de la aplicación Windows y, después, incluye el mensaje en el informe de errores que se envía a Microsoft.</summary>
      <param name="message">Mensaje que explica por qué finalizó el proceso o null si no se proporciona ninguna explicación.</param>
    </member>
    <member name="M:System.Environment.FailFast(System.String,System.Exception)">
      <summary>Finaliza inmediatamente un proceso después de escribir un mensaje en el registro de eventos de la aplicación Windows y, luego, incluye el mensaje y la información de excepción en el informe de errores que se envía a Microsoft.</summary>
      <param name="message">Mensaje que explica por qué finalizó el proceso o null si no se proporciona ninguna explicación.</param>
      <param name="exception">Una excepción que representa el error que causó la finalización.Esta es normalmente la excepción en un bloque catch.</param>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariable(System.String)">
      <summary>Recupera el valor de una variable de entorno del proceso actual. </summary>
      <returns>El valor de la variable de entorno especificada por <paramref name="variable" /> o null si no se encuentra la variable de entorno.</returns>
      <param name="variable">Nombre de la variable de entorno.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariables">
      <summary>Recupera todos los nombres de las variables de entorno y sus valores del proceso actual.</summary>
      <returns>Un diccionario que contiene todos los nombres de las variables de entorno y sus valores; de lo contrario, un diccionario vacío si no se encuentra ninguna variable de entorno.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <exception cref="T:System.OutOfMemoryException">The buffer is out of memory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.HasShutdownStarted">
      <summary>Obtiene un valor que indica si el dominio de aplicación actual se está descargando o si Common Language Runtime (CLR) se está cerrando. </summary>
      <returns>Es true si se descarga el dominio de aplicación actual o el CLR se está cerrando; de lo contrario, es false..</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.NewLine">
      <summary>Obtiene la cadena de nueva línea definida para este entorno.</summary>
      <returns>Una cadena que contiene "\r\n" para las plataformas que no son Unix o una cadena que contiene "\n" para las plataformas Unix.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.ProcessorCount">
      <summary>Obtiene el número de procesadores de la máquina actual.</summary>
      <returns>Un entero con signo de 32 bits que especifica el número de procesadores de la máquina actual.No existe ningún valor predeterminado.Si la máquina actual contiene varios grupos de procesador, esta propiedad devuelve el número de procesadores lógicos disponibles para el uso por parte de Common Language Runtime (CLR).</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="NUMBER_OF_PROCESSORS" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.SetEnvironmentVariable(System.String,System.String)">
      <summary>Crea, modifica o elimina una variable de entorno almacenada en el proceso actual.</summary>
      <param name="variable">Nombre de una variable de entorno.</param>
      <param name="value">Valor que se va asignar a <paramref name="variable" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="variable" /> contains a zero-length string, an initial hexadecimal zero character (0x00), or an equal sign ("="). -or-The length of <paramref name="variable" /> or <paramref name="value" /> is greater than or equal to 32,767 characters.-or-An error occurred during the execution of this operation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.StackTrace">
      <summary>Obtiene información de seguimiento de la pila actual.</summary>
      <returns>Cadena que contiene información acerca del seguimiento de la pila.Este valor puede ser <see cref="F:System.String.Empty" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack trace information is out of range.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.TickCount">
      <summary>Obtiene el número de milisegundos transcurridos desde que se inició el sistema.</summary>
      <returns>Un entero de 32 bits que contiene el tiempo en milisegundos que transcurrió desde la última vez que se inició el equipo. </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Math">
      <summary>Proporciona constantes y métodos estáticos para operaciones trigonométricas, logarítmicas y otras funciones matemáticas comunes.Para examinar el código fuente de .NET Framework para este tipo, consulte el Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Decimal)">
      <summary>Devuelve el valor absoluto de un número <see cref="T:System.Decimal" />.</summary>
      <returns>Número decimal, x, como 0 ≤ x ≤<see cref="F:System.Decimal.MaxValue" />.</returns>
      <param name="value">Número mayor o igual que <see cref="F:System.Decimal.MinValue" />, pero menor o igual que <see cref="F:System.Decimal.MaxValue" />. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Double)">
      <summary>Devuelve el valor absoluto de un número de punto flotante de precisión doble.</summary>
      <returns>Número de punto flotante de precisión doble, x, tal que 0 ≤ x ≤<see cref="F:System.Double.MaxValue" />.</returns>
      <param name="value">Número mayor o igual que <see cref="F:System.Double.MinValue" />, pero menor o igual que <see cref="F:System.Double.MaxValue" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int16)">
      <summary>Devuelve el valor absoluto de un entero de 16 bits con signo.</summary>
      <returns>Número entero de 16 bits con signo, x, tal que 0 ≤ x ≤<see cref="F:System.Int16.MaxValue" />.</returns>
      <param name="value">Número mayor que <see cref="F:System.Int16.MinValue" />, pero menor o igual que <see cref="F:System.Int16.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es igual que <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int32)">
      <summary>Devuelve el valor absoluto de un entero de 32 bits con signo.</summary>
      <returns>Número entero de 32 bits con signo, x, tal que 0 ≤ x ≤<see cref="F:System.Int32.MaxValue" />.</returns>
      <param name="value">Número mayor que <see cref="F:System.Int32.MinValue" />, pero menor o igual que <see cref="F:System.Int32.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es igual que <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int64)">
      <summary>Devuelve el valor absoluto de un entero de 64 bits con signo.</summary>
      <returns>Número entero de 64 bits con signo, x, tal que 0 ≤ x ≤<see cref="F:System.Int64.MaxValue" />.</returns>
      <param name="value">Número mayor que <see cref="F:System.Int64.MinValue" />, pero menor o igual que <see cref="F:System.Int64.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es igual que <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.SByte)">
      <summary>Devuelve el valor absoluto de un entero de 8 bits con signo.</summary>
      <returns>Número entero de 8 bits con signo, x, tal que 0 ≤ x ≤<see cref="F:System.SByte.MaxValue" />.</returns>
      <param name="value">Número mayor que <see cref="F:System.SByte.MinValue" />, pero menor o igual que <see cref="F:System.SByte.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> es igual que <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Single)">
      <summary>Devuelve el valor absoluto de un número de punto flotante de precisión sencilla.</summary>
      <returns>Número de punto flotante de precisión sencilla, x, tal que 0 ≤ x ≤<see cref="F:System.Single.MaxValue" />.</returns>
      <param name="value">Número mayor o igual que <see cref="F:System.Single.MinValue" />, pero menor o igual que <see cref="F:System.Single.MaxValue" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Acos(System.Double)">
      <summary>Devuelve el ángulo cuyo coseno es el número especificado.</summary>
      <returns>Ángulo, θ, medido en radianes, tal que 0 ≤θ≤πo bien <see cref="F:System.Double.NaN" /> si <paramref name="d" /> &lt; -1 o <paramref name="d" /> &gt; 1 o <paramref name="d" /> es igual que <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Número que representa un coseno, donde <paramref name="d" /> debe ser mayor o igual que -1 y menor o igual que 1.  </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Asin(System.Double)">
      <summary>Devuelve el ángulo cuyo seno es el número especificado.</summary>
      <returns>Ángulo, θ, medido en radianes, tal que -π/2 ≤θ≤π/2 o bien <see cref="F:System.Double.NaN" /> si <paramref name="d" /> &lt; -1 o <paramref name="d" /> &gt; 1 o <paramref name="d" /> es igual que <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Número que representa un seno, donde <paramref name="d" /> debe ser mayor o igual que -1 y menor o igual que 1. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan(System.Double)">
      <summary>Devuelve el ángulo cuya tangente corresponde al número especificado.</summary>
      <returns>Ángulo, θ, medido en radianes, tal que -π/2 ≤θ≤π/2.o bien <see cref="F:System.Double.NaN" /> si <paramref name="d" /> es igual que <see cref="F:System.Double.NaN" />, -π/2 redondeado a precisión doble (-1,5707963267949) si <paramref name="d" /> es igual a <see cref="F:System.Double.NegativeInfinity" /> o π/2 redondeado a precisión doble (1,5707963267949) si <paramref name="d" /> es igual a <see cref="F:System.Double.PositiveInfinity" />.</returns>
      <param name="d">Número que representa una tangente. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan2(System.Double,System.Double)">
      <summary>Devuelve el ángulo cuya tangente es el cociente de dos números especificados.</summary>
      <returns>Ángulo, θ, medido en radianes, tal que -π≤θ≤ y π tan(θ) = <paramref name="y" /> / <paramref name="x" />, donde (<paramref name="x" />, <paramref name="y" />) es un punto del plano cartesiano.Observe lo siguiente:Para (<paramref name="x" />, <paramref name="y" />) en el cuadrante 1, 0 &lt; θ &lt; π/2.Para (<paramref name="x" />, <paramref name="y" />) en el cuadrante 2, π/2 &lt; θ≤π.Para (<paramref name="x" />, <paramref name="y" />) en el cuadrante 3,-π &lt; θ &lt;-π/2.Para (<paramref name="x" />, <paramref name="y" />) en el cuadrante 4,-π/2 &lt; θ &lt; 0.Para los puntos en los límites de los cuadrantes, el valor devuelto es el siguiente:Si y es 0 y x no es negativo, θ = 0.Si y es 0 y x es negativo, θ = π.Si y es positivo y x es 0, θ = π/2.Si y es negativo y el valor de x es 0, θ = -π/2.Si y es 0 y x es 0, θ = 0. Si <paramref name="x" /> o <paramref name="y" /> es <see cref="F:System.Double.NaN" /> o si <paramref name="x" /> y <paramref name="y" /> son <see cref="F:System.Double.PositiveInfinity" /> o <see cref="F:System.Double.NegativeInfinity" />, el método devuelve <see cref="F:System.Double.NaN" />.</returns>
      <param name="y">Coordenada Y de un punto. </param>
      <param name="x">Coordenada X de un punto. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Decimal)">
      <summary>Devuelve el valor integral más pequeño que es mayor o igual que el número decimal especificado.</summary>
      <returns>Valor entero más pequeño que es mayor o igual que <paramref name="d" />.Tenga en cuenta que este método devuelve un valor <see cref="T:System.Decimal" /> en lugar de un tipo entero.</returns>
      <param name="d">Número decimal. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Double)">
      <summary>Devuelve el valor integral más pequeño que es mayor o igual que el número de punto flotante de precisión doble especificado.</summary>
      <returns>Valor entero más pequeño que es mayor o igual que <paramref name="a" />.Si <paramref name="a" /> es igual que <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, se devuelve ese valor.Tenga en cuenta que este método devuelve un valor <see cref="T:System.Double" /> en lugar de un tipo entero.</returns>
      <param name="a">Número de punto flotante de precisión doble. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cos(System.Double)">
      <summary>Devuelve el coseno del ángulo especificado.</summary>
      <returns>Coseno de <paramref name="d" />.Si <paramref name="d" /> es igual a <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, este método devuelve <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Ángulo, medido en radianes. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cosh(System.Double)">
      <summary>Devuelve el coseno hiperbólico del ángulo especificado.</summary>
      <returns>Coseno hiperbólico de <paramref name="value" />.Si <paramref name="value" /> es igual que <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, se devuelve <see cref="F:System.Double.PositiveInfinity" />.Si <paramref name="value" /> es igual que <see cref="F:System.Double.NaN" />, se devuelve <see cref="F:System.Double.NaN" />.</returns>
      <param name="value">Ángulo, medido en radianes. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.E">
      <summary>Representa la base logarítmica natural, especificada por la constante, e.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Exp(System.Double)">
      <summary>Devuelve e elevado a la potencia especificada.</summary>
      <returns>Número e elevado a la potencia <paramref name="d" />.Si <paramref name="d" /> es igual que <see cref="F:System.Double.NaN" /> o <see cref="F:System.Double.PositiveInfinity" />, se devuelve ese valor.Si <paramref name="d" /> es igual que <see cref="F:System.Double.NegativeInfinity" />, se devuelve 0.</returns>
      <param name="d">Número que especifica una potencia. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Decimal)">
      <summary>Devuelve el número entero más grande menor o igual que el número decimal especificado.</summary>
      <returns>Número entero más alto que es menor o igual que <paramref name="d" />.Tenga en cuenta que el método devuelve un valor entero de tipo <see cref="T:System.Math" />.</returns>
      <param name="d">Número decimal. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Double)">
      <summary>Devuelve el número entero más grande menor o igual que el número de punto flotante de precisión doble especificado.</summary>
      <returns>Número entero más alto que es menor o igual que <paramref name="d" />.Si <paramref name="d" /> es igual que <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, se devuelve ese valor.</returns>
      <param name="d">Número de punto flotante de precisión doble. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.IEEERemainder(System.Double,System.Double)">
      <summary>Devuelve el resto de la división de dos números especificados.</summary>
      <returns>Número igual a <paramref name="x" /> - (<paramref name="y" /> Q), donde Q es el cociente de <paramref name="x" /> / <paramref name="y" /> redondeado a su entero más próximo (si <paramref name="x" /> / <paramref name="y" /> se encuentra entre dos enteros, se devuelve el entero par).Si <paramref name="x" /> - (<paramref name="y" /> Q) es cero, se devuelve el valor +0 si <paramref name="x" /> es positivo o -0 si <paramref name="x" /> es negativo.Si <paramref name="y" /> = 0, se devuelve <see cref="F:System.Double.NaN" />.</returns>
      <param name="x">Dividendo. </param>
      <param name="y">Divisor. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double)">
      <summary>Devuelve el logaritmo natural (en base e) de un número especificado.</summary>
      <returns>Uno de los valores de la tabla siguiente. <paramref name="d" /> parámetroValor devuelto Positivo El logaritmo natural de <paramref name="d" />; es decir, ln <paramref name="d" />, registro o e<paramref name="d" />Cero <see cref="F:System.Double.NegativeInfinity" />Negativo <see cref="F:System.Double.NaN" />Es igual a <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Es igual a <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Número cuyo logaritmo se va a calcular. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double,System.Double)">
      <summary>Devuelve el logaritmo de un número especificado en una base determinada.</summary>
      <returns>Uno de los valores de la tabla siguiente.(+Infinito denota <see cref="F:System.Double.PositiveInfinity" />, -Infinito denota <see cref="F:System.Double.NegativeInfinity" /> y NaN denota <see cref="F:System.Double.NaN" />)<paramref name="a" /><paramref name="newBase" />Valor devuelto<paramref name="a" />&gt; 0(0 &lt;<paramref name="newBase" />&lt; 1) -or-(<paramref name="newBase" />&gt; 1)lognewBase(a)<paramref name="a" />&lt; 0(cualquier valor)NaN(cualquier valor)<paramref name="newBase" />&lt; 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = + InfinitoNaN<paramref name="a" /> = NaN(cualquier valor)NaN(cualquier valor)<paramref name="newBase" /> = NaNNaN(cualquier valor)<paramref name="newBase" /> = 1NaN<paramref name="a" /> = 00 &lt;<paramref name="newBase" />&lt; 1 +Infinito<paramref name="a" /> = 0<paramref name="newBase" />&gt; 1-Infinity<paramref name="a" /> = + Infinito0 &lt;<paramref name="newBase" />&lt; 1-Infinity<paramref name="a" /> = + Infinito<paramref name="newBase" />&gt; 1+Infinito<paramref name="a" /> = 1<paramref name="newBase" /> = 00<paramref name="a" /> = 1<paramref name="newBase" /> = + Infinito0</returns>
      <param name="a">Número cuyo logaritmo se va a calcular. </param>
      <param name="newBase">Base del logaritmo. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log10(System.Double)">
      <summary>Devuelve el logaritmo en base 10 de un número especificado.</summary>
      <returns>Uno de los valores de la tabla siguiente. <paramref name="d" /> parámetro Valor devuelto Positivo Logaritmo en base 10 de <paramref name="d" />; es decir, log 10<paramref name="d" />. Cero <see cref="F:System.Double.NegativeInfinity" />Negativo <see cref="F:System.Double.NaN" />Es igual a <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Es igual a <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Número cuyo logaritmo hay que calcular. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Byte,System.Byte)">
      <summary>Devuelve el mayor de dos enteros de 8 bits sin signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.</returns>
      <param name="val1">Primer entero de 8 bits sin signo de los dos que se van a comparar. </param>
      <param name="val2">Segundo entero de 8 bits sin signo de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Decimal,System.Decimal)">
      <summary>Devuelve el mayor de dos números decimales.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.</returns>
      <param name="val1">Primer número de dos números decimales que se van a comparar. </param>
      <param name="val2">Segundo número de dos decimales que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Double,System.Double)">
      <summary>Devuelve el mayor de dos números de punto flotante de precisión doble.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.Si <paramref name="val1" />, <paramref name="val2" /> o ambos ( <paramref name="val1" /> y <paramref name="val2" />) son iguales que <see cref="F:System.Double.NaN" />, se devuelve <see cref="F:System.Double.NaN" />.</returns>
      <param name="val1">Primer número de punto flotante de precisión doble de los dos que se van a comparar. </param>
      <param name="val2">Segundo número de punto flotante de precisión doble de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int16,System.Int16)">
      <summary>Devuelve el mayor de dos enteros de 16 bits con signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.</returns>
      <param name="val1">Primero de los dos enteros de 16 bits con signo que se van a comparar. </param>
      <param name="val2">Segundo de los dos enteros de 16 bits con signo que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int32,System.Int32)">
      <summary>Devuelve el mayor de dos enteros de 32 bits con signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.</returns>
      <param name="val1">Primero de los dos enteros de 32 bits con signo que se van a comparar. </param>
      <param name="val2">Segundo de los dos enteros de 32 bits con signo que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int64,System.Int64)">
      <summary>Devuelve el mayor de dos enteros de 64 bits con signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.</returns>
      <param name="val1">Primero de los dos enteros de 64 bits con signo que se van a comparar. </param>
      <param name="val2">Segundo de los dos enteros de 64 bits con signo que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.SByte,System.SByte)">
      <summary>Devuelve el mayor de dos enteros de 8 bits con signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.</returns>
      <param name="val1">Primero de los dos enteros de 8 bits con signo que se van a comparar. </param>
      <param name="val2">Segundo de los dos enteros de 8 bits con signo que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Single,System.Single)">
      <summary>Devuelve el mayor de dos números de punto flotante de precisión sencilla.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.Si <paramref name="val1" /> o <paramref name="val2" />, o ambos (<paramref name="val1" /> y <paramref name="val2" />) son iguales a <see cref="F:System.Single.NaN" />, <see cref="F:System.Single.NaN" /> se devuelve.</returns>
      <param name="val1">Primer número de punto flotante de precisión sencilla de los dos que se van a comparar. </param>
      <param name="val2">Segundo número de punto flotante de precisión sencilla de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt16,System.UInt16)">
      <summary>Devuelve el mayor de dos enteros de 16 bits sin signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.</returns>
      <param name="val1">Primer entero de 16 bits sin signo de los dos que se van a comparar. </param>
      <param name="val2">Segundo entero de 16 bits sin signo de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt32,System.UInt32)">
      <summary>Devuelve el mayor de dos enteros de 32 bits sin signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.</returns>
      <param name="val1">Primer entero de 32 bits sin signo de los dos que se van a comparar. </param>
      <param name="val2">Segundo entero de 32 bits sin signo de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt64,System.UInt64)">
      <summary>Devuelve el mayor de dos enteros de 64 bits sin signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea mayor.</returns>
      <param name="val1">Primer entero de 64 bits sin signo de los dos que se van a comparar. </param>
      <param name="val2">Segundo entero de 64 bits sin signo de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Byte,System.Byte)">
      <summary>Devuelve el menor de dos enteros de 8 bits sin signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.</returns>
      <param name="val1">Primer entero de 8 bits sin signo de los dos que se van a comparar. </param>
      <param name="val2">Segundo entero de 8 bits sin signo de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Decimal,System.Decimal)">
      <summary>Devuelve el menor de dos números decimales.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.</returns>
      <param name="val1">Primer número de dos números decimales que se van a comparar. </param>
      <param name="val2">Segundo número de dos decimales que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Double,System.Double)">
      <summary>Devuelve el menor de dos números de punto flotante de precisión doble.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.Si <paramref name="val1" />, <paramref name="val2" /> o ambos ( <paramref name="val1" /> y <paramref name="val2" />) son iguales que <see cref="F:System.Double.NaN" />, se devuelve <see cref="F:System.Double.NaN" />.</returns>
      <param name="val1">Primer número de punto flotante de precisión doble de los dos que se van a comparar. </param>
      <param name="val2">Segundo número de punto flotante de precisión doble de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int16,System.Int16)">
      <summary>Devuelve el menor de dos enteros de 16 bits con signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.</returns>
      <param name="val1">Primero de los dos enteros de 16 bits con signo que se van a comparar. </param>
      <param name="val2">Segundo de los dos enteros de 16 bits con signo que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int32,System.Int32)">
      <summary>Devuelve el menor de dos enteros de 32 bits con signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.</returns>
      <param name="val1">Primero de los dos enteros de 32 bits con signo que se van a comparar. </param>
      <param name="val2">Segundo de los dos enteros de 32 bits con signo que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int64,System.Int64)">
      <summary>Devuelve el menor de dos enteros de 64 bits con signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.</returns>
      <param name="val1">Primero de los dos enteros de 64 bits con signo que se van a comparar. </param>
      <param name="val2">Segundo de los dos enteros de 64 bits con signo que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.SByte,System.SByte)">
      <summary>Devuelve el menor de dos enteros de 8 bits con signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.</returns>
      <param name="val1">Primero de los dos enteros de 8 bits con signo que se van a comparar. </param>
      <param name="val2">Segundo de los dos enteros de 8 bits con signo que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Single,System.Single)">
      <summary>Devuelve el menor de dos números de punto flotante de precisión sencilla.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.Si <paramref name="val1" />, <paramref name="val2" /> o ambos ( <paramref name="val1" /> y <paramref name="val2" />) son iguales que <see cref="F:System.Single.NaN" />, se devuelve <see cref="F:System.Single.NaN" />.</returns>
      <param name="val1">Primer número de punto flotante de precisión sencilla de los dos que se van a comparar. </param>
      <param name="val2">Segundo número de punto flotante de precisión sencilla de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt16,System.UInt16)">
      <summary>Devuelve el menor de dos enteros de 16 bits sin signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.</returns>
      <param name="val1">Primer entero de 16 bits sin signo de los dos que se van a comparar. </param>
      <param name="val2">Segundo entero de 16 bits sin signo de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt32,System.UInt32)">
      <summary>Devuelve el menor de dos enteros de 32 bits sin signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.</returns>
      <param name="val1">Primer entero de 32 bits sin signo de los dos que se van a comparar. </param>
      <param name="val2">Segundo entero de 32 bits sin signo de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt64,System.UInt64)">
      <summary>Devuelve el menor de dos enteros de 64 bits sin signo.</summary>
      <returns>Parámetro <paramref name="val1" /> o <paramref name="val2" />, el que sea menor.</returns>
      <param name="val1">Primer entero de 64 bits sin signo de los dos que se van a comparar. </param>
      <param name="val2">Segundo entero de 64 bits sin signo de los dos que se van a comparar. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.PI">
      <summary>Representa la relación entre la longitud de la circunferencia de un círculo y su diámetro, especificada por la constante π.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Pow(System.Double,System.Double)">
      <summary>Devuelve un número especificado elevado a la potencia especificada.</summary>
      <returns>Número <paramref name="x" /> elevado a la potencia <paramref name="y" />.</returns>
      <param name="x">Número de punto flotante de precisión doble que se desea elevar a una potencia. </param>
      <param name="y">Número de punto flotante de precisión doble que especifica una potencia. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal)">
      <summary>Redondea un valor decimal al valor integral más próximo.</summary>
      <returns>Parámetro <paramref name="d" /> del entero más cercano.Si el componente fraccionario de <paramref name="d" /> se encuentra en medio del intervalo entre dos números, uno par y el otro impar, se devuelve el número impar.Tenga en cuenta que este método devuelve un valor <see cref="T:System.Decimal" /> en lugar de un tipo entero.</returns>
      <param name="d">Número decimal que se va a redondear. </param>
      <exception cref="T:System.OverflowException">El resultado está fuera del intervalo de un <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32)">
      <summary>Redondea un valor decimal al número especificado de dígitos fraccionarios.</summary>
      <returns>Número más cercano a <paramref name="d" /> que contiene varios dígitos fraccionarios iguales a <paramref name="decimals" />. </returns>
      <param name="d">Número decimal que se va a redondear. </param>
      <param name="decimals">Número de posiciones decimales del valor devuelto. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> es menor que 0 o mayor que 28. </exception>
      <exception cref="T:System.OverflowException">El resultado está fuera del intervalo de un <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32,System.MidpointRounding)">
      <summary>Redondea un valor decimal al número especificado de dígitos fraccionarios.Un parámetro especifica el redondeo del valor si está a la mitad del intervalo entre dos números.</summary>
      <returns>Número más cercano a <paramref name="d" /> que contiene varios dígitos fraccionarios iguales que <paramref name="decimals" />.Si <paramref name="d" /> tiene menos dígitos fraccionarios que <paramref name="decimals" />, <paramref name="d" /> se devuelve sin modificar.</returns>
      <param name="d">Número decimal que se va a redondear. </param>
      <param name="decimals">Número de posiciones decimales del valor devuelto. </param>
      <param name="mode">Especificación sobre el redondeo de <paramref name="d" /> si se encuentra a la mitad del intervalo comprendido entre otros dos números.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> es menor que 0 o mayor que 28. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> no es un valor válido de <see cref="T:System.MidpointRounding" />.</exception>
      <exception cref="T:System.OverflowException">El resultado está fuera del intervalo de un <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.MidpointRounding)">
      <summary>Redondea un valor decimal al entero más próximo.Un parámetro especifica el redondeo del valor si está a la mitad del intervalo entre dos números.</summary>
      <returns>
        <paramref name="d" /> entero más cercano.Si <paramref name="d" /> se encuentra a la mitad del intervalo entre dos números, uno par y el otro impar, <paramref name="mode" /> determina los dos números que se devuelven.</returns>
      <param name="d">Número decimal que se va a redondear. </param>
      <param name="mode">Especificación sobre el redondeo de <paramref name="d" /> si se encuentra a la mitad del intervalo comprendido entre otros dos números.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> no es un valor válido de <see cref="T:System.MidpointRounding" />.</exception>
      <exception cref="T:System.OverflowException">El resultado está fuera del intervalo de un <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double)">
      <summary>Redondea un valor de punto flotante de precisión doble al valor integral más cercano.</summary>
      <returns>
        <paramref name="a" /> entero más cercano.Si el componente fraccionario de <paramref name="a" /> se encuentra en medio del intervalo entre dos números, uno par y el otro impar, se devuelve el número impar.Tenga en cuenta que este método devuelve un valor <see cref="T:System.Double" /> en lugar de un tipo entero.</returns>
      <param name="a">Número de punto flotante de precisión doble que se va a redondear. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32)">
      <summary>Redondea un valor de punto flotante de precisión doble al número especificado de dígitos fraccionarios.</summary>
      <returns>Número más cercano a <paramref name="value" /> que contiene varios dígitos fraccionarios iguales a <paramref name="digits" />.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a redondear. </param>
      <param name="digits">Número de dígitos fraccionarios del valor devuelto. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> es menor que 0 o mayor que 15. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32,System.MidpointRounding)">
      <summary>Redondea un valor de punto flotante de precisión doble al número especificado de dígitos fraccionarios.Un parámetro especifica el redondeo del valor si está a la mitad del intervalo entre dos números.</summary>
      <returns>Número más cercano a <paramref name="value" /> que tiene varios dígitos fraccionarios iguales a <paramref name="digits" />.Si <paramref name="value" /> tiene menos dígitos fraccionarios que <paramref name="digits" />, <paramref name="value" /> se devuelve sin modificar.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a redondear. </param>
      <param name="digits">Número de dígitos fraccionarios del valor devuelto. </param>
      <param name="mode">Especificación sobre el redondeo de <paramref name="value" /> si se encuentra a la mitad del intervalo comprendido entre otros dos números.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> es menor que 0 o mayor que 15. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> no es un valor válido de <see cref="T:System.MidpointRounding" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.MidpointRounding)">
      <summary>Redondea un valor de punto flotante de precisión doble al entero más cercano.Un parámetro especifica el redondeo del valor si está a la mitad del intervalo entre dos números.</summary>
      <returns>
        <paramref name="value" /> entero más cercano.Si <paramref name="value" /> se encuentra a la mitad del intervalo entre dos enteros, uno par y el otro impar, <paramref name="mode" /> determina los dos números que se devuelven.</returns>
      <param name="value">Número de punto flotante de precisión doble que se va a redondear. </param>
      <param name="mode">Especificación sobre el redondeo de <paramref name="value" /> si se encuentra a la mitad del intervalo comprendido entre otros dos números.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> no es un valor válido de <see cref="T:System.MidpointRounding" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Decimal)">
      <summary>Devuelve un valor que indica el signo de un número decimal.</summary>
      <returns>Número que indica el signo de <paramref name="value" />, como puede verse en la tabla siguiente.Valor devuelto Significado -1 <paramref name="value" /> es menor que cero. 0 <paramref name="value" /> es igual a cero. 1 <paramref name="value" /> es mayor que cero. </returns>
      <param name="value">Número decimal con signo. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Double)">
      <summary>Devuelve un valor que indica el signo de un número de punto flotante de precisión doble.</summary>
      <returns>Número que indica el signo de <paramref name="value" />, como puede verse en la tabla siguiente.Valor devuelto Significado -1 <paramref name="value" /> es menor que cero. 0 <paramref name="value" /> es igual a cero. 1 <paramref name="value" /> es mayor que cero. </returns>
      <param name="value">Número con signo. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> es igual a <see cref="F:System.Double.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int16)">
      <summary>Devuelve un valor que indica el signo de un entero de 16 bits con signo.</summary>
      <returns>Número que indica el signo de <paramref name="value" />, como puede verse en la tabla siguiente.Valor devuelto Significado -1 <paramref name="value" /> es menor que cero. 0 <paramref name="value" /> es igual a cero. 1 <paramref name="value" /> es mayor que cero. </returns>
      <param name="value">Número con signo. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int32)">
      <summary>Devuelve un valor que indica el signo de un entero de 32 bits con signo.</summary>
      <returns>Número que indica el signo de <paramref name="value" />, como puede verse en la tabla siguiente.Valor devuelto Significado -1 <paramref name="value" /> es menor que cero. 0 <paramref name="value" /> es igual a cero. 1 <paramref name="value" /> es mayor que cero. </returns>
      <param name="value">Número con signo. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int64)">
      <summary>Devuelve un valor que indica el signo de un entero de 64 bits con signo.</summary>
      <returns>Número que indica el signo de <paramref name="value" />, como puede verse en la tabla siguiente.Valor devuelto Significado -1 <paramref name="value" /> es menor que cero. 0 <paramref name="value" /> es igual a cero. 1 <paramref name="value" /> es mayor que cero. </returns>
      <param name="value">Número con signo. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.SByte)">
      <summary>Devuelve un valor que indica el signo de un entero de 8 bits con signo.</summary>
      <returns>Número que indica el signo de <paramref name="value" />, como puede verse en la tabla siguiente.Valor devuelto Significado -1 <paramref name="value" /> es menor que cero. 0 <paramref name="value" /> es igual a cero. 1 <paramref name="value" /> es mayor que cero. </returns>
      <param name="value">Número con signo. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Single)">
      <summary>Devuelve un valor que indica el signo de un número de punto flotante de precisión sencilla.</summary>
      <returns>Número que indica el signo de <paramref name="value" />, como puede verse en la tabla siguiente.Valor devuelto Significado -1 <paramref name="value" /> es menor que cero. 0 <paramref name="value" /> es igual a cero. 1 <paramref name="value" /> es mayor que cero. </returns>
      <param name="value">Número con signo. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> es igual a <see cref="F:System.Single.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sin(System.Double)">
      <summary>Devuelve el seno del ángulo especificado.</summary>
      <returns>Seno de <paramref name="a" />.Si <paramref name="a" /> es igual a <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, este método devuelve <see cref="F:System.Double.NaN" />.</returns>
      <param name="a">Ángulo, medido en radianes. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sinh(System.Double)">
      <summary>Devuelve el seno hiperbólico del ángulo especificado.</summary>
      <returns>Seno hiperbólico de <paramref name="value" />.Si <paramref name="value" /> es igual a <see cref="F:System.Double.NegativeInfinity" />, <see cref="F:System.Double.PositiveInfinity" /> o <see cref="F:System.Double.NaN" />, este método devuelve <see cref="T:System.Double" /> igual a <paramref name="value" />.</returns>
      <param name="value">Ángulo, medido en radianes. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sqrt(System.Double)">
      <summary>Devuelve la raíz cuadrada de un número especificado.</summary>
      <returns>Uno de los valores de la tabla siguiente. <paramref name="d" /> parámetro Valor devuelto Cero o positivo Raíz cuadrada positiva de <paramref name="d" />. Negativo <see cref="F:System.Double.NaN" />Es igual a <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Es igual a <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Número cuya raíz cuadrada se va a calcular. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tan(System.Double)">
      <summary>Devuelve la tangente del ángulo especificado.</summary>
      <returns>Tangente de <paramref name="a" />.Si <paramref name="a" /> es igual a <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, este método devuelve <see cref="F:System.Double.NaN" />.</returns>
      <param name="a">Ángulo, medido en radianes. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tanh(System.Double)">
      <summary>Devuelve la tangente hiperbólica del ángulo especificado.</summary>
      <returns>Tangente hiperbólica de <paramref name="value" />.Si <paramref name="value" /> es igual a <see cref="F:System.Double.NegativeInfinity" />, este método devuelve -1.Si el valor es igual a <see cref="F:System.Double.PositiveInfinity" />, este método devuelve 1.Si <paramref name="value" /> es igual a <see cref="F:System.Double.NaN" />, este método devuelve <see cref="F:System.Double.NaN" />.</returns>
      <param name="value">Ángulo, medido en radianes. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Decimal)">
      <summary>Calcula la parte entera de un número decimal especificado. </summary>
      <returns>Parte entera de <paramref name="d" />, es decir, el número que queda después de descartar los dígitos fraccionarios.</returns>
      <param name="d">Número que se va a truncar.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Double)">
      <summary>Calcula la parte entera de un número de punto flotante de precisión doble especificado. </summary>
      <returns>La parte entera de <paramref name="d" />, es decir, el número que permanece una vez descartado cualquier dígito fraccionario o uno de los valores enumerados en la siguiente tabla. <paramref name="d" />Valor devuelto<see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Número que se va a truncar.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.MidpointRounding">
      <summary>Especifica cómo los métodos de redondeo matemáticos deben procesar un número que está comprendido entre dos números.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.MidpointRounding.AwayFromZero">
      <summary>Cuando un número está comprendido entre otros dos, se redondea hacia el número más cercano y más alejado de cero.</summary>
    </member>
    <member name="F:System.MidpointRounding.ToEven">
      <summary>Cuando un número está comprendido entre otros dos, se redondea hacia el número par más cercano.</summary>
    </member>
    <member name="T:System.Progress`1">
      <summary>Proporciona un objeto <see cref="T:System.IProgress`1" /> que invoca las devoluciones de llamada para cada valor de progreso notificado.</summary>
      <typeparam name="T">Especifica el tipo de valor del informe de progreso.</typeparam>
    </member>
    <member name="M:System.Progress`1.#ctor">
      <summary>Inicializa el objeto <see cref="T:System.Progress`1" />.</summary>
    </member>
    <member name="M:System.Progress`1.#ctor(System.Action{`0})">
      <summary>Inicializa el objeto <see cref="T:System.Progress`1" /> con la devolución de llamada especificada.</summary>
      <param name="handler">Un controlador para invocar a cada valor de progreso indicado.Se invocará este controlador además de cualquier delegado registrado con el evento <see cref="E:System.Progress`1.ProgressChanged" />.Dependiendo de la instancia de <see cref="T:System.Threading.SynchronizationContext" /> capturada por <see cref="T:System.Progress`1" /> en la construcción, es posible que esta instancia de controlador se pueda invocar simultáneamente a sí misma.</param>
    </member>
    <member name="M:System.Progress`1.OnReport(`0)">
      <summary>Notifica un cambio de progreso.</summary>
      <param name="value">Valor del progreso actualizado.</param>
    </member>
    <member name="E:System.Progress`1.ProgressChanged">
      <summary>Se genera para cada valor de progreso indicado.</summary>
    </member>
    <member name="M:System.Progress`1.System#IProgress{T}#Report(`0)">
      <summary>Notifica un cambio de progreso.</summary>
      <param name="value">Valor del progreso actualizado.</param>
    </member>
    <member name="T:System.Random">
      <summary>Representa un generador de números seudoaleatorios, que es un dispositivo que genera una secuencia de números que cumplen determinados requisitos estadísticos de aleatoriedad.Para examinar el código fuente de .NET Framework para este tipo, vea el origen de referencia.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Random" /> mediante un valor de inicialización predeterminado que depende del tiempo.</summary>
    </member>
    <member name="M:System.Random.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Random" /> usando el valor de inicialización especificado.</summary>
      <param name="Seed">Número que se usa para calcular el valor inicial de la secuencia de números seudoaleatorios.Si se especifica un número negativo, se usa el valor absoluto del número.</param>
    </member>
    <member name="M:System.Random.Next">
      <summary>Devuelve un entero aleatorio no negativo.</summary>
      <returns>Número entero de 32 bits con signo mayor o igual que cero y menor que <see cref="F:System.Int32.MaxValue" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32)">
      <summary>Devuelve un entero aleatorio no negativo que es menor que el valor máximo especificado.</summary>
      <returns>Número entero de 32 bits con signo mayor o igual que cero y menor que <paramref name="maxValue" />; es decir, dentro del intervalo de valores devueltos se incluye cero, pero no <paramref name="maxValue" />.Sin embargo, si el valor de <paramref name="maxValue" /> es 0, se devuelve <paramref name="maxValue" />.</returns>
      <param name="maxValue">Límite superior exclusivo del número aleatorio que se va a generar.<paramref name="maxValue" /> debe ser mayor o igual que cero.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxValue" /> is less than 0. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32,System.Int32)">
      <summary>Devuelve un entero aleatorio que está dentro de un intervalo especificado.</summary>
      <returns>Número entero de 32 bits con signo mayor o igual que <paramref name="minValue" />y menor que <paramref name="maxValue" />; es decir, dentro del intervalo de valores devueltos se incluye <paramref name="minValue" /> pero no <paramref name="maxValue" />.Si <paramref name="minValue" /> es igual a <paramref name="maxValue" />, se devuelve <paramref name="minValue" />.</returns>
      <param name="minValue">Límite inferior inclusivo del número aleatorio devuelto. </param>
      <param name="maxValue">Límite superior exclusivo del número aleatorio devuelto.<paramref name="maxValue" /> debe ser mayor o igual que <paramref name="minValue" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minValue" /> is greater than <paramref name="maxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextBytes(System.Byte[])">
      <summary>Rellena con números aleatorios los elementos de una matriz de bytes especificada.</summary>
      <param name="buffer">Matriz de bytes que contiene números aleatorios. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextDouble">
      <summary>Devuelve un número de punto flotante aleatorio que es mayor o igual que 0,0 y menor que 1,0.</summary>
      <returns>Número de punto flotante de doble precisión que es mayor o igual que 0,0 y menor que 1,0.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Sample">
      <summary>Devuelve un número de punto flotante aleatorio entre 0,0 y 1,0.</summary>
      <returns>Número de punto flotante de doble precisión que es mayor o igual que 0,0 y menor que 1,0.</returns>
    </member>
    <member name="T:System.StringComparer">
      <summary>Representa una operación de comparación de cadenas que utiliza reglas específicas basadas en la referencia cultural y el uso de mayúsculas y minúsculas o reglas específicas de comparación de ordinales.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.StringComparer.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.StringComparer" />. </summary>
    </member>
    <member name="M:System.StringComparer.Compare(System.String,System.String)">
      <summary>Cuando se reemplaza en una clase derivada, compara dos cadenas y devuelve una indicación de su criterio de ordenación relativo.</summary>
      <returns>Entero con signo que indica los valores relativos de <paramref name="x" /> y <paramref name="y" />, como se muestra en la tabla siguiente.ValorSignificadoMenor que cero<paramref name="x" /> precede a <paramref name="y" /> en el criterio de ordenación.O bien<paramref name="x" /> es null y <paramref name="y" /> no es null.Cero<paramref name="x" /> es igual a <paramref name="y" />.o bien<paramref name="x" /> y <paramref name="y" /> son null. Mayor que cero<paramref name="x" /> sigue <paramref name="y" /> en el criterio de ordenación.O bien<paramref name="y" /> es null y <paramref name="x" /> no es null. </returns>
      <param name="x">Cadena que se va a comparar con <paramref name="y" />.</param>
      <param name="y">Cadena que se va a comparar con <paramref name="x" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.CurrentCulture">
      <summary>Obtiene un objeto <see cref="T:System.StringComparer" /> que realiza una comparación de cadenas con distinción entre mayúsculas y minúsculas usando las reglas de comparación de palabras de la referencia cultural actual.</summary>
      <returns>Un nuevo objeto <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.StringComparer.CurrentCultureIgnoreCase">
      <summary>Obtiene un objeto <see cref="T:System.StringComparer" /> que realiza comparaciones de cadenas sin distinción entre mayúsculas y minúsculas usando las reglas de comparación de palabras de la referencia cultural actual.</summary>
      <returns>Un nuevo objeto <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.StringComparer.Equals(System.String,System.String)">
      <summary>Cuando se reemplaza en una clase derivada, indica si dos cadenas son iguales.</summary>
      <returns>true si <paramref name="x" /> y <paramref name="y" /> hacen referencia al mismo objecto, o si <paramref name="x" /> y <paramref name="y" /> son iguales, o si <paramref name="x" /> y <paramref name="y" /> son null; en caso contrario, false.</returns>
      <param name="x">Cadena que se va a comparar con <paramref name="y" />.</param>
      <param name="y">Cadena que se va a comparar con <paramref name="x" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.GetHashCode(System.String)">
      <summary>Cuando se reemplaza en una clase derivada, obtiene el código hash de la cadena especificada.</summary>
      <returns>Código hash de 32 bits con signo a partir del valor del parámetro <paramref name="obj" />.</returns>
      <param name="obj">Una cadena.</param>
      <exception cref="T:System.ArgumentException">Memoria insuficiente está disponible para asignar el búfer exigido para calcular el código hash.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="obj" /> es null. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.StringComparer.Ordinal">
      <summary>Obtiene un objeto <see cref="T:System.StringComparer" /> que realiza una comparación de cadenas de ordinales con distinción entre mayúsculas y minúsculas</summary>
      <returns>Objeto <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.OrdinalIgnoreCase">
      <summary>Obtiene un objeto <see cref="T:System.StringComparer" /> que realiza una comparación de cadenas de ordinales sin distinción entre mayúsculas y minúsculas</summary>
      <returns>Objeto <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Compara dos objetos y devuelve un valor que indica si uno de ellos es menor, igual o mayor que el otro.</summary>
      <returns>Entero con signo que indica los valores relativos de <paramref name="x" /> y <paramref name="y" />, como se muestra en la tabla siguiente.ValorSignificadoMenor que cero<paramref name="x" /> es menor que <paramref name="y" />.Cero<paramref name="x" /> es igual que <paramref name="y" />.Mayor que cero<paramref name="x" /> es mayor que <paramref name="y" />.</returns>
      <param name="x">Primer objeto que se va a comparar.</param>
      <param name="y">Segundo objeto que se va a comparar.</param>
      <exception cref="T:System.ArgumentException">Ni <paramref name="x" /> ni <paramref name="y" /> implementa la <see cref="T:System.IComparable" /> interfaz.O bien<paramref name="x" /> y <paramref name="y" /> son de tipos diferentes y ninguno pueden controlar comparaciones con el otro.</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Determina si los objetos especificados son iguales.</summary>
      <returns>true si los objetos especificados son iguales; en caso contrario, false. </returns>
      <param name="x">Primer objeto que se va a comparar.</param>
      <param name="y">Segundo objeto que se va a comparar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> y <paramref name="y" /> son de tipos diferentes y ninguna de ellas pueden controlar comparaciones con el otro. </exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Devuelve un código hash para el objeto especificado.</summary>
      <returns>Código hash para el objeto especificado. </returns>
      <param name="obj">Objeto para el que se va a devolver un código hash. </param>
      <exception cref="T:System.ArgumentNullException">El tipo de <paramref name="obj" /> es un tipo de referencia y <paramref name="obj" /> es null. </exception>
    </member>
    <member name="T:System.UriBuilder">
      <summary>Proporciona un constructor personalizado para los identificadores uniformes de recursos (URI) y modifica los URI para la clase <see cref="T:System.Uri" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.UriBuilder" />.</summary>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.UriBuilder" /> con el identificador URI especificado.</summary>
      <param name="uri">Cadena URI. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> es null. </exception>
      <exception cref="T:System.UriFormatException">En las API de .NET para aplicaciones de la Tienda Windows o en la Biblioteca de clases portable, encuentre la excepción de la clase base, <see cref="T:System.FormatException" />, en su lugar.<paramref name="uri" /> es una cadena de longitud cero o sólo contiene espacios.O bien La rutina de análisis detectó un servicio en un formato no válido.O bien El analizador detectó más de dos barras diagonales consecutivas en un identificador URI que no utiliza el servicio "file".O bien <paramref name="uri" /> no es una dirección URI válida. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.UriBuilder" /> con el servicio y host que se hayan especificado.</summary>
      <param name="schemeName">Protocolo de acceso a Internet. </param>
      <param name="hostName">Nombre de dominio de estilo DNS o dirección IP. </param>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.UriBuilder" /> con el servicio, el host y el puerto que se hayan especificado.</summary>
      <param name="scheme">Protocolo de acceso a Internet. </param>
      <param name="host">Nombre de dominio de estilo DNS o dirección IP. </param>
      <param name="portNumber">Número de puerto IP para el servicio. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portNumber" /> es menor que -1 o mayor que 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.UriBuilder" /> con el servicio, el host, el número de puerto y la ruta de acceso que se hayan especificado.</summary>
      <param name="scheme">Protocolo de acceso a Internet. </param>
      <param name="host">Nombre de dominio de estilo DNS o dirección IP. </param>
      <param name="port">Número de puerto IP para el servicio. </param>
      <param name="pathValue">Ruta de acceso al recurso de Internet. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> es menor que -1 o mayor que 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.UriBuilder" /> con el servicio, el host, el número de puerto, la ruta de acceso y la cadena de consulta o el identificador de campo que se hayan especificado.</summary>
      <param name="scheme">Protocolo de acceso a Internet. </param>
      <param name="host">Nombre de dominio de estilo DNS o dirección IP. </param>
      <param name="port">Número de puerto IP para el servicio. </param>
      <param name="path">Ruta de acceso al recurso de Internet. </param>
      <param name="extraValue">Cadena de consulta o identificador de campo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="extraValue" /> no es null ni <see cref="F:System.String.Empty" />, ni hay un identificador de fragmento válido que comience por un signo de número (#), ni hay ninguna cadena de consulta válida que comience por un signo de interrogación de cierre (?). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> es menor que -1 o mayor que 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.Uri)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.UriBuilder" /> con la instancia de <see cref="T:System.Uri" /> especificada.</summary>
      <param name="uri">Instancia de la clase <see cref="T:System.Uri" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> es null. </exception>
    </member>
    <member name="M:System.UriBuilder.Equals(System.Object)">
      <summary>Compara una instancia existente de <see cref="T:System.Uri" /> con el contenido de <see cref="T:System.UriBuilder" /> para comprobar la igualdad.</summary>
      <returns>Es true si <paramref name="rparam" /> representa el mismo <see cref="T:System.Uri" /> que el <see cref="T:System.Uri" /> construido por esta instancia de <see cref="T:System.UriBuilder" />; en caso contrario, es false.</returns>
      <param name="rparam">Objeto que se va a comparar con la instancia actual. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Fragment">
      <summary>Obtiene o establece la parte correspondiente al campo de la dirección URI.</summary>
      <returns>Parte correspondiente al campo de la dirección URI.El identificador de campo ("#") se agrega al principio del campo.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.GetHashCode">
      <summary>Devuelve el código hash de la dirección URI.</summary>
      <returns>Código hash generado para la dirección URI.</returns>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Host">
      <summary>Obtiene o establece el nombre de host DNS (Sistema de nombres de dominio) o la dirección IP de un servidor.</summary>
      <returns>Nombre de host DNS (Sistema de nombres de dominio) o dirección IP del servidor.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Password">
      <summary>Obtiene o establece la contraseña asociada al usuario que obtiene acceso al identificador URI.</summary>
      <returns>Contraseña del usuario que obtiene acceso al identificador URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Path">
      <summary>Obtiene o establece la ruta de acceso al recurso al que hace referencia la dirección URI.</summary>
      <returns>Ruta de acceso al recurso al que hace referencia la dirección URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Port">
      <summary>Obtiene o establece el número de puerto de la dirección URI.</summary>
      <returns>Número de puerto de la dirección URI.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">El puerto no puede establecerse en un valor menor que 1 o mayor que 65.535. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Query">
      <summary>Obtiene o establece la información de consulta incluida en la dirección URI.</summary>
      <returns>Información de consulta incluida en la dirección URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Scheme">
      <summary>Obtiene o establece el nombre de servicio de la dirección URI.</summary>
      <returns>Servicio de la dirección URI.</returns>
      <exception cref="T:System.ArgumentException">El esquema no puede establecerse en un nombre de esquema no válido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.UriBuilder.ToString">
      <summary>Devuelve la cadena de presentación de la instancia de <see cref="T:System.UriBuilder" /> especificada.</summary>
      <returns>Cadena que contiene la cadena de presentación de <see cref="T:System.UriBuilder" />, sin caracteres de escape.</returns>
      <exception cref="T:System.UriFormatException">En las API de .NET para aplicaciones de la Tienda Windows o en la Biblioteca de clases portable, encuentre la excepción de la clase base, <see cref="T:System.FormatException" />, en su lugar.La instancia de <see cref="T:System.UriBuilder" /> tiene una contraseña incorrecta. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Uri">
      <summary>Obtiene la instancia de <see cref="T:System.Uri" /> creada mediante la instancia de <see cref="T:System.UriBuilder" /> especificada.</summary>
      <returns>
        <see cref="T:System.Uri" /> que contiene el identificador URI creado por <see cref="T:System.UriBuilder" />.</returns>
      <exception cref="T:System.UriFormatException">En las API de .NET para aplicaciones de la Tienda Windows o en la Biblioteca de clases portable, encuentre la excepción de la clase base, <see cref="T:System.FormatException" />, en su lugar.La dirección URI creada mediante las propiedades de la clase <see cref="T:System.UriBuilder" /> no es válida. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.UserName">
      <summary>Nombre de usuario asociado al usuario que obtiene acceso al identificador URI.</summary>
      <returns>Nombre de usuario del usuario que obtiene acceso al identificador URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Diagnostics.Stopwatch">
      <summary>Proporciona un conjunto de métodos y propiedades que puede usar para medir el tiempo transcurrido con precisión.Para examinar el código fuente de .NET Framework para este tipo, vea el origen de referencia.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Diagnostics.Stopwatch" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.Elapsed">
      <summary>Obtiene el tiempo total transcurrido medido por la instancia actual.</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> de solo lectura que representa el tiempo total transcurrido medido por la instancia actual.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedMilliseconds">
      <summary>Obtiene el tiempo total transcurrido medido por la instancia actual, en milisegundos.</summary>
      <returns>Entero largo de solo lectura que representa el número total de milisegundos medido por la instancia actual.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedTicks">
      <summary>Obtiene el tiempo total transcurrido medido por la instancia actual, en tics de temporizador.</summary>
      <returns>Un entero largo de solo lectura que representa el número total de tics de temporizador medido por la instancia actual.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.Frequency">
      <summary>Obtiene la frecuencia del temporizador en forma de número de tics por segundo.Este campo es de solo lectura.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.GetTimestamp">
      <summary>Obtiene el número actual de tics del mecanismo de temporización.</summary>
      <returns>Entero largo que representa el valor del contador de tics del mecanismo de temporización subyacente.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.IsHighResolution">
      <summary>Indica si el temporizador se basa en un contador de rendimiento de alta resolución.Este campo es de solo lectura.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.IsRunning">
      <summary>Obtiene un valor que indica si el temporizador <see cref="T:System.Diagnostics.Stopwatch" /> está en funcionamiento.</summary>
      <returns>Es true si la instancia de <see cref="T:System.Diagnostics.Stopwatch" /> se está ejecutando actualmente y está midiendo el tiempo transcurrido para un intervalo; en caso contrario, es false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Reset">
      <summary>Detiene la medición del intervalo de tiempo y restablece el tiempo transcurrido en cero.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Restart">
      <summary>Detiene la medición del intervalo de tiempo, restablece el tiempo transcurrido en cero y comienza a medir el tiempo transcurrido.</summary>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Start">
      <summary>Inicia o reanuda la medición del tiempo transcurrido para un intervalo.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.StartNew">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Diagnostics.Stopwatch" />, establece la propiedad de tiempo transcurrido en cero e inicia la medición de tiempo transcurrido.</summary>
      <returns>
        <see cref="T:System.Diagnostics.Stopwatch" /> que acaba de iniciar la medición de tiempo transcurrido.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Stop">
      <summary>Detiene la medición del tiempo transcurrido para un intervalo.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.Path">
      <summary>Ejecuta operaciones en instancias de <see cref="T:System.String" /> que contienen información de rutas de acceso de archivos o directorios.Estas operaciones se ejecutan de forma adecuada para múltiples plataformas.Para examinar el código fuente de .NET Framework para este tipo, consulte el Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.AltDirectorySeparatorChar">
      <summary>Proporciona un carácter alternativo específico de la plataforma, que se utiliza para separar niveles de directorios en una cadena de ruta de acceso que refleja una organización jerárquica del sistema de archivos.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.ChangeExtension(System.String,System.String)">
      <summary>Cambia la extensión de una cadena de ruta de acceso.</summary>
      <returns>Información de la ruta de acceso modificada.En plataformas de escritorio basadas en Windows, si <paramref name="path" /> es null o una cadena vacía (""), la información de ruta de acceso se devuelve sin modificar.Si <paramref name="extension" /> es null, la cadena devuelta contiene la ruta de acceso especificada, de la que se elimina la extensión.Si <paramref name="path" /> no tiene extensión y <paramref name="extension" /> no es null, la cadena de ruta de acceso devuelta contiene <paramref name="extension" /> anexado al final de <paramref name="path" />.</returns>
      <param name="path">La información de ruta de acceso que se va a modificar.La ruta de acceso no puede contener ninguno de los caracteres definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />.</param>
      <param name="extension">Nueva extensión (con o sin un punto inicial).Especifique null para quitar una extensión existente de <paramref name="path" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o varios de los caracteres no válidos definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String)">
      <summary>Combina dos cadenas en una ruta de acceso.</summary>
      <returns>Rutas de acceso combinadas.Si una de las rutas de acceso especificadas es una cadena de longitud cero, este método devuelve la otra ruta de acceso.Si <paramref name="path2" /> contiene una ruta de acceso absoluta, este método devuelve <paramref name="path2" />.</returns>
      <param name="path1">Primera ruta de acceso que se va a combinar. </param>
      <param name="path2">Segunda ruta de acceso que se va a combinar. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" /> o <paramref name="path2" /> contiene uno o varios de los caracteres no válidos definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path1" /> o <paramref name="path2" /> es null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String,System.String)">
      <summary>Combina tres cadenas en una ruta de acceso.</summary>
      <returns>Rutas de acceso combinadas.</returns>
      <param name="path1">Primera ruta de acceso que se va a combinar. </param>
      <param name="path2">Segunda ruta de acceso que se va a combinar. </param>
      <param name="path3">Tercera ruta de acceso que se va a combinar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" />, <paramref name="path2" />, o <paramref name="path3" /> contiene uno o varios de los caracteres no válidos definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" />, <paramref name="path2" />, or <paramref name="path3" /> is null. </exception>
    </member>
    <member name="M:System.IO.Path.Combine(System.String[])">
      <summary>Combina una matriz de cadenas en una ruta de acceso.</summary>
      <returns>Rutas de acceso combinadas.</returns>
      <param name="paths">Matriz de elementos de la ruta de acceso.</param>
      <exception cref="T:System.ArgumentException">Una de las cadenas de la matriz contiene uno o varios de los caracteres no válidos definen en <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Una de las cadenas de la matriz es null. </exception>
    </member>
    <member name="F:System.IO.Path.DirectorySeparatorChar">
      <summary>Proporciona un carácter específico de la plataforma, que se utiliza para separar niveles de directorios en una cadena de ruta de acceso que refleja una organización jerárquica del sistema de archivos.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetDirectoryName(System.String)">
      <summary>Devuelve la información de directorio para la cadena de ruta de acceso especificada.</summary>
      <returns>Información de directorio para <paramref name="path" />, o null si <paramref name="path" /> denota un directorio raíz o es null.Devuelve <see cref="F:System.String.Empty" /> si <paramref name="path" /> no contiene información sobre directorios.</returns>
      <param name="path">Ruta de acceso de un archivo o directorio. </param>
      <exception cref="T:System.ArgumentException">El <paramref name="path" /> parámetro contiene caracteres no válidos, está vacío o contiene sólo espacios en blanco. </exception>
      <exception cref="T:System.IO.PathTooLongException">En el .NET for Windows Store apps o biblioteca de clases Portable, detecte la excepción de la clase base, <see cref="T:System.IO.IOException" />, en su lugar.El <paramref name="path" /> parámetro es mayor que la longitud máxima definida por el sistema.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetExtension(System.String)">
      <summary>Devuelve la extensión de la cadena de ruta de acceso especificada.</summary>
      <returns>Extensión de la ruta de acceso especificada (incluido el punto "."), null o <see cref="F:System.String.Empty" />.Si <paramref name="path" /> es null, <see cref="M:System.IO.Path.GetExtension(System.String)" /> devuelve null.Si <paramref name="path" /> no tiene información de extensión, <see cref="M:System.IO.Path.GetExtension(System.String)" /> devuelve <see cref="F:System.String.Empty" />.</returns>
      <param name="path">Cadena de ruta de acceso de la cual se obtiene la extensión. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o varios de los caracteres no válidos definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />.  </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileName(System.String)">
      <summary>Devuelve el nombre de archivo y la extensión de la cadena de ruta de acceso especificada.</summary>
      <returns>Caracteres que siguen al último carácter de directorio en <paramref name="path" />.Si el último carácter de <paramref name="path" /> es un carácter separador de directorios o volúmenes, este método devuelve <see cref="F:System.String.Empty" />.Si <paramref name="path" /> es null este método devuelve null.</returns>
      <param name="path">Cadena de ruta de acceso de la cual se obtiene el nombre de archivo y la extensión. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o varios de los caracteres no válidos definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileNameWithoutExtension(System.String)">
      <summary>Devuelve el nombre de archivo y la cadena de ruta de acceso especificada sin la extensión.</summary>
      <returns>La cadena devuelta por <see cref="M:System.IO.Path.GetFileName(System.String)" />, menos el último punto (.) y todos los caracteres siguientes.</returns>
      <param name="path">Ruta de acceso del archivo. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o varios de los caracteres no válidos definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFullPath(System.String)">
      <summary>Devuelve la ruta de acceso absoluta para la cadena de ruta de acceso especificada.</summary>
      <returns>Ubicación completa de <paramref name="path" />, como "C:\MyFile.txt".</returns>
      <param name="path">El archivo o directorio para el que se va a obtener información de la ruta de acceso absoluta. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> es una cadena de longitud cero, contiene sólo espacios en blanco o contiene uno o varios de los caracteres no válidos definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien El sistema no pudo recuperar la ruta de acceso absoluta. </exception>
      <exception cref="T:System.Security.SecurityException">El llamador no tiene los permisos requeridos. </exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="path" /> es null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contiene dos puntos (":") que no forma parte de un identificador de volumen (por ejemplo, "c:\"). </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso deben ser inferiores a 248 caracteres y los nombres de archivo deben ser inferiores a 260 caracteres.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetInvalidFileNameChars">
      <summary>Obtiene una matriz que contiene los caracteres no permitidos en los nombres de archivo.</summary>
      <returns>Matriz que contiene los caracteres no permitidos en los nombres de archivo.</returns>
    </member>
    <member name="M:System.IO.Path.GetInvalidPathChars">
      <summary>Obtiene una matriz que contiene los caracteres no permitidos en los nombres de ruta de acceso.</summary>
      <returns>Matriz que contiene los caracteres no permitidos en los nombres de ruta de acceso.</returns>
    </member>
    <member name="M:System.IO.Path.GetPathRoot(System.String)">
      <summary>Obtiene información del directorio raíz de la ruta de acceso especificada.</summary>
      <returns>Directorio raíz de <paramref name="path" />, como "C:\", o null si <paramref name="path" /> es null, o una cadena vacía si <paramref name="path" /> no contiene información del directorio raíz.</returns>
      <param name="path">Ruta de acceso de la que se obtiene información de directorio raíz. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o varios de los caracteres no válidos definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />.o bien <see cref="F:System.String.Empty" /> se pasó a <paramref name="path" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetRandomFileName">
      <summary>Devuelve un nombre de carpeta o de archivo aleatorio.</summary>
      <returns>Nombre de carpeta o de archivo aleatorio.</returns>
    </member>
    <member name="M:System.IO.Path.GetTempFileName">
      <summary>Crea un archivo temporal de cero bytes y nombre único en el disco y devuelve la ruta de acceso completa a ese archivo.</summary>
      <returns>Ruta de acceso completa del archivo temporal.</returns>
      <exception cref="T:System.IO.IOException">Se produce un error de E/S, por ejemplo, no está disponible ningún nombre de archivo temporal único.o bienEste método no ha podido crear un archivo temporal.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetTempPath">
      <summary>Devuelve la ruta de acceso de la carpeta temporal del usuario actual.</summary>
      <returns>Ruta de acceso a la carpeta temporal, que termina con una barra diagonal inversa.</returns>
      <exception cref="T:System.Security.SecurityException">El llamador no tiene los permisos requeridos. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.HasExtension(System.String)">
      <summary>Determina si una ruta de acceso incluye una extensión de nombre de archivo.</summary>
      <returns>Es true si los caracteres que siguen al último separador de directorio (\\ o /) o separador de volumen (:) en la ruta de acceso incluyen un punto (.) seguido de uno o más caracteres; de lo contrario, es false.</returns>
      <param name="path">Ruta de acceso donde se busca una extensión. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o varios de los caracteres no válidos definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.IsPathRooted(System.String)">
      <summary>Obtiene un valor que indica si la cadena de ruta de acceso especificada contiene una raíz.</summary>
      <returns>Es true si <paramref name="path" /> contiene una raíz; de lo contrario, es false.</returns>
      <param name="path">Ruta de acceso que se va a probar. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o varios de los caracteres no válidos definidos en <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.PathSeparator">
      <summary>Carácter separador específico de la plataforma que se utiliza para separar cadenas de ruta de acceso en variables de entorno.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.VolumeSeparatorChar">
      <summary>Proporciona un carácter separador de volúmenes específico de la plataforma.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Net.WebUtility">
      <summary>Proporciona métodos para codificar y descodificar direcciones URL cuando se procesan solicitudes Web. </summary>
    </member>
    <member name="M:System.Net.WebUtility.HtmlDecode(System.String)">
      <summary>Convierte una cadena con código HTML en una cadena descodificada para la transmisión HTTP.</summary>
      <returns>Cadena descodificada.</returns>
      <param name="value">Cadena que se va a descodificar.</param>
    </member>
    <member name="M:System.Net.WebUtility.HtmlEncode(System.String)">
      <summary>Convierte una cadena en una cadena codificada en HTML.</summary>
      <returns>Cadena codificada.</returns>
      <param name="value">Cadena que se va a codificar.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecode(System.String)">
      <summary>Convierte en cadena descodificada una cadena que se ha codificado para su transmisión en una dirección URL.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena descodificada.</returns>
      <param name="encodedValue">Cadena codificada como dirección URL que se va a descodificar.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Convierte una matriz de bytes codificada que se ha codificado para la transmisión en una dirección URL en una matriz de bytes descodificada.</summary>
      <returns>Devuelve <see cref="T:System.Byte" />.Una matriz <see cref="T:System.Byte" /> descodificada.</returns>
      <param name="encodedValue">Matriz <see cref="T:System.Byte" /> codificada como dirección URL que se va a descodificar.</param>
      <param name="offset">El desplazamiento, en bytes, desde el principio de la matriz <see cref="T:System.Byte" /> para descodificar.</param>
      <param name="count">El recuento, en bytes, para descodificar de la matriz <see cref="T:System.Byte" />.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncode(System.String)">
      <summary>Convierte una cadena de texto en una cadena codificada como una dirección URL.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena codificada como una dirección URL.</returns>
      <param name="value">Texto que se va a codificar con una dirección URL.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Convierte una matriz de bytes en una matriz de bytes con código URL.</summary>
      <returns>Devuelve <see cref="T:System.Byte" />.Una matriz de <see cref="T:System.Byte" /> codificada.</returns>
      <param name="value">Matriz <see cref="T:System.Byte" /> que se va a codificar con una dirección URL.</param>
      <param name="offset">El desplazamiento, en bytes, desde el principio de la matriz <see cref="T:System.Byte" /> para codificar.</param>
      <param name="count">El recuento, en bytes, para codificar de la matriz <see cref="T:System.Byte" />.</param>
    </member>
    <member name="T:System.Runtime.Versioning.FrameworkName">
      <summary>Representa el nombre de una versión de .NET Framework.</summary>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.Versioning.FrameworkName" /> a partir de una cadena que contiene información sobre una versión de .NET Framework.</summary>
      <param name="frameworkName">Cadena que contiene información de versión de .NET Framework.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="frameworkName" /> es <see cref="F:System.String.Empty" />.O bien<paramref name="frameworkName" /> tiene menos de dos componentes o más de tres.O bien<paramref name="frameworkName" /> no incluye un número de versión principal o secundaria.O bien<paramref name="frameworkName " /> no incluye un número de versión válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="frameworkName" /> es null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.Versioning.FrameworkName" /> a partir de una cadena y un objeto <see cref="T:System.Version" /> que identifica una versión de .NET Framework.</summary>
      <param name="identifier">Cadena que identifica una versión de .NET Framework. </param>
      <param name="version">Objeto que contiene información de versión de .NET Framework.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> es <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> es null.O bien<paramref name="version" /> es null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.Versioning.FrameworkName" /> a partir de una cadena, un objeto <see cref="T:System.Version" /> que identifica una versión de .NET Framework y un nombre de perfil.</summary>
      <param name="identifier">Cadena que identifica una versión de .NET Framework.</param>
      <param name="version">Objeto que contiene información de versión de .NET Framework.</param>
      <param name="profile">Nombre de perfil.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> es <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> es null.O bien<paramref name="version" /> es null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Object)">
      <summary>Devuelve un valor que indica si esta instancia de <see cref="T:System.Runtime.Versioning.FrameworkName" /> representa la misma versión de .NET Framework que un objeto especificado.</summary>
      <returns>Es true si cada componente del objeto <see cref="T:System.Runtime.Versioning.FrameworkName" /> actual coincide con el componente correspondiente del parámetro <paramref name="obj" />; de lo contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con la actual instancia.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Runtime.Versioning.FrameworkName)">
      <summary>Devuelve un valor que indica si esta instancia de <see cref="T:System.Runtime.Versioning.FrameworkName" /> representa la misma versión de .NET Framework que una instancia de <see cref="T:System.Runtime.Versioning.FrameworkName" /> especificada.</summary>
      <returns>Es true si cada componente del objeto <see cref="T:System.Runtime.Versioning.FrameworkName" /> actual coincide con el componente correspondiente del parámetro <paramref name="other" />; de lo contrario, es false.</returns>
      <param name="other">Objeto que se va a comparar con la actual instancia.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.FullName">
      <summary>Obtiene el nombre completo de este objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Nombre completo de este objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.GetHashCode">
      <summary>Devuelve el código hash del objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Entero de 32 bits con signo que representa el código hash de esta instancia.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Identifier">
      <summary>Obtiene el identificador de este objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Identificador de este objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Equality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Devuelve un valor que indica si dos objetos <see cref="T:System.Runtime.Versioning.FrameworkName" /> representan la misma versión de .NET Framework.</summary>
      <returns>Es true si los parámetros <paramref name="left" /> y <paramref name="right" /> representan la misma versión de .NET Framework; de lo contrario, es false.</returns>
      <param name="left">Primer objeto que se va a comparar.</param>
      <param name="right">Segundo objeto que se va a comparar.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Inequality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Devuelve un valor que indica si dos objetos <see cref="T:System.Runtime.Versioning.FrameworkName" /> representan distintas versiones de .NET Framework.</summary>
      <returns>Es true si los parámetros <paramref name="left" /> y <paramref name="right" /> representan distintas versiones de .NET Framework; de lo contrario, es false.</returns>
      <param name="left">Primer objeto que se va a comparar.</param>
      <param name="right">Segundo objeto que se va a comparar.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Profile">
      <summary>Obtiene el nombre de perfil de este objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Nombre de perfil de este objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.ToString">
      <summary>Devuelve la representación de cadena de este objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Cadena que representa este objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Version">
      <summary>Obtiene la versión de este objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Objeto que contiene información de versión sobre este objeto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
  </members>
</doc>