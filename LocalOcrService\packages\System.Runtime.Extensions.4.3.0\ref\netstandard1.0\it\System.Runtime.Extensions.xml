﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.BitConverter">
      <summary>Converte i tipi di dati di base in una matrice di byte e viceversa.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.BitConverter.DoubleToInt64Bits(System.Double)">
      <summary>Converte il numero in virgola mobile e precisione doppia specificato in un intero con segno a 64 bit.</summary>
      <returns>Un intero con segno a 64 bit il cui valore è equivalente a <paramref name="value" />.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Boolean)">
      <summary>Restituisce il valore Boolean specificato come matrice di byte.</summary>
      <returns>Una matrice di byte di lunghezza 1.</returns>
      <param name="value">Un valore Boolean. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Char)">
      <summary>Restituisce il valore del carattere Unicode specificato come matrice di byte.</summary>
      <returns>Una matrice di byte di lunghezza 2.</returns>
      <param name="value">Carattere da convertire </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Double)">
      <summary>Restituisce il valore in virgola mobile e precisione doppia specificato come matrice di byte.</summary>
      <returns>Una matrice di byte di lunghezza 8.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int16)">
      <summary>Restituisce il valore dell'integer con segno a 16 bit specificato come matrice di byte.</summary>
      <returns>Una matrice di byte di lunghezza 2.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int32)">
      <summary>Restituisce il valore dell'integer con segno a 32 bit specificato come matrice di byte.</summary>
      <returns>Una matrice di byte di lunghezza 4.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int64)">
      <summary>Restituisce il valore dell'integer con segno a 64 bit specificato come matrice di byte.</summary>
      <returns>Una matrice di byte di lunghezza 8.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Single)">
      <summary>Restituisce il valore in virgola mobile e precisione singola specificato come matrice di byte.</summary>
      <returns>Una matrice di byte di lunghezza 4.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt16)">
      <summary>Restituisce il valore dell'integer senza segno a 16 bit specificato come matrice di byte.</summary>
      <returns>Una matrice di byte di lunghezza 2.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt32)">
      <summary>Restituisce il valore dell'integer senza segno a 32 bit specificato come matrice di byte.</summary>
      <returns>Una matrice di byte di lunghezza 4.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt64)">
      <summary>Restituisce il valore dell'integer senza segno a 64 bit specificato come matrice di byte.</summary>
      <returns>Una matrice di byte di lunghezza 8.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.Int64BitsToDouble(System.Int64)">
      <summary>Converte l'intero con segno a 64 bit specificato in un numero in virgola mobile e precisione doppia.</summary>
      <returns>Numero in virgola mobile e precisione doppia il cui valore è equivalente a <paramref name="value" />.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.BitConverter.IsLittleEndian">
      <summary>Indica l'ordine dei byte (caratteristica endian) per la memorizzazione dei dati all'interno dell'architettura di computer in uso.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToBoolean(System.Byte[],System.Int32)">
      <summary>Restituisce un valore Boolean convertito da un byte in una posizione specificata in una matrice di byte.</summary>
      <returns>true se il byte in <paramref name="startIndex" /> di <paramref name="value" /> è diverso da zero; in caso contrario false.</returns>
      <param name="value">Matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToChar(System.Byte[],System.Int32)">
      <summary>Restituisce un carattere Unicode convertito da due byte nella posizione specificata in una matrice di byte.</summary>
      <returns>Carattere formato da due byte a partire da <paramref name="startIndex" />.</returns>
      <param name="value">Una matrice. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> è uguale alla lunghezza di <paramref name="value" /> meno 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToDouble(System.Byte[],System.Int32)">
      <summary>Restituisce un numero in virgola mobile e precisione doppia convertito da otto byte nella posizione specificata in una matrice di byte.</summary>
      <returns>Un numero in virgola mobile e precisione doppia formato da otto byte a partire da <paramref name="startIndex" />.</returns>
      <param name="value">Matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> è maggiore o uguale alla lunghezza di <paramref name="value" /> meno 7 e minore o uguale alla lunghezza di <paramref name="value" /> meno 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt16(System.Byte[],System.Int32)">
      <summary>Restituisce un intero con segno a 16 bit convertito da due byte nella posizione specificata in una matrice di byte.</summary>
      <returns>Un intero con segno a 16 bit formato da due byte a partire da <paramref name="startIndex" />.</returns>
      <param name="value">Matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> è uguale alla lunghezza di <paramref name="value" /> meno 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt32(System.Byte[],System.Int32)">
      <summary>Restituisce un intero con segno a 32 bit convertito da quattro byte nella posizione specificata in una matrice di byte.</summary>
      <returns>Un intero con segno a 32 bit formato da quattro byte a partire da <paramref name="startIndex" />.</returns>
      <param name="value">Matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> è maggiore o uguale alla lunghezza di <paramref name="value" /> meno 3 e minore o uguale alla lunghezza di <paramref name="value" /> meno 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt64(System.Byte[],System.Int32)">
      <summary>Restituisce un intero con segno a 64 bit convertito da otto byte nella posizione specificata in una matrice di byte.</summary>
      <returns>Un intero con segno a 64 bit formato da otto byte a partire da <paramref name="startIndex" />.</returns>
      <param name="value">Matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> è maggiore o uguale alla lunghezza di <paramref name="value" /> meno 7 e minore o uguale alla lunghezza di <paramref name="value" /> meno 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToSingle(System.Byte[],System.Int32)">
      <summary>Restituisce un numero in virgola mobile e precisione singola convertito da quattro byte nella posizione specificata in una matrice di byte.</summary>
      <returns>Un numero in virgola mobile e precisione singola formato da quattro byte a partire da <paramref name="startIndex" />.</returns>
      <param name="value">Matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> è maggiore o uguale alla lunghezza di <paramref name="value" /> meno 3 e minore o uguale alla lunghezza di <paramref name="value" /> meno 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[])">
      <summary>Converte il valore numerico di ogni elemento di una matrice specificata di byte nella relativa rappresentazione di stringa esadecimale equivalente.</summary>
      <returns>Stringa di coppie esadecimali separate da trattini, dove ogni coppia rappresenta l'elemento corrispondente in <paramref name="value" />, ad esempio "7F-2C-4A-00".</returns>
      <param name="value">Matrice di byte. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32)">
      <summary>Converte il valore numerico di ogni elemento di una sottomatrice specificata di byte nella relativa rappresentazione di stringa esadecimale equivalente.</summary>
      <returns>Stringa di coppie esadecimali separate da trattini, dove ogni coppia rappresenta l'elemento corrispondente in una sottomatrice di <paramref name="value" />, ad esempio "7F-2C-4A-00".</returns>
      <param name="value">Matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32,System.Int32)">
      <summary>Converte il valore numerico di ogni elemento di una sottomatrice specificata di byte nella relativa rappresentazione di stringa esadecimale equivalente.</summary>
      <returns>Stringa di coppie esadecimali separate da trattini, dove ogni coppia rappresenta l'elemento corrispondente in una sottomatrice di <paramref name="value" />, ad esempio "7F-2C-4A-00".</returns>
      <param name="value">Matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <param name="length">Numero di elementi della matrice di <paramref name="value" /> da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro <paramref name="startIndex" /> o <paramref name="length" /> è minore di zero.- oppure -<paramref name="startIndex" /> è maggiore di zero ed è maggiore o uguale alla lunghezza di <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentException">La combinazione di <paramref name="startIndex" /> e di <paramref name="length" /> non specifica una posizione all'interno di <paramref name="value" />, vale a dire che il parametro <paramref name="startIndex" /> è maggiore della lunghezza di <paramref name="value" /> meno il parametro <paramref name="length" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt16(System.Byte[],System.Int32)">
      <summary>Restituisce un intero senza segno a 16 bit convertito da due byte nella posizione specificata in una matrice di byte.</summary>
      <returns>Un intero senza segno a 16 bit formato da due byte a partire da <paramref name="startIndex" />.</returns>
      <param name="value">La matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> è uguale alla lunghezza di <paramref name="value" /> meno 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt32(System.Byte[],System.Int32)">
      <summary>Restituisce un intero senza segno a 32 bit convertito da quattro byte nella posizione specificata in una matrice di byte.</summary>
      <returns>Un intero senza segno a 32 bit formato da quattro byte a partire da <paramref name="startIndex" />.</returns>
      <param name="value">Matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> è maggiore o uguale alla lunghezza di <paramref name="value" /> meno 3 e minore o uguale alla lunghezza di <paramref name="value" /> meno 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt64(System.Byte[],System.Int32)">
      <summary>Restituisce un intero senza segno a 64 bit convertito da otto byte nella posizione specificata in una matrice di byte.</summary>
      <returns>Un intero senza segno a 64 bit formato da otto byte a partire da <paramref name="startIndex" />.</returns>
      <param name="value">Matrice di byte. </param>
      <param name="startIndex">Posizione iniziale all'interno di <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> è maggiore o uguale alla lunghezza di <paramref name="value" /> meno 7 e minore o uguale alla lunghezza di <paramref name="value" /> meno 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />è minore di zero o maggiore della lunghezza di <paramref name="value" /> meno 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Convert">
      <summary>Converte un tipo di dati di base in un altro tipo di dati di base.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type)">
      <summary>Restituisce un oggetto del tipo specificato e il cui valore è equivalente all'oggetto specificato.</summary>
      <returns>Oggetto il cui tipo è <paramref name="conversionType" /> e il cui valore è equivalente a <paramref name="value" />.-oppure-Riferimento Null (Nothing in Visual Basic), se <paramref name="value" /> è null e <paramref name="conversionType" /> non è un tipo di valore. </returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="conversionType">Tipo di oggetto da restituire. </param>
      <exception cref="T:System.InvalidCastException">Questa conversione non è supportata.  -oppure-<paramref name="value" /> è null e <paramref name="conversionType" /> è un tipo di valore.-oppure-<paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è in un formato riconosciuto da <paramref name="conversionType" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero non incluso nell'intervallo di <paramref name="conversionType" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> è null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type,System.IFormatProvider)">
      <summary>Restituisce un oggetto del tipo specificato, il cui valore è equivalente all'oggetto specificato.Un parametro fornisce le informazioni di formattazione specifiche delle impostazioni cultura.</summary>
      <returns>Oggetto il cui tipo è <paramref name="conversionType" /> e il cui valore è equivalente a <paramref name="value" />.-oppure- <paramref name="value" />, se gli attributi <see cref="T:System.Type" /> di <paramref name="value" /> e <paramref name="conversionType" /> sono uguali.-oppure- Riferimento Null (Nothing in Visual Basic), se <paramref name="value" /> è null e <paramref name="conversionType" /> non è un tipo di valore.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="conversionType">Tipo di oggetto da restituire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.InvalidCastException">Questa conversione non è supportata. -oppure-<paramref name="value" /> è null e <paramref name="conversionType" /> è un tipo di valore.-oppure-<paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è in un formato per <paramref name="conversionType" /> riconosciuto da <paramref name="provider" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero non incluso nell'intervallo di <paramref name="conversionType" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> è null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.TypeCode,System.IFormatProvider)">
      <summary>Restituisce un oggetto del tipo specificato, il cui valore è equivalente all'oggetto specificato.Un parametro fornisce le informazioni di formattazione specifiche delle impostazioni cultura.</summary>
      <returns>Oggetto il cui tipo sottostante è <paramref name="typeCode" /> e il cui valore è equivalente a <paramref name="value" />.-oppure- Riferimento Null (Nothing in Visual Basic), se <paramref name="value" /> è null e <paramref name="typeCode" /> è <see cref="F:System.TypeCode.Empty" />, <see cref="F:System.TypeCode.String" /> o <see cref="F:System.TypeCode.Object" />.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="typeCode">Tipo di oggetto da restituire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.InvalidCastException">Questa conversione non è supportata.  -oppure-<paramref name="value" /> è null e <paramref name="typeCode" /> specifica un tipo di valore.-oppure-<paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è in un formato per il tipo <paramref name="typeCode" /> riconosciuto da <paramref name="provider" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero non incluso nell'intervallo del tipo <paramref name="typeCode" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeCode" /> non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64CharArray(System.Char[],System.Int32,System.Int32)">
      <summary>Converte un sottoinsieme di una matrice di caratteri Unicode, in cui sono codificati dati binari come cifre base 64, in una matrice di interi senza segno a 8 bit equivalente.I parametri specificano il sottoinsieme nella matrice di input e il numero di elementi da convertire.</summary>
      <returns>Matrice di interi senza segno a 8 bit equivalente agli elementi <paramref name="length" /> in corrispondenza della posizione <paramref name="offset" /> in <paramref name="inArray" />.</returns>
      <param name="inArray">Matrice di caratteri Unicode. </param>
      <param name="offset">Posizione all'interno di <paramref name="inArray" />. </param>
      <param name="length">Numero di elementi di <paramref name="inArray" /> da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> o <paramref name="length" /> è minore di 0.-oppure- La somma di <paramref name="offset" /> e <paramref name="length" /> indica una posizione non compresa in <paramref name="inArray" />. </exception>
      <exception cref="T:System.FormatException">La lunghezza di <paramref name="inArray" />, caratteri di spazio vuoto esclusi, non è zero o un multiplo di 4. -oppure-Il formato di <paramref name="inArray" /> non è valido.<paramref name="inArray" /> contiene un carattere non Base 64, più di due caratteri di spaziatura interna o un carattere tra i caratteri di spaziatura interna.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64String(System.String)">
      <summary>Converte la stringa specificata, che codifica dati binari come cifre base 64, in una matrice di interi senza segno a 8 bit equivalente.</summary>
      <returns>Matrice di interi senza segno a 8 bit equivalente a <paramref name="s" />.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> è null. </exception>
      <exception cref="T:System.FormatException">La lunghezza di <paramref name="s" />, caratteri di spazio vuoto esclusi, non è zero o un multiplo di 4. -oppure-Il formato di <paramref name="s" /> non è valido.<paramref name="s" /> contiene un carattere non Base 64, più di due caratteri di spaziatura interna o un carattere tra i caratteri di spaziatura interna.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.GetTypeCode(System.Object)">
      <summary>Restituisce l'attributo <see cref="T:System.TypeCode" /> dell'oggetto specificato.</summary>
      <returns>Oggetto <see cref="T:System.TypeCode" /> di <paramref name="value" /> oppure <see cref="F:System.TypeCode.Empty" /> se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64CharArray(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Converte un sottoinsieme di una matrice di interi senza segno a 8 bit in un sottoinsieme equivalente di una matrice di caratteri Unicode codificati con cifre base 64.I parametri specificano i sottoinsiemi come offset nelle matrici di input e output e il numero di elementi nella matrice di input da convertire.</summary>
      <returns>Un intero con segno a 32 bit contenente il numero di byte in <paramref name="outArray" />.</returns>
      <param name="inArray">Matrice di input di interi senza segno a 8 bit. </param>
      <param name="offsetIn">Posizione all'interno di <paramref name="inArray" />. </param>
      <param name="length">Numero di elementi di <paramref name="inArray" /> da convertire. </param>
      <param name="outArray">Matrice di output di caratteri Unicode. </param>
      <param name="offsetOut">Posizione all'interno di <paramref name="outArray" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> o <paramref name="outArray" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offsetIn" />, <paramref name="offsetOut" /> o <paramref name="length" /> è negativo.-oppure- la somma di <paramref name="offsetIn" /> e <paramref name="length" /> è maggiore della lunghezza di <paramref name="inArray" />.-oppure- La somma di <paramref name="offsetOut" /> e del numero di elementi da restituire è maggiore della lunghezza di <paramref name="outArray" /> . </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[])">
      <summary>Converte una matrice di interi senza segno a 8 bit nella relativa rappresentazione di stringa equivalente codificata con cifre base 64.</summary>
      <returns>Rappresentazione di stringa, in base 64, del contenuto di <paramref name="inArray" />.</returns>
      <param name="inArray">Matrice di interi senza segno a 8 bit. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> è null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[],System.Int32,System.Int32)">
      <summary>Converte un sottoinsieme di una matrice di interi senza segno a 8 bit nella relativa rappresentazione di stringa equivalente codificata con cifre base 64.I parametri specificano il sottoinsieme come offset nella matrice di input e il numero di elementi nella matrice da convertire.</summary>
      <returns>Rappresentazione di stringa in base 64 di elementi <paramref name="length" /> di <paramref name="inArray" />, a partire dalla posizione <paramref name="offset" />.</returns>
      <param name="inArray">Matrice di interi senza segno a 8 bit. </param>
      <param name="offset">Offset in <paramref name="inArray" />. </param>
      <param name="length">Numero di elementi di <paramref name="inArray" /> da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore di <paramref name="offset" /> o <paramref name="length" /> è negativo.-oppure- la somma di <paramref name="offset" /> e <paramref name="length" /> è maggiore della lunghezza di <paramref name="inArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Boolean)">
      <summary>Restituisce il valore booleano specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Valore booleano da restituire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Numero da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object)">
      <summary>Converte il valore di un oggetto specificato in un valore booleano equivalente.</summary>
      <returns>true o false, che riflette il valore restituito richiamando il metodo <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> per il tipo sottostante di <paramref name="value" />.Se <paramref name="value" /> è null, il metodo restituisce false.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> è una che non equivale a <see cref="F:System.Boolean.TrueString" /> o a <see cref="F:System.Boolean.FalseString" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />.-oppure-La conversione di <paramref name="value" /> in <see cref="T:System.Boolean" /> non è supportata.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un valore booleano equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>true o false, che riflette il valore restituito richiamando il metodo <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> per il tipo sottostante di <paramref name="value" />.Se <paramref name="value" /> è null, il metodo restituisce false.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> è una che non equivale a <see cref="F:System.Boolean.TrueString" /> o a <see cref="F:System.Boolean.FalseString" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />.-oppure-La conversione di <paramref name="value" /> in <see cref="T:System.Boolean" /> non è supportata. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un valore logico nel relativo valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> è uguale a <see cref="F:System.Boolean.TrueString" /> oppure false se <paramref name="value" /> è uguale a <see cref="F:System.Boolean.FalseString" /> o a null.</returns>
      <param name="value">Stringa che contiene il valore <see cref="F:System.Boolean.TrueString" /> o <see cref="F:System.Boolean.FalseString" />. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> è diverso da <see cref="F:System.Boolean.TrueString" /> oppure da <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un valore logico nel valore booleano equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>true se <paramref name="value" /> è uguale a <see cref="F:System.Boolean.TrueString" /> oppure false se <paramref name="value" /> è uguale a <see cref="F:System.Boolean.FalseString" /> o a null.</returns>
      <param name="value">Stringa che contiene il valore <see cref="F:System.Boolean.TrueString" /> o <see cref="F:System.Boolean.FalseString" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura.Questo parametro viene ignorato.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> è diverso da <see cref="F:System.Boolean.TrueString" /> oppure da <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt32)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt64)">
      <summary>Converte il valore dell'intero senza segno a 64 bit specificato in un valore booleano equivalente.</summary>
      <returns>true se <paramref name="value" /> non è zero; in caso contrario, false.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Boolean)">
      <summary>Converte il valore booleano specificato nell'intero senza segno a 8 bit equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Byte)">
      <summary>Restituisce l'intero senza segno a 8 bit specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Intero senza segno a 8 bit da restituire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Char)">
      <summary>Converte il valore del carattere Unicode specificato nell'intero senza segno a 8 bit equivalente.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Carattere Unicode da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero maggiore di <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un intero senza segno a 8 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 8 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Byte.MaxValue" /> o minore di <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un intero senza segno a 8 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 8 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Byte.MaxValue" /> o minore di <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato in un intero senza segno a 8 bit equivalente.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di <see cref="F:System.Byte.MinValue" /> o maggiore di <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un intero senza segno a 8 bit equivalente.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di <see cref="F:System.Byte.MinValue" /> o maggiore di <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato in un intero senza segno a 8 bit equivalente.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di <see cref="F:System.Byte.MinValue" /> o maggiore di <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un intero senza segno a 8 bit.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato della proprietà per un valore <see cref="T:System.Byte" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa <see cref="T:System.IConvertible" />. -oppure-La conversione da <paramref name="value" /> al tipo <see cref="T:System.Byte" /> non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Byte.MinValue" /> o maggiore di <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un intero senza segno a 8 bit mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato della proprietà per un valore <see cref="T:System.Byte" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa <see cref="T:System.IConvertible" />. -oppure-La conversione da <paramref name="value" /> al tipo <see cref="T:System.Byte" /> non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Byte.MinValue" /> o maggiore di <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato in un intero senza segno a 8 bit equivalente.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato in un intero senza segno a 8 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 8 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Byte.MaxValue" /> o minore di <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero senza segno a 8 bit equivalente.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Byte.MinValue" /> o maggiore di <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero senza segno a 8 bit equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Byte.MinValue" /> o maggiore di <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.Int32)">
      <summary>Converte la rappresentazione di stringa di un numero in una base specificata nell'intero senza segno a 8 bit equivalente.</summary>
      <returns>Intero senza segno a 8 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="fromBase">Base del numero in <paramref name="value" />, che deve essere 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> non è 2, 8, 10 o 16. -oppure-<paramref name="value" />, che rappresenta un numero senza segno non base 10, è preceduto da un segno negativo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> è <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carattere costituito da una cifra non valida nella base specificata da <paramref name="fromBase" />.Nel messaggio dell'eccezione viene indicato che non sono presenti cifre da convertire se il primo carattere in <paramref name="value" /> non è valido. In caso contrario, viene indicato che <paramref name="value" /> contiene caratteri finali non validi.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, che rappresenta un numero senza segno base 10, è preceduto da un segno negativo.-oppure-<paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Byte.MinValue" /> o maggiore di <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato in un intero senza segno a 8 bit equivalente.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt32)">
      <summary>Converte l'intero senza segno a 32 bit specificato in un equivalente intero senza segno a 8 bit.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>Intero senza segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nel carattere Unicode equivalente.</summary>
      <returns>Carattere Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato nel carattere Unicode equivalente.</summary>
      <returns>Carattere Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato nel carattere Unicode equivalente.</summary>
      <returns>Carattere Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di <see cref="F:System.Char.MinValue" /> o maggiore di <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato nel carattere Unicode equivalente.</summary>
      <returns>Carattere Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di <see cref="F:System.Char.MinValue" /> o maggiore di <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un carattere Unicode.</summary>
      <returns>Carattere Unicode equivalente a value oppure <see cref="F:System.Char.MinValue" /> se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è una stringa Null.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />.-oppure-La conversione di <paramref name="value" /> in <see cref="T:System.Char" /> non è supportata. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di <see cref="F:System.Char.MinValue" /> o maggiore di <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato nel relativo carattere Unicode equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Carattere Unicode equivalente a <paramref name="value" /> oppure <see cref="F:System.Char.MinValue" /> se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è una stringa Null.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione di <paramref name="value" /> in <see cref="T:System.Char" /> non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di <see cref="F:System.Char.MinValue" /> o maggiore di <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nel carattere Unicode equivalente.</summary>
      <returns>Carattere Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String)">
      <summary>Converte il primo carattere di una stringa specificata in un carattere Unicode.</summary>
      <returns>Carattere Unicode equivalente al primo e unico carattere in <paramref name="value" />.</returns>
      <param name="value">Stringa di lunghezza 1. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.FormatException">La lunghezza di <paramref name="value" /> non è 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String,System.IFormatProvider)">
      <summary>Converte il primo carattere di una stringa specificata in un carattere Unicode mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Carattere Unicode equivalente al primo e unico carattere in <paramref name="value" />.</returns>
      <param name="value">Stringa di lunghezza 1 oppure null. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura.Questo parametro viene ignorato.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.FormatException">La lunghezza di <paramref name="value" /> non è 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nel carattere Unicode equivalente.</summary>
      <returns>Carattere Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt32)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato nel carattere Unicode equivalente.</summary>
      <returns>Carattere Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt64)">
      <summary>Converte il valore dell'intero senza segno a 64 bit specificato nel carattere Unicode equivalente.</summary>
      <returns>Carattere Unicode equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un oggetto <see cref="T:System.DateTime" />.</summary>
      <returns>Data e ora equivalenti al valore di <paramref name="value" /> oppure data e ora equivalenti a <see cref="F:System.DateTime.MinValue" /> se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è un valore valido per indicare la data o l'ora.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un oggetto <see cref="T:System.DateTime" /> mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Data e ora equivalenti al valore di <paramref name="value" /> oppure data e ora equivalenti a <see cref="F:System.DateTime.MinValue" /> se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è un valore valido per indicare la data o l'ora.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di una data e un'ora in un valore di data e ora equivalente.</summary>
      <returns>Data e ora equivalenti al valore di <paramref name="value" /> oppure data e ora equivalenti a <see cref="F:System.DateTime.MinValue" /> se <paramref name="value" /> è null.</returns>
      <param name="value">Rappresentazione di stringa di una data e ora.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è una stringa di data e ora nel formato corretto. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un valore di data e ora equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Data e ora equivalenti al valore di <paramref name="value" /> oppure data e ora equivalenti a <see cref="F:System.DateTime.MinValue" /> se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente una data e un'ora da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è una stringa di data e ora nel formato corretto. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Boolean)">
      <summary>Converte il valore booleano specificato nel numero decimale equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nel numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Decimal)">
      <summary>Restituisce il numero decimale specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Numero decimale. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" />. </returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Decimal.MaxValue" /> o minore di <see cref="F:System.Decimal.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato in un numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato in un numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è in un formato appropriato per un tipo <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Decimal.MinValue" /> o maggiore di <see cref="F:System.Decimal.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un numero decimale equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è in un formato appropriato per un tipo <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />.-oppure-La conversione non è supportata. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Decimal.MinValue" /> o maggiore di <see cref="F:System.Decimal.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nel numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato nel numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" />. </returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Decimal.MaxValue" /> o minore di <see cref="F:System.Decimal.MinValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente un numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è un numero di formato valido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Decimal.MinValue" /> o maggiore di <see cref="F:System.Decimal.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un numero decimale equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Numero decimale equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente un numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è un numero di formato valido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Decimal.MinValue" /> o maggiore di <see cref="F:System.Decimal.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato in un numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt32)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato in un numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt64)">
      <summary>Converte il valore dell'intero senza segno a 64 bit specificato in un numero decimale equivalente.</summary>
      <returns>Numero decimale equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Boolean)">
      <summary>Converte il valore booleano specificato nel numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nel numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" />.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Double)">
      <summary>Restituisce il numero a virgola mobile a precisione doppia specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da restituire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato in un numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato in un numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un numero a virgola mobile a precisione doppia.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è in un formato appropriato per un tipo <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Double.MinValue" /> o maggiore di <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un numero a virgola mobile a precisione doppia mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è in un formato appropriato per un tipo <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Double.MinValue" /> o maggiore di <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nel numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato in un numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" />.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è un numero di formato valido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Double.MinValue" /> o maggiore di <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un numero a virgola mobile a precisione doppia equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è un numero di formato valido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Double.MinValue" /> o maggiore di <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nel numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt32)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato in un numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt64)">
      <summary>Converte il valore dell'intero senza segno a 64 bit specificato in un numero a virgola mobile a precisione doppia equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione doppia equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Boolean)">
      <summary>Converte il valore booleano specificato nell'intero con segno a 16 bit equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 16-bit signed integer.</summary>
      <returns>Intero con segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Char)">
      <summary>Converte il valore del carattere Unicode specificato nell'intero con segno a 16 bit equivalente.</summary>
      <returns>Intero con segno a 16 bit equivalente a <paramref name="value" />. </returns>
      <param name="value">Carattere Unicode da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un intero con segno a 16 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 16 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int16.MaxValue" /> o minore di <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un intero con segno a 16 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 16 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int16.MaxValue" /> o minore di <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int16)">
      <summary>Restituisce l'intero con segno a 16 bit specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Intero con segno a 16 bit da restituire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un intero con segno a 16 bit equivalente.</summary>
      <returns>Intero con segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int16.MaxValue" /> o minore di <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato in un intero con segno a 16 bit equivalente.</summary>
      <returns>Intero con segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int16.MaxValue" /> o minore di <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un intero con segno a 16 bit.</summary>
      <returns>Intero con segno a 16 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è in un formato appropriato per un tipo <see cref="T:System.Int16" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int16.MinValue" /> o maggiore di <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un intero con segno a 16 bit mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero con segno a 16 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è in un formato appropriato per un tipo <see cref="T:System.Int16" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int16.MinValue" /> o maggiore di <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nell'intero con segno a 16 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato in un intero con segno a 16 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 16 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int16.MaxValue" /> o minore di <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero con segno a 16 bit equivalente.</summary>
      <returns>Intero con segno a 16 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int16.MinValue" /> o maggiore di <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero con segno a 16 bit equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero con segno a 16 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int16.MinValue" /> o maggiore di <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.Int32)">
      <summary>Converte la rappresentazione di stringa di un numero in una base specificata nell'intero con segno a 16 bit equivalente.</summary>
      <returns>Intero con segno a 16 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="fromBase">Base del numero in <paramref name="value" />, che deve essere 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> non è 2, 8, 10 o 16. -oppure-<paramref name="value" />, che rappresenta un numero con segno non base 10, è preceduto da un segno negativo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> è <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carattere costituito da una cifra non valida nella base specificata da <paramref name="fromBase" />.Nel messaggio dell'eccezione viene indicato che non sono presenti cifre da convertire se il primo carattere in <paramref name="value" /> non è valido. In caso contrario, viene indicato che <paramref name="value" /> contiene caratteri finali non validi.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, che rappresenta un numero con segno non base 10, è preceduto da un segno negativo.-oppure-<paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int16.MinValue" /> o maggiore di <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nell'intero con segno a 16 bit equivalente.</summary>
      <returns>Intero con segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt32)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato in un intero con segno a 16 bit equivalente.</summary>
      <returns>Intero con segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt64)">
      <summary>Converte il valore dell'intero senza segno a 64 bit specificato in un intero con segno a 16 bit equivalente.</summary>
      <returns>Intero con segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Boolean)">
      <summary>Converte il valore booleano specificato nell'intero con segno a 32 bit equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nell'intero con segno a 32 bit equivalente.</summary>
      <returns>Intero con segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Char)">
      <summary>Converte il valore del carattere Unicode specificato nell'intero con segno a 32 bit equivalente.</summary>
      <returns>Intero con segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Carattere Unicode da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un intero con segno a 32 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 32 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int32.MaxValue" /> o minore di <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un intero con segno a 32 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 32 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int32.MaxValue" /> o minore di <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>Intero con segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int32)">
      <summary>Restituisce l'intero con segno a 32 bit specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Intero con segno a 32 bit da restituire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>Intero con segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int32.MaxValue" /> o minore di <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un intero con segno a 32 bit.</summary>
      <returns>Intero con segno a 32 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int32.MinValue" /> o maggiore di <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un intero con segno a 32 bit mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero con segno a 32 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int32.MinValue" /> o maggiore di <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nell'intero con segno a 32 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato in un intero con segno a 32 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 32 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int32.MaxValue" /> o minore di <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero con segno a 32 bit equivalente.</summary>
      <returns>Intero con segno a 32 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int32.MinValue" /> o maggiore di <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero con segno a 32 bit equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero con segno a 32 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int32.MinValue" /> o maggiore di <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.Int32)">
      <summary>Converte la rappresentazione di stringa di un numero in una base specificata nell'intero con segno a 32 bit equivalente.</summary>
      <returns>Intero con segno a 32 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="fromBase">Base del numero in <paramref name="value" />, che deve essere 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> non è 2, 8, 10 o 16. -oppure-<paramref name="value" />, che rappresenta un numero con segno non base 10, è preceduto da un segno negativo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> è <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carattere costituito da una cifra non valida nella base specificata da <paramref name="fromBase" />.Nel messaggio dell'eccezione viene indicato che non sono presenti cifre da convertire se il primo carattere in <paramref name="value" /> non è valido. In caso contrario, viene indicato che <paramref name="value" /> contiene caratteri finali non validi.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, che rappresenta un numero con segno non base 10, è preceduto da un segno negativo.-oppure-<paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int32.MinValue" /> o maggiore di <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nell'intero con segno a 32 bit equivalente.</summary>
      <returns>Intero con segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt32)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato in un intero con segno a 32 bit equivalente.</summary>
      <returns>Intero con segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 32-bit signed integer.</summary>
      <returns>Intero con segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Boolean)">
      <summary>Converte il valore booleano specificato nell'intero con segno a 64 bit equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nell'intero con segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Char)">
      <summary>Converte il valore del carattere Unicode specificato nell'intero con segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Carattere Unicode da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un intero con segno a 64 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 64 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int64.MaxValue" /> o minore di <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un intero con segno a 64 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 64 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int64.MaxValue" /> o minore di <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato in un intero con segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un intero con segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int64)">
      <summary>Restituisce l'intero con segno a 64 bit specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Intero con segno a 64 bit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un intero con segno a 64 bit.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int64.MinValue" /> o maggiore di <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un intero con segno a 64 bit mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />.-oppure-La conversione non è supportata. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int64.MinValue" /> o maggiore di <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nell'intero con segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato in un intero con segno a 64 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 64 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int64.MaxValue" /> o minore di <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero con segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente un numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int64.MinValue" /> o maggiore di <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero con segno a 64 bit equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero con segno a 64 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int64.MinValue" /> o maggiore di <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.Int32)">
      <summary>Converte la rappresentazione di stringa di un numero in una base specificata nell'intero con segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="fromBase">Base del numero in <paramref name="value" />, che deve essere 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> non è 2, 8, 10 o 16. -oppure-<paramref name="value" />, che rappresenta un numero con segno non base 10, è preceduto da un segno negativo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> è <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carattere costituito da una cifra non valida nella base specificata da <paramref name="fromBase" />.Nel messaggio dell'eccezione viene indicato che non sono presenti cifre da convertire se il primo carattere in <paramref name="value" /> non è valido. In caso contrario, viene indicato che <paramref name="value" /> contiene caratteri finali non validi.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, che rappresenta un numero con segno non base 10, è preceduto da un segno negativo.-oppure-<paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Int64.MinValue" /> o maggiore di <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nell'intero con segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt32)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato in un intero con segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt64)">
      <summary>Converte il valore dell'intero senza segno a 64 bit specificato in un intero con segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Boolean)">
      <summary>Converte il valore booleano specificato nell'intero con segno a 8 bit equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nell'intero con segno a 8 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Char)">
      <summary>Converte il valore del carattere Unicode specificato nell'intero con segno a 8 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Carattere Unicode da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un intero con segno a 8 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 8 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" /> o minore di <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un intero con segno a 8 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 8 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" /> o minore di <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 8-bit signed integer.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" /> o minore di <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un intero con segno a 8 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" /> o minore di <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato in un intero con segno a 8 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" /> o minore di <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un intero con segno a 8 bit.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.SByte.MinValue" /> o maggiore di <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un intero con segno a 8 bit mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.SByte.MinValue" /> o maggiore di <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.SByte)">
      <summary>Restituisce l'intero con segno a 8 bit specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Intero con segno a 8 bit da restituire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato in un intero con segno a 8 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero con segno a 8 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" /> o minore di <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero con segno a 8 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se value è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.SByte.MinValue" /> o maggiore di <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero con segno a 8 bit equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.SByte.MinValue" /> o maggiore di <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.Int32)">
      <summary>Converte la rappresentazione di stringa di un numero in una base specificata nell'intero con segno a 8 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="fromBase">Base del numero in <paramref name="value" />, che deve essere 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> non è 2, 8, 10 o 16. -oppure-<paramref name="value" />, che rappresenta un numero con segno non base 10, è preceduto da un segno negativo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> è <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carattere costituito da una cifra non valida nella base specificata da <paramref name="fromBase" />.Nel messaggio dell'eccezione viene indicato che non sono presenti cifre da convertire se il primo carattere in <paramref name="value" /> non è valido. In caso contrario, viene indicato che <paramref name="value" /> contiene caratteri finali non validi.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, che rappresenta un numero con segno non base 10, è preceduto da un segno negativo.-oppure-<paramref name="value" /> rappresenta un numero minore di <see cref="F:System.SByte.MinValue" /> o maggiore di <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nell'intero con segno a 8 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt32)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato in un intero con segno a 8 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" /> o minore di <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt64)">
      <summary>Converte il valore dell'intero senza segno a 64 bit specificato in un intero con segno a 8 bit equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.SByte.MaxValue" /> o minore di <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Boolean)">
      <summary>Converte il valore booleano specificato nel numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nel numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" />.<paramref name="value" /> è arrotondato utilizzando l'arrotondamento al più vicino.Se arrotondato a due numeri decimali, ad esempio, il valore 2,345 diventa 2,34 e il valore 2,355 diventa 2,36.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" />.<paramref name="value" /> è arrotondato utilizzando l'arrotondamento al più vicino.Se arrotondato a due numeri decimali, ad esempio, il valore 2,345 diventa 2,34 e il valore 2,355 diventa 2,36.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato in un numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato in un numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un numero a virgola mobile a precisione singola.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Single.MinValue" /> o maggiore di <see cref="F:System.Single.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un numero a virgola mobile a precisione singola mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Single.MinValue" /> o maggiore di <see cref="F:System.Single.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nel numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Intero con segno a 8 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Single)">
      <summary>Restituisce il numero a virgola mobile a precisione singola specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da restituire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è un numero di formato valido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Single.MinValue" /> o maggiore di <see cref="F:System.Single.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un numero a virgola mobile a precisione singola equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è un numero di formato valido.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.Single.MinValue" /> o maggiore di <see cref="F:System.Single.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nel numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt32)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato in un numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt64)">
      <summary>Converte il valore dell'intero senza segno a 64 bit specificato in un numero a virgola mobile a precisione singola equivalente.</summary>
      <returns>Numero a virgola mobile e con precisione singola equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean)">
      <summary>Converte il valore booleano specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean,System.IFormatProvider)">
      <summary>Converte il valore booleano specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <param name="provider">Istanza di un oggetto.Questo parametro viene ignorato.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.IFormatProvider)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.Int32)">
      <summary>Converte il valore di un intero senza segno a 8 bit nella rappresentazione di stringa equivalente in una base specificata.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <param name="toBase">Base del valore restituito, che deve essere 2, 8, 10 o 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> non è 2, 8, 10 o 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char)">
      <summary>Converte il valore del carattere Unicode specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Carattere Unicode da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char,System.IFormatProvider)">
      <summary>Converte il valore del carattere Unicode specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Carattere Unicode da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura.Questo parametro viene ignorato.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime)">
      <summary>Converte il valore dell'oggetto <see cref="T:System.DateTime" /> specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Valore di data e ora da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto <see cref="T:System.DateTime" /> specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Valore di data e ora da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal,System.IFormatProvider)">
      <summary>Converte il valore del numero decimale specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double,System.IFormatProvider)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.IFormatProvider)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.Int32)">
      <summary>Converte il valore di un intero con segno a 16 bit nella relativa rappresentazione di stringa equivalente in una base specificata.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <param name="toBase">Base del valore restituito, che deve essere 2, 8, 10 o 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> non è 2, 8, 10 o 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.IFormatProvider)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.Int32)">
      <summary>Converte il valore di un intero con segno a 32 bit nella relativa rappresentazione di stringa equivalente in una base specificata.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <param name="toBase">Base del valore restituito, che deve essere 2, 8, 10 o 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> non è 2, 8, 10 o 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.IFormatProvider)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.Int32)">
      <summary>Converte il valore di un intero con segno a 64 bit nella relativa rappresentazione di stringa equivalente in una base specificata.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <param name="toBase">Base del valore restituito, che deve essere 2, 8, 10 o 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> non è 2, 8, 10 o 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object)">
      <summary>Converte il valore dell'oggetto specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" /> o <see cref="F:System.String.Empty" /> se <paramref name="value" /> è un oggetto il cui valore è null.Se <paramref name="value" /> è null, il metodo restituisce null.</returns>
      <param name="value">Oggetto che fornisce il valore da convertire oppure null. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" /> o <see cref="F:System.String.Empty" /> se <paramref name="value" /> è un oggetto il cui valore è null.Se <paramref name="value" /> è null, il metodo restituisce null.</returns>
      <param name="value">Oggetto che fornisce il valore da convertire oppure null. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte,System.IFormatProvider)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single,System.IFormatProvider)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16,System.IFormatProvider)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32,System.IFormatProvider)">
      <summary>Converte il valore dell'intero senza segno a 32 bit specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64)">
      <summary>Converte il valore dell'intero senza segno a 64 bit specificato nella relativa rappresentazione di stringa equivalente.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64,System.IFormatProvider)">
      <summary>Converte il valore dell'intero senza segno a 64 bit specificato nella relativa rappresentazione di stringa equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Rappresentazione di stringa di <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Boolean)">
      <summary>Converte il valore booleano specificato nell'intero senza segno a 16 bit equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nell'intero senza segno a 16 bit equivalente.</summary>
      <returns>Intero senza segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Char)">
      <summary>Converte il valore del carattere Unicode specificato nell'intero senza segno a 16 bit equivalente.</summary>
      <returns>Intero senza segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Carattere Unicode da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un intero senza segno a 16 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 16 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un intero senza segno a 16 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 16 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato nell'intero senza segno a 16 bit equivalente.</summary>
      <returns>Intero senza segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un intero senza segno a 16 bit equivalente.</summary>
      <returns>Intero senza segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato in un intero senza segno a 16 bit equivalente.</summary>
      <returns>Intero senza segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un intero senza segno a 16 bit.</summary>
      <returns>Intero senza segno a 16 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt16.MinValue" /> o maggiore di <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un intero senza segno a 16 bit mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero senza segno a 16 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt16.MinValue" /> o maggiore di <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nell'intero senza segno a 16 bit equivalente.</summary>
      <returns>Intero senza segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato in un intero senza segno a 16 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 16 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero senza segno a 16 bit equivalente.</summary>
      <returns>Intero senza segno a 16 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt16.MinValue" /> o maggiore di <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero senza segno a 16 bit equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero senza segno a 16 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt16.MinValue" /> o maggiore di <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.Int32)">
      <summary>Converte la rappresentazione di stringa di un numero in una base specificata nell'intero senza segno a 16 bit equivalente.</summary>
      <returns>Intero senza segno a 16 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="fromBase">Base del numero in <paramref name="value" />, che deve essere 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> non è 2, 8, 10 o 16. -oppure-<paramref name="value" />, che rappresenta un numero senza segno non base 10, è preceduto da un segno negativo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> è <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carattere costituito da una cifra non valida nella base specificata da <paramref name="fromBase" />.Nel messaggio dell'eccezione viene indicato che non sono presenti cifre da convertire se il primo carattere in <paramref name="value" /> non è valido. In caso contrario, viene indicato che <paramref name="value" /> contiene caratteri finali non validi.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, che rappresenta un numero senza segno non base 10, è preceduto da un segno negativo.-oppure-<paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt16.MinValue" /> o maggiore di <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt16)">
      <summary>Restituisce l'intero senza segno a 16 bit specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Intero senza segno a 16 bit da restituire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt32)">
      <summary>Converte l'intero senza segno a 32 bit specificato in un equivalente intero senza segno a 16 bit.</summary>
      <returns>Intero senza segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 16-bit unsigned integer.</summary>
      <returns>Intero senza segno a 16 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Boolean)">
      <summary>Converte il valore booleano specificato nell'intero senza segno a 32 bit equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nell'intero senza segno a 32 bit equivalente.</summary>
      <returns>Intero senza segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Char)">
      <summary>Converte il valore del carattere Unicode specificato nell'intero senza segno a 32 bit equivalente.</summary>
      <returns>Intero senza segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Carattere Unicode da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un intero senza segno a 32 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 32 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un intero senza segno a 32 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 32 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato nell'intero senza segno a 32 bit equivalente.</summary>
      <returns>Intero senza segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un intero senza segno a 32 bit equivalente.</summary>
      <returns>Intero senza segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato in un intero senza segno a 32 bit equivalente.</summary>
      <returns>Intero senza segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un intero senza segno a 32 bit.</summary>
      <returns>Intero senza segno a 32 bit equivalente a <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt32.MinValue" /> o maggiore di <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un intero senza segno a 32 bit mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero senza segno a 32 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt32.MinValue" /> o maggiore di <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nell'intero senza segno a 32 bit equivalente.</summary>
      <returns>Intero senza segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato in un intero senza segno a 32 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 32 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero senza segno a 32 bit equivalente.</summary>
      <returns>Intero senza segno a 32 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt32.MinValue" /> o maggiore di <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero senza segno a 32 bit equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero senza segno a 32 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt32.MinValue" /> o maggiore di <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.Int32)">
      <summary>Converte la rappresentazione di stringa di un numero in una base specificata nell'intero senza segno a 32 bit equivalente.</summary>
      <returns>Intero senza segno a 32 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="fromBase">Base del numero in <paramref name="value" />, che deve essere 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> non è 2, 8, 10 o 16. -oppure-<paramref name="value" />, che rappresenta un numero senza segno non base 10, è preceduto da un segno negativo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> è <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carattere costituito da una cifra non valida nella base specificata da <paramref name="fromBase" />.Nel messaggio dell'eccezione viene indicato che non sono presenti cifre da convertire se il primo carattere in <paramref name="value" /> non è valido. In caso contrario, viene indicato che <paramref name="value" /> contiene caratteri finali non validi.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, che rappresenta un numero senza segno non base 10, è preceduto da un segno negativo.-oppure-<paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt32.MinValue" /> o maggiore di <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nell'intero senza segno a 32 bit equivalente.</summary>
      <returns>Intero senza segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt32)">
      <summary>Restituisce l'intero senza segno a 32 bit specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Intero senza segno a 32 bit da restituire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 32-bit unsigned integer.</summary>
      <returns>Intero senza segno a 32 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è maggiore di <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Boolean)">
      <summary>Converte il valore booleano specificato nell'intero senza segno a 64 bit equivalente.</summary>
      <returns>Il numero 1 se <paramref name="value" /> è true; in caso contrario, 0.</returns>
      <param name="value">Valore booleano da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Byte)">
      <summary>Converte il valore dell'intero senza segno a 8 bit specificato nell'intero senza segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 8 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Char)">
      <summary>Converte il valore del carattere Unicode specificato nell'intero senza segno a 64 bit equivalente.</summary>
      <returns>Intero senza segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Carattere Unicode da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Decimal)">
      <summary>Converte il valore del numero decimale specificato in un intero senza segno a 64 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 64 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero decimale da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Double)">
      <summary>Converte il valore del numero a virgola mobile a precisione doppia specificato in un intero senza segno a 64 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 64 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione doppia da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int16)">
      <summary>Converte il valore dell'intero con segno a 16 bit specificato nell'intero senza segno a 64 bit equivalente.</summary>
      <returns>Intero senza segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 16 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int32)">
      <summary>Converte il valore dell'intero con segno a 32 bit specificato in un intero senza segno a 64 bit equivalente.</summary>
      <returns>Intero senza segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 32 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int64)">
      <summary>Converte il valore dell'intero con segno a 64 bit specificato in un intero senza segno a 64 bit equivalente.</summary>
      <returns>Intero senza segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 64 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object)">
      <summary>Converte il valore dell'oggetto specificato in un intero senza segno a 64 bit.</summary>
      <returns>Intero senza segno a 64 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" /> oppure null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt64.MinValue" /> o maggiore di <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object,System.IFormatProvider)">
      <summary>Converte il valore dell'oggetto specificato in un intero senza segno a 64 bit mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero senza segno a 64 bit equivalente a <paramref name="value" /> oppure zero se <paramref name="value" /> è null.</returns>
      <param name="value">Oggetto che implementa l'interfaccia <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è nel formato appropriato.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> non implementa l'interfaccia <see cref="T:System.IConvertible" />. -oppure-La conversione non è supportata.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt64.MinValue" /> o maggiore di <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.SByte)">
      <summary>Converte il valore dell'intero con segno a 8 bit specificato nell'intero senza segno a 64 bit equivalente.</summary>
      <returns>Intero senza segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero con segno a 8 bit da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Single)">
      <summary>Converte il valore del numero a virgola mobile a precisione singola specificato in un intero senza segno a 64 bit equivalente.</summary>
      <returns>
        <paramref name="value" /> arrotondato all'intero senza segno a 64 bit più vicino.Se <paramref name="value" /> si trova a metà tra due numeri interi, viene restituito il numero intero pari; vale a dire, 4,5 viene convertito in 4, mentre 5,5 viene convertito in 6.</returns>
      <param name="value">Numero a virgola mobile e con precisione singola da convertire. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è minore di zero o maggiore di <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero senza segno a 64 bit equivalente.</summary>
      <returns>Intero con segno a 64 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt64.MinValue" /> o maggiore di <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.IFormatProvider)">
      <summary>Converte la rappresentazione di stringa specificata di un numero in un intero senza segno a 64 bit equivalente mediante le informazioni di formattazione specifiche delle impostazioni cultura indicate.</summary>
      <returns>Intero senza segno a 64 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="provider">Oggetto che fornisce informazioni di formattazione specifiche delle impostazioni cultura. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> non è composto da un segno opzionale seguito da un sequenza di cifre (da 0 a 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt64.MinValue" /> o maggiore di <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.Int32)">
      <summary>Converte la rappresentazione di stringa di un numero in una base specificata nell'intero senza segno a 64 bit equivalente.</summary>
      <returns>Intero senza segno a 64 bit equivalente al numero in <paramref name="value" /> oppure 0 (zero) se <paramref name="value" /> è null.</returns>
      <param name="value">Stringa contenente il numero da convertire. </param>
      <param name="fromBase">Base del numero in <paramref name="value" />, che deve essere 2, 8, 10 o 16.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> non è 2, 8, 10 o 16. -oppure-<paramref name="value" />, che rappresenta un numero senza segno non base 10, è preceduto da un segno negativo. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> è <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contiene un carattere costituito da una cifra non valida nella base specificata da <paramref name="fromBase" />.Nel messaggio dell'eccezione viene indicato che non sono presenti cifre da convertire se il primo carattere in <paramref name="value" /> non è valido. In caso contrario, viene indicato che <paramref name="value" /> contiene caratteri finali non validi.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, che rappresenta un numero senza segno non base 10, è preceduto da un segno negativo.-oppure-<paramref name="value" /> rappresenta un numero minore di <see cref="F:System.UInt64.MinValue" /> o maggiore di <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt16)">
      <summary>Converte il valore dell'intero senza segno a 16 bit specificato nell'intero senza segno a 64 bit equivalente.</summary>
      <returns>Intero senza segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 16 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt32)">
      <summary>Converte l'intero senza segno a 32 bit specificato in un equivalente intero senza segno a 64 bit.</summary>
      <returns>Intero senza segno a 64 bit equivalente a <paramref name="value" />.</returns>
      <param name="value">Intero senza segno a 32 bit da convertire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt64)">
      <summary>Restituisce l'intero senza segno a 64 bit specificato; non viene eseguita alcuna conversione effettiva.</summary>
      <returns>
        <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Intero senza segno a 64 bit da restituire. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Environment">
      <summary>Fornisce informazioni e mezzi per manipolare l'ambiente e la piattaforma correnti.La classe non può essere ereditata.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.CurrentManagedThreadId">
      <summary>Ottiene un identificatore univoco per il thread gestito corrente.</summary>
      <returns>Intero che rappresenta un identificatore univoco per il thread gestito.</returns>
    </member>
    <member name="M:System.Environment.ExpandEnvironmentVariables(System.String)">
      <summary>Sostituisce il nome di ogni variabile di ambiente incorporata nella stringa specificata con l'equivalente di stringa del valore della variabile, quindi restituisce la stringa risultante.</summary>
      <returns>Stringa in cui ogni variabile di ambiente viene sostituita dal relativo valore.</returns>
      <param name="name">Stringa contenente i nomi di zero o più variabili di ambiente.Ogni variabile di ambiente è inserita tra apici con il simbolo di percentuale (%).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.FailFast(System.String)">
      <summary>Interrompe immediatamente un processo dopo aver scritto un messaggio nel registro eventi dell'applicazione di Windows. Quindi, include il messaggio nella segnalazione errori da inviare a Microsoft.</summary>
      <param name="message">Messaggio in cui viene indicato il motivo dell'interruzione del processo o null se non è fornita alcuna spiegazione.</param>
    </member>
    <member name="M:System.Environment.FailFast(System.String,System.Exception)">
      <summary>Interrompe immediatamente un processo dopo aver scritto un messaggio nel registro eventi dell'applicazione di Windows. Quindi, include il messaggio e informazioni sull'eccezione nella segnalazione errori da inviare a Microsoft.</summary>
      <param name="message">Messaggio in cui viene indicato il motivo dell'interruzione del processo o null se non è fornita alcuna spiegazione.</param>
      <param name="exception">Eccezione che rappresenta l'errore che ha causato l'interruzione.In genere si tratta di un'eccezione in un blocco catch.</param>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariable(System.String)">
      <summary>Recupera dal processo corrente il valore di una variabile di ambiente. </summary>
      <returns>Valore della variabile di ambiente specificata nel parametro <paramref name="variable" /> o null se la variabile di ambiente non viene trovata.</returns>
      <param name="variable">Nome della variabile di ambiente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariables">
      <summary>Recupera tutti i nomi delle variabili di ambiente e i rispettivi valori dal processo corrente.</summary>
      <returns>Dizionario contenente tutti i nomi delle variabili di ambiente e i rispettivi valori, se presenti; in caso contrario, se non viene trovata alcuna variabile di ambiente, verrà restituito un dizionario vuoto.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <exception cref="T:System.OutOfMemoryException">The buffer is out of memory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.HasShutdownStarted">
      <summary>Ottiene un valore che indica se si sta scaricando il dominio applicazione corrente o se è in corso la chiusura di Common Language Runtime (CLR). </summary>
      <returns>true se il dominio dell'applicazione corrente è in fase di scaricamento o Common Language Runtime è in fase di chiusura; in caso contrario, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.NewLine">
      <summary>Ottiene la stringa della nuova riga definita per questo ambiente.</summary>
      <returns>Stringa che contiene "\r\n" per le piattaforme non Unix o "\n" per le piattaforme Unix.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.ProcessorCount">
      <summary>Ottiene il numero di processori sul computer corrente.</summary>
      <returns>Intero con segno a 32 bit che specifica il numero di processori sul computer corrente.Non è prevista alcuna impostazione predefinita.Se il computer corrente contiene più gruppi di processori, questa proprietà restituisce il numero di processori logici disponibili per l'uso da parte di Common Language Runtime (CLR).</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="NUMBER_OF_PROCESSORS" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.SetEnvironmentVariable(System.String,System.String)">
      <summary>Crea, modifica o elimina una variabile di ambiente memorizzata nel processo corrente.</summary>
      <param name="variable">Nome di una variabile di ambiente.</param>
      <param name="value">Valore da assegnare a <paramref name="variable" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="variable" /> contains a zero-length string, an initial hexadecimal zero character (0x00), or an equal sign ("="). -or-The length of <paramref name="variable" /> or <paramref name="value" /> is greater than or equal to 32,767 characters.-or-An error occurred during the execution of this operation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.StackTrace">
      <summary>Ottiene informazioni sull'analisi dello stack corrente.</summary>
      <returns>Stringa contenente informazioni sull'analisi dello stack.Il valore può essere <see cref="F:System.String.Empty" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack trace information is out of range.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.TickCount">
      <summary>Ottiene il numero di millisecondi trascorsi dall'avvio del sistema.</summary>
      <returns>Intero con segno a 32 bit contenente il tempo espresso in millisecondi che sono trascorsi dall'ultimo avvio del computer. </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Math">
      <summary>Vengono forniti costanti e metodi statici per eseguire funzioni trigonometriche, logaritmiche e normali funzioni matematiche.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere il Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Decimal)">
      <summary>Restituisce il valore assoluto di un numero <see cref="T:System.Decimal" />.</summary>
      <returns>Numero decimale x tale che 0 ≤ x ≤<see cref="F:System.Decimal.MaxValue" />.</returns>
      <param name="value">Numero maggiore o uguale a <see cref="F:System.Decimal.MinValue" />, ma minore o uguale a <see cref="F:System.Decimal.MaxValue" />. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Double)">
      <summary>Restituisce il valore assoluto di un numero a virgola mobile e precisione doppia.</summary>
      <returns>Numero a virgola mobile e precisione doppia, x, tale che 0 ≤ x ≤<see cref="F:System.Double.MaxValue" />.</returns>
      <param name="value">Numero maggiore o uguale a <see cref="F:System.Double.MinValue" />, ma minore o uguale a <see cref="F:System.Double.MaxValue" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int16)">
      <summary>Restituisce il valore assoluto di un intero con segno a 16 bit.</summary>
      <returns>Intero con segno a 16 bit, x, tale che 0 ≤ x ≤<see cref="F:System.Int16.MaxValue" />.</returns>
      <param name="value">Numero maggiore di <see cref="F:System.Int16.MinValue" />, ma minore o uguale a <see cref="F:System.Int16.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è uguale a <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int32)">
      <summary>Restituisce il valore assoluto di un intero con segno a 32 bit.</summary>
      <returns>Intero con segno a 32 bit, x, tale che 0 ≤ x ≤<see cref="F:System.Int32.MaxValue" />.</returns>
      <param name="value">Numero maggiore di <see cref="F:System.Int32.MinValue" />, ma minore o uguale a <see cref="F:System.Int32.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è uguale a <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int64)">
      <summary>Restituisce il valore assoluto di un intero con segno a 64 bit.</summary>
      <returns>Intero con segno a 64 bit, x, tale che 0 ≤ x ≤<see cref="F:System.Int64.MaxValue" />.</returns>
      <param name="value">Numero maggiore di <see cref="F:System.Int64.MinValue" />, ma minore o uguale a <see cref="F:System.Int64.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è uguale a <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.SByte)">
      <summary>Restituisce il valore assoluto di un intero con segno a 8 bit.</summary>
      <returns>Intero con segno a 8 bit, x, tale che 0 ≤ x ≤<see cref="F:System.SByte.MaxValue" />.</returns>
      <param name="value">Numero maggiore di <see cref="F:System.SByte.MinValue" />, ma minore o uguale a <see cref="F:System.SByte.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> è uguale a <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Single)">
      <summary>Restituisce il valore assoluto di un numero a virgola mobile e precisione singola.</summary>
      <returns>Numero a virgola mobile e precisione singola, x, tale che 0 ≤ x ≤<see cref="F:System.Single.MaxValue" />.</returns>
      <param name="value">Numero maggiore o uguale a <see cref="F:System.Single.MinValue" />, ma minore o uguale a <see cref="F:System.Single.MaxValue" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Acos(System.Double)">
      <summary>Restituisce l'angolo il cui coseno è il numero specificato.</summary>
      <returns>Angolo, θ, misurato in radianti, tale che 0 ≤θ≤π-oppure- <see cref="F:System.Double.NaN" /> se <paramref name="d" /> &lt; -1 o <paramref name="d" /> &gt; 1 o <paramref name="d" /> = <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Numero che rappresenta un coseno, dove <paramref name="d" /> deve essere maggiore o uguale a -1, ma minore o uguale a 1. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Asin(System.Double)">
      <summary>Restituisce l'angolo il cui seno è il numero specificato.</summary>
      <returns>Angolo, θ, misurato in radianti, tale che  -π/2 ≤θ≤π/2 -oppure- <see cref="F:System.Double.NaN" /> se <paramref name="d" /> &lt; -1 o <paramref name="d" /> &gt; 1 o <paramref name="d" /> = <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Numero che rappresenta un seno, dove <paramref name="d" /> deve essere maggiore o uguale a -1, ma minore o uguale a 1. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan(System.Double)">
      <summary>Restituisce l'angolo la cui tangente è il numero specificato.</summary>
      <returns>Angolo, θ, misurato in radianti, tale che -π/2 ≤θ≤π/2.-oppure- <see cref="F:System.Double.NaN" /> se <paramref name="d" /> è uguale a <see cref="F:System.Double.NaN" />, -π/2 arrotondato al valore a precisione doppia (-1.5707963267949) se <paramref name="d" /> è uguale a <see cref="F:System.Double.NegativeInfinity" /> o π/2 arrotondato al valore a precisione doppia (1.5707963267949) se <paramref name="d" /> è uguale a <see cref="F:System.Double.PositiveInfinity" />.</returns>
      <param name="d">Numero che rappresenta una tangente. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan2(System.Double,System.Double)">
      <summary>Restituisce l'angolo la cui tangente è il quoziente di due numeri specificati.</summary>
      <returns>Angolo, θ, espresso in radianti, tale che -π≤θ≤π e tan(θ) = <paramref name="y" /> / <paramref name="x" />, dove (<paramref name="x" />, <paramref name="y" />) è un punto del piano cartesiano.Osservare quanto segue:Per (<paramref name="x" />, <paramref name="y" />) nel quadrante 1, 0 &lt; θ &lt; π/2.Per (<paramref name="x" />, <paramref name="y" />) nel quadrante 2, π/2 &lt; θ≤π.Per (<paramref name="x" />, <paramref name="y" />) nel quadrante 3,-π &lt; θ &lt;-π/2.Per (<paramref name="x" />, <paramref name="y" />) nel quadrante 4,-π/2 &lt; θ &lt; 0.Per i punti sui limiti dei quadranti, il valore restituito è il seguente:Se y è 0 e x non è negativo, θ = 0.Se y è 0 e x è negativo, θ = π.Se y è positivo e x è 0, θ = π/2.Se y è negativo e x è 0, θ = -π/2.Se y è 0 e x è 0, θ = 0. Se <paramref name="x" /> o <paramref name="y" /> è <see cref="F:System.Double.NaN" /> o se <paramref name="x" /> e <paramref name="y" /> sono <see cref="F:System.Double.PositiveInfinity" /> o <see cref="F:System.Double.NegativeInfinity" />, il metodo restituisce <see cref="F:System.Double.NaN" />.</returns>
      <param name="y">Coordinata y di un punto. </param>
      <param name="x">Coordinata x di un punto. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Decimal)">
      <summary>Restituisce il valore integrale minimo maggiore o uguale al numero decimale specificato.</summary>
      <returns>Valore intero minimo maggiore o uguale a <paramref name="d" />.Si noti che questo metodo restituisce <see cref="T:System.Decimal" /> anziché un tipo integrale.</returns>
      <param name="d">Numero decimale. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Double)">
      <summary>Restituisce il valore integrale minimo maggiore del o uguale al numero a virgola mobile e con precisione doppia specificato.</summary>
      <returns>Valore intero minimo maggiore o uguale a <paramref name="a" />.Se <paramref name="a" /> è uguale a <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, verrà restituito tale valore.Si noti che questo metodo restituisce <see cref="T:System.Double" /> anziché un tipo integrale.</returns>
      <param name="a">Numero a virgola mobile a precisione doppia. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cos(System.Double)">
      <summary>Restituisce il coseno dell'angolo specificato.</summary>
      <returns>Coseno di <paramref name="d" />.Se <paramref name="d" /> è uguale a <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, questo metodo restituisce <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Angolo, espresso in radianti. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cosh(System.Double)">
      <summary>Restituisce il coseno iperbolico dell'angolo specificato.</summary>
      <returns>Coseno iperbolico di <paramref name="value" />.Se <paramref name="value" /> è uguale a <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, verrà restituito <see cref="F:System.Double.PositiveInfinity" />.Se <paramref name="value" /> è uguale a <see cref="F:System.Double.NaN" />, verrà restituito <see cref="F:System.Double.NaN" />.</returns>
      <param name="value">Angolo, espresso in radianti. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.E">
      <summary>Rappresenta la base logaritmica naturale, specificata dalla costante, e.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Exp(System.Double)">
      <summary>Restituisce e elevato alla potenza specificata.</summary>
      <returns>Numero e elevato alla potenza <paramref name="d" />.Se <paramref name="d" /> è uguale a <see cref="F:System.Double.NaN" /> o <see cref="F:System.Double.PositiveInfinity" />, verrà restituito il valore.Se <paramref name="d" /> è uguale a <see cref="F:System.Double.NegativeInfinity" />, verrà restituito 0.</returns>
      <param name="d">Numero che specifica una potenza. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Decimal)">
      <summary>Restituisce l'intero massimo minore del o uguale al numero decimale specificato.</summary>
      <returns>Intero massimo minore di o uguale a <paramref name="d" />.Si noti che il metodo restituisce un valore intero del tipo <see cref="T:System.Math" />.</returns>
      <param name="d">Numero decimale. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Double)">
      <summary>Restituisce l'intero massimo minore del o uguale al numero a virgola mobile e precisione doppia specificato.</summary>
      <returns>Intero massimo minore di o uguale a <paramref name="d" />.Se <paramref name="d" /> è uguale a <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, verrà restituito tale valore.</returns>
      <param name="d">Numero a virgola mobile a precisione doppia. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.IEEERemainder(System.Double,System.Double)">
      <summary>Restituisce il resto della divisione tra il numero specificato e un altro numero specificato.</summary>
      <returns>Numero uguale a <paramref name="x" /> - (<paramref name="y" /> Q), dove Q è il quoziente di <paramref name="x" />/ <paramref name="y" /> arrotondato all'intero più vicino; se <paramref name="x" /> / <paramref name="y" /> è contenuto tra due interi, verrà restituito l'intero pari.Se <paramref name="x" /> - (<paramref name="y" /> Q) è zero, verrà restituito il valore +0 se <paramref name="x" /> è positivo oppure -0 se <paramref name="x" /> è negativo.Se <paramref name="y" /> = 0, verrà restituito <see cref="F:System.Double.NaN" />.</returns>
      <param name="x">Dividendo. </param>
      <param name="y">Divisore. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double)">
      <summary>Restituisce il logaritmo naturale (in base e) di un numero specificato.</summary>
      <returns>Uno dei valori della tabella seguente. Parametro <paramref name="d" />.Valore restituito Positivo Il logaritmo naturale di <paramref name="d" />; ovvero, ln <paramref name="d" />, log o e<paramref name="d" />Zero <see cref="F:System.Double.NegativeInfinity" />Negativo <see cref="F:System.Double.NaN" />Uguale a <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Uguale a <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Numero di cui è necessario trovare il logaritmo. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double,System.Double)">
      <summary>Restituisce il logaritmo del numero specificato in una base specificata.</summary>
      <returns>Uno dei valori della tabella seguente.(+Infinito indica <see cref="F:System.Double.PositiveInfinity" />, -Infinito indica <see cref="F:System.Double.NegativeInfinity" /> e NaN indica <see cref="F:System.Double.NaN" />).<paramref name="a" /><paramref name="newBase" />Valore restituito<paramref name="a" />&gt; 0(0 &lt;<paramref name="newBase" />&lt; 1) -o-(<paramref name="newBase" />&gt; 1)lognewBase(a)<paramref name="a" />&lt; 0(qualsiasi valore)NaN(qualsiasi valore)<paramref name="newBase" />&lt; 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = +InfinitoNaN<paramref name="a" /> = NaN(qualsiasi valore)NaN(qualsiasi valore)<paramref name="newBase" /> = NaNNaN(qualsiasi valore)<paramref name="newBase" /> = 1NaN<paramref name="a" /> = 00 &lt;<paramref name="newBase" />&lt; 1 +Infinito<paramref name="a" /> = 0<paramref name="newBase" />&gt; 1-Infinito<paramref name="a" /> = + Infinito0 &lt;<paramref name="newBase" />&lt; 1-Infinito<paramref name="a" /> = + Infinito<paramref name="newBase" />&gt; 1+Infinito<paramref name="a" /> = 1<paramref name="newBase" /> = 00<paramref name="a" /> = 1<paramref name="newBase" /> = +Infinito0</returns>
      <param name="a">Numero di cui è necessario trovare il logaritmo. </param>
      <param name="newBase">Base del logaritmo. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log10(System.Double)">
      <summary>Restituisce il logaritmo in base 10 del numero specificato.</summary>
      <returns>Uno dei valori della tabella seguente. Parametro <paramref name="d" />. Valore restituito Positivo La base 10 di log <paramref name="d" />; ovvero, log 10<paramref name="d" />. Zero <see cref="F:System.Double.NegativeInfinity" />Negativo <see cref="F:System.Double.NaN" />Uguale a <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Uguale a <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Numero di cui trovare il logaritmo. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Byte,System.Byte)">
      <summary>Restituisce il più elevato tra due interi senza segno a 8 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.</returns>
      <param name="val1">Primo di due interi senza segno a 8 bit da confrontare. </param>
      <param name="val2">Secondo di due interi senza segno a 8 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Decimal,System.Decimal)">
      <summary>Restituisce il più elevato tra due numeri decimali.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.</returns>
      <param name="val1">Primo di due numeri decimali da confrontare. </param>
      <param name="val2">Secondo di due numeri decimali da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Double,System.Double)">
      <summary>Restituisce il più elevato tra due numeri a virgola mobile e precisione doppia.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.Se <paramref name="val1" />, <paramref name="val2" /> o sia <paramref name="val1" /> che <paramref name="val2" /> sono uguali a <see cref="F:System.Double.NaN" />, verrà restituito <see cref="F:System.Double.NaN" />.</returns>
      <param name="val1">Primo di due numeri a virgola mobile e precisione doppia da confrontare. </param>
      <param name="val2">Secondo di due numeri a virgola mobile e precisione doppia da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int16,System.Int16)">
      <summary>Restituisce il più elevato tra due interi con segno a 16 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.</returns>
      <param name="val1">Primo di due interi con segno a 16 bit da confrontare. </param>
      <param name="val2">Secondo di due interi con segno a 16 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int32,System.Int32)">
      <summary>Restituisce il più elevato tra due interi con segno a 32 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.</returns>
      <param name="val1">Primo di due interi con segno a 32 bit da confrontare. </param>
      <param name="val2">Secondo di due interi con segno a 32 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int64,System.Int64)">
      <summary>Restituisce il più elevato tra due interi con segno a 64 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.</returns>
      <param name="val1">Primo di due interi con segno a 64 bit da confrontare. </param>
      <param name="val2">Secondo di due interi con segno a 64 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.SByte,System.SByte)">
      <summary>Restituisce il più elevato tra due interi con segno a 8 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.</returns>
      <param name="val1">Primo di due interi con segno a 8 bit da confrontare. </param>
      <param name="val2">Secondo di due interi con segno a 8 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Single,System.Single)">
      <summary>Restituisce il più elevato tra due numeri a virgola mobile e precisione singola.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.Se <paramref name="val1" />, <paramref name="val2" /> o entrambi <paramref name="val1" /> e <paramref name="val2" /> sono uguali a <see cref="F:System.Single.NaN" />, verrà restituito <see cref="F:System.Single.NaN" />.</returns>
      <param name="val1">Primo di due numeri a virgola mobile e precisione singola da confrontare. </param>
      <param name="val2">Secondo di due numeri a virgola mobile e precisione singola da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt16,System.UInt16)">
      <summary>Restituisce il più elevato tra due interi senza segno a 16 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.</returns>
      <param name="val1">Primo di due interi senza segno a 16 bit da confrontare. </param>
      <param name="val2">Secondo di due interi senza segno a 16 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt32,System.UInt32)">
      <summary>Restituisce il più elevato tra due interi senza segno a 32 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.</returns>
      <param name="val1">Primo di due interi senza segno a 32 bit da confrontare. </param>
      <param name="val2">Secondo di due interi senza segno a 32 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt64,System.UInt64)">
      <summary>Restituisce il più elevato tra due interi senza segno a 64 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il maggiore.</returns>
      <param name="val1">Primo di due interi senza segno a 64 bit da confrontare. </param>
      <param name="val2">Secondo di due interi senza segno a 64 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Byte,System.Byte)">
      <summary>Restituisce il meno elevato tra due interi senza segno a 8 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.</returns>
      <param name="val1">Primo di due interi senza segno a 8 bit da confrontare. </param>
      <param name="val2">Secondo di due interi senza segno a 8 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Decimal,System.Decimal)">
      <summary>Restituisce il meno elevato tra due numeri decimali.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.</returns>
      <param name="val1">Primo di due numeri decimali da confrontare. </param>
      <param name="val2">Secondo di due numeri decimali da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Double,System.Double)">
      <summary>Restituisce il meno elevato tra due numeri a virgola mobile e precisione doppia.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.Se <paramref name="val1" />, <paramref name="val2" /> o sia <paramref name="val1" /> che <paramref name="val2" /> sono uguali a <see cref="F:System.Double.NaN" />, verrà restituito <see cref="F:System.Double.NaN" />.</returns>
      <param name="val1">Primo di due numeri a virgola mobile e precisione doppia da confrontare. </param>
      <param name="val2">Secondo di due numeri a virgola mobile e precisione doppia da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int16,System.Int16)">
      <summary>Restituisce il meno elevato tra due interi con segno a 16 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.</returns>
      <param name="val1">Primo di due interi con segno a 16 bit da confrontare. </param>
      <param name="val2">Secondo di due interi con segno a 16 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int32,System.Int32)">
      <summary>Restituisce il meno elevato tra due interi con segno a 32 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.</returns>
      <param name="val1">Primo di due interi con segno a 32 bit da confrontare. </param>
      <param name="val2">Secondo di due interi con segno a 32 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int64,System.Int64)">
      <summary>Restituisce il meno elevato tra due interi con segno a 64 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.</returns>
      <param name="val1">Primo di due interi con segno a 64 bit da confrontare. </param>
      <param name="val2">Secondo di due interi con segno a 64 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.SByte,System.SByte)">
      <summary>Restituisce il meno elevato tra due interi con segno a 8 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.</returns>
      <param name="val1">Primo di due interi con segno a 8 bit da confrontare. </param>
      <param name="val2">Secondo di due interi con segno a 8 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Single,System.Single)">
      <summary>Restituisce il meno elevato tra due numeri a virgola mobile e precisione singola.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.Se <paramref name="val1" />, <paramref name="val2" /> o sia <paramref name="val1" /> che <paramref name="val2" /> sono uguali a <see cref="F:System.Single.NaN" />, verrà restituito <see cref="F:System.Single.NaN" />.</returns>
      <param name="val1">Primo di due numeri a virgola mobile e precisione singola da confrontare. </param>
      <param name="val2">Secondo di due numeri a virgola mobile e precisione singola da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt16,System.UInt16)">
      <summary>Restituisce il meno elevato tra due interi senza segno a 16 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.</returns>
      <param name="val1">Primo di due interi senza segno a 16 bit da confrontare. </param>
      <param name="val2">Secondo di due interi senza segno a 16 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt32,System.UInt32)">
      <summary>Restituisce il meno elevato tra due interi senza segno a 32 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.</returns>
      <param name="val1">Primo di due interi senza segno a 32 bit da confrontare. </param>
      <param name="val2">Secondo di due interi senza segno a 32 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt64,System.UInt64)">
      <summary>Restituisce il meno elevato tra due interi senza segno a 64 bit.</summary>
      <returns>Parametro <paramref name="val1" /> o <paramref name="val2" />, qualunque sia il minore.</returns>
      <param name="val1">Primo di due interi senza segno a 64 bit da confrontare. </param>
      <param name="val2">Secondo di due interi senza segno a 64 bit da confrontare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.PI">
      <summary>Rappresenta il rapporto tra la circonferenza del cerchio e il suo diametro, specificato dalla costante, π.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Pow(System.Double,System.Double)">
      <summary>Restituisce il numero specificato elevato alla potenza specificata.</summary>
      <returns>Numero <paramref name="x" /> elevato alla potenza <paramref name="y" />.</returns>
      <param name="x">Numero a virgola mobile e precisione doppia da elevare a potenza. </param>
      <param name="y">Numero a virgola mobile a precisione doppia che specifica una potenza. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal)">
      <summary>Arrotonda un valore decimale al valore integrale più vicino.</summary>
      <returns>Parametro più vicino all'intero <paramref name="d" />.Se il componente frazionario di <paramref name="d" /> è a metà fra due interi, uno pari e uno dispari, viene restituito il numero pari.Si noti che questo metodo restituisce <see cref="T:System.Decimal" /> anziché un tipo integrale.</returns>
      <param name="d">Numero decimale da arrotondare. </param>
      <exception cref="T:System.OverflowException">Il risultato non è compreso nell'intervallo di un oggetto <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32)">
      <summary>Arrotonda un valore decimale a un numero di cifre frazionarie specificato.</summary>
      <returns>Numero più vicino a <paramref name="d" /> contenente un numero di cifre frazionarie pari a <paramref name="decimals" />. </returns>
      <param name="d">Numero decimale da arrotondare. </param>
      <param name="decimals">Numero di posizioni decimali nel valore restituito. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> è minore di 0 o maggiore di 28. </exception>
      <exception cref="T:System.OverflowException">Il risultato non è compreso nell'intervallo di un oggetto <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32,System.MidpointRounding)">
      <summary>Arrotonda un valore decimale a un numero di cifre frazionarie specificato.Un parametro specifica come arrotondare il valore se si trova a metà tra due numeri.</summary>
      <returns>Numero più vicino a <paramref name="d" /> contenente un numero di cifre frazionarie pari a <paramref name="decimals" />.Se <paramref name="d" /> ha meno cifre frazionarie di <paramref name="decimals" />, <paramref name="d" /> viene restituito invariato.</returns>
      <param name="d">Numero decimale da arrotondare. </param>
      <param name="decimals">Numero di posizioni decimali nel valore restituito. </param>
      <param name="mode">Spiegazione della modalità di arrotondamento di <paramref name="d" /> se si trova a metà tra due numeri.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> è minore di 0 o maggiore di 28. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> non è un valore di <see cref="T:System.MidpointRounding" /> valido.</exception>
      <exception cref="T:System.OverflowException">Il risultato non è compreso nell'intervallo di un oggetto <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.MidpointRounding)">
      <summary>Arrotonda un valore decimale all'intero più vicino.Un parametro specifica come arrotondare il valore se si trova a metà tra due numeri.</summary>
      <returns>Il <paramref name="d" /> più vicino all'intero.Se <paramref name="d" /> è contenuto tra due numeri, di cui per definizione uno è pari e l'altro dispari, <paramref name="mode" /> determina quale dei due numeri deve essere restituito.</returns>
      <param name="d">Numero decimale da arrotondare. </param>
      <param name="mode">Spiegazione della modalità di arrotondamento di <paramref name="d" /> se si trova a metà tra due numeri.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> non è un valore di <see cref="T:System.MidpointRounding" /> valido.</exception>
      <exception cref="T:System.OverflowException">Il risultato non è compreso nell'intervallo di un oggetto <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double)">
      <summary>Arrotonda un valore a virgola mobile e precisione doppia al valore integrale più vicino.</summary>
      <returns>Il <paramref name="a" /> più vicino all'intero.Se il componente frazionario di <paramref name="a" /> è compreso tra due interi, di cui uno è pari e l'altro dispari, allora viene restituito il numero pari.Si noti che questo metodo restituisce <see cref="T:System.Double" /> anziché un tipo integrale.</returns>
      <param name="a">Numero a virgola mobile e precisione doppia da arrotondare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32)">
      <summary>Arrotonda un valore a virgola mobile e precisione doppia a un numero di cifre frazionarie specificato.</summary>
      <returns>Numero più vicino a <paramref name="value" /> contenente un numero di cifre frazionarie pari a <paramref name="digits" />.</returns>
      <param name="value">Numero a virgola mobile e precisione doppia da arrotondare. </param>
      <param name="digits">Numero di cifre frazionarie nel valore restituito. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> è minore di 0 o maggiore di 15. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32,System.MidpointRounding)">
      <summary>Arrotonda un valore a virgola mobile e precisione doppia a un numero di cifre frazionarie specificato.Un parametro specifica come arrotondare il valore se si trova a metà tra due numeri.</summary>
      <returns>Numero più vicino a <paramref name="value" /> contenente un numero di cifre frazionarie pari a <paramref name="digits" />.Se <paramref name="value" /> ha meno cifre frazionarie di <paramref name="digits" />, <paramref name="value" /> viene restituito invariato.</returns>
      <param name="value">Numero a virgola mobile e precisione doppia da arrotondare. </param>
      <param name="digits">Numero di cifre frazionarie nel valore restituito. </param>
      <param name="mode">Spiegazione della modalità di arrotondamento di <paramref name="value" /> se si trova a metà tra due numeri.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> è minore di 0 o maggiore di 15. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> non è un valore di <see cref="T:System.MidpointRounding" /> valido.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.MidpointRounding)">
      <summary>Arrotonda un valore a virgola mobile e precisione doppia all'intero più vicino.Un parametro specifica come arrotondare il valore se si trova a metà tra due numeri.</summary>
      <returns>Il <paramref name="value" /> più vicino all'intero.Se <paramref name="value" /> è contenuto tra due interi, di cui per definizione uno è pari e l'altro dispari, <paramref name="mode" /> determina quale dei due numeri deve essere restituito.</returns>
      <param name="value">Numero a virgola mobile e precisione doppia da arrotondare. </param>
      <param name="mode">Spiegazione della modalità di arrotondamento di <paramref name="value" /> se si trova a metà tra due numeri.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> non è un valore di <see cref="T:System.MidpointRounding" /> valido.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Decimal)">
      <summary>Restituisce un valore che indica il segno di un numero decimale.</summary>
      <returns>Numero che indica il segno di <paramref name="value" />, come illustrato nella tabella seguente.Valore restituito Significato -1 <paramref name="value" /> è minore di zero. 0 <paramref name="value" /> è uguale a zero. 1 <paramref name="value" /> è maggiore di zero. </returns>
      <param name="value">Numero decimale con segno. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Double)">
      <summary>Restituisce un valore che indica il segno di un numero a virgola mobile e precisione doppia.</summary>
      <returns>Numero che indica il segno di <paramref name="value" />, come illustrato nella tabella seguente.Valore restituito Significato -1 <paramref name="value" /> è minore di zero. 0 <paramref name="value" /> è uguale a zero. 1 <paramref name="value" /> è maggiore di zero. </returns>
      <param name="value">Numero con segno. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> è uguale a <see cref="F:System.Double.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int16)">
      <summary>Restituisce un valore che indica il segno di un intero con segno a 16 bit.</summary>
      <returns>Numero che indica il segno di <paramref name="value" />, come illustrato nella tabella seguente.Valore restituito Significato -1 <paramref name="value" /> è minore di zero. 0 <paramref name="value" /> è uguale a zero. 1 <paramref name="value" /> è maggiore di zero. </returns>
      <param name="value">Numero con segno. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int32)">
      <summary>Restituisce un valore che indica il segno di un intero con segno a 32 bit.</summary>
      <returns>Numero che indica il segno di <paramref name="value" />, come illustrato nella tabella seguente.Valore restituito Significato -1 <paramref name="value" /> è minore di zero. 0 <paramref name="value" /> è uguale a zero. 1 <paramref name="value" /> è maggiore di zero. </returns>
      <param name="value">Numero con segno. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int64)">
      <summary>Restituisce un valore che indica il segno di un intero con segno a 64 bit.</summary>
      <returns>Numero che indica il segno di <paramref name="value" />, come illustrato nella tabella seguente.Valore restituito Significato -1 <paramref name="value" /> è minore di zero. 0 <paramref name="value" /> è uguale a zero. 1 <paramref name="value" /> è maggiore di zero. </returns>
      <param name="value">Numero con segno. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.SByte)">
      <summary>Restituisce un valore che indica il segno di un intero con segno a 8 bit.</summary>
      <returns>Numero che indica il segno di <paramref name="value" />, come illustrato nella tabella seguente.Valore restituito Significato -1 <paramref name="value" /> è minore di zero. 0 <paramref name="value" /> è uguale a zero. 1 <paramref name="value" /> è maggiore di zero. </returns>
      <param name="value">Numero con segno. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Single)">
      <summary>Restituisce un valore che indica il segno di un numero a virgola mobile e precisione singola.</summary>
      <returns>Numero che indica il segno di <paramref name="value" />, come illustrato nella tabella seguente.Valore restituito Significato -1 <paramref name="value" /> è minore di zero. 0 <paramref name="value" /> è uguale a zero. 1 <paramref name="value" /> è maggiore di zero. </returns>
      <param name="value">Numero con segno. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> è uguale a <see cref="F:System.Single.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sin(System.Double)">
      <summary>Restituisce il seno dell'angolo specificato.</summary>
      <returns>Seno di <paramref name="a" />.Se <paramref name="a" /> è uguale a <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, questo metodo restituisce <see cref="F:System.Double.NaN" />.</returns>
      <param name="a">Angolo, espresso in radianti. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sinh(System.Double)">
      <summary>Restituisce il seno iperbolico dell'angolo specificato.</summary>
      <returns>Seno iperbolico di <paramref name="value" />.Se <paramref name="value" /> è uguale a <see cref="F:System.Double.NegativeInfinity" />, <see cref="F:System.Double.PositiveInfinity" /> o <see cref="F:System.Double.NaN" />, verrà restituito dal metodo un oggetto <see cref="T:System.Double" /> uguale a <paramref name="value" />.</returns>
      <param name="value">Angolo, espresso in radianti. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sqrt(System.Double)">
      <summary>Restituisce la radice quadrata del numero specificato.</summary>
      <returns>Uno dei valori della tabella seguente. Parametro <paramref name="d" />. Valore restituito Zero o positivo Radice quadrata positiva di <paramref name="d" />. Negativo <see cref="F:System.Double.NaN" />Uguale a <see cref="F:System.Double.NaN" />.<see cref="F:System.Double.NaN" />Uguale a <see cref="F:System.Double.PositiveInfinity" />.<see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Numero di cui è necessario trovare la radice quadrata. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tan(System.Double)">
      <summary>Restituisce la tangente dell'angolo specificato.</summary>
      <returns>Tangente di <paramref name="a" />.Se <paramref name="a" /> è uguale a <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> o <see cref="F:System.Double.PositiveInfinity" />, questo metodo restituisce <see cref="F:System.Double.NaN" />.</returns>
      <param name="a">Angolo, espresso in radianti. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tanh(System.Double)">
      <summary>Restituisce la tangente iperbolica dell'angolo specificato.</summary>
      <returns>Tangente iperbolica di <paramref name="value" />.Se <paramref name="value" /> è uguale a <see cref="F:System.Double.NegativeInfinity" />, il metodo restituirà -1.Se il valore è uguale a <see cref="F:System.Double.PositiveInfinity" />, questo metodo restituirà 1.Se <paramref name="value" /> è uguale a <see cref="F:System.Double.NaN" />, questo metodo restituisce <see cref="F:System.Double.NaN" />.</returns>
      <param name="value">Angolo, espresso in radianti. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Decimal)">
      <summary>Calcola la parte integrale di un numero decimale specificato. </summary>
      <returns>Parte integrale di <paramref name="d" />, vale a dire il numero che rimane dopo l'eliminazione delle cifre frazionarie.</returns>
      <param name="d">Numero da troncare.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Double)">
      <summary>Calcola la parte integrale di un numero a virgola mobile e precisione doppia specificato. </summary>
      <returns>La parte integrale di <paramref name="d" />, vale a dire il numero che rimane dopo l'eliminazione delle cifre frazionarie o uno dei valori elencati nella seguente tabella. <paramref name="d" />Valore restituito<see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Numero da troncare.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.MidpointRounding">
      <summary>Specifica l'elaborazione di un numero che si trova a metà tra due numeri con i metodi di elaborazione matematica.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.MidpointRounding.AwayFromZero">
      <summary>Quando un numero si trova a metà tra due numeri, viene arrotondato al numero più prossimo e più distante dallo zero.</summary>
    </member>
    <member name="F:System.MidpointRounding.ToEven">
      <summary>Quando un numero si trova a metà tra due numeri, viene arrotondato al numero pari più vicino.</summary>
    </member>
    <member name="T:System.Progress`1">
      <summary>Fornisce un oggetto <see cref="T:System.IProgress`1" /> che richiama i callback per ogni valore dello stato di avanzamento segnalato.</summary>
      <typeparam name="T">Specifica il tipo del valore del report stato di avanzamento.</typeparam>
    </member>
    <member name="M:System.Progress`1.#ctor">
      <summary>Inizializza l'oggetto <see cref="T:System.Progress`1" />.</summary>
    </member>
    <member name="M:System.Progress`1.#ctor(System.Action{`0})">
      <summary>Inizializza l'oggetto <see cref="T:System.Progress`1" /> con il callback specificato.</summary>
      <param name="handler">Gestore da richiamare per ogni valore dello stato di avanzamento segnalato.Il gestore verrà richiamato oltre a tutti i delegati registrati con l'evento <see cref="E:System.Progress`1.ProgressChanged" /> .In base all'istanza di <see cref="T:System.Threading.SynchronizationContext" /> acquisita da <see cref="T:System.Progress`1" /> in fase di creazione, è possibile che questa istanza del gestore venga richiamata contemporaneamente a se stessa.</param>
    </member>
    <member name="M:System.Progress`1.OnReport(`0)">
      <summary>Segnala una modifica dello stato.</summary>
      <param name="value">Valore del progresso aggiornato.</param>
    </member>
    <member name="E:System.Progress`1.ProgressChanged">
      <summary>Generato per ogni valore di stato di avanzamento segnalato.</summary>
    </member>
    <member name="M:System.Progress`1.System#IProgress{T}#Report(`0)">
      <summary>Segnala una modifica dello stato.</summary>
      <param name="value">Valore del progresso aggiornato.</param>
    </member>
    <member name="T:System.Random">
      <summary>Rappresenta un generatore di numeri pseudo-casuali, ovvero un dispositivo che produce una sequenza di numeri che soddisfano determinati requisiti statistici di casualità.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere Origine riferimento.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Random" /> usando un valore di inizializzazione predefinito dipendente da un fattore temporale.</summary>
    </member>
    <member name="M:System.Random.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Random" /> usando il valore di inizializzazione specificato.</summary>
      <param name="Seed">Numero usato per calcolare un valore iniziale per la sequenza di numeri pseudo-casuali.Se viene specificato un numero negativo, viene usato il valore assoluto del numero.</param>
    </member>
    <member name="M:System.Random.Next">
      <summary>Restituisce un intero casuale non negativo.</summary>
      <returns>Intero con segno a 32 bit maggiore o uguale a zero e minore di <see cref="F:System.Int32.MaxValue" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32)">
      <summary>Restituisce un intero casuale non negativo inferiore al massimo specificato.</summary>
      <returns>Intero con segno a 32 bit superiore o pari a zero e inferiore a <paramref name="maxValue" />; ovvero, l'intervallo dei valori restituiti in genere includerà zero ma non <paramref name="maxValue" />.Se <paramref name="maxValue" /> è uguale a 0, verrà restituito <paramref name="maxValue" />.</returns>
      <param name="maxValue">Limite superiore esclusivo del numero casuale da generare.<paramref name="maxValue" /> deve essere maggiore di o uguale a 0.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxValue" /> is less than 0. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32,System.Int32)">
      <summary>Restituisce un intero casuale all'interno di un intervallo specificato.</summary>
      <returns>Intero con segno a 32 bit maggiore o uguale a <paramref name="minValue" /> e minore di <paramref name="maxValue" />: l'intervallo dei valori restituiti includerà <paramref name="minValue" /> ma non <paramref name="maxValue" />.Se <paramref name="minValue" /> è uguale a <paramref name="maxValue" />, viene restituito <paramref name="minValue" />.</returns>
      <param name="minValue">Limite inferiore inclusivo del numero casuale restituito. </param>
      <param name="maxValue">Limite superiore esclusivo del numero casuale da restituire.<paramref name="maxValue" /> deve essere maggiore di o uguale a <paramref name="minValue" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minValue" /> is greater than <paramref name="maxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextBytes(System.Byte[])">
      <summary>Inserisce numeri casuali negli elementi di una matrice di byte specificata.</summary>
      <param name="buffer">Matrice di byte che deve contenere numeri casuali. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextDouble">
      <summary>Restituisce un numero casuale a virgola mobile e precisione doppia maggiore o uguale a 0,0 e minore di 1,0.</summary>
      <returns>Numero a virgola mobile e precisione doppia maggiore o uguale a 0,0 e minore di 1,0.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Sample">
      <summary>Restituisce un numero a virgola mobile casuale compreso tra 0,0 e 1,0.</summary>
      <returns>Numero a virgola mobile e precisione doppia maggiore o uguale a 0,0 e minore di 1,0.</returns>
    </member>
    <member name="T:System.StringComparer">
      <summary>Rappresenta un'operazione di confronto tra stringhe che usa regole specifiche di confronto basate sull'uso di maiuscole e minuscole e specifiche delle impostazioni cultura o per ordinale.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.StringComparer.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.StringComparer" />. </summary>
    </member>
    <member name="M:System.StringComparer.Compare(System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, confronta due stringhe e restituisce un'indicazione del relativo ordinamento.</summary>
      <returns>Intero con segno che indica i valori relativi di <paramref name="x" /> e <paramref name="y" />, come illustrato nella tabella seguente.ValoreSignificatoMinore di zero<paramref name="x" /> precede <paramref name="y" /> nell'ordinamento.-oppure-<paramref name="x" /> è null e <paramref name="y" /> non è null.Zero<paramref name="x" /> è uguale a <paramref name="y" />.-oppure-<paramref name="x" /> e <paramref name="y" /> sono entrambi null. Maggiore di zero<paramref name="x" /> segue <paramref name="y" /> nell'ordinamento.-oppure-<paramref name="y" /> è null e <paramref name="x" /> non è null. </returns>
      <param name="x">Stringa da confrontare con <paramref name="y" />.</param>
      <param name="y">Stringa da confrontare con <paramref name="x" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.CurrentCulture">
      <summary>Ottiene un oggetto <see cref="T:System.StringComparer" /> che esegue un confronto tra stringhe con distinzione tra maiuscole e minuscole usando le regole di confronto per parola delle impostazioni cultura correnti.</summary>
      <returns>Nuovo oggetto <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.StringComparer.CurrentCultureIgnoreCase">
      <summary>Ottiene un oggetto <see cref="T:System.StringComparer" /> che esegue un confronto tra stringhe con distinzione tra maiuscole e minuscole usando le regole di confronto per parola delle impostazioni cultura correnti.</summary>
      <returns>Nuovo oggetto <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.StringComparer.Equals(System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, indica se due stringhe sono uguali.</summary>
      <returns>true se <paramref name="x" /> e <paramref name="y" /> fanno riferimento allo stesso oggetto o <paramref name="x" /> e <paramref name="y" /> sono uguali oppure <paramref name="x" /> e <paramref name="y" /> sono null; in caso contrario, false.</returns>
      <param name="x">Stringa da confrontare con <paramref name="y" />.</param>
      <param name="y">Stringa da confrontare con <paramref name="x" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.GetHashCode(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il codice hash per la stringa specificata.</summary>
      <returns>Codice hash con segno a 32 bit calcolato dal valore del parametro <paramref name="obj" />.</returns>
      <param name="obj">Stringa.</param>
      <exception cref="T:System.ArgumentException">Memoria insufficiente per l'allocazione del buffer necessario per calcolare il codice hash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> è null. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.StringComparer.Ordinal">
      <summary>Ottiene un oggetto <see cref="T:System.StringComparer" /> che esegue un confronto ordinale tra stringhe con distinzione tra maiuscole e minuscole.</summary>
      <returns>Oggetto <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.OrdinalIgnoreCase">
      <summary>Ottiene un oggetto <see cref="T:System.StringComparer" /> che esegue un confronto ordinale tra stringhe senza distinzione tra maiuscole e minuscole.</summary>
      <returns>Oggetto <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Confronta due oggetti e restituisce un valore che indica se un oggetto è minore, uguale o maggiore dell’altro.</summary>
      <returns>Intero con segno che indica i valori relativi di <paramref name="x" /> e <paramref name="y" />, come illustrato nella tabella seguente.ValoreSignificatoMinore di zero<paramref name="x" /> è minore di <paramref name="y" />.Zero<paramref name="x" /> è uguale a <paramref name="y" />.Maggiore di zero<paramref name="x" /> è maggiore di <paramref name="y" />.</returns>
      <param name="x">Primo oggetto da confrontare.</param>
      <param name="y">Secondo oggetto da confrontare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> e <paramref name="y" /> non implementano l'interfaccia <see cref="T:System.IComparable" />.-oppure-<paramref name="x" /> e <paramref name="y" /> sono di tipi diversi e nessuno dei due può gestire confronti con l'altro.</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Determina se gli oggetti specificati sono uguali.</summary>
      <returns>true se gli oggetti specificati sono uguali; in caso contrario, false. </returns>
      <param name="x">Primo oggetto da confrontare.</param>
      <param name="y">Secondo oggetto da confrontare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> e <paramref name="y" /> sono di tipi diversi e nessuno dei due può gestire confronti con l'altro. </exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Restituisce un codice hash per l'oggetto specificato.</summary>
      <returns>Codice hash per l'oggetto specificato. </returns>
      <param name="obj">Oggetto per cui deve essere restituito un codice hash. </param>
      <exception cref="T:System.ArgumentNullException">Il tipo di <paramref name="obj" /> è un tipo di riferimento e <paramref name="obj" /> è null. </exception>
    </member>
    <member name="T:System.UriBuilder">
      <summary>Fornisce un costruttore personalizzato per gli identificatori URI (Uniform Resource Identifier) e modifica gli URI per la classe <see cref="T:System.Uri" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.UriBuilder" />.</summary>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.UriBuilder" /> con l'URI specificato.</summary>
      <param name="uri">Stringa URI. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> è null. </exception>
      <exception cref="T:System.UriFormatException">Nell'API.NET per le applicazioni Windows o nella Libreria di classi portabile, rilevare piuttosto l'eccezione della classe di base <see cref="T:System.FormatException" />.<paramref name="uri" /> è una stringa di lunghezza zero oppure contiene solo spazi.- oppure - Durante la routine di analisi è stato rilevato uno schema in un form non valido.- oppure - Il parser ha consentito di rilevare più di due barre consecutive in un URI in cui non è utilizzato lo schema "file".- oppure - <paramref name="uri" /> non è un URI valido. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.UriBuilder" /> con lo schema e l'host specificati.</summary>
      <param name="schemeName">Protocollo di accesso Internet. </param>
      <param name="hostName">Nome dominio di tipo DNS o indirizzo IP. </param>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.UriBuilder" /> con lo schema, l'host e la porta specificati.</summary>
      <param name="scheme">Protocollo di accesso Internet. </param>
      <param name="host">Nome dominio di tipo DNS o indirizzo IP. </param>
      <param name="portNumber">Numero di porta IP per il servizio. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portNumber" /> è minore di -1 o maggiore di 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.UriBuilder" /> con lo schema, l'host, il numero di porta e il percorso specificati.</summary>
      <param name="scheme">Protocollo di accesso Internet. </param>
      <param name="host">Nome dominio di tipo DNS o indirizzo IP. </param>
      <param name="port">Numero di porta IP per il servizio. </param>
      <param name="pathValue">Percorso alla risorsa Internet. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> è minore di -1 o maggiore di 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.UriBuilder" /> con lo schema, l'host, il numero di porta, il percorso e la stringa di query o l'identificatore di frammento specificati.</summary>
      <param name="scheme">Protocollo di accesso Internet. </param>
      <param name="host">Nome dominio di tipo DNS o indirizzo IP. </param>
      <param name="port">Numero di porta IP per il servizio. </param>
      <param name="path">Percorso alla risorsa Internet. </param>
      <param name="extraValue">Stringa di query o identificatore di frammento. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="extraValue" /> non è null né <see cref="F:System.String.Empty" />, un identificatore di frammento valido non inizia con un simbolo di cancelletto (#), né una stringa di query valida inizia con un punto interrogativo (?). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> è minore di -1 o maggiore di 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.Uri)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.UriBuilder" /> con l'istanza di <see cref="T:System.Uri" /> specificata.</summary>
      <param name="uri">Istanza della classe <see cref="T:System.Uri" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> è null. </exception>
    </member>
    <member name="M:System.UriBuilder.Equals(System.Object)">
      <summary>Confronta un'istanza esistente di <see cref="T:System.Uri" /> con il contenuto della classe <see cref="T:System.UriBuilder" /> per trovare un'uguaglianza.</summary>
      <returns>true se <paramref name="rparam" /> rappresenta lo stesso oggetto <see cref="T:System.Uri" /> di quello <see cref="T:System.Uri" /> costruito dalla presente istanza di <see cref="T:System.UriBuilder" />. In caso contrario, false.</returns>
      <param name="rparam">Oggetto da confrontare con l'istanza corrente. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Fragment">
      <summary>Ottiene o imposta la parte di frammento dell'URI.</summary>
      <returns>Parte di frammento dell'URI.L'identificatore di frammento ("#") viene aggiunto all'inizio del frammento.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.GetHashCode">
      <summary>Restituisce il codice hash dell'URI.</summary>
      <returns>Codice hash generato per l'URI.</returns>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Host">
      <summary>Ottiene o imposta il nome host DNS (Domain Name System) o l'indirizzo IP di un server.</summary>
      <returns>Nome host DNS o indirizzo IP del server.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Password">
      <summary>Ottiene o imposta la password associata all'utente che accede all'URI.</summary>
      <returns>Password dell'utente che accede all'URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Path">
      <summary>Ottiene o imposta il percorso alla risorsa cui fa riferimento l'URI.</summary>
      <returns>Percorso alla risorsa cui fa riferimento l'URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Port">
      <summary>Ottiene o imposta il numero di porta dell'URI.</summary>
      <returns>Numero di porta dell'URI.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">La porta non può essere impostata su un valore minore di -1 o maggiore di 65.535. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Query">
      <summary>Ottiene o imposta le informazioni sulle query incluse nell'URI.</summary>
      <returns>Informazioni sulle query incluse nell'URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Scheme">
      <summary>Ottiene o imposta il nome dello schema dell'URI.</summary>
      <returns>Schema dell'URI.</returns>
      <exception cref="T:System.ArgumentException">Impossibile impostare un nome non valido per lo schema. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.UriBuilder.ToString">
      <summary>Restituisce la stringa di visualizzazione dell'istanza di <see cref="T:System.UriBuilder" /> specificata.</summary>
      <returns>Stringa contenente la stringa di visualizzazione senza caratteri escape dell'oggetto <see cref="T:System.UriBuilder" />.</returns>
      <exception cref="T:System.UriFormatException">Nell'API.NET per le applicazioni Windows o nella Libreria di classi portabile, rilevare piuttosto l'eccezione della classe di base <see cref="T:System.FormatException" />.L'istanza <see cref="T:System.UriBuilder" /> dispone di una password errata. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Uri">
      <summary>Ottiene l'istanza di <see cref="T:System.Uri" /> costruita dall'istanza di <see cref="T:System.UriBuilder" /> specificata.</summary>
      <returns>Oggetto <see cref="T:System.Uri" /> contenente l'URI costruito dalla classe <see cref="T:System.UriBuilder" />.</returns>
      <exception cref="T:System.UriFormatException">Nell'API.NET per le applicazioni Windows o nella Libreria di classi portabile, rilevare piuttosto l'eccezione della classe di base <see cref="T:System.FormatException" />.L'URI costruito dalle proprietà di <see cref="T:System.UriBuilder" /> non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.UserName">
      <summary>Nome utente associato all'utente che accede all'URI.</summary>
      <returns>Nome dell'utente che accede all'URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Diagnostics.Stopwatch">
      <summary>Fornisce un set di metodi e proprietà che possono essere usati per misurare con precisione il tempo trascorso.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere Origine riferimento.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Diagnostics.Stopwatch" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.Elapsed">
      <summary>Ottiene il tempo totale trascorso misurato dall'istanza corrente.</summary>
      <returns>Struttura <see cref="T:System.TimeSpan" /> di sola lettura che rappresenta il tempo totale trascorso misurato dall'istanza corrente.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedMilliseconds">
      <summary>Ottiene il tempo totale trascorso misurato dall'istanza corrente in millisecondi.</summary>
      <returns>Valore long Integer di sola lettura che rappresenta il numero totale di millisecondi misurato dall'istanza corrente.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedTicks">
      <summary>Ottiene il tempo totale trascorso misurato dall'istanza corrente in cicli del timer.</summary>
      <returns>Valore long Integer di sola lettura che rappresenta il numero totale di cicli del timer misurati dall'istanza corrente.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.Frequency">
      <summary>Ottiene la frequenza del timer sotto forma di numero di cicli al secondo.Questo campo è di sola lettura.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.GetTimestamp">
      <summary>Ottiene il numero corrente di cicli nel meccanismo del timer.</summary>
      <returns>Valore long Integer che rappresenta il valore del contatore dei cicli del meccanismo del timer sottostante.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.IsHighResolution">
      <summary>Indica se il timer è basato su un contatore delle prestazioni ad alta risoluzione.Questo campo è di sola lettura.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.IsRunning">
      <summary>Ottiene un valore che indica se il timer <see cref="T:System.Diagnostics.Stopwatch" /> è in funzione.</summary>
      <returns>true se l'istanza di <see cref="T:System.Diagnostics.Stopwatch" /> è attualmente in funzione e sta misurando il tempo trascorso per un intervallo; in caso contrario, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Reset">
      <summary>Interrompe la misurazione dell'intervallo di tempo e reimposta il tempo trascorso su zero.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Restart">
      <summary>Arresta la misurazione dell'intervallo di tempo, azzera il tempo trascorso e avvia la misurazione del tempo trascorso.</summary>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Start">
      <summary>Avvia o riprende la misurazione del tempo trascorso per un intervallo.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.StartNew">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Diagnostics.Stopwatch" />, imposta su zero la proprietà del tempo trascorso e avvia la misurazione del tempo trascorso.</summary>
      <returns>Timer <see cref="T:System.Diagnostics.Stopwatch" /> che ha appena iniziato a misurare il tempo trascorso.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Stop">
      <summary>Interrompe la misurazione del tempo trascorso per un intervallo.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.Path">
      <summary>Esegue operazioni sulle istanze <see cref="T:System.String" /> che contengono informazioni sul percorso di file o directory.Le operazioni vengono eseguite su più piattaforme.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere il Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.AltDirectorySeparatorChar">
      <summary>Fornisce un carattere alternativo specifico della piattaforma usato per separare i livelli di directory in una stringa di percorso che riflette un'organizzazione di file system gerarchica.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.ChangeExtension(System.String,System.String)">
      <summary>Cambia l'estensione di una stringa di percorso.</summary>
      <returns>Informazioni sul percorso modificato.Nelle piattaforme desktop basate su Windows, se <paramref name="path" /> è null o una stringa vuota (""), le informazioni sul percorso verranno restituite immutate.Se <paramref name="extension" /> è null, la stringa restituita conterrà il percorso specificato senza estensione.Se <paramref name="path" /> non ha estensione e <paramref name="extension" /> non è null, la stringa di percorso restituita conterrà <paramref name="extension" /> alla fine di <paramref name="path" />.</returns>
      <param name="path">Informazioni sul percorso da modificare.Il percorso non può contenere i caratteri definiti in <see cref="M:System.IO.Path.GetInvalidPathChars" />.</param>
      <param name="extension">Nuova estensione, con o senza un punto iniziale.Specificare null per rimuovere un'estensione esistente da <paramref name="path" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o più dei caratteri non validi definiti nel metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String)">
      <summary>Combina due stringhe in un percorso.</summary>
      <returns>Percorsi combinati.Se uno dei percorsi specificati è una stringa di lunghezza zero, il metodo restituirà solo l'altro percorso.Se <paramref name="path2" /> contiene un percorso assoluto, il metodo restituisce <paramref name="path2" />.</returns>
      <param name="path1">Primo percorso da combinare. </param>
      <param name="path2">Secondo percorso da combinare. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" /> o <paramref name="path2" /> contiene uno o più caratteri non validi definiti in <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" /> o <paramref name="path2" /> è null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String,System.String)">
      <summary>Combina tre stringhe in un percorso.</summary>
      <returns>Percorsi combinati.</returns>
      <param name="path1">Primo percorso da combinare. </param>
      <param name="path2">Secondo percorso da combinare. </param>
      <param name="path3">Terzo percorso da combinare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" />, <paramref name="path2" /> o <paramref name="path3" /> contiene uno o più dei caratteri non validi definiti in <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" />, <paramref name="path2" /> o <paramref name="path3" /> è null. </exception>
    </member>
    <member name="M:System.IO.Path.Combine(System.String[])">
      <summary>Combina una matrice di stringhe in un percorso.</summary>
      <returns>Percorsi combinati.</returns>
      <param name="paths">Matrice di parti del percorso.</param>
      <exception cref="T:System.ArgumentException">Una delle stringhe nella matrice contiene almeno un carattere non valido definito in <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Una delle stringhe nella matrice è null. </exception>
    </member>
    <member name="F:System.IO.Path.DirectorySeparatorChar">
      <summary>Fornisce un carattere specifico della piattaforma usato per separare i livelli di directory in una stringa di percorso che riflette un'organizzazione di file system gerarchica.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetDirectoryName(System.String)">
      <summary>Restituisce le informazioni sulla directory per la stringa di percorso specificata.</summary>
      <returns>Informazioni sulla directory per <paramref name="path" /> o null se <paramref name="path" /> indica una directory radice o è Null.Restituisce <see cref="F:System.String.Empty" /> se <paramref name="path" /> non contiene informazioni sulla directory.</returns>
      <param name="path">Percorso di un file o di una directory. </param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="path" /> contiene caratteri non validi, è vuoto o contiene solo spazi vuoti. </exception>
      <exception cref="T:System.IO.PathTooLongException">Nel .NET for Windows Store apps o libreria di classi portabile, intercettare l'eccezione della classe di base, <see cref="T:System.IO.IOException" />, al contrario.Il parametro <paramref name="path" /> è maggiore della lunghezza massima definita dal sistema.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetExtension(System.String)">
      <summary>Restituisce l'estensione della stringa di percorso specificata.</summary>
      <returns>Estensione del percorso specificato, incluso il punto ("."), oppure null oppure <see cref="F:System.String.Empty" />.Se <paramref name="path" /> è null, <see cref="M:System.IO.Path.GetExtension(System.String)" /> restituisce null.Se <paramref name="path" /> non include informazioni sull'estensione, <see cref="M:System.IO.Path.GetExtension(System.String)" /> restituisce <see cref="F:System.String.Empty" />.</returns>
      <param name="path">Stringa di percorso dalla quale ottenere l'estensione. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o più dei caratteri non validi definiti nel metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.  </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileName(System.String)">
      <summary>Restituisce il nome del file e l'estensione della stringa di percorso specificata.</summary>
      <returns>Caratteri successivi all'ultimo carattere di directory in <paramref name="path" />.Se l'ultimo carattere di <paramref name="path" /> è un separatore di directory o di volumi, questo metodo restituisce <see cref="F:System.String.Empty" />.Se <paramref name="path" /> è null, questo metodo restituisce null.</returns>
      <param name="path">Stringa di percorso dalla quale ottenere il nome del file e l'estensione. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o più dei caratteri non validi definiti nel metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileNameWithoutExtension(System.String)">
      <summary>Restituisce il nome del file della stringa di percorso specificata senza estensione.</summary>
      <returns>Stringa restituita da <see cref="M:System.IO.Path.GetFileName(System.String)" />, senza l'ultimo punto (.) e tutti i caratteri che lo seguono.</returns>
      <param name="path">Percorso del file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o più dei caratteri non validi definiti nel metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFullPath(System.String)">
      <summary>Restituisce il percorso assoluto della stringa di percorso specificata.</summary>
      <returns>Percorso completo di <paramref name="path" />, ad esempio "C:\MyFile.txt".</returns>
      <param name="path">File o directory per cui ottenere informazioni relative al percorso assoluto. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> è una stringa di lunghezza zero, contiene solo spazi vuoti oppure uno o più caratteri non validi definiti nel metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure- Il sistema non è riuscito a recuperare il percorso assoluto. </exception>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone delle autorizzazioni necessarie. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> è null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contiene due punti (":") che non fanno parte di un identificatore di volume (ad esempio, "c:\"). </exception>
      <exception cref="T:System.IO.PathTooLongException">Il percorso, il nome file o entrambi superano la lunghezza massima definita dal sistema.Su piattaforme Windows, ad esempio, i percorsi devono essere composti di un numero di caratteri inferiore a 248 e i nomi file devono essere composti di un numero di caratteri inferiore a 260.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetInvalidFileNameChars">
      <summary>Ottiene una matrice contenente i caratteri non consentiti nei nomi di file.</summary>
      <returns>Matrice contenente i caratteri non consentiti nei nomi di file.</returns>
    </member>
    <member name="M:System.IO.Path.GetInvalidPathChars">
      <summary>Ottiene una matrice contenente i caratteri non consentiti nei nomi di percorso.</summary>
      <returns>Matrice contenente i caratteri non consentiti nei nomi di percorso.</returns>
    </member>
    <member name="M:System.IO.Path.GetPathRoot(System.String)">
      <summary>Ottiene la directory radice del percorso specificato.</summary>
      <returns>La directory radice di <paramref name="path" />, ad esempio "C:\", oppure null se <paramref name="path" /> è null oppure una stringa vuota se <paramref name="path" /> non contiene informazioni sulla directory radice.</returns>
      <param name="path">Percorso dal quale ottenere informazioni sulla directory radice. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o più dei caratteri non validi definiti nel metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />.-oppure- <see cref="F:System.String.Empty" /> è stato passato a <paramref name="path" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetRandomFileName">
      <summary>Restituisce un nome casuale di cartella o di file.</summary>
      <returns>Nome casuale di cartella o di file.</returns>
    </member>
    <member name="M:System.IO.Path.GetTempFileName">
      <summary>Crea un file temporaneo a zero byte con nome univoco sul disco e restituisce il percorso intero di quel file.</summary>
      <returns>Percorso completo del file temporaneo.</returns>
      <exception cref="T:System.IO.IOException">Si verifica un errore I/O, ad esempio non è disponibile alcun nome univoco del file temporaneo.-oppure-Il metodo non consente la creazione di un file temporaneo.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetTempPath">
      <summary>Restituisce il percorso della cartella temporanea dell'utente corrente.</summary>
      <returns>Percorso alla cartella temporanea che termina con una barra rovesciata.</returns>
      <exception cref="T:System.Security.SecurityException">Il chiamante non dispone delle autorizzazioni necessarie. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.HasExtension(System.String)">
      <summary>Determina se un percorso include un'estensione di nome di file.</summary>
      <returns>true se i caratteri che seguono l'ultimo separatore di directory (\\ o /) o di volume (:) nel percorso includono un punto (.) seguito da uno o più caratteri; in caso contrario, false.</returns>
      <param name="path">Percorso in cui cercare un'estensione. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o più dei caratteri non validi definiti nel metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.IsPathRooted(System.String)">
      <summary>Ottiene un valore che indica se la stringa di percorso specificata contiene una directory radice.</summary>
      <returns>true se <paramref name="path" /> contiene una radice; in caso contrario, false.</returns>
      <param name="path">Percorso da testare. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contiene uno o più dei caratteri non validi definiti nel metodo <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.PathSeparator">
      <summary>Carattere separatore specifico della piattaforma, usato per separare le stringhe di percorso nelle variabili di ambiente.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.VolumeSeparatorChar">
      <summary>Fornisce un carattere separatore di volume specifico della piattaforma.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Net.WebUtility">
      <summary>Fornisce metodi per codificare e decodificare URL durante l'elaborazione di richieste Web. </summary>
    </member>
    <member name="M:System.Net.WebUtility.HtmlDecode(System.String)">
      <summary>Consente di convertire una stringa codificata in HTML per la trasmissione HTTP in una stringa decodificata.</summary>
      <returns>Stringa decodificata.</returns>
      <param name="value">Stringa da decodificare.</param>
    </member>
    <member name="M:System.Net.WebUtility.HtmlEncode(System.String)">
      <summary>Consente di eseguire la conversione di una stringa in una stringa codificata in HTML.</summary>
      <returns>Stringa codificata.</returns>
      <param name="value">Stringa da codificare.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecode(System.String)">
      <summary>Consente di convertire in una stringa decodificata una stringa codificata per la trasmissione in un URL.</summary>
      <returns>Restituisce <see cref="T:System.String" />.Stringa decodificata.</returns>
      <param name="encodedValue">Stringa con codifica URL da decodificare.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Converte una matrice di byte codificata per la trasmissione in un URL in una matrice di byte decodificata.</summary>
      <returns>Restituisce <see cref="T:System.Byte" />.Una matrice <see cref="T:System.Byte" /> decodificata.</returns>
      <param name="encodedValue">Una matrice <see cref="T:System.Byte" /> con codifica URL da decodificare.</param>
      <param name="offset">Offset, in byte, dall'inizio della matrice <see cref="T:System.Byte" /> da decodificare.</param>
      <param name="count">Conteggio, in byte, per decodificare dalla matrice <see cref="T:System.Byte" />.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncode(System.String)">
      <summary>Converte una stringa di testo in una stringa con codifica URL.</summary>
      <returns>Restituisce <see cref="T:System.String" />.Stringa con codifica URL.</returns>
      <param name="value">Testo da codificare in URL.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Consente di convertire una matrice di byte in una matrice di byte con codifica URL.</summary>
      <returns>Restituisce <see cref="T:System.Byte" />.Matrice <see cref="T:System.Byte" /> di byte codificata.</returns>
      <param name="value">Matrice <see cref="T:System.Byte" /> da codificare in URL.</param>
      <param name="offset">Offset, in byte, dall'inizio della matrice <see cref="T:System.Byte" /> da codificare.</param>
      <param name="count">Conteggio, in byte, per codificare dalla matrice <see cref="T:System.Byte" />.</param>
    </member>
    <member name="T:System.Runtime.Versioning.FrameworkName">
      <summary>Rappresenta il nome della versione di .NET Framework.</summary>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.Versioning.FrameworkName" /> da una stringa che contiene informazioni su una versione di .NET Framework.</summary>
      <param name="frameworkName">Stringa che contiene informazioni sulla versione di .NET Framework.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="frameworkName" /> è <see cref="F:System.String.Empty" />.In alternativa<paramref name="frameworkName" /> è composto da meno di due componenti o da più di tre componenti.In alternativa<paramref name="frameworkName" /> non include un numero di versione principale e secondaria.In alternativa<paramref name="frameworkName " />non include un numero di versione valido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="frameworkName" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.Versioning.FrameworkName" /> da una stringa e un oggetto <see cref="T:System.Version" /> che identifica una versione della .NET Framework.</summary>
      <param name="identifier">Stringa che identifica una versione di .NET Framework. </param>
      <param name="version">Oggetto che contiene informazioni sulla versione di .NET Framework.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> è <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> è null.In alternativa<paramref name="version" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.Versioning.FrameworkName" /> da una stringa, un oggetto <see cref="T:System.Version" /> che identifica una versione di .NET Framework e un nome di profilo.</summary>
      <param name="identifier">Stringa che identifica una versione di .NET Framework.</param>
      <param name="version">Oggetto che contiene informazioni sulla versione di .NET Framework.</param>
      <param name="profile">Nome profilo.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> è <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> è null.In alternativa<paramref name="version" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Object)">
      <summary>Restituisce un valore che indica se questa istanza <see cref="T:System.Runtime.Versioning.FrameworkName" /> rappresenta la stessa versione di .NET Framework di un oggetto specificato.</summary>
      <returns>true se ogni componente dell'oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" /> corrente corrisponde al componente corrispondente di <paramref name="obj" />; in caso contrario false.</returns>
      <param name="obj">Oggetto da confrontare con l'istanza corrente.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Runtime.Versioning.FrameworkName)">
      <summary>Restituisce un valore che indica se questa istanza <see cref="T:System.Runtime.Versioning.FrameworkName" /> rappresenta la stessa versione di .NET Framework di una istanza <see cref="T:System.Runtime.Versioning.FrameworkName" /> specificata.</summary>
      <returns>true se ogni componente dell'oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" /> corrente corrisponde al componente corrispondente di <paramref name="other" />; in caso contrario false.</returns>
      <param name="other">Oggetto da confrontare con l'istanza corrente.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.FullName">
      <summary>Ottiene il nome completo oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Nome completo di questo oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.GetHashCode">
      <summary>Restituisce il codice hash dell'oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Signed Integer a 32 bit che rappresenta il codice hash di questa istanza.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Identifier">
      <summary>Ottiene l'identificatore di questo oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Identificatore di questo oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Equality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Restituisce un valore che indica se due oggetti <see cref="T:System.Runtime.Versioning.FrameworkName" /> rappresentano la stessa versione di .NET Framework.</summary>
      <returns>true se i parametri <paramref name="left" /> e <paramref name="right" /> rappresentano la stessa versione di .NET Framework; in caso contrario, false.</returns>
      <param name="left">Primo oggetto da confrontare.</param>
      <param name="right">Secondo oggetto da confrontare.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Inequality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Restituisce un valore che indica se due oggetti <see cref="T:System.Runtime.Versioning.FrameworkName" /> rappresentano diverse versioni di .NET Framework.</summary>
      <returns>true se i parametri <paramref name="left" /> e <paramref name="right" /> rappresentano diverse versioni di .NET Framework; in caso contrario, false.</returns>
      <param name="left">Primo oggetto da confrontare.</param>
      <param name="right">Secondo oggetto da confrontare.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Profile">
      <summary>Ottiene il nome del profilo di questo oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Nome del profilo di questo oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.ToString">
      <summary>Restituisce la rappresentazione di stringa di questo oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Stringa che rappresenta l'oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Version">
      <summary>Ottiene la versione di questo oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Oggetto che contiene informazioni di versione su questo oggetto <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
  </members>
</doc>