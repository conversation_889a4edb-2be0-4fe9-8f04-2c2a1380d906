﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.BitConverter">
      <summary>Konvertiert Basisdatentypen in ein Bytearray und ein Bytearray in Basisdatentypen.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.BitConverter.DoubleToInt64Bits(System.Double)">
      <summary>Konvertiert die angegebene Gleitkommazahl mit doppelter Genauigkeit in eine 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, deren Wert <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Boolean)">
      <summary>Gibt den angegebenen booleschen Wert als Bytearray zurück.</summary>
      <returns>Ein Bytearray mit der Länge 1.</returns>
      <param name="value">Ein boolescher Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Char)">
      <summary>Gibt den Wert des angegebenen Unicode-Zeichens als Bytearray zurück.</summary>
      <returns>Ein Bytearray mit der Länge 2.</returns>
      <param name="value">Ein zu konvertierendes Zeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Double)">
      <summary>Gibt den angegebenen Gleitkommawert mit doppelter Genauigkeit als Bytearray zurück.</summary>
      <returns>Ein Bytearray mit der Länge 8.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int16)">
      <summary>Gibt den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen als Bytearray zurück.</summary>
      <returns>Ein Bytearray mit der Länge 2.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int32)">
      <summary>Gibt den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen als Bytearray zurück.</summary>
      <returns>Ein Bytearray mit der Länge 4.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int64)">
      <summary>Gibt den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen als Bytearray zurück.</summary>
      <returns>Ein Bytearray mit der Länge 8.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Single)">
      <summary>Gibt den angegebenen Gleitkommawert mit einfacher Genauigkeit als Bytearray zurück.</summary>
      <returns>Ein Bytearray mit der Länge 4.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt16)">
      <summary>Gibt den Wert der angegebenen vorzeichenlosen 16-Bit-Ganzzahl als Bytearray zurück.</summary>
      <returns>Ein Bytearray mit der Länge 2.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt32)">
      <summary>Gibt den Wert der angegebenen vorzeichenlosen 32-Bit-Ganzzahl als Bytearray zurück.</summary>
      <returns>Ein Bytearray mit der Länge 4.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt64)">
      <summary>Gibt den Wert der angegebenen vorzeichenlosen 64-Bit-Ganzzahl als Bytearray zurück.</summary>
      <returns>Ein Bytearray mit der Länge 8.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.Int64BitsToDouble(System.Int64)">
      <summary>Konvertiert die angegebene 64-Bit-Ganzzahl mit Vorzeichen in eine Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, deren Wert <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.BitConverter.IsLittleEndian">
      <summary>Gibt die Bytereihenfolge (Endian-Reihenfolge) an, in der Daten in dieser Computerarchitektur gespeichert werden.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToBoolean(System.Byte[],System.Int32)">
      <summary>Gibt einen booleschen Wert zurück, der aus einem Byte an der angegebenen Position eines Bytearrays konvertiert wurde.</summary>
      <returns>true, wenn das Byte an der Position <paramref name="startIndex" /> in <paramref name="value" /> ungleich null ist, andernfalls false.</returns>
      <param name="value">Ein Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToChar(System.Byte[],System.Int32)">
      <summary>Gibt ein Unicode-Zeichen zurück, das aus zwei Bytes an der angegebenen Position in einem Bytearray konvertiert wurde.</summary>
      <returns>Ein Zeichen, das beginnend bei <paramref name="startIndex" /> aus zwei Bytes gebildet wird.</returns>
      <param name="value">Ein Array. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> entspricht der Länge von <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToDouble(System.Byte[],System.Int32)">
      <summary>Gibt eine Gleitkommazahl mit doppelter Genauigkeit zurück, die aus acht Bytes an der angegebenen Position in einem Bytearray konvertiert wurde.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die beginnend bei <paramref name="startIndex" /> aus acht Bytes gebildet wird.</returns>
      <param name="value">Ein Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> ist größer oder gleich der Länge von <paramref name="value" /> minus 7 und ist kleiner oder gleich der Länge von <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt16(System.Byte[],System.Int32)">
      <summary>Gibt eine 16-Bit-Ganzzahl mit Vorzeichen zurück, die aus zwei Bytes an der angegebenen Position in einem Bytearray konvertiert wurde.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die beginnend bei <paramref name="startIndex" /> aus zwei Bytes gebildet wird.</returns>
      <param name="value">Ein Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> entspricht der Länge von <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt32(System.Byte[],System.Int32)">
      <summary>Gibt eine 32-Bit-Ganzzahl mit Vorzeichen zurück, die aus vier Bytes an der angegebenen Position in einem Bytearray konvertiert wurde.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die beginnend bei <paramref name="startIndex" /> aus vier Bytes gebildet wird.</returns>
      <param name="value">Ein Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> ist größer oder gleich der Länge von <paramref name="value" /> minus 3 und ist kleiner oder gleich der Länge von <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt64(System.Byte[],System.Int32)">
      <summary>Gibt eine 64-Bit-Ganzzahl mit Vorzeichen zurück, die aus acht Bytes an der angegebenen Position in einem Bytearray konvertiert wurde.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die beginnend bei <paramref name="startIndex" /> aus acht Bytes gebildet wird.</returns>
      <param name="value">Ein Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> ist größer oder gleich der Länge von <paramref name="value" /> minus 7 und ist kleiner oder gleich der Länge von <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToSingle(System.Byte[],System.Int32)">
      <summary>Gibt eine Gleitkommazahl mit einfacher Genauigkeit zurück, die aus vier Bytes an der angegebenen Position in einem Bytearray konvertiert wurde.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die beginnend bei <paramref name="startIndex" /> aus vier Bytes gebildet wird.</returns>
      <param name="value">Ein Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> ist größer oder gleich der Länge von <paramref name="value" /> minus 3 und ist kleiner oder gleich der Länge von <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[])">
      <summary>Konvertiert den numerischen Wert jedes Elements im angegebenen Bytearray in die entsprechende hexadezimale Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolge aus hexadezimalen Paaren, die durch einen Bindestrich getrennt sind. Jedes Paar stellt das entsprechende Element in <paramref name="value" /> dar, z. B. "7F-2C-4A-00".</returns>
      <param name="value">Ein Bytearray. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32)">
      <summary>Konvertiert den numerischen Wert jedes Elements in einem Teil des angegebenen Bytearrays in die entsprechende hexadezimale Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolge aus hexadezimalen Paaren, die durch einen Bindestrich getrennt sind. Jedes Paar stellt ein Element aus der Teilmenge von <paramref name="value" /> dar (z. B. "7F-2C-4A-00").</returns>
      <param name="value">Ein Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32,System.Int32)">
      <summary>Konvertiert den numerischen Wert jedes Elements in einem Teil des angegebenen Bytearrays in die entsprechende hexadezimale Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolge aus hexadezimalen Paaren, die durch einen Bindestrich getrennt sind. Jedes Paar stellt ein Element aus der Teilmenge von <paramref name="value" /> dar (z. B. "7F-2C-4A-00").</returns>
      <param name="value">Ein Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <param name="length">Die Anzahl der zu konvertierenden Elemente in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> oder <paramref name="length" /> ist kleiner als 0 (null).– oder –<paramref name="startIndex" /> ist größer als 0 (null) und größer als oder gleich der Länge von <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentException">Die Kombination von <paramref name="startIndex" /> und <paramref name="length" /> gibt keine Position in <paramref name="value" /> an. Das heißt, der <paramref name="startIndex" />-Parameter ist größer als die Länge von <paramref name="value" /> abzüglich des <paramref name="length" />-Parameters.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt16(System.Byte[],System.Int32)">
      <summary>Gibt eine vorzeichenlose 16-Bit-Ganzzahl zurück, die aus zwei Bytes an der angegebenen Position in einem Bytearray konvertiert wurde.</summary>
      <returns>Eine vorzeichenlose 16-Bit-Ganzzahl, die beginnend bei <paramref name="startIndex" /> aus zwei Bytes gebildet wird.</returns>
      <param name="value">Das Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> entspricht der Länge von <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt32(System.Byte[],System.Int32)">
      <summary>Gibt eine vorzeichenlose 32-Bit-Ganzzahl zurück, die aus vier Bytes an der angegebenen Position in einem Bytearray konvertiert wurde.</summary>
      <returns>Eine vorzeichenlose 32-Bit-Ganzzahl, die beginnend bei <paramref name="startIndex" /> aus vier Bytes gebildet wird.</returns>
      <param name="value">Ein Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> ist größer oder gleich der Länge von <paramref name="value" /> minus 3 und ist kleiner oder gleich der Länge von <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt64(System.Byte[],System.Int32)">
      <summary>Gibt eine vorzeichenlose 64-Bit-Ganzzahl zurück, die aus acht Bytes an der angegebenen Position in einem Bytearray konvertiert wurde.</summary>
      <returns>Eine vorzeichenlose 64-Bit-Ganzzahl, die beginnend bei <paramref name="startIndex" /> aus acht Bytes gebildet wird.</returns>
      <param name="value">Ein Bytearray. </param>
      <param name="startIndex">Die Anfangsposition in <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> ist größer oder gleich der Länge von <paramref name="value" /> minus 7 und ist kleiner oder gleich der Länge von <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ist kleiner als 0 (null) oder größer als die Länge von <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Convert">
      <summary>Konvertiert einen Basisdatentyp in einen anderen Basisdatentyp.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type)">
      <summary>Gibt ein Objekt vom angegebenen Typ zurück, dessen Wert dem angegebenen Objekt entspricht.</summary>
      <returns>Ein Objekt, dessen Typ <paramref name="conversionType" /> ist und dessen Wert <paramref name="value" /> entspricht.- oder - Ein Nullverweis (Nothing in Visual Basic), wenn <paramref name="value" />null ist und <paramref name="conversionType" /> kein Werttyp. </returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="conversionType">Der Typ des zurückzugebenden Objekts. </param>
      <exception cref="T:System.InvalidCastException">Diese Konvertierung wird nicht unterstützt.  - oder - <paramref name="value" /> ist null, und <paramref name="conversionType" /> ist ein Werttyp.- oder - <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein von <paramref name="conversionType" /> erkanntes Format auf.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die außerhalb des Bereichs von <paramref name="conversionType" /> liegt.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> ist null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type,System.IFormatProvider)">
      <summary>Gibt ein Objekt vom angegebenen Typ zurück, dessen Wert dem angegebenen Objekt entspricht.Ein Parameter liefert kulturspezifische Formatierungsinformationen.</summary>
      <returns>Ein Objekt, dessen Typ <paramref name="conversionType" /> ist und dessen Wert <paramref name="value" /> entspricht.- oder -  <paramref name="value" />, wenn der <see cref="T:System.Type" /> von <paramref name="value" /> und <paramref name="conversionType" /> gleich sind.- oder -  Ein Nullverweis (Nothing in Visual Basic), wenn <paramref name="value" />null ist und <paramref name="conversionType" /> kein Werttyp.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="conversionType">Der Typ des zurückzugebenden Objekts. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.InvalidCastException">Diese Konvertierung wird nicht unterstützt. - oder - <paramref name="value" /> ist null, und <paramref name="conversionType" /> ist ein Werttyp.- oder - <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besitzt kein Format für <paramref name="conversionType" />, das von <paramref name="provider" /> erkannt wird.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die außerhalb des Bereichs von <paramref name="conversionType" /> liegt.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> ist null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.TypeCode,System.IFormatProvider)">
      <summary>Gibt ein Objekt vom angegebenen Typ zurück, dessen Wert dem angegebenen Objekt entspricht.Ein Parameter liefert kulturspezifische Formatierungsinformationen.</summary>
      <returns>Ein Objekt, dem <paramref name="typeCode" /> als Typ zugrunde liegt und dessen Wert <paramref name="value" /> entspricht.- oder -  Ein Nullverweis (Nothing in Visual Basic), wenn <paramref name="value" />null ist und <paramref name="typeCode" /><see cref="F:System.TypeCode.Empty" />, <see cref="F:System.TypeCode.String" /> oder <see cref="F:System.TypeCode.Object" /> ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="typeCode">Der Typ des zurückzugebenden Objekts. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.InvalidCastException">Diese Konvertierung wird nicht unterstützt.  - oder - <paramref name="value" /> ist null, und <paramref name="typeCode" /> gibt einen Werttyp an.- oder - <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besitzt kein Format für den <paramref name="typeCode" />-Typ, das von <paramref name="provider" /> erkannt wird.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die außerhalb des Bereichs des <paramref name="typeCode" />-Typs liegt.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeCode" /> ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64CharArray(System.Char[],System.Int32,System.Int32)">
      <summary>Konvertiert eine Teilmenge eines Unicode-Zeichenarrays, das Binärdaten als Base-64-Ziffern codiert, in ein entsprechendes Array von 8-Bit-Ganzzahlen ohne Vorzeichen.Parameter geben die Teilmenge im Eingabearray und die Anzahl der zu konvertierenden Elemente an.</summary>
      <returns>Ein Array von 8-Bit-Ganzzahlen ohne Vorzeichen, das <paramref name="length" />-Elementen an der Position <paramref name="offset" /> in <paramref name="inArray" /> entspricht.</returns>
      <param name="inArray">Ein Array von Unicode-Zeichen. </param>
      <param name="offset">Eine Position in <paramref name="inArray" />. </param>
      <param name="length">Die Anzahl der zu konvertierenden Elemente in <paramref name="inArray" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="length" /> ist kleiner als 0.- oder -  Die Summe von <paramref name="offset" /> und <paramref name="length" /> gibt eine Position an, die nicht in <paramref name="inArray" /> liegt. </exception>
      <exception cref="T:System.FormatException">Die Länge von <paramref name="inArray" /> ist ohne Leerraumzeichen nicht 0 (null) und kein Vielfaches von 4. - oder - Das Format von <paramref name="inArray" /> ist ungültig.<paramref name="inArray" /> enthält ein Nicht-Base-64-Zeichen, mehr als zwei Füllzeichen oder ein Füllzeichen, das kein Leerraumzeichen ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64String(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolge, die Binärdaten als Base-64-Ziffern codiert, in ein entsprechendes Array von 8-Bit-Ganzzahlen ohne Vorzeichen.</summary>
      <returns>Ein Array von 8-Bit-Ganzzahlen ohne Vorzeichen, das <paramref name="s" /> entspricht.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> ist null. </exception>
      <exception cref="T:System.FormatException">Die Länge von <paramref name="s" /> ist ohne Leerraumzeichen nicht 0 (null) und kein Vielfaches von 4. - oder - Das Format von <paramref name="s" /> ist ungültig.<paramref name="s" /> enthält ein Nicht-Base-64-Zeichen, mehr als zwei Füllzeichen oder ein Füllzeichen, das kein Leerraumzeichen ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.GetTypeCode(System.Object)">
      <summary>Gibt den <see cref="T:System.TypeCode" /> für das angegebene Objekt zurück.</summary>
      <returns>Der <see cref="T:System.TypeCode" /> für <paramref name="value" /> oder<see cref="F:System.TypeCode.Empty" /> wenn <paramref name="value" />null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64CharArray(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Konvertiert eine Teilmenge eines Arrays von 8-Bit-Ganzzahlen ohne Vorzeichen in eine entsprechende Teilmenge eines Arrays von Unicode-Zeichen, das mit Base-64-Ziffern codiert wurde.Parameter geben die Teilmengen als Offsets des Eingabe- und Ausgabearrays und die Anzahl der Elemente im zu konvertierenden Eingabearray an.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen mit der Anzahl von Bytes in <paramref name="outArray" />.</returns>
      <param name="inArray">Ein Eingabearray von 8-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <param name="offsetIn">Eine Position in <paramref name="inArray" />. </param>
      <param name="length">Die Anzahl der zu konvertierenden Elemente aus <paramref name="inArray" />. </param>
      <param name="outArray">Ein Ausgabearray von Unicode-Zeichen. </param>
      <param name="offsetOut">Eine Position in <paramref name="outArray" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> oder <paramref name="outArray" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offsetIn" />, <paramref name="offsetOut" /> oder <paramref name="length" /> ist negativ.- oder -  Die Summe von <paramref name="offsetIn" /> und <paramref name="length" /> ist größer als die Länge von <paramref name="inArray" />.- oder -  Die Summe von <paramref name="offsetOut" /> und der Anzahl der zurückzugebenden Elemente ist größer als die Länge von <paramref name="outArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[])">
      <summary>Konvertiert ein Array von 8-Bit-Ganzzahlen ohne Vorzeichen in die entsprechende mit Base-64-Ziffern codierte Zeichenfolgendarstellung.</summary>
      <returns>Die Zeichenfolgendarstellung des Inhalts von <paramref name="inArray" /> als Base-64.</returns>
      <param name="inArray">Ein Array von 8-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> ist null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[],System.Int32,System.Int32)">
      <summary>Konvertiert eine Teilmenge eines Arrays von 8-Bit-Ganzzahlen ohne Vorzeichen in die entsprechende mit Base-64-Ziffern codierte Zeichenfolgendarstellung.Parameter geben die Teilmenge als Offset im Eingabearray und die Anzahl der Elemente im zu konvertierenden Array an.</summary>
      <returns>Die Zeichenfolgendarstellung von <paramref name="length" /> Elementen von <paramref name="inArray" /> ab Position <paramref name="offset" /> als Base-64.</returns>
      <param name="inArray">Ein Array von 8-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <param name="offset">Ein Offset in <paramref name="inArray" />. </param>
      <param name="length">Die Anzahl der zu konvertierenden Elemente aus <paramref name="inArray" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="length" /> ist negativ.- oder -  Die Summe von <paramref name="offset" /> und <paramref name="length" /> ist größer als die Länge von <paramref name="inArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Boolean)">
      <summary>Gibt den angegebenen booleschen Wert zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Der zurückzugebende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object)">
      <summary>Konvertiert den Wert eines angegebenen Objekts in einen entsprechenden booleschen Wert.</summary>
      <returns>true oder false. Damit wird der Wert wiedergegeben, der beim Aufrufen der <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" />-Methode für den zugrunde liegenden Typ von <paramref name="value" /> zurückgegeben wird.Wenn <paramref name="value" /> gleich null ist, gibt die Methode false zurück.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist eine Zeichenfolge, die nicht gleich<see cref="F:System.Boolean.TrueString" /> oder <see cref="F:System.Boolean.FalseString" /> ist.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht.- oder - Die Konvertierung von <paramref name="value" /> in <see cref="T:System.Boolean" /> wird nicht unterstützt.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in einen entsprechenden booleschen Wert.</summary>
      <returns>true oder false. Damit wird der Wert wiedergegeben, der beim Aufrufen der <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" />-Methode für den zugrunde liegenden Typ von <paramref name="value" /> zurückgegeben wird.Wenn <paramref name="value" /> gleich null ist, gibt die Methode false zurück.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist eine Zeichenfolge, die nicht gleich<see cref="F:System.Boolean.TrueString" /> oder <see cref="F:System.Boolean.FalseString" /> ist.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht.- oder - Die Konvertierung von <paramref name="value" /> in <see cref="T:System.Boolean" /> wird nicht unterstützt. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung eines logischen Werts in seine boolesche Entsprechung.</summary>
      <returns>true, wenn <paramref name="value" /> gleich <see cref="F:System.Boolean.TrueString" /> ist, oder false, wenn <paramref name="value" /> gleich <see cref="F:System.Boolean.FalseString" /> oder null ist.</returns>
      <param name="value">Eine Zeichenfolge, die den Wert <see cref="F:System.Boolean.TrueString" /> oder <see cref="F:System.Boolean.FalseString" /> enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist ungleich <see cref="F:System.Boolean.TrueString" /> oder <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung eines logischen Werts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in seine boolesche Entsprechung.</summary>
      <returns>true, wenn <paramref name="value" /> gleich <see cref="F:System.Boolean.TrueString" /> ist, oder false, wenn <paramref name="value" /> gleich <see cref="F:System.Boolean.FalseString" /> oder null ist.</returns>
      <param name="value">Eine Zeichenfolge, die den Wert <see cref="F:System.Boolean.TrueString" /> oder <see cref="F:System.Boolean.FalseString" /> enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt.Dieser Parameter wird ignoriert.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist ungleich <see cref="F:System.Boolean.TrueString" /> oder <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in einen entsprechenden booleschen Wert.</summary>
      <returns>true, wenn <paramref name="value" /> nicht 0 (null) ist, andernfalls false.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Byte)">
      <summary>Gibt die angegebene 8-Bit-Ganzzahl ohne Vorzeichen zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Die zurückzugebende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Char)">
      <summary>Konvertiert den Wert des angegebenen Unicode-Zeichens in die entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Das zu konvertierende Unicode-Zeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die größer als <see cref="F:System.Byte.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in die entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 8-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Zahl. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Byte.MaxValue" /> oder kleiner als <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in eine entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 8-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Byte.MaxValue" /> oder kleiner als <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als <see cref="F:System.Byte.MinValue" /> oder größer als <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als <see cref="F:System.Byte.MinValue" /> oder größer als <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als <see cref="F:System.Byte.MinValue" /> oder größer als <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist nicht das Eigenschaftenformat für einen <see cref="T:System.Byte" />-Wert auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert <see cref="T:System.IConvertible" /> nicht. - oder - Die Konvertierung von <paramref name="value" /> in den <see cref="T:System.Byte" />-Typ wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Byte.MinValue" /> oder größer als <see cref="F:System.Byte.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist nicht das Eigenschaftenformat für einen <see cref="T:System.Byte" />-Wert auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert <see cref="T:System.IConvertible" /> nicht. - oder - Die Konvertierung von <paramref name="value" /> in den <see cref="T:System.Byte" />-Typ wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Byte.MinValue" /> oder größer als <see cref="F:System.Byte.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 8-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Eine Gleitkommazahl mit einfacher Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Byte.MaxValue" /> oder kleiner als <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Byte.MinValue" /> oder größer als <see cref="F:System.Byte.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Byte.MinValue" /> oder größer als <see cref="F:System.Byte.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.Int32)">
      <summary>Konvertiert die Zeichenfolgendarstellung einer Zahl in einer angegebenen Basis in eine entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="fromBase">Die Basis der Zahl in <paramref name="value" />, die 2, 8, 10 oder 16 sein muss.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> ist nicht 2, 8, 10 oder 16. - oder - <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl ohne Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> ist <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> enthält in der durch <paramref name="fromBase" /> angegebenen Basis ein Zeichen, das keine gültige Ziffer ist.Die Ausnahmemeldung gibt an, dass keine zu konvertierenden Ziffern vorhanden sind, wenn das erste Zeichen in <paramref name="value" /> ungültig ist, andernfalls gibt die Meldung an, dass <paramref name="value" /> ungültige nachfolgende Zeichen enthält.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, der eine zur Basis 10 gehörende Zahl ohne Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf.- oder - <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Byte.MinValue" /> oder größer als <see cref="F:System.Byte.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 8-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in das entsprechende Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in das entsprechende Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in das entsprechende Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als <see cref="F:System.Char.MinValue" /> oder größer als <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in das entsprechende Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als <see cref="F:System.Char.MinValue" /> oder größer als <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in ein Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das dem Wert entspricht, oder <see cref="F:System.Char.MinValue" />, wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist eine NULL-Zeichenfolge.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht.- oder - Die Konvertierung von <paramref name="value" /> in <see cref="T:System.Char" /> wird nicht unterstützt. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als <see cref="F:System.Char.MinValue" /> oder größer als <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in das entsprechende Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das dem <paramref name="value" /> entspricht, oder <see cref="F:System.Char.MinValue" /> wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist eine NULL-Zeichenfolge.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung von <paramref name="value" /> in <see cref="T:System.Char" /> wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als <see cref="F:System.Char.MinValue" /> oder größer als <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in das entsprechende Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String)">
      <summary>Konvertiert das erste Zeichen einer angegebenen Zeichenfolge in ein Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das dem einzigen Zeichen in <paramref name="value" /> entspricht.</returns>
      <param name="value">Eine Zeichenfolge mit der Länge 1. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.FormatException">Die Länge von <paramref name="value" /> ist nicht 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String,System.IFormatProvider)">
      <summary>Konvertiert das erste Zeichen einer angegebenen Zeichenfolge unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in ein Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das dem einzigen Zeichen in <paramref name="value" /> entspricht.</returns>
      <param name="value">Eine Zeichenfolge mit der Länge 1, oder null. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt.Dieser Parameter wird ignoriert.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.FormatException">Die Länge von <paramref name="value" /> ist nicht 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in das entsprechende Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in das entsprechende Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in das entsprechende Unicode-Zeichen.</summary>
      <returns>Ein Unicode-Zeichen, das <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in ein <see cref="T:System.DateTime" />-Objekt.</summary>
      <returns>Die Datums- und Uhrzeitentsprechung des Werts von <paramref name="value" /> oder eine Datums- und Uhrzeitentsprechung von <see cref="F:System.DateTime.MinValue" />, wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist kein gültiger Datums- und Uhrzeitwert.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in ein <see cref="T:System.DateTime" />-Objekt.</summary>
      <returns>Die Datums- und Uhrzeitentsprechung des Werts von <paramref name="value" /> oder die Datums- und Uhrzeitentsprechung von <see cref="F:System.DateTime.MinValue" />, wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist kein gültiger Datums- und Uhrzeitwert.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung eines Datums und einer Uhrzeit in einen entsprechenden Datums- und Uhrzeitwert.</summary>
      <returns>Die Datums- und Uhrzeitentsprechung des Werts von <paramref name="value" /> oder die Datums- und Uhrzeitentsprechung von <see cref="F:System.DateTime.MinValue" />, wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Die Zeichenfolgendarstellung eines Datums- und Uhrzeitwerts.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist keine korrekt formatierte Datums- und Zeitzeichenfolge. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in ein entsprechendes Datum und eine entsprechende Uhrzeit.</summary>
      <returns>Die Datums- und Uhrzeitentsprechung des Werts von <paramref name="value" /> oder die Datums- und Uhrzeitentsprechung von <see cref="F:System.DateTime.MinValue" />, wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die eine zu konvertierende Datums- und Zeitangabe enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist keine korrekt formatierte Datums- und Zeitzeichenfolge. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende Dezimalzahl.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Dezimalzahl.</summary>
      <returns>Die Dezimalzahl, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Decimal)">
      <summary>Gibt die angegebene Dezimalzahl zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Eine Dezimalzahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in eine entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die <paramref name="value" /> entspricht. </returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Decimal.MaxValue" /> oder kleiner als <see cref="F:System.Decimal.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in eine entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in eine entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in eine entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein für einen <see cref="T:System.Decimal" />-Typ geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Decimal.MinValue" /> oder größer als <see cref="F:System.Decimal.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein für einen <see cref="T:System.Decimal" />-Typ geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht.- oder - Die Konvertierung wird nicht unterstützt. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Decimal.MinValue" /> oder größer als <see cref="F:System.Decimal.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in die entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die <paramref name="value" /> entspricht. </returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Decimal.MaxValue" /> oder kleiner als <see cref="F:System.Decimal.MinValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die eine zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist keine Zahl in einem gültigen Format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Decimal.MinValue" /> oder größer als <see cref="F:System.Decimal.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die eine zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist keine Zahl in einem gültigen Format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Decimal.MinValue" /> oder größer als <see cref="F:System.Decimal.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende Dezimalzahl.</summary>
      <returns>Die Dezimalzahl, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende Dezimalzahl.</summary>
      <returns>Eine Dezimalzahl, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Die Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in eine entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Double)">
      <summary>Gibt die angegebene Gleitkommazahl mit doppelter Genauigkeit zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Die zurückzugebende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein für einen <see cref="T:System.Double" />-Typ geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Double.MinValue" /> oder größer als <see cref="F:System.Double.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein für einen <see cref="T:System.Double" />-Typ geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Double.MinValue" /> oder größer als <see cref="F:System.Double.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Die 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die Gleitkommazahl mit einfacher Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist keine Zahl in einem gültigen Format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Double.MinValue" /> oder größer als <see cref="F:System.Double.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist keine Zahl in einem gültigen Format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Double.MinValue" /> oder größer als <see cref="F:System.Double.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 16-bit signed integer.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Char)">
      <summary>Konvertiert den Wert des angegebenen Unicode-Zeichens in die entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht. </returns>
      <param name="value">Das zu konvertierende Unicode-Zeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in eine entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 16-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int16.MaxValue" /> oder kleiner als <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in eine entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 16-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int16.MaxValue" /> oder kleiner als <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int16)">
      <summary>Gibt die angegebene 16-Bit-Ganzzahl mit Vorzeichen zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Die zurückzugebende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Die 16-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int16.MaxValue" /> oder kleiner als <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int16.MaxValue" /> oder kleiner als <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein für einen <see cref="T:System.Int16" />-Typ geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int16.MinValue" /> oder größer als <see cref="F:System.Int16.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein für einen <see cref="T:System.Int16" />-Typ geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert <see cref="T:System.IConvertible" /> nicht. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int16.MinValue" /> oder größer als <see cref="F:System.Int16.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in die entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 16-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int16.MaxValue" /> oder kleiner als <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int16.MinValue" /> oder größer als <see cref="F:System.Int16.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int16.MinValue" /> oder größer als <see cref="F:System.Int16.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.Int32)">
      <summary>Konvertiert die Zeichenfolgendarstellung einer Zahl in einer angegebenen Basis in eine entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="fromBase">Die Basis der Zahl in <paramref name="value" />, die 2, 8, 10 oder 16 sein muss.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> ist nicht 2, 8, 10 oder 16. - oder - <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl mit Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> ist <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> enthält in der durch <paramref name="fromBase" /> angegebenen Basis ein Zeichen, das keine gültige Ziffer ist.Die Ausnahmemeldung gibt an, dass keine zu konvertierenden Ziffern vorhanden sind, wenn das erste Zeichen in <paramref name="value" /> ungültig ist, andernfalls gibt die Meldung an, dass <paramref name="value" /> ungültige nachfolgende Zeichen enthält.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl mit Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf.- oder - <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int16.MinValue" /> oder größer als <see cref="F:System.Int16.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 16-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Char)">
      <summary>Konvertiert den Wert des angegebenen Unicode-Zeichens in die entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Das zu konvertierende Unicode-Zeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in eine entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 32-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int32.MaxValue" /> oder kleiner als <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in eine entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 32-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int32.MaxValue" /> oder kleiner als <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int32)">
      <summary>Gibt die angegebene 32-Bit-Ganzzahl mit Vorzeichen zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Die zurückzugebende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int32.MaxValue" /> oder kleiner als <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int32.MinValue" /> oder größer als <see cref="F:System.Int32.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert <see cref="T:System.IConvertible" /> nicht. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int32.MinValue" /> oder größer als <see cref="F:System.Int32.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in die entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 32-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int32.MaxValue" /> oder kleiner als <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int32.MinValue" /> oder größer als <see cref="F:System.Int32.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int32.MinValue" /> oder größer als <see cref="F:System.Int32.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.Int32)">
      <summary>Konvertiert die Zeichenfolgendarstellung einer Zahl in einer angegebenen Basis in eine entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="fromBase">Die Basis der Zahl in <paramref name="value" />, die 2, 8, 10 oder 16 sein muss.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> ist nicht 2, 8, 10 oder 16. - oder - <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl mit Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> ist <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> enthält in der durch <paramref name="fromBase" /> angegebenen Basis ein Zeichen, das keine gültige Ziffer ist.Die Ausnahmemeldung gibt an, dass keine zu konvertierenden Ziffern vorhanden sind, wenn das erste Zeichen in <paramref name="value" /> ungültig ist, andernfalls gibt die Meldung an, dass <paramref name="value" /> ungültige nachfolgende Zeichen enthält.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl mit Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf.- oder - <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int32.MinValue" /> oder größer als <see cref="F:System.Int32.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 32-bit signed integer.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Char)">
      <summary>Konvertiert den Wert des angegebenen Unicode-Zeichens in die entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Das zu konvertierende Unicode-Zeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in eine entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 64-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int64.MaxValue" /> oder kleiner als <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in eine entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 64-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int64.MaxValue" /> oder kleiner als <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int64)">
      <summary>Gibt die angegebene 64-Bit-Ganzzahl mit Vorzeichen zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Eine 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int64.MinValue" /> oder größer als <see cref="F:System.Int64.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht.- oder - Die Konvertierung wird nicht unterstützt. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int64.MinValue" /> oder größer als <see cref="F:System.Int64.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in die entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 64-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int64.MaxValue" /> oder kleiner als <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die eine zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int64.MinValue" /> oder größer als <see cref="F:System.Int64.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int64.MinValue" /> oder größer als <see cref="F:System.Int64.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.Int32)">
      <summary>Konvertiert die Zeichenfolgendarstellung einer Zahl in einer angegebenen Basis in eine entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="fromBase">Die Basis der Zahl in <paramref name="value" />, die 2, 8, 10 oder 16 sein muss.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> ist nicht 2, 8, 10 oder 16. - oder - <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl mit Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> ist <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> enthält in der durch <paramref name="fromBase" /> angegebenen Basis ein Zeichen, das keine gültige Ziffer ist.Die Ausnahmemeldung gibt an, dass keine zu konvertierenden Ziffern vorhanden sind, wenn das erste Zeichen in <paramref name="value" /> ungültig ist, andernfalls gibt die Meldung an, dass <paramref name="value" /> ungültige nachfolgende Zeichen enthält.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl mit Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf.- oder - <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Int64.MinValue" /> oder größer als <see cref="F:System.Int64.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Char)">
      <summary>Konvertiert den Wert des angegebenen Unicode-Zeichens in die entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Das zu konvertierende Unicode-Zeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in eine entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 8-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" /> oder kleiner als <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in eine entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 8-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" /> oder kleiner als <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 8-bit signed integer.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" /> oder kleiner als <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" /> oder kleiner als <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" /> oder kleiner als <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.SByte.MinValue" /> oder größer als <see cref="F:System.SByte.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.SByte.MinValue" /> oder größer als <see cref="F:System.SByte.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.SByte)">
      <summary>Gibt die angegebene 8-Bit-Ganzzahl mit Vorzeichen zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Die zurückzugebende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 8-Bit-Ganzzahl mit Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" /> oder kleiner als <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn der Wert gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.SByte.MinValue" /> oder größer als <see cref="F:System.SByte.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.SByte.MinValue" /> oder größer als <see cref="F:System.SByte.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.Int32)">
      <summary>Konvertiert die Zeichenfolgendarstellung einer Zahl in einer angegebenen Basis in eine entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="fromBase">Die Basis der Zahl in <paramref name="value" />, die 2, 8, 10 oder 16 sein muss.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> ist nicht 2, 8, 10 oder 16. - oder - <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl mit Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> ist <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> enthält in der durch <paramref name="fromBase" /> angegebenen Basis ein Zeichen, das keine gültige Ziffer ist.Die Ausnahmemeldung gibt an, dass keine zu konvertierenden Ziffern vorhanden sind, wenn das erste Zeichen in <paramref name="value" /> ungültig ist, andernfalls gibt die Meldung an, dass <paramref name="value" /> ungültige nachfolgende Zeichen enthält.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl mit Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf.- oder - <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.SByte.MinValue" /> oder größer als <see cref="F:System.SByte.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" /> oder kleiner als <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 8-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.SByte.MaxValue" /> oder kleiner als <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in eine entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht.<paramref name="value" /> wird auf den nächsten Wert gerundet.Der Wert 2,345 wird z. B. bei einer Rundung auf zwei Dezimalziffern zu 2,34, und der Wert 2,355 wird zu 2,36.</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in eine entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht.<paramref name="value" /> wird auf den nächsten Wert gerundet.Der Wert 2,345 wird z. B. bei einer Rundung auf zwei Dezimalziffern zu 2,34, und der Wert 2,355 wird zu 2,36.</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in eine entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in eine entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in eine entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Single.MinValue" /> oder größer als <see cref="F:System.Single.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert <see cref="T:System.IConvertible" /> nicht. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Single.MinValue" /> oder größer als <see cref="F:System.Single.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in die entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine 8-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Single)">
      <summary>Gibt die angegebene Gleitkommazahl mit einfacher Genauigkeit zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Die zurückzugebende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist keine Zahl in einem gültigen Format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Single.MinValue" /> oder größer als <see cref="F:System.Single.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> ist keine Zahl in einem gültigen Format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.Single.MinValue" /> oder größer als <see cref="F:System.Single.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Eine Gleitkommazahl mit einfacher Genauigkeit, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean,System.IFormatProvider)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <param name="provider">Eine Instanz eines Objekts.Dieser Parameter wird ignoriert.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.Int32)">
      <summary>Konvertiert den Wert einer 8-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Zeichenfolgendarstellung in einer angegebenen Basis.</summary>
      <returns>Die Zeichenfolgendarstellung von <paramref name="value" /> zur Basis <paramref name="toBase" />.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <param name="toBase">Die Basis des Rückgabewerts, die 2, 8, 10 oder 16 sein muss. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> ist nicht 2, 8, 10 oder 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char)">
      <summary>Konvertiert den Wert des angegebenen Unicode-Zeichens in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Das zu konvertierende Unicode-Zeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Unicode-Zeichens unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Das zu konvertierende Unicode-Zeichen. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt.Dieser Parameter wird ignoriert.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime)">
      <summary>Konvertiert den Wert der angegebenen <see cref="T:System.DateTime" /> in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Der Datums- und Uhrzeitwert, der konvertiert werden soll. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen <see cref="T:System.DateTime" /> unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Der Datums- und Uhrzeitwert, der konvertiert werden soll. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.Int32)">
      <summary>Konvertiert den Wert einer 16-Bit-Ganzzahl mit Vorzeichen in die entsprechende Zeichenfolgendarstellung in einer angegebenen Basis.</summary>
      <returns>Die Zeichenfolgendarstellung von <paramref name="value" /> zur Basis <paramref name="toBase" />.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <param name="toBase">Die Basis des Rückgabewerts, die 2, 8, 10 oder 16 sein muss. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> ist nicht 2, 8, 10 oder 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.Int32)">
      <summary>Konvertiert den Wert einer 32-Bit-Ganzzahl mit Vorzeichen in die entsprechende Zeichenfolgendarstellung in einer angegebenen Basis.</summary>
      <returns>Die Zeichenfolgendarstellung von <paramref name="value" /> zur Basis <paramref name="toBase" />.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <param name="toBase">Die Basis des Rückgabewerts, die 2, 8, 10 oder 16 sein muss. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> ist nicht 2, 8, 10 oder 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.Int32)">
      <summary>Konvertiert den Wert einer 64-Bit-Ganzzahl mit Vorzeichen in die entsprechende Zeichenfolgendarstellung in einer angegebenen Basis.</summary>
      <returns>Die Zeichenfolgendarstellung von <paramref name="value" /> zur Basis <paramref name="toBase" />.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <param name="toBase">Die Basis des Rückgabewerts, die 2, 8, 10 oder 16 sein muss. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> ist nicht 2, 8, 10 oder 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Die Zeichenfolgendarstellung von <paramref name="value" /> oder <see cref="F:System.String.Empty" />, wenn <paramref name="value" /> ein Objekt ist, dessen Wert null beträgt.Wenn <paramref name="value" /> gleich null ist, gibt die Methode null zurück.</returns>
      <param name="value">Ein Objekt, das den zu konvertierenden Wert angibt, oder null. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Die Zeichenfolgendarstellung von <paramref name="value" /> oder <see cref="F:System.String.Empty" />, wenn <paramref name="value" /> ein Objekt ist, dessen Wert null beträgt.Wenn <paramref name="value" /> gleich null ist, gibt die Methode null zurück.</returns>
      <param name="value">Ein Objekt, das den zu konvertierenden Wert angibt, oder null. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64,System.IFormatProvider)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in die entsprechende Zeichenfolgendarstellung.</summary>
      <returns>Eine Zeichenfolgendarstellung von <paramref name="value" />.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 16-bit unsigned integer.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Char)">
      <summary>Konvertiert den Wert des angegebenen Unicode-Zeichens in die entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Die 16-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Das zu konvertierende Unicode-Zeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in die entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 16-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in eine entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 16-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in die entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als Null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt16.MinValue" /> oder größer als <see cref="F:System.UInt16.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt16.MinValue" /> oder größer als <see cref="F:System.UInt16.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in die entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als Null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 16-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt16.MinValue" /> oder größer als <see cref="F:System.UInt16.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt16.MinValue" /> oder größer als <see cref="F:System.UInt16.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.Int32)">
      <summary>Konvertiert die Zeichenfolgendarstellung einer Zahl in einer angegebenen Basis in eine entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="fromBase">Die Basis der Zahl in <paramref name="value" />, die 2, 8, 10 oder 16 sein muss.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> ist nicht 2, 8, 10 oder 16. - oder - <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl ohne Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> ist <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> enthält in der durch <paramref name="fromBase" /> angegebenen Basis ein Zeichen, das keine gültige Ziffer ist.Die Ausnahmemeldung gibt an, dass keine zu konvertierenden Ziffern vorhanden sind, wenn das erste Zeichen in <paramref name="value" /> ungültig ist, andernfalls gibt die Meldung an, dass <paramref name="value" /> ungültige nachfolgende Zeichen enthält.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl ohne Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf.- oder - <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt16.MinValue" /> oder größer als <see cref="F:System.UInt16.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt16)">
      <summary>Gibt die angegebene 16-Bit-Ganzzahl ohne Vorzeichen zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Die zurückzugebende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 16-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 16-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Char)">
      <summary>Konvertiert den Wert des angegebenen Unicode-Zeichens in die entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Das zu konvertierende Unicode-Zeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in die entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 32-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in eine entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 32-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in die entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als Null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als Null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt32.MinValue" /> oder größer als <see cref="F:System.UInt32.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt32.MinValue" /> oder größer als <see cref="F:System.UInt32.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in die entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als Null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 32-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt32.MinValue" /> oder größer als <see cref="F:System.UInt32.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt32.MinValue" /> oder größer als <see cref="F:System.UInt32.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.Int32)">
      <summary>Konvertiert die Zeichenfolgendarstellung einer Zahl in einer angegebenen Basis in eine entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="fromBase">Die Basis der Zahl in <paramref name="value" />, die 2, 8, 10 oder 16 sein muss.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> ist nicht 2, 8, 10 oder 16. - oder - <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl ohne Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> ist <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> enthält in der durch <paramref name="fromBase" /> angegebenen Basis ein Zeichen, das keine gültige Ziffer ist.Die Ausnahmemeldung gibt an, dass keine zu konvertierenden Ziffern vorhanden sind, wenn das erste Zeichen in <paramref name="value" /> ungültig ist, andernfalls gibt die Meldung an, dass <paramref name="value" /> ungültige nachfolgende Zeichen enthält.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl ohne Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf.- oder - <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt32.MinValue" /> oder größer als <see cref="F:System.UInt32.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt32)">
      <summary>Gibt die angegebene 32-Bit-Ganzzahl ohne Vorzeichen zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Die zurückzugebende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl ohne Vorzeichen in eine entsprechende 32-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 32-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist größer als <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Boolean)">
      <summary>Konvertiert den angegebenen booleschen Wert in die entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Die Zahl 1, wenn <paramref name="value" />true ist, andernfalls 0 (null).</returns>
      <param name="value">Der zu konvertierende boolesche Wert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Byte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Char)">
      <summary>Konvertiert den Wert des angegebenen Unicode-Zeichens in die entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Das zu konvertierende Unicode-Zeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Decimal)">
      <summary>Konvertiert den Wert der angegebenen Dezimalzahl in die entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 64-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Dezimalzahl. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Double)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit doppelter Genauigkeit in eine entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 64-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl mit Vorzeichen in die entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als Null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int32)">
      <summary>Konvertiert den Wert der angegebenen 32-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als Null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int64)">
      <summary>Konvertiert den Wert der angegebenen 64-Bit-Ganzzahl mit Vorzeichen in eine entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 64-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als Null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object)">
      <summary>Konvertiert den Wert des angegebenen Objekts in eine 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert, oder null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt64.MinValue" /> oder größer als <see cref="F:System.UInt64.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object,System.IFormatProvider)">
      <summary>Konvertiert den Wert des angegebenen Objekts unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Ein Objekt, das die <see cref="T:System.IConvertible" />-Schnittstelle implementiert. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> weist kein geeignetes Format auf.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> implementiert die <see cref="T:System.IConvertible" />-Schnittstelle nicht. - oder - Die Konvertierung wird nicht unterstützt.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt64.MinValue" /> oder größer als <see cref="F:System.UInt64.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.SByte)">
      <summary>Konvertiert den Wert der angegebenen 8-Bit-Ganzzahl mit Vorzeichen in die entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 8-Bit-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als Null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Single)">
      <summary>Konvertiert den Wert der angegebenen Gleitkommazahl mit einfacher Genauigkeit in die entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>
        <paramref name="value" />, auf die nächste 64-Bit-Ganzzahl ohne Vorzeichen gerundet.Wenn <paramref name="value" /> genau zwischen zwei ganzen Zahlen liegt, wird die gerade Zahl zurückgegeben (d. h. 4,5 wird in 4 und 5,5 in 6 konvertiert).</returns>
      <param name="value">Die zu konvertierende Gleitkommazahl mit einfacher Genauigkeit. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist kleiner als 0 oder größer als <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl in eine entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl mit Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt64.MinValue" /> oder größer als <see cref="F:System.UInt64.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.IFormatProvider)">
      <summary>Konvertiert die angegebene Zeichenfolgendarstellung einer Zahl unter Verwendung der angegebenen kulturspezifischen Formatierungsinformationen in eine entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="provider">Ein Objekt, das kulturspezifische Formatierungsinformationen bereitstellt. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> besteht nicht aus einem optionalen Vorzeichen und einer Folge von Ziffern (0 bis 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt64.MinValue" /> oder größer als <see cref="F:System.UInt64.MaxValue" /> ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.Int32)">
      <summary>Konvertiert die Zeichenfolgendarstellung einer Zahl in einer angegebenen Basis in eine entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die der Zahl in <paramref name="value" /> entspricht, oder 0 (null), wenn <paramref name="value" /> gleich null ist.</returns>
      <param name="value">Eine Zeichenfolge, die die zu konvertierende Zahl enthält. </param>
      <param name="fromBase">Die Basis der Zahl in <paramref name="value" />, die 2, 8, 10 oder 16 sein muss.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> ist nicht 2, 8, 10 oder 16. - oder - <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl ohne Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> ist <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> enthält in der durch <paramref name="fromBase" /> angegebenen Basis ein Zeichen, das keine gültige Ziffer ist.Die Ausnahmemeldung gibt an, dass keine zu konvertierenden Ziffern vorhanden sind, wenn das erste Zeichen in <paramref name="value" /> ungültig ist, andernfalls gibt die Meldung an, dass <paramref name="value" /> ungültige nachfolgende Zeichen enthält.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, der eine nicht zur Basis 10 gehörende Zahl ohne Vorzeichen darstellt, weist ein negatives Vorzeichen als Präfix auf.- oder - <paramref name="value" /> stellt eine Zahl dar, die kleiner als <see cref="F:System.UInt64.MinValue" /> oder größer als <see cref="F:System.UInt64.MaxValue" /> ist.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt16)">
      <summary>Konvertiert den Wert der angegebenen 16-Bit-Ganzzahl ohne Vorzeichen in die entsprechende 64-Bit-Ganzzahl ohne Vorzeichen.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 16-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 64-bit unsigned integer.</summary>
      <returns>Eine 64-Bit-Ganzzahl ohne Vorzeichen, die <paramref name="value" /> entspricht.</returns>
      <param name="value">Die zu konvertierende 32-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt64)">
      <summary>Gibt die angegebene 64-Bit-Ganzzahl ohne Vorzeichen zurück. Es wird keine wirkliche Konvertierung durchgeführt.</summary>
      <returns>
        <paramref name="value" /> wird unverändert zurückgegeben.</returns>
      <param name="value">Die zurückzugebende 64-Bit-Ganzzahl ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Environment">
      <summary>Stellt Informationen und Veränderungsmöglichkeiten für die aktuelle Umgebung und Plattform zur Verfügung.Diese Klasse kann nicht vererbt werden.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.CurrentManagedThreadId">
      <summary>Ruft einen eindeutigen Bezeichner für den aktuellen verwalteten Thread ab.</summary>
      <returns>Eine Ganzzahl, die einen eindeutigen Bezeichner für diesen verwalteten Thread darstellt.</returns>
    </member>
    <member name="M:System.Environment.ExpandEnvironmentVariables(System.String)">
      <summary>Ersetzt den Namen aller Umgebungsvariablen, die in die angegebene Zeichenfolge eingebettet sind, durch die Zeichenfolgenentsprechung für den Wert der Variablen und gibt anschließend das Ergebnis als Zeichenfolge zurück.</summary>
      <returns>Eine Zeichenfolge, bei der alle Umgebungsvariablen durch ihren Wert ersetzt wurden.</returns>
      <param name="name">Eine Zeichenfolge, die die Namen von 0 (null) oder mehr Umgebungsvariablen enthält.Jede Umgebungsvariable wird mit dem Prozentzeichen (%) angegeben.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.FailFast(System.String)">
      <summary>Beendet einen Prozess sofort, nachdem eine Meldung in das Windows-Anwendungsereignisprotokoll geschrieben wurde, und schließt dann die Meldung in Fehlerberichte an Microsoft ein.</summary>
      <param name="message">Eine Meldung, die erklärt, warum der Prozess beendet wurde, oder null, wenn keine Erklärung gegeben wird.</param>
    </member>
    <member name="M:System.Environment.FailFast(System.String,System.Exception)">
      <summary>Beendet einen Prozess sofort, nachdem eine Meldung in das Windows-Anwendungsereignisprotokoll geschrieben wurde, und schließt dann die Meldung und Ausnahmeinformationen in Fehlerberichte an Microsoft ein.</summary>
      <param name="message">Eine Meldung, die erklärt, warum der Prozess beendet wurde, oder null, wenn keine Erklärung gegeben wird.</param>
      <param name="exception">Eine Ausnahme, die den Fehler darstellt, der die Beendigung verursacht hat.Dies ist in der Regel die Ausnahme in einem catch-Block.</param>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariable(System.String)">
      <summary>Ruft den Wert einer Umgebungsvariable vom aktuellen Prozess ab. </summary>
      <returns>Der Wert der von <paramref name="variable" /> angegebenen Umgebungsvariablen oder null, wenn die Umgebungsvariable nicht gefunden wird.</returns>
      <param name="variable">Der Name der Umgebungsvariablen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariables">
      <summary>Ruft alle Namen und Werte der Umgebungsvariablen vom aktuellen Prozess ab.</summary>
      <returns>Ein Wörterbuch, das die Namen und Werte aller Umgebungsvariablen enthält, andernfalls ein leeres Wörterbuch, wenn keine Umgebungsvariablen gefunden werden.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <exception cref="T:System.OutOfMemoryException">The buffer is out of memory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.HasShutdownStarted">
      <summary>Ruft einen Wert ab, der angibt, ob die derzeitige Anwendungsdomäne entladen wird oder die Common Language Runtime (CLR) heruntergefahren wird. </summary>
      <returns>true, wenn die aktuelle Anwendungsdomäne entladen oder die CLR heruntergefahren wird, andernfalls false..</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.NewLine">
      <summary>Ruft die für diese Umgebung definierte Zeichenfolge für einen Zeilenumbruch ab.</summary>
      <returns>Eine Zeichenfolge für Nicht-Unix-Plattformen, die "\r\n" enthält, oder eine Zeichenfolge für Unix-Plattformen, die "\n" enthält.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.ProcessorCount">
      <summary>Ruft die Anzahl von Prozessoren im aktuellen Computer ab.</summary>
      <returns>Die 32-Bit-Ganzzahl mit Vorzeichen, die die Anzahl von Prozessoren im aktuellen Computer angibt.Es ist kein Standardwert vorhanden.Wenn der aktuelle Computer mehrere Prozessorgruppen enthält, gibt diese Eigenschaft die Anzahl logischer Prozessoren zurück, die für die Common Language Runtime (CLR) verfügbar sind.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="NUMBER_OF_PROCESSORS" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.SetEnvironmentVariable(System.String,System.String)">
      <summary>Erstellt, ändert oder löscht eine im aktuellen Prozess gespeicherte Umgebungsvariable.</summary>
      <param name="variable">Der Name einer Umgebungsvariablen.</param>
      <param name="value">Ein Wert, der <paramref name="variable" /> zugewiesen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="variable" /> contains a zero-length string, an initial hexadecimal zero character (0x00), or an equal sign ("="). -or-The length of <paramref name="variable" /> or <paramref name="value" /> is greater than or equal to 32,767 characters.-or-An error occurred during the execution of this operation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.StackTrace">
      <summary>Ruft die aktuellen Stapelüberwachungsinformationen ab.</summary>
      <returns>Eine Zeichenfolge, die Stapelüberwachungsinformationen enthält.Dieser Wert kann <see cref="F:System.String.Empty" /> sein.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack trace information is out of range.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.TickCount">
      <summary>Ruft die Anzahl der Millisekunden ab, die seit dem Systemstart verstrichen sind.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die die seit dem letzten Start des Computers vergangene Zeit in Millisekunden enthält. </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Math">
      <summary>Stellt Konstanten und statische Methoden für trigonometrische, logarithmische und andere gebräuchliche mathematische Funktionen bereit.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie unter der Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Decimal)">
      <summary>Gibt den Absolutbetrag einer <see cref="T:System.Decimal" />-Zahl zurück.</summary>
      <returns>Eine Dezimalzahl x im Bereich 0 ≤ x ≤<see cref="F:System.Decimal.MaxValue" />.</returns>
      <param name="value">Eine Zahl, die größer oder gleich <see cref="F:System.Decimal.MinValue" />, aber kleiner oder gleich <see cref="F:System.Decimal.MaxValue" /> ist. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Double)">
      <summary>Gibt den absoluten Wert einer Gleitkommazahl mit doppelter Genauigkeit zurück.</summary>
      <returns>Eine Gleitkommazahl x mit doppelter Genauigkeit im Bereich 0 ≤ x ≤<see cref="F:System.Double.MaxValue" />.</returns>
      <param name="value">Eine Zahl, die größer oder gleich <see cref="F:System.Double.MinValue" />, aber kleiner oder gleich <see cref="F:System.Double.MaxValue" /> ist.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int16)">
      <summary>Gibt den absoluten Wert einer 16-Bit-Ganzzahl mit Vorzeichen zurück.</summary>
      <returns>Eine 16-Bit-Ganzzahl x mit Vorzeichen im Bereich 0 ≤ x ≤<see cref="F:System.Int16.MaxValue" />.</returns>
      <param name="value">Eine Zahl, die größer als <see cref="F:System.Int16.MinValue" />, aber kleiner oder gleich <see cref="F:System.Int16.MaxValue" /> ist.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist gleich <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int32)">
      <summary>Gibt den absoluten Wert einer 32-Bit-Ganzzahl mit Vorzeichen zurück.</summary>
      <returns>Eine 32-Bit-Ganzzahl x mit Vorzeichen im Bereich 0 ≤ x ≤<see cref="F:System.Int32.MaxValue" />.</returns>
      <param name="value">Eine Zahl, die größer als <see cref="F:System.Int32.MinValue" />, aber kleiner oder gleich <see cref="F:System.Int32.MaxValue" /> ist.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist gleich <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int64)">
      <summary>Gibt den absoluten Wert einer 64-Bit-Ganzzahl mit Vorzeichen zurück.</summary>
      <returns>Eine 64-Bit-Ganzzahl x mit Vorzeichen im Bereich 0 ≤ x ≤<see cref="F:System.Int64.MaxValue" />.</returns>
      <param name="value">Eine Zahl, die größer als <see cref="F:System.Int64.MinValue" />, aber kleiner oder gleich <see cref="F:System.Int64.MaxValue" /> ist.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist gleich <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.SByte)">
      <summary>Gibt den absoluten Wert einer 8-Bit-Ganzzahl mit Vorzeichen zurück.</summary>
      <returns>Eine 8-Bit-Ganzzahl x mit Vorzeichen im Bereich 0 ≤ x ≤<see cref="F:System.SByte.MaxValue" />.</returns>
      <param name="value">Eine Zahl, die größer als <see cref="F:System.SByte.MinValue" />, aber kleiner oder gleich <see cref="F:System.SByte.MaxValue" /> ist.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> ist gleich <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Single)">
      <summary>Gibt den absoluten Wert einer Gleitkommazahl mit einfacher Genauigkeit zurück.</summary>
      <returns>Eine Gleitkommazahl x mit einfacher Genauigkeit im Bereich 0 ≤ x ≤<see cref="F:System.Single.MaxValue" />.</returns>
      <param name="value">Eine Zahl, die größer oder gleich <see cref="F:System.Single.MinValue" />, aber kleiner oder gleich <see cref="F:System.Single.MaxValue" /> ist.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Acos(System.Double)">
      <summary>Gibt einen Winkel zurück, dessen Kosinus die angegebene Zahl ist.</summary>
      <returns>Ein Winkel θ im Bogenmaß im Bereich 0 ≤θ≤π- oder -  <see cref="F:System.Double.NaN" />, wenn <paramref name="d" /> &lt; -1 oder <paramref name="d" /> &gt; 1 oder <paramref name="d" /> gleich <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Eine Zahl, die einen Kosinus darstellt, wobei <paramref name="d" /> größer oder gleich -1, aber kleiner oder gleich 1 sein muss. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Asin(System.Double)">
      <summary>Gibt einen Winkel zurück, dessen Sinus die angegebene Zahl ist.</summary>
      <returns>Ein Winkel θ im Bogenmaß im Bereich π/2 ≤θ≤π/2 - oder -  <see cref="F:System.Double.NaN" />, wenn <paramref name="d" /> &lt; -1 oder <paramref name="d" /> &gt; 1 oder <paramref name="d" /> gleich <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Eine Zahl, die einen Sinus darstellt, wobei <paramref name="d" /> größer oder gleich -1, aber kleiner oder gleich 1 sein muss. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan(System.Double)">
      <summary>Gibt einen Winkel zurück, dessen Tangens die angegebene Zahl ist.</summary>
      <returns>Ein Winkel θ im Bogenmaß im Bereich -π/2 ≤θ≤π/2.- oder -  <see cref="F:System.Double.NaN" />, wenn <paramref name="d" /> gleich <see cref="F:System.Double.NaN" /> ist, -π/2 auf doppelte Genauigkeit gerundet (-1,5707963267949), wenn <paramref name="d" /> gleich <see cref="F:System.Double.NegativeInfinity" /> ist, oder π/2 auf doppelte Genauigkeit gerundet (1,5707963267949), wenn <paramref name="d" /> gleich <see cref="F:System.Double.PositiveInfinity" /> ist.</returns>
      <param name="d">Eine Zahl, die einen Tangens darstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan2(System.Double,System.Double)">
      <summary>Gibt einen Winkel zurück, dessen Tangens der Quotient zweier angegebener Zahlen ist.</summary>
      <returns>Ein Winkel θ im Bogenmaß im Bereich-π≤θ≤π, und tan(θ) = <paramref name="y" /> / <paramref name="x" />, wobei (<paramref name="x" />, <paramref name="y" />) ein Punkt in der kartesischen Ebene ist.Beachten Sie dabei:Für (<paramref name="x" />, <paramref name="y" />) in Quadrant 1, 0 &lt; θ &lt; π/2.Für (<paramref name="x" />, <paramref name="y" />) im Quadranten 2, π/2 &lt; θ≤π.Für (<paramref name="x" />, <paramref name="y" />) im Quadranten 3-π &lt; θ &lt;-π/2.Für (<paramref name="x" />, <paramref name="y" />) im Quadranten 4-π/2 &lt; θ &lt; 0.Für Punkte an den Begrenzungen der Quadranten wird der folgende Rückgabewert zurückgegeben:Wenn y gleich 0 und x nicht negativ ist, gilt θ = 0.Wenn y gleich 0 und x negativ ist, gilt θ = π.Wenn y positiv und x gleich 0 ist, gilt θ = π/2.Wenn y negativ ist und x gleich 0 ist, gilt θ = -π/2.Wenn y gleich 0 ist und x gleich 0 ist, gilt θ = 0. Wenn <paramref name="x" /> oder <paramref name="y" /> gleich <see cref="F:System.Double.NaN" /> ist oder wenn <paramref name="x" /> und <paramref name="y" /> entweder gleich <see cref="F:System.Double.PositiveInfinity" /> oder gleich <see cref="F:System.Double.NegativeInfinity" /> sind, gibt die Methode <see cref="F:System.Double.NaN" /> zurück.</returns>
      <param name="y">Die y-Koordinate eines Punkts. </param>
      <param name="x">Die x-Koordinate eines Punkts. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Decimal)">
      <summary>Gibt den kleinsten ganzzahligen Wert zurück, der größer oder gleich der angegebenen Dezimalzahl ist.</summary>
      <returns>Der kleinste ganzzahlige Wert, der größer oder gleich <paramref name="d" /> ist.Beachten Sie, dass diese Methode einen <see cref="T:System.Decimal" /> anstelle eines ganzzahligen Typs zurückgibt.</returns>
      <param name="d">Eine Dezimalzahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Double)">
      <summary>Gibt den kleinsten ganzzahligen Wert zurück, der größer oder gleich der angegebenen Gleitkommazahl mit doppelter Genauigkeit ist.</summary>
      <returns>Der kleinste ganzzahlige Wert, der größer oder gleich <paramref name="a" /> ist.Wenn <paramref name="a" /> gleich <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> oder <see cref="F:System.Double.PositiveInfinity" /> ist, wird dieser Wert zurückgegeben.Beachten Sie, dass diese Methode einen <see cref="T:System.Double" /> anstelle eines ganzzahligen Typs zurückgibt.</returns>
      <param name="a">Eine Gleitkommazahl mit doppelter Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cos(System.Double)">
      <summary>Gibt den Kosinus des angegebenen Winkels zurück.</summary>
      <returns>Der Kosinus von <paramref name="d" />.Wenn <paramref name="d" /> gleich <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> oder <see cref="F:System.Double.PositiveInfinity" /> ist, wird <see cref="F:System.Double.NaN" /> von dieser Methode zurückgegeben.</returns>
      <param name="d">Ein im Bogenmaß angegebener Winkel. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cosh(System.Double)">
      <summary>Gibt den Hyperbelkosinus des angegebenen Winkels zurück.</summary>
      <returns>Der Hyperbelkosinus von <paramref name="value" />.Wenn <paramref name="value" /> gleich <see cref="F:System.Double.NegativeInfinity" /> oder <see cref="F:System.Double.PositiveInfinity" /> ist, wird <see cref="F:System.Double.PositiveInfinity" /> zurückgegeben.Wenn <paramref name="value" /> gleich <see cref="F:System.Double.NaN" /> ist, wird <see cref="F:System.Double.NaN" /> zurückgegeben.</returns>
      <param name="value">Ein im Bogenmaß angegebener Winkel. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.E">
      <summary>Stellt die Basis des natürlichen Logarithmus durch die Konstante e dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Exp(System.Double)">
      <summary>Gibt den Wert von e hoch angegebenem Exponenten zurück.</summary>
      <returns>Die Zahl e hoch <paramref name="d" />.Wenn <paramref name="d" /> gleich <see cref="F:System.Double.NaN" /> oder <see cref="F:System.Double.PositiveInfinity" /> ist, wird dieser Wert zurückgegeben.Wenn <paramref name="d" /> gleich <see cref="F:System.Double.NegativeInfinity" /> ist, wird 0 zurückgegeben.</returns>
      <param name="d">Eine Zahl, die einen Exponenten angibt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Decimal)">
      <summary>Gibt die größte Ganzzahl zurück, die kleiner oder gleich der angegebenen Dezimalzahl ist.</summary>
      <returns>Die größte Ganzzahl, die kleiner oder gleich <paramref name="d" /> ist.Beachten Sie, dass die Methode einen Ganzzahlwert vom Typ <see cref="T:System.Math" /> zurückgibt.</returns>
      <param name="d">Eine Dezimalzahl. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Double)">
      <summary>Gibt die größte Ganzzahl zurück, die kleiner oder gleich der angegebenen Gleitkommazahl mit doppelter Genauigkeit ist.</summary>
      <returns>Die größte Ganzzahl, die kleiner oder gleich <paramref name="d" /> ist.Wenn <paramref name="d" /> gleich <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> oder <see cref="F:System.Double.PositiveInfinity" /> ist, wird dieser Wert zurückgegeben.</returns>
      <param name="d">Eine Gleitkommazahl mit doppelter Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.IEEERemainder(System.Double,System.Double)">
      <summary>Gibt den Rest der Division zweier angegebener Zahlen zurück.</summary>
      <returns>Die Zahl, die gleich<paramref name="x" /> - (<paramref name="y" /> Q) ist, wobei Q der auf die nächste ganze Zahl gerundete Quotient <paramref name="x" />/<paramref name="y" /> ist (wenn <paramref name="x" />/<paramref name="y" /> genau in der Mitte zwischen zwei ganzen Zahlen liegt, wird die gerade ganze Zahl zurückgegeben).Wenn <paramref name="x" /> - (<paramref name="y" />Q) gleich 0 ist, wird bei positivem <paramref name="x" /> +0 und bei negativem <paramref name="x" /> -0 zurückgegeben.Wenn <paramref name="y" /> = 0 ist, wird <see cref="F:System.Double.NaN" /> zurückgegeben.</returns>
      <param name="x">Ein Dividend. </param>
      <param name="y">Ein Divisor. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double)">
      <summary>Gibt den natürlichen Logarithmus (zur Basis e) der angegebenen Zahl zurück.</summary>
      <returns>Einer der Werte aus der folgenden Tabelle. <paramref name="d" />-ParameterRückgabewert Positiv Der natürliche Logarithmus des <paramref name="d" />d. h. ln <paramref name="d" />, oder e<paramref name="d" />Zero <see cref="F:System.Double.NegativeInfinity" />Negativ <see cref="F:System.Double.NaN" />Gleich <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Gleich <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Die Zahl, deren Logarithmus bestimmt werden soll. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double,System.Double)">
      <summary>Gibt den Logarithmus einer angegebenen Zahl bezüglich einer angegebenen Basis zurück.</summary>
      <returns>Einer der Werte aus der folgenden Tabelle.(Plus unendlich steht für <see cref="F:System.Double.PositiveInfinity" />, minus unendlich für <see cref="F:System.Double.NegativeInfinity" /> und NaN für <see cref="F:System.Double.NaN" />.)<paramref name="a" /><paramref name="newBase" />Rückgabewert<paramref name="a" />&gt; 0(0 &lt;<paramref name="newBase" />&lt; 1) – oder – (<paramref name="newBase" />&gt; 1)lognewBase(a)<paramref name="a" />&lt; 0(beliebiger Wert)NaN(beliebiger Wert)<paramref name="newBase" />&lt; 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = plus unendlichNaN<paramref name="a" /> = NaN(beliebiger Wert)NaN(beliebiger Wert)<paramref name="newBase" /> = NaNNaN(beliebiger Wert)<paramref name="newBase" /> = 1NaN<paramref name="a" /> = 00 &lt;<paramref name="newBase" />&lt; 1 plus unendlich<paramref name="a" /> = 0<paramref name="newBase" />&gt; 1minus unendlich<paramref name="a" /> = Plus unendlich0 &lt;<paramref name="newBase" />&lt; 1minus unendlich<paramref name="a" /> = Plus unendlich<paramref name="newBase" />&gt; 1plus unendlich<paramref name="a" /> = 1<paramref name="newBase" /> = 00<paramref name="a" /> = 1<paramref name="newBase" /> = plus unendlich0</returns>
      <param name="a">Die Zahl, deren Logarithmus bestimmt werden soll. </param>
      <param name="newBase">Die Basis des Logarithmus. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log10(System.Double)">
      <summary>Gibt den Logarithmus einer angegebenen Zahl zur Basis 10 zurück.</summary>
      <returns>Einer der Werte aus der folgenden Tabelle. <paramref name="d" />-Parameter Rückgabewert Positiv Der Logarithmus zur Basis 10 des <paramref name="d" />; das heißt, melden Sie sich 10<paramref name="d" />. Zero <see cref="F:System.Double.NegativeInfinity" />Negativ <see cref="F:System.Double.NaN" />Gleich <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Gleich <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Eine Zahl, deren Logarithmus gesucht wird. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Byte,System.Byte)">
      <summary>Gibt die größere von zwei 8-Bit-Ganzzahlen ohne Vorzeichen zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 8-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 8-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Decimal,System.Decimal)">
      <summary>Gibt die größere von zwei Dezimalzahlen zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden Dezimalzahlen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden Dezimalzahlen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Double,System.Double)">
      <summary>Gibt die größere von zwei Gleitkommazahlen mit doppelter Genauigkeit zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.Wenn entweder <paramref name="val1" /> oder <paramref name="val2" /> oder sowohl <paramref name="val1" /> als auch <paramref name="val2" /> gleich <see cref="F:System.Double.NaN" /> ist, wird <see cref="F:System.Double.NaN" /> zurückgegeben.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden Gleitkommazahlen mit doppelter Genauigkeit. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden Gleitkommazahlen mit doppelter Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int16,System.Int16)">
      <summary>Gibt die größere von zwei 16-Bit-Ganzzahlen mit Vorzeichen zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 16-Bit-Ganzzahlen mit Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 16-Bit-Ganzzahlen mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int32,System.Int32)">
      <summary>Gibt die größere von zwei 32-Bit-Ganzzahlen mit Vorzeichen zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 32-Bit-Ganzzahlen mit Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 32-Bit-Ganzzahlen mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int64,System.Int64)">
      <summary>Gibt die größere von zwei 64-Bit-Ganzzahlen mit Vorzeichen zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 64-Bit-Ganzzahlen mit Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 64-Bit-Ganzzahlen mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.SByte,System.SByte)">
      <summary>Gibt die größere von zwei 8-Bit-Ganzzahlen mit Vorzeichen zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 8-Bit-Ganzzahlen mit Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 8-Bit-Ganzzahlen mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Single,System.Single)">
      <summary>Gibt die größere von zwei Gleitkommazahlen mit einfacher Genauigkeit zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.Wenn entweder <paramref name="val1" /> oder <paramref name="val2" /> oder sowohl <paramref name="val1" /> als auch <paramref name="val2" /> gleich <see cref="F:System.Single.NaN" /> ist, wird <see cref="F:System.Single.NaN" /> zurückgegeben.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden Gleitkommazahlen mit einfacher Genauigkeit. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden Gleitkommazahlen mit einfacher Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt16,System.UInt16)">
      <summary>Gibt die größere von zwei 16-Bit-Ganzzahlen ohne Vorzeichen zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 16-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 16-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt32,System.UInt32)">
      <summary>Gibt die größere von zwei 32-Bit-Ganzzahlen ohne Vorzeichen zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 32-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 32-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt64,System.UInt64)">
      <summary>Gibt die größere von zwei 64-Bit-Ganzzahlen ohne Vorzeichen zurück.</summary>
      <returns>Der größere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 64-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 64-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Byte,System.Byte)">
      <summary>Gibt die kleinere von zwei 8-Bit-Ganzzahlen ohne Vorzeichen zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 8-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 8-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Decimal,System.Decimal)">
      <summary>Gibt die kleinere von zwei Dezimalzahlen zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden Dezimalzahlen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden Dezimalzahlen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Double,System.Double)">
      <summary>Gibt die kleinere von zwei Gleitkommazahlen mit doppelter Genauigkeit zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.Wenn entweder <paramref name="val1" /> oder <paramref name="val2" /> oder sowohl <paramref name="val1" /> als auch <paramref name="val2" /> gleich <see cref="F:System.Double.NaN" /> ist, wird <see cref="F:System.Double.NaN" /> zurückgegeben.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden Gleitkommazahlen mit doppelter Genauigkeit. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden Gleitkommazahlen mit doppelter Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int16,System.Int16)">
      <summary>Gibt die kleinere von zwei 16-Bit-Ganzzahlen mit Vorzeichen zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 16-Bit-Ganzzahlen mit Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 16-Bit-Ganzzahlen mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int32,System.Int32)">
      <summary>Gibt die kleinere von zwei 32-Bit-Ganzzahlen mit Vorzeichen zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 32-Bit-Ganzzahlen mit Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 32-Bit-Ganzzahlen mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int64,System.Int64)">
      <summary>Gibt die kleinere von zwei 64-Bit-Ganzzahlen mit Vorzeichen zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 64-Bit-Ganzzahlen mit Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 64-Bit-Ganzzahlen mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.SByte,System.SByte)">
      <summary>Gibt die kleinere von zwei 8-Bit-Ganzzahlen mit Vorzeichen zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 8-Bit-Ganzzahlen mit Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 8-Bit-Ganzzahlen mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Single,System.Single)">
      <summary>Gibt die kleinere von zwei Gleitkommazahlen mit einfacher Genauigkeit zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.Wenn entweder <paramref name="val1" /> oder <paramref name="val2" /> oder sowohl <paramref name="val1" /> als auch <paramref name="val2" /> gleich <see cref="F:System.Single.NaN" /> ist, wird <see cref="F:System.Single.NaN" /> zurückgegeben.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden Gleitkommazahlen mit einfacher Genauigkeit. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden Gleitkommazahlen mit einfacher Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt16,System.UInt16)">
      <summary>Gibt die kleinere von zwei 16-Bit-Ganzzahlen ohne Vorzeichen zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 16-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 16-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt32,System.UInt32)">
      <summary>Gibt die kleinere von zwei 32-Bit-Ganzzahlen ohne Vorzeichen zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 32-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 32-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt64,System.UInt64)">
      <summary>Gibt die kleinere von zwei 64-Bit-Ganzzahlen ohne Vorzeichen zurück.</summary>
      <returns>Der kleinere der Parameter <paramref name="val1" /> und <paramref name="val2" />.</returns>
      <param name="val1">Die erste von zwei zu vergleichenden 64-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <param name="val2">Die zweite von zwei zu vergleichenden 64-Bit-Ganzzahlen ohne Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.PI">
      <summary>Stellt das Verhältnis eines Kreisumfangs zum Kreisdurchmesser durch die Konstante π dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Pow(System.Double,System.Double)">
      <summary>Potenziert eine angegebene Zahl mit dem angegebenen Exponenten.</summary>
      <returns>Die Zahl <paramref name="x" /> hoch <paramref name="y" />.</returns>
      <param name="x">Eine Gleitkommazahl mit doppelter Genauigkeit, die potenziert werden soll. </param>
      <param name="y">Eine Gleitkommazahl mit doppelter Genauigkeit, die einen Exponenten darstellt. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal)">
      <summary>Rundet einen Dezimalwert auf den nächsten ganzzahligen Wert.</summary>
      <returns>Die zum Parameter <paramref name="d" /> nächste Ganzzahl.Wenn der Nachkommawert von <paramref name="d" /> genau in der Mitte zwischen zwei Ganzzahlen liegt, von denen eine gerade und die andere ungerade ist, wird die gerade Zahl zurückgegeben.Beachten Sie, dass diese Methode einen <see cref="T:System.Decimal" /> anstelle eines ganzzahligen Typs zurückgibt.</returns>
      <param name="d">Eine zu rundende Dezimalzahl. </param>
      <exception cref="T:System.OverflowException">Das Ergebnis liegt außerhalb des Bereichs von <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32)">
      <summary>Rundet einen Dezimalwert auf die angegebene Anzahl von Bruchziffern.</summary>
      <returns>Die Zahl, die <paramref name="d" /> am nächsten liegt und deren Anzahl von Nachkommastellen gleich <paramref name="decimals" /> ist. </returns>
      <param name="d">Eine zu rundende Dezimalzahl. </param>
      <param name="decimals">Die Anzahl von Dezimalstellen im Rückgabewert. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> ist kleiner als 0 oder größer als 28. </exception>
      <exception cref="T:System.OverflowException">Das Ergebnis liegt außerhalb des Bereichs von <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32,System.MidpointRounding)">
      <summary>Rundet einen Dezimalwert auf die angegebene Anzahl von Bruchziffern.Ein Parameter gibt an, wie der Wert gerundet wird, wenn er genau zwischen zwei Zahlen liegt.</summary>
      <returns>Die Zahl, die <paramref name="d" /> am nächsten liegt und deren Anzahl von Nachkommastellen gleich <paramref name="decimals" /> ist.Wenn <paramref name="d" /> weniger Dezimalstellen als <paramref name="decimals" /> hat, wird <paramref name="d" /> unverändert zurückgegeben.</returns>
      <param name="d">Eine zu rundende Dezimalzahl. </param>
      <param name="decimals">Die Anzahl von Dezimalstellen im Rückgabewert. </param>
      <param name="mode">Angabe, wie <paramref name="d" /> gerundet werden soll, wenn der Wert genau in der Mitte zwischen zwei anderen Zahlen liegt.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> ist kleiner als 0 oder größer als 28. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> ist kein gültiger <see cref="T:System.MidpointRounding" />-Wert.</exception>
      <exception cref="T:System.OverflowException">Das Ergebnis liegt außerhalb des Bereichs von <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.MidpointRounding)">
      <summary>Rundet einen Dezimalwert auf die nächste Ganzzahl.Ein Parameter gibt an, wie der Wert gerundet wird, wenn er genau zwischen zwei Zahlen liegt.</summary>
      <returns>Die zu <paramref name="d" /> nächste Ganzzahl.Wenn <paramref name="d" /> genau in der Mitte zwischen zwei Zahlen liegt, von denen eine gerade und die andere ungerade ist, bestimmt der <paramref name="mode" />-Parameter, welche der beiden Zahlen zurückgegeben wird.</returns>
      <param name="d">Eine zu rundende Dezimalzahl. </param>
      <param name="mode">Angabe, wie <paramref name="d" /> gerundet werden soll, wenn der Wert genau in der Mitte zwischen zwei anderen Zahlen liegt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> ist kein gültiger <see cref="T:System.MidpointRounding" />-Wert.</exception>
      <exception cref="T:System.OverflowException">Das Ergebnis liegt außerhalb des Bereichs von <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double)">
      <summary>Rundet einen Gleitkommawert mit doppelter Genauigkeit auf den nächsten ganzzahligen Wert.</summary>
      <returns>Die zu <paramref name="a" /> nächste Ganzzahl.Wenn der Nachkommawert von <paramref name="a" /> genau in der Mitte zwischen zwei Ganzzahlen liegt, von denen eine gerade und die andere ungerade ist, wird die gerade Zahl zurückgegeben.Beachten Sie, dass diese Methode einen <see cref="T:System.Double" /> anstelle eines ganzzahligen Typs zurückgibt.</returns>
      <param name="a">Eine zu rundende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32)">
      <summary>Rundet einen Gleitkommawert mit doppelter Genauigkeit auf eine angegebene Anzahl von Bruchziffern.</summary>
      <returns>Die Zahl, die <paramref name="value" /> am nächsten liegt und deren Anzahl von Nachkommastellen gleich <paramref name="digits" /> ist.</returns>
      <param name="value">Eine zu rundende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <param name="digits">Die Anzahl von Dezimalstellen im Rückgabewert. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> ist kleiner als 0 oder größer als 15. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32,System.MidpointRounding)">
      <summary>Rundet einen Gleitkommawert mit doppelter Genauigkeit auf eine angegebene Anzahl von Bruchziffern.Ein Parameter gibt an, wie der Wert gerundet wird, wenn er genau zwischen zwei Zahlen liegt.</summary>
      <returns>Die Zahl, die <paramref name="value" /> am nächsten liegt und deren Anzahl von Dezimalstellen gleich <paramref name="digits" /> ist.Wenn <paramref name="value" /> weniger Dezimalstellen als <paramref name="digits" /> hat, wird <paramref name="value" /> unverändert zurückgegeben.</returns>
      <param name="value">Eine zu rundende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <param name="digits">Die Anzahl von Dezimalstellen im Rückgabewert. </param>
      <param name="mode">Angabe, wie <paramref name="value" /> gerundet werden soll, wenn der Wert genau in der Mitte zwischen zwei anderen Zahlen liegt.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> ist kleiner als 0 oder größer als 15. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> ist kein gültiger <see cref="T:System.MidpointRounding" />-Wert.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.MidpointRounding)">
      <summary>Rundet einen Gleitkommawert mit doppelter Genauigkeit auf die nächste Ganzzahl.Ein Parameter gibt an, wie der Wert gerundet wird, wenn er genau zwischen zwei Zahlen liegt.</summary>
      <returns>Die zu <paramref name="value" /> nächste Ganzzahl.Wenn <paramref name="value" /> genau in der Mitte zwischen zwei Ganzzahlen liegt, von denen eine gerade und die andere ungerade ist, bestimmt <paramref name="mode" />, welche der beiden Zahlen zurückgegeben wird.</returns>
      <param name="value">Eine zu rundende Gleitkommazahl mit doppelter Genauigkeit. </param>
      <param name="mode">Angabe, wie <paramref name="value" /> gerundet werden soll, wenn der Wert genau in der Mitte zwischen zwei anderen Zahlen liegt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> ist kein gültiger <see cref="T:System.MidpointRounding" />-Wert.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Decimal)">
      <summary>Gibt einen Wert zurück, der das Vorzeichen einer ganzen Zahl angibt.</summary>
      <returns>Eine Zahl, die das Vorzeichen von <paramref name="value" /> angibt, wie in der folgenden Tabelle veranschaulicht.Rückgabewert Bedeutung -1 <paramref name="value" /> ist kleiner als Null. 0 <paramref name="value" /> ist gleich 0 (null). 1 <paramref name="value" /> ist größer als 0 (null). </returns>
      <param name="value">Eine Dezimalzahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Double)">
      <summary>Gibt einen Wert zurück, der das Vorzeichen einer Gleitkommazahl mit doppelter Genauigkeit angibt.</summary>
      <returns>Eine Zahl, die das Vorzeichen von <paramref name="value" /> angibt, wie in der folgenden Tabelle veranschaulicht.Rückgabewert Bedeutung -1 <paramref name="value" /> ist kleiner als Null. 0 <paramref name="value" /> ist gleich 0 (null). 1 <paramref name="value" /> ist größer als 0 (null). </returns>
      <param name="value">Eine Zahl mit Vorzeichen. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> ist gleich <see cref="F:System.Double.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int16)">
      <summary>Gibt einen Wert zurück, der das Vorzeichen einer 16-Bit-Ganzzahl mit Vorzeichen angibt.</summary>
      <returns>Eine Zahl, die das Vorzeichen von <paramref name="value" /> angibt, wie in der folgenden Tabelle veranschaulicht.Rückgabewert Bedeutung -1 <paramref name="value" /> ist kleiner als Null. 0 <paramref name="value" /> ist gleich 0 (null). 1 <paramref name="value" /> ist größer als 0 (null). </returns>
      <param name="value">Eine Zahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int32)">
      <summary>Gibt einen Wert zurück, der das Vorzeichen einer 32-Bit-Ganzzahl mit Vorzeichen angibt.</summary>
      <returns>Eine Zahl, die das Vorzeichen von <paramref name="value" /> angibt, wie in der folgenden Tabelle veranschaulicht.Rückgabewert Bedeutung -1 <paramref name="value" /> ist kleiner als Null. 0 <paramref name="value" /> ist gleich 0 (null). 1 <paramref name="value" /> ist größer als 0 (null). </returns>
      <param name="value">Eine Zahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int64)">
      <summary>Gibt einen Wert zurück, der das Vorzeichen einer 64-Bit-Ganzzahl mit Vorzeichen angibt.</summary>
      <returns>Eine Zahl, die das Vorzeichen von <paramref name="value" /> angibt, wie in der folgenden Tabelle veranschaulicht.Rückgabewert Bedeutung -1 <paramref name="value" /> ist kleiner als Null. 0 <paramref name="value" /> ist gleich 0 (null). 1 <paramref name="value" /> ist größer als 0 (null). </returns>
      <param name="value">Eine Zahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.SByte)">
      <summary>Gibt einen Wert zurück, der das Vorzeichen einer 8-Bit-Ganzzahl mit Vorzeichen angibt.</summary>
      <returns>Eine Zahl, die das Vorzeichen von <paramref name="value" /> angibt, wie in der folgenden Tabelle veranschaulicht.Rückgabewert Bedeutung -1 <paramref name="value" /> ist kleiner als Null. 0 <paramref name="value" /> ist gleich 0 (null). 1 <paramref name="value" /> ist größer als 0 (null). </returns>
      <param name="value">Eine Zahl mit Vorzeichen. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Single)">
      <summary>Gibt einen Wert zurück, der das Vorzeichen einer Gleitkommazahl mit einfacher Genauigkeit angibt.</summary>
      <returns>Eine Zahl, die das Vorzeichen von <paramref name="value" /> angibt, wie in der folgenden Tabelle veranschaulicht.Rückgabewert Bedeutung -1 <paramref name="value" /> ist kleiner als Null. 0 <paramref name="value" /> ist gleich 0 (null). 1 <paramref name="value" /> ist größer als 0 (null). </returns>
      <param name="value">Eine Zahl mit Vorzeichen. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> ist gleich <see cref="F:System.Single.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sin(System.Double)">
      <summary>Gibt den Sinus des angegebenen Winkels zurück.</summary>
      <returns>Der Sinus von <paramref name="a" />.Wenn <paramref name="a" /> gleich <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> oder <see cref="F:System.Double.PositiveInfinity" /> ist, wird <see cref="F:System.Double.NaN" /> von dieser Methode zurückgegeben.</returns>
      <param name="a">Ein im Bogenmaß angegebener Winkel. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sinh(System.Double)">
      <summary>Gibt den Hyperbelsinus des angegebenen Winkels zurück.</summary>
      <returns>Der Hyperbelsinus von <paramref name="value" />.Wenn <paramref name="value" /> gleich <see cref="F:System.Double.NegativeInfinity" />, <see cref="F:System.Double.PositiveInfinity" /> oder <see cref="F:System.Double.NaN" /> ist, gibt diese Methode einen <see cref="T:System.Double" /> mit dem Wert <paramref name="value" /> zurück.</returns>
      <param name="value">Ein im Bogenmaß angegebener Winkel. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sqrt(System.Double)">
      <summary>Gibt die Quadratwurzel einer angegebenen Zahl zurück.</summary>
      <returns>Einer der Werte aus der folgenden Tabelle. <paramref name="d" />-Parameter Rückgabewert 0 oder positiv Die positive Quadratwurzel von <paramref name="d" />. Negativ <see cref="F:System.Double.NaN" />Entspricht <see cref="F:System.Double.NaN" />.<see cref="F:System.Double.NaN" />Entspricht <see cref="F:System.Double.PositiveInfinity" />.<see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Die Zahl, deren Quadratwurzel bestimmt werden soll. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tan(System.Double)">
      <summary>Gibt den Tangens des angegebenen Winkels zurück.</summary>
      <returns>Der Tangens von <paramref name="a" />.Wenn <paramref name="a" /> gleich <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> oder <see cref="F:System.Double.PositiveInfinity" /> ist, wird <see cref="F:System.Double.NaN" /> von dieser Methode zurückgegeben.</returns>
      <param name="a">Ein im Bogenmaß angegebener Winkel. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tanh(System.Double)">
      <summary>Gibt den Hyperbeltangens des angegebenen Winkels zurück.</summary>
      <returns>Der Hyperbeltangens von <paramref name="value" />.Wenn <paramref name="value" /> gleich <see cref="F:System.Double.NegativeInfinity" /> ist, gibt diese Methode -1 zurück.Wenn "value" gleich <see cref="F:System.Double.PositiveInfinity" /> ist, gibt diese Methode 1 zurück.Wenn <paramref name="value" /> gleich <see cref="F:System.Double.NaN" /> ist, gibt diese Methode <see cref="F:System.Double.NaN" /> zurück.</returns>
      <param name="value">Ein im Bogenmaß angegebener Winkel. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Decimal)">
      <summary>Berechnet den ganzzahligen Teil einer angegebenen Dezimalzahl. </summary>
      <returns>Der ganzzahlige Teil von <paramref name="d" />, d. h, die Zahl, die nach dem Verwerfen der Dezimalstellen übrig bleibt.</returns>
      <param name="d">Eine abzuschneidende Zahl.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Double)">
      <summary>Berechnet den den ganzzahligen Teil einer angegebenen Gleitkommazahl mit doppelter Genauigkeit. </summary>
      <returns>Der ganzzahlige Teil von <paramref name="d" />, d. h. die Zahl, die übrig bleibt, wenn alle Dezimalstellen verworfen wurden, oder einer der Werte in der folgenden Tabelle. <paramref name="d" />Rückgabewert<see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Eine abzuschneidende Zahl.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.MidpointRounding">
      <summary>Gibt an, wie mathematische Rundungsmethoden eine Zahl verarbeiten sollen, die genau zwischen zwei Zahlen liegt.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.MidpointRounding.AwayFromZero">
      <summary>Wenn eine Zahl genau zwischen zwei Zahlen liegt, wird sie auf die nächste größere Zahl aufgerundet.</summary>
    </member>
    <member name="F:System.MidpointRounding.ToEven">
      <summary>Wenn eine Zahl genau zwischen zwei Zahlen liegt, wird sie auf die nächste gerade Zahl gerundet.</summary>
    </member>
    <member name="T:System.Progress`1">
      <summary>Stellt ein <see cref="T:System.IProgress`1" /> bereit, das Rückrufe für jeden gemeldeten Statuswert aufruft.</summary>
      <typeparam name="T">Gibt den Typ des Werts des Statusberichts an.</typeparam>
    </member>
    <member name="M:System.Progress`1.#ctor">
      <summary>Initialisiert das <see cref="T:System.Progress`1" />-Objekt.</summary>
    </member>
    <member name="M:System.Progress`1.#ctor(System.Action{`0})">
      <summary>Initialisiert das <see cref="T:System.Progress`1" />-Objekt mit des angegebenen Rückrufs.</summary>
      <param name="handler">Ein Handler, der für jeden gemeldeten Fortschrittswert aufgerufen werden soll.Dieser Handler wird zusätzlich zu allen Delegaten aufgerufen, die beim <see cref="E:System.Progress`1.ProgressChanged" />-Ereignis registriert sind.Abhängig von der <see cref="T:System.Threading.SynchronizationContext" />-Instanz, die von <see cref="T:System.Progress`1" /> bei Konstruktion aufgezeichnet wird, kann es vorkommen, dass dieser Handler gleichzeitig mit sich selbst aufgerufen werden kann.</param>
    </member>
    <member name="M:System.Progress`1.OnReport(`0)">
      <summary>Berichtet eine Statusänderung.</summary>
      <param name="value">Der Wert des aktualisierten Status.</param>
    </member>
    <member name="E:System.Progress`1.ProgressChanged">
      <summary>Wird für jeden gemeldeten Statuswert ausgelöst.</summary>
    </member>
    <member name="M:System.Progress`1.System#IProgress{T}#Report(`0)">
      <summary>Berichtet eine Statusänderung.</summary>
      <param name="value">Der Wert des aktualisierten Status.</param>
    </member>
    <member name="T:System.Random">
      <summary>Stellt einen Generator für Pseudozufallszahlen dar, d. h. ein Gerät, das eine Zahlenfolge erzeugt, die bestimmte statistische Anforderungen hinsichtlich ihrer Zufälligkeit erfüllt.Weitere Informationen zum Durchsuchen des .NET Framework-Quellcodes für diesen Typ finden Sie unter Verweisquelle.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Random" />-Klasse unter Verwendung eines zeitabhängigen Standardstartwerts.</summary>
    </member>
    <member name="M:System.Random.#ctor(System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Random" />-Klasse unter Verwendung des angegebenen Startwerts.</summary>
      <param name="Seed">Eine Zahl, mit der ein Startwert für Folgen von Pseudozufallszahlen berechnet wird.Wenn eine negative Zahl angegeben wird, wird der absolute Wert der Zahl verwendet.</param>
    </member>
    <member name="M:System.Random.Next">
      <summary>Gibt eine nicht negative Zufallsganzzahl zurück.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die größer oder gleich 0 (null) und kleiner als <see cref="F:System.Int32.MaxValue" /> ist.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32)">
      <summary>Gibt eine nicht negative Zufallsganzzahl zurück, die kleiner als das angegebene Maximum ist.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die größer oder gleich 0 und kleiner als <paramref name="maxValue" /> ist, d. h. der Bereich der Rückgabewerte umfasst 0, aber nicht <paramref name="maxValue" />.Wenn jedoch <paramref name="maxValue" /> 0 (null) entspricht, wird <paramref name="maxValue" /> zurückgegeben.</returns>
      <param name="maxValue">Die exklusive obere Grenze der Zufallszahl, die generiert werden soll.<paramref name="maxValue" /> muss größer oder gleich 0 sein.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxValue" /> is less than 0. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32,System.Int32)">
      <summary>Gibt eine Zufallsganzzahl zurück, die in einem angegebenen Bereich liegt.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die größer oder gleich <paramref name="minValue" /> und kleiner als <paramref name="maxValue" />ist, d. h. der Bereich der Rückgabewerte umfasst <paramref name="minValue" />, aber nicht <paramref name="maxValue" />.Wenn <paramref name="minValue" /> gleich <paramref name="maxValue" /> ist, wird <paramref name="minValue" /> zurückgegeben.</returns>
      <param name="minValue">Die inklusive untere Grenze der zurückgegebenen Zufallszahl. </param>
      <param name="maxValue">Die exklusive obere Grenze der Zufallszahl, die zurückgegeben werden soll.<paramref name="maxValue" /> muss größer oder gleich <paramref name="minValue" /> sein.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minValue" /> is greater than <paramref name="maxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextBytes(System.Byte[])">
      <summary>Füllt die Elemente eines angegebenen Bytearrays mit Zufallszahlen.</summary>
      <param name="buffer">Ein Bytearray, das für Zufallszahlen vorgesehen ist. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextDouble">
      <summary>Gibt eine zufällige Gleitkommazahl zurück, die größer oder gleich 0,0 und kleiner als 1,0 ist.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die größer oder gleich 0,0 und kleiner als 1,0 ist.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Sample">
      <summary>Gibt eine zufällige Gleitkommazahl zwischen 0,0 und 1,0 zurück.</summary>
      <returns>Eine Gleitkommazahl mit doppelter Genauigkeit, die größer oder gleich 0,0 und kleiner als 1,0 ist.</returns>
    </member>
    <member name="T:System.StringComparer">
      <summary>Stellt einen Zeichenfolgenvergleichsvorgang dar, der bestimmte Regeln zur Groß- und Kleinschreibung und kulturbasierte bzw. Ordinalvergleichsregeln verwendet.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.StringComparer.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.StringComparer" />-Klasse. </summary>
    </member>
    <member name="M:System.StringComparer.Compare(System.String,System.String)">
      <summary>Beim Überschreiben in einer abgeleiteten Klasse wird ein Vergleich von zwei Zeichenfolgen durchgeführt und eine Angabe der relativen Sortierreihenfolge zurückgegeben.</summary>
      <returns>Eine ganze Zahl mit Vorzeichen, die die relativen Werte von <paramref name="x" /> und <paramref name="y" /> angibt, wie in der folgenden Tabelle veranschaulicht.WertBedeutungKleiner als 0 (null)<paramref name="x" /> vorausgeht <paramref name="y" /> in der Sortierreihenfolge.- oder - <paramref name="x" /> ist null, und <paramref name="y" /> ist nicht null.Zero<paramref name="x" /> ist gleich <paramref name="y" />.- oder -<paramref name="x" /> und <paramref name="y" /> sind jeweils null. Größer als 0 (null)<paramref name="x" /> folgende <paramref name="y" /> in der Sortierreihenfolge.- oder - <paramref name="y" /> ist null, und <paramref name="x" /> ist nicht null. </returns>
      <param name="x">Eine Zeichenfolge, die mit <paramref name="y" /> verglichen werden soll.</param>
      <param name="y">Eine Zeichenfolge, die mit <paramref name="x" /> verglichen werden soll.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.CurrentCulture">
      <summary>Ruft ein <see cref="T:System.StringComparer" />-Objekt ab, das mit den Wortvergleichsregeln der aktuellen Kultur einen Zeichenfolgenvergleich mit Berücksichtigung von Groß- und Kleinschreibung ausführt.</summary>
      <returns>Ein neues <see cref="T:System.StringComparer" />-Objekt.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.StringComparer.CurrentCultureIgnoreCase">
      <summary>Ruft ein <see cref="T:System.StringComparer" />-Objekt ab, das Zeichenfolgenvergleiche ohne Unterscheidung der Groß- und Kleinschreibung mit den Wortvergleichsregeln der aktuellen Kultur ausführt.</summary>
      <returns>Ein neues <see cref="T:System.StringComparer" />-Objekt.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.StringComparer.Equals(System.String,System.String)">
      <summary>Beim Überschreiben in einer abgeleiteten Klasse wird angegeben, ob zwei Zeichenfolgen gleich sind.</summary>
      <returns>true, wenn <paramref name="x" /> und <paramref name="y" /> auf dasselbe Objekt verweisen oder wenn <paramref name="x" /> und <paramref name="y" /> gleich sind, oder wenn <paramref name="x" /> und <paramref name="y" /> gleich null sind; andernfalls false.</returns>
      <param name="x">Eine Zeichenfolge, die mit <paramref name="y" /> verglichen werden soll.</param>
      <param name="y">Eine Zeichenfolge, die mit <paramref name="x" /> verglichen werden soll.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.GetHashCode(System.String)">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Hashcode für die angegebene Zeichenfolge ab.</summary>
      <returns>Ein 32-Bit-Hashcode mit Vorzeichen, berechnet aus dem Wert des <paramref name="obj" />-Parameters.</returns>
      <param name="obj">Eine Zeichenfolge.</param>
      <exception cref="T:System.ArgumentException">Nicht genug Arbeitsspeicher ist verfügbar, um den zur Berechnung des Hashcodes erforderlichen Puffer zuzuweisen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> ist null. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.StringComparer.Ordinal">
      <summary>Ruft ein <see cref="T:System.StringComparer" />-Objekt ab, das einen Ordinalzeichenfolgenvergleich mit Berücksichtigung der Groß- und Kleinschreibung ausführt.</summary>
      <returns>Ein <see cref="T:System.StringComparer" />-Objekt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.OrdinalIgnoreCase">
      <summary>Ruft ein <see cref="T:System.StringComparer" />-Objekt ab, das einen Ordinalzeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung ausführt.</summary>
      <returns>Ein <see cref="T:System.StringComparer" />-Objekt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Vergleicht zwei Objekte und gibt über den zurückgegebenen Wert an, ob eines der Objekte kleiner, gleich oder größer als das andere Objekt ist.</summary>
      <returns>Eine ganze Zahl mit Vorzeichen, die die relativen Werte von <paramref name="x" /> und <paramref name="y" /> angibt, wie in der folgenden Tabelle veranschaulicht.WertBedeutungKleiner als 0 (null)<paramref name="x" /> ist kleiner als <paramref name="y" />.Zero<paramref name="x" /> ist gleich <paramref name="y" />.Größer als 0 (null)<paramref name="x" /> ist größer als <paramref name="y" />.</returns>
      <param name="x">Das erste zu vergleichende Objekt.</param>
      <param name="y">Das zweite zu vergleichende Objekt.</param>
      <exception cref="T:System.ArgumentException">Weder <paramref name="x" /> noch <paramref name="y" /> implementieren die <see cref="T:System.IComparable" />-Schnittstelle.- oder - <paramref name="x" /> und <paramref name="y" /> sind nicht vom gleichen Typ. Keines der beiden kann einen Vergleich mit dem jeweils anderen Objekt behandeln.</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Bestimmt, ob die angegebenen Objekte gleich sind.</summary>
      <returns>true, wenn die angegebenen Objekte gleich sind, andernfalls false. </returns>
      <param name="x">Das erste zu vergleichende Objekt.</param>
      <param name="y">Das zweite zu vergleichende Objekt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> und <paramref name="y" /> sind nicht vom gleichen Typ und können keinen Vergleich miteinander ausführen. </exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Gibt einen Hashcode für das angegebene Objekt zurück.</summary>
      <returns>Ein Hashcode für das angegebene Objekt. </returns>
      <param name="obj">Das Objekt, für das ein Hashcode zurückgegeben werden soll. </param>
      <exception cref="T:System.ArgumentNullException">Der Typ von <paramref name="obj" /> ist ein Referenztyp, und <paramref name="obj" /> ist null. </exception>
    </member>
    <member name="T:System.UriBuilder">
      <summary>Stellt einen benutzerdefinierten Konstruktor für URIs (Uniform Resource Identifier) bereit und ändert URIs für die <see cref="T:System.Uri" />-Klasse.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.UriBuilder" />-Klasse.</summary>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.UriBuilder" />-Klasse mit dem angegebenen URI.</summary>
      <param name="uri">Eine URI-Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> ist null. </exception>
      <exception cref="T:System.UriFormatException">Unter .NET for Windows Store apps oder in der Portable Klassenbibliothek verwenden Sie stattdessen die Basisklassenausnahme <see cref="T:System.FormatException" />.<paramref name="uri" /> ist eine Zeichenfolge der Länge 0 oder enthält nur Leerzeichen.– oder – Die Analyseroutine hat ein Schema in einem ungültigen Formular gefunden.– oder – Der Parser hat mehr als zwei aufeinander folgende Schrägstriche in einem URI gefunden, der nicht das Schema "file" verwendet.– oder – "<paramref name="uri" />" ist keine gültige URI. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.UriBuilder" />-Klasse mit dem angegebenen Schema und dem angegebenen Host.</summary>
      <param name="schemeName">Ein Internetprotokoll. </param>
      <param name="hostName">Ein Domänenname im DNS-Format oder eine IP-Adresse. </param>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.UriBuilder" />-Klasse mit den Angaben für Schema, Host und Anschluss.</summary>
      <param name="scheme">Ein Internetprotokoll. </param>
      <param name="host">Ein Domänenname im DNS-Format oder eine IP-Adresse. </param>
      <param name="portNumber">Eine IP-Anschlussnummer für den Dienst. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portNumber" /> ist kleiner als -1 oder größer als 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.UriBuilder" />-Klasse mit den Angaben für Schema, Host, Anschluss und Pfad.</summary>
      <param name="scheme">Ein Internetprotokoll. </param>
      <param name="host">Ein Domänenname im DNS-Format oder eine IP-Adresse. </param>
      <param name="port">Eine IP-Anschlussnummer für den Dienst. </param>
      <param name="pathValue">Der Pfad zur Internetressource. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> ist kleiner als -1 oder größer als 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.UriBuilder" />-Klasse mit den Angaben für Schema, Host, Anschluss, Pfad und Abfragezeichenfolge oder Fragmentbezeichner.</summary>
      <param name="scheme">Ein Internetprotokoll. </param>
      <param name="host">Ein Domänenname im DNS-Format oder eine IP-Adresse. </param>
      <param name="port">Eine IP-Anschlussnummer für den Dienst. </param>
      <param name="path">Der Pfad zur Internetressource. </param>
      <param name="extraValue">Eine Abfragezeichenfolge oder ein Fragmentbezeichner. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="extraValue" /> ist weder null noch <see cref="F:System.String.Empty" />, und weder beginnt ein gültiger Fragmentbezeichner mit einem Nummernzeichen (#), noch eine gültige Abfragezeichenfolge mit einem Fragezeichen (?). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> ist kleiner als -1 oder größer als 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.Uri)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.UriBuilder" />-Klasse mit der angegebenen <see cref="T:System.Uri" />-Instanz.</summary>
      <param name="uri">Eine Instanz der <see cref="T:System.Uri" />-Klasse. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> ist null. </exception>
    </member>
    <member name="M:System.UriBuilder.Equals(System.Object)">
      <summary>Überprüft eine vorhandene <see cref="T:System.Uri" />-Instanz und den Inhalt des <see cref="T:System.UriBuilder" /> auf Gleichheit.</summary>
      <returns>true, wenn <paramref name="rparam" /> denselben <see cref="T:System.Uri" /> wie der <see cref="T:System.Uri" /> darstellt, der durch diese <see cref="T:System.UriBuilder" />-Instanz erstellt wurde, andernfalls false.</returns>
      <param name="rparam">Das Objekt, das mit der aktuellen Instanz verglichen werden soll. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Fragment">
      <summary>Ruft den Fragmentteil des URIs ab oder legt diesen fest.</summary>
      <returns>Der Fragmentteil des URIs.Der Fragmentbezeichner ("#") wird am Anfang des Fragments hinzugefügt.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.GetHashCode">
      <summary>Gibt den Hashcode für den URI zurück.</summary>
      <returns>Der für den URI generierte Hashcode.</returns>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Host">
      <summary>Ruft den DNS-Hostnamen (Domain Name System) oder die IP-Adresse eines Servers ab oder legt diese fest.</summary>
      <returns>Der DNS-Hostname oder die IP-Adresse des Servers.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Password">
      <summary>Ruft das Kennwort des Benutzers ab, der auf den URI zugreift, oder legt dieses fest.</summary>
      <returns>Das Kennwort des Benutzers, der auf den URI zugreift.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Path">
      <summary>Ruft den Pfad der Ressource ab, auf die der URI verweist, oder legt diesen fest.</summary>
      <returns>Der Pfad der Ressource, auf die der URI verweist.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Port">
      <summary>Ruft die Anschlussnummer des URIs ab oder legt diese fest.</summary>
      <returns>Die Anschlussnummer des URIs.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Für diesen Port kann kein geringerer Wert als -1 und keine höherer Wert als 65.535 festgelegt werden. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Query">
      <summary>Ruft zum URI gehörende Abfrageinformationen ab oder legt diese fest.</summary>
      <returns>Die zum URI gehörenden Abfrageinformationen.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Scheme">
      <summary>Ruft den Schemanamen des URIs ab oder legt diesen fest.</summary>
      <returns>Das Schema des URIs.</returns>
      <exception cref="T:System.ArgumentException">Das Schema kann nicht auf einen ungültigen Schemanamen festgelegt werden. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.UriBuilder.ToString">
      <summary>Gibt die Anzeigezeichenfolge für die angegebene <see cref="T:System.UriBuilder" />-Instanz zurück.</summary>
      <returns>Die Zeichenfolge mit der Anzeigezeichenfolge des <see cref="T:System.UriBuilder" /> ohne Escapesequenzen.</returns>
      <exception cref="T:System.UriFormatException">Unter .NET for Windows Store apps oder in der Portable Klassenbibliothek verwenden Sie stattdessen die Basisklassenausnahme <see cref="T:System.FormatException" />.Die <see cref="T:System.UriBuilder" />-Instanz hat ein ungültiges Kennwort. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Uri">
      <summary>Ruft die von der angegebenen <see cref="T:System.UriBuilder" />-Instanz erstellte <see cref="T:System.Uri" />-Instanz ab.</summary>
      <returns>Ein <see cref="T:System.Uri" /> mit dem von <see cref="T:System.UriBuilder" /> erstellten URI.</returns>
      <exception cref="T:System.UriFormatException">Unter .NET for Windows Store apps oder in der Portable Klassenbibliothek verwenden Sie stattdessen die Basisklassenausnahme <see cref="T:System.FormatException" />.Der mit den <see cref="T:System.UriBuilder" />-Eigenschaften erstellte URI ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.UserName">
      <summary>Der Benutzername des Benutzers, der auf den URI zugreift.</summary>
      <returns>Der Benutzername des Benutzers, der auf den URI zugreift.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Diagnostics.Stopwatch">
      <summary>Stellt eine Gruppe von Methoden und Eigenschaften bereit, mit denen die verstrichene Zeit exakt gemessen werden kann.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, rufen Sie die Verweisquelle auf.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Diagnostics.Stopwatch" />-Klasse.</summary>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.Elapsed">
      <summary>Ruft die gesamte verstrichene Zeit ab, die von der aktuellen Instanz gemessen wurde.</summary>
      <returns>Eine schreibgeschützte <see cref="T:System.TimeSpan" />, die die gesamte, von der aktuellen Instanz gemessene verstrichene Zeit darstellt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedMilliseconds">
      <summary>Ruft die gesamte verstrichene Zeit in Millisekunden ab, die von der aktuellen Instanz gemessen wurde.</summary>
      <returns>Eine schreibgeschützte Long-Integer-Zahl, die die Gesamtanzahl der von der aktuellen Instanz gemessenen Millisekunden angibt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedTicks">
      <summary>Ruft die gesamte verstrichene Zeit, die von der aktuellen Instanz gemessen wurde, in Zeitgeberintervallen (Ticks) ab.</summary>
      <returns>Eine schreibgeschützte Long-Integer-Zahl, die die Gesamtanzahl der von der aktuellen Instanz gemessenen Zeitgeberintervalle angibt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.Frequency">
      <summary>Ruft die Frequenz des Zeitgebers als Anzahl der Ticks pro Sekunde ab.Dieses Feld ist schreibgeschützt.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.GetTimestamp">
      <summary>Ruft die aktuelle Anzahl der Ticks im Zeitgebermechanismus ab.</summary>
      <returns>Eine Long-Integer-Zahl, die den Tickzählerwert des zugrunde liegenden Zeitgebermechanismus angibt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.IsHighResolution">
      <summary>Gibt an, ob der Zeitgeber auf einem hochauflösenden Leistungsindikator basiert.Dieses Feld ist schreibgeschützt.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.IsRunning">
      <summary>Ruft einen Wert ab, der angibt, ob der <see cref="T:System.Diagnostics.Stopwatch" />-Zeitgeber ausgeführt wird.</summary>
      <returns>true, wenn die <see cref="T:System.Diagnostics.Stopwatch" />-Instanz derzeit ausgeführt wird und die verstrichene Zeit für ein Intervall misst; andernfalls false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Reset">
      <summary>Beendet die Zeitintervallmessung und setzt die verstrichene Zeit auf 0 (null) zurück.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Restart">
      <summary>Beendet die Zeitintervallmessung, setzt die verstrichene Zeit auf 0 (null) zurück, und startet die Messung der verstrichenen Zeit.</summary>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Start">
      <summary>Startet den Messvorgang der verstrichenen Zeit für ein Intervall oder nimmt diesen wieder auf.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.StartNew">
      <summary>Initialisiert eine neue <see cref="T:System.Diagnostics.Stopwatch" />-Instanz, legt die Eigenschaft der verstrichenen Zeit auf 0 (null) fest und beginnt mit dem Messen der verstrichenen Zeit.</summary>
      <returns>Eine <see cref="T:System.Diagnostics.Stopwatch" />, die gerade mit dem Messen der verstrichenen Zeit begonnen hat.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Stop">
      <summary>Beendet das Messen der verstrichenen Zeit für ein Intervall.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.Path">
      <summary>Führt Vorgänge für <see cref="T:System.String" />-Instanzen aus, die Datei- oder Verzeichnispfadinformationen enthalten.Diese Vorgänge werden plattformübergreifend durchgeführt.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie unter der Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.AltDirectorySeparatorChar">
      <summary>Stellt ein plattformspezifisches, alternatives Zeichen bereit, das zur Trennung von Verzeichnisebenen in einer Pfadzeichenfolge verwendet wird und eine hierarchische Dateisystemorganisation wiedergibt.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.ChangeExtension(System.String,System.String)">
      <summary>Ändert die Erweiterung einer Pfadzeichenfolge.</summary>
      <returns>Die geänderten Pfadinformationen.Auf Desktopplattformen auf Grundlage von Windows werden die Pfadinformationen unverändert zurückgegeben, wenn <paramref name="path" />null oder eine leere Zeichenfolge ("") ist.Wenn <paramref name="extension" />null ist, enthält die zurückgegebene Zeichenfolge den angegebenen Pfad ohne die Erweiterung.Wenn <paramref name="path" /> keine Erweiterung besitzt und <paramref name="extension" /> nicht null ist, enthält die zurückgegebene Pfadzeichenfolge <paramref name="extension" />, angefügt an das Ende von <paramref name="path" />.</returns>
      <param name="path">Die zu ändernden Pfadinformationen.Der Pfad darf keines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten Zeichen enthalten.</param>
      <param name="extension">Die neue Erweiterung (mit oder ohne führenden Punkt).Geben Sie null an, um eine vorhandene Erweiterung aus <paramref name="path" /> zu entfernen.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> enthält mindestens eines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten ungültigen Zeichen.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String)">
      <summary>Kombiniert zwei Zeichenfolgen zu einem Pfad.</summary>
      <returns>Die kombinierten Pfade.Wenn einer der beiden angegebenen Pfade eine Zeichenfolge der Länge 0 ist, gibt diese Methode den anderen Pfad zurück.Wenn <paramref name="path2" /> einen absoluten Pfad enthält, gibt diese Methode <paramref name="path2" /> zurück.</returns>
      <param name="path1">Der erste zu kombinierende Pfad. </param>
      <param name="path2">Der zweite zu kombinierende Pfad. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" /> oder <paramref name="path2" /> enthält mindestens eines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten ungültigen Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" /> oder <paramref name="path2" /> ist null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String,System.String)">
      <summary>Kombiniert drei Zeichenfolgen zu einem Pfad.</summary>
      <returns>Die kombinierten Pfade.</returns>
      <param name="path1">Der erste zu kombinierende Pfad. </param>
      <param name="path2">Der zweite zu kombinierende Pfad. </param>
      <param name="path3">Der dritte zu kombinierende Pfad.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" />, <paramref name="path2" /> oder <paramref name="path3" /> enthält mindestens eines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten ungültigen Zeichen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" />, <paramref name="path2" /> oder <paramref name="path3" /> ist null. </exception>
    </member>
    <member name="M:System.IO.Path.Combine(System.String[])">
      <summary>Kombiniert ein Array von Zeichenfolgen zu einem Pfad.</summary>
      <returns>Die kombinierten Pfade.</returns>
      <param name="paths">Ein Array der Teile des Pfads.</param>
      <exception cref="T:System.ArgumentException">Eine der Zeichenfolgen im Array enthält eines oder mehrere der ungültigen Zeichen, die in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definiert sind. </exception>
      <exception cref="T:System.ArgumentNullException">Eine der Zeichenfolgen im Array ist null. </exception>
    </member>
    <member name="F:System.IO.Path.DirectorySeparatorChar">
      <summary>Stellt ein plattformspezifisches Zeichen bereit, das zur Trennung von Verzeichnisebenen in einer Pfadzeichenfolge verwendet wird und eine hierarchische Dateisystemorganisation wiedergibt.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetDirectoryName(System.String)">
      <summary>Gibt die Verzeichnisinformationen für die angegebene Pfadzeichenfolge zurück.</summary>
      <returns>Verzeichnisinformationen für <paramref name="path" /> oder null, wenn <paramref name="path" /> ein Stammverzeichnis bezeichnet oder NULL ist.Gibt <see cref="F:System.String.Empty" /> zurück, wenn <paramref name="path" /> keine Verzeichnisinformationen enthält.</returns>
      <param name="path">Der Pfad einer Datei oder eines Verzeichnisses. </param>
      <exception cref="T:System.ArgumentException">Der <paramref name="path" />-Parameter enthält ungültige Zeichen, ist leer oder enthält nur Leerräume. </exception>
      <exception cref="T:System.IO.PathTooLongException">In der .NET for Windows Store apps oder Portable Klassenbibliothek, fangen Sie die Ausnahme Basisklasse <see cref="T:System.IO.IOException" />, stattdessen.Der <paramref name="path" />-Parameter überschreitet die systemdefinierte maximale Länge.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetExtension(System.String)">
      <summary>Gibt die Erweiterung der angegebenen Pfadzeichenfolge zurück.</summary>
      <returns>Die Erweiterung des angegebenen Pfads (einschließlich des Punkts ".") oder null oder <see cref="F:System.String.Empty" />.Wenn <paramref name="path" /> gleich null ist, gibt <see cref="M:System.IO.Path.GetExtension(System.String)" />null zurück.Wenn <paramref name="path" /> keine Informationen über die Erweiterung enthält, gibt <see cref="M:System.IO.Path.GetExtension(System.String)" /><see cref="F:System.String.Empty" /> zurück.</returns>
      <param name="path">Die Pfadzeichenfolge, aus der die Erweiterung abgerufen werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> enthält mindestens eines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten ungültigen Zeichen.  </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileName(System.String)">
      <summary>Gibt den Dateinamen und die Erweiterung der angegebenen Pfadzeichenfolge zurück.</summary>
      <returns>Die Zeichen nach dem letzten Verzeichniszeichen in <paramref name="path" />.Wenn das letzte Zeichen von <paramref name="path" /> ein Verzeichnis- bzw. Volumetrennzeichen ist, gibt diese Methode <see cref="F:System.String.Empty" /> zurück.Wenn <paramref name="path" /> gleich null ist, gibt die Methode null zurück.</returns>
      <param name="path">Die Pfadzeichenfolge, aus der der Dateiname und die Erweiterung abgerufen werden sollen. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> enthält mindestens eines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten ungültigen Zeichen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileNameWithoutExtension(System.String)">
      <summary>Gibt den Dateinamen der angegebenen Pfadzeichenfolge ohne Erweiterung zurück.</summary>
      <returns>Die von <see cref="M:System.IO.Path.GetFileName(System.String)" /> zurückgegebene Zeichenfolge ohne den letzten Punkt (.) und alle folgenden Zeichen.</returns>
      <param name="path">Der Pfad der Datei. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> enthält mindestens eines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten ungültigen Zeichen.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFullPath(System.String)">
      <summary>Gibt den absoluten Pfad für die angegebene Pfadzeichenfolge zurück.</summary>
      <returns>Der vollqualifizierte Speicherort von <paramref name="path" />, z. B. "C:\MyFile.txt".</returns>
      <param name="path">Die Datei oder das Verzeichnis, für das die absoluten Pfadinformationen abgerufen werden sollen. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> ist eine Zeichenfolge der Länge 0 (null), besteht nur aus Leerraum oder enthält mindestens eines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten ungültige Zeichen.- oder -  Das System konnte den absoluten Pfad nicht abrufen. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderlichen Berechtigungen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> ist null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> enthält einen Doppelpunkt (":"), der kein Teil eines Volumebezeichners (z. B. "c: \\") ist. </exception>
      <exception cref="T:System.IO.PathTooLongException">Der angegebene Pfad und/oder der Dateiname überschreiten die vom System vorgegebene Höchstlänge.Beispielsweise müssen Pfade auf Windows-Plattformen weniger als 248 Zeichen und Dateinamen weniger als 260 Zeichen haben.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetInvalidFileNameChars">
      <summary>Ruft ein Array ab, das die Zeichen enthält, die in Dateinamen nicht zulässig sind.</summary>
      <returns>Ein Array, das die Zeichen enthält, die in Dateinamen nicht zulässig sind.</returns>
    </member>
    <member name="M:System.IO.Path.GetInvalidPathChars">
      <summary>Ruft ein Array ab, das die Zeichen enthält, die in Pfadnamen nicht zulässig sind.</summary>
      <returns>Ein Array, das die Zeichen enthält, die in Pfadnamen nicht zulässig sind.</returns>
    </member>
    <member name="M:System.IO.Path.GetPathRoot(System.String)">
      <summary>Ruft die Informationen über das Stammverzeichnis des angegebenen Pfads ab.</summary>
      <returns>Das Stammverzeichnis von <paramref name="path" /> (z. B. "C:\") oder null, wenn <paramref name="path" />null ist, oder eine leere Zeichenfolge, wenn <paramref name="path" /> keine Informationen über das Stammverzeichnis enthält.</returns>
      <param name="path">Der Pfad, von dem Informationen über das Stammverzeichnis abgerufen werden sollen. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> enthält mindestens eines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten ungültigen Zeichen.- oder -  <see cref="F:System.String.Empty" /> wurde an <paramref name="path" /> übergeben. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetRandomFileName">
      <summary>Gibt einen zufälligen Ordnernamen oder Dateinamen zurück.</summary>
      <returns>Ein zufälliger Ordnername oder Dateiname.</returns>
    </member>
    <member name="M:System.IO.Path.GetTempFileName">
      <summary>Erstellt eine eindeutig benannte temporäre Datei auf dem Datenträger mit einer Größe von 0 Byte und gibt den vollständigen Pfad dieser Datei zurück.</summary>
      <returns>Der vollständige Pfad der temporären Datei.</returns>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler tritt auf, z. B. ist kein eindeutiger temporärer Dateiname verfügbar.- oder -Diese Methode konnte keine temporäre Datei erstellen.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetTempPath">
      <summary>Gibt den Pfad des temporären Ordners des aktuellen Benutzers zurück.</summary>
      <returns>Der Pfad zum temporären Ordner, endend mit einem umgekehrten Schrägstrich.</returns>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderlichen Berechtigungen. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.HasExtension(System.String)">
      <summary>Bestimmt, ob ein Pfad eine Dateinamenerweiterung enthält.</summary>
      <returns>true, wenn die Zeichen, die auf das letzte Verzeichnistrennzeichen (\\ oder /) oder Volumetrennzeichen (:) im Pfad folgen, einen Punkt (.) gefolgt von einem oder mehreren Zeichen enthalten, andernfalls false.</returns>
      <param name="path">Der Pfad, in dem nach einer Erweiterung gesucht werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> enthält mindestens eines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten ungültigen Zeichen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.IsPathRooted(System.String)">
      <summary>Ruft einen Wert ab, der angibt, ob die angegebene Pfadzeichenfolge einen Stamm enthält.</summary>
      <returns>true, wenn <paramref name="path" /> einen Stamm enthält, andernfalls false.</returns>
      <param name="path">Der zu testende Pfad. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> enthält mindestens eines der in <see cref="M:System.IO.Path.GetInvalidPathChars" /> definierten ungültigen Zeichen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.PathSeparator">
      <summary>Ein plattformspezifisches Trennzeichen, das zur Trennung von Pfadzeichenfolgen in Umgebungsvariablen verwendet wird.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.VolumeSeparatorChar">
      <summary>Stellt ein plattformspezifisches Volumetrennzeichen bereit.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Net.WebUtility">
      <summary>Stellt Methoden für das Codieren und Decodieren von URLs beim Verarbeiten von Webanforderungen bereit. </summary>
    </member>
    <member name="M:System.Net.WebUtility.HtmlDecode(System.String)">
      <summary>Konvertiert eine Zeichenfolge, die für die HTTP-Übertragung HTML-codiert wurde, in eine decodierte Zeichenfolge.</summary>
      <returns>Eine decodierte Zeichenfolge.</returns>
      <param name="value">Die zu decodierende Zeichenfolge.</param>
    </member>
    <member name="M:System.Net.WebUtility.HtmlEncode(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine HTML-codierte Zeichenfolge.</summary>
      <returns>Eine codierte Zeichenfolge.</returns>
      <param name="value">Die zu codierende Zeichenfolge.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecode(System.String)">
      <summary>Konvertiert eine Zeichenfolge, die für die Übertragung in eine URL codiert wurde, in eine decodierte Zeichenfolge.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine decodierte Zeichenfolge.</returns>
      <param name="encodedValue">Eine zu decodierende URL-codierte Zeichenfolge.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Konvertiert ein codiertes Bytearray, das für die Übertragung in eine URL in ein codiertes Bytearray  codiert wurde.</summary>
      <returns>Gibt <see cref="T:System.Byte" /> zurück.Ein decodiertes <see cref="T:System.Byte" /> Array.</returns>
      <param name="encodedValue">Ein zu decodierende URL-codiertes <see cref="T:System.Byte" /> Array.</param>
      <param name="offset">Der Offset, in Bytes, vom Anfang des zu decodierenden <see cref="T:System.Byte" /> Arrays.</param>
      <param name="count">Die Anzahl, in Bytes, die vom <see cref="T:System.Byte" /> Array decodiert werden.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncode(System.String)">
      <summary>Konvertiert eine Textzeichenfolge in eine URL-codierte Zeichenfolge.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine URL-codierte Zeichenfolge.</returns>
      <param name="value">Der als URL zu codierende Text.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Konvertiert ein Bytearray in ein URL-codiertes URL-Bytearray.</summary>
      <returns>Gibt <see cref="T:System.Byte" /> zurück.Ein codiertes <see cref="T:System.Byte" /> array.</returns>
      <param name="value">Das mit URL zu codierende <see cref="T:System.Byte" /> Array.</param>
      <param name="offset">Der Offset, in Bytes, vom Anfang des zu codierenden <see cref="T:System.Byte" /> Arrays.</param>
      <param name="count">Die Anzahl, in Bytes, die vom <see cref="T:System.Byte" /> Array codiert werden.</param>
    </member>
    <member name="T:System.Runtime.Versioning.FrameworkName">
      <summary>Stellt den Namen einer .NET Framework-Version dar.</summary>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.Versioning.FrameworkName" />-Klasse aus einer Zeichenfolge, die Informationen zu einer Version von .NET Framework enthält.</summary>
      <param name="frameworkName">Eine Zeichenfolge, die .NET Framework-Versionsinformationen enthält.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="frameworkName" /> ist <see cref="F:System.String.Empty" />.- oder -<paramref name="frameworkName" /> enthält weniger als zwei oder mehr als drei Komponenten.- oder -<paramref name="frameworkName" /> enthält keine Haupt- und Nebenversionsnummer.- oder -<paramref name="frameworkName " />enthält keine gültige Versionsnummer.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="frameworkName" /> ist null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.Versioning.FrameworkName" />-Klasse aus einer Zeichenfolge und einem <see cref="T:System.Version" />-Objekt, mit denen eine .NET Framework-Version identifiziert wird.</summary>
      <param name="identifier">Eine Zeichenfolge, die eine .NET Framework-Version identifiziert. </param>
      <param name="version">Ein Objekt, das .NET Framework-Versionsinformationen enthält.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> ist <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> ist null.- oder -<paramref name="version" /> ist null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.Versioning.FrameworkName" />-Klasse aus einer Zeichenfolge, einem <see cref="T:System.Version" />-Objekt, das eine .NET Framework-Version identifiziert, und einem Profilnamen.</summary>
      <param name="identifier">Eine Zeichenfolge, die eine .NET Framework-Version identifiziert.</param>
      <param name="version">Ein Objekt, das .NET Framework-Versionsinformationen enthält.</param>
      <param name="profile">Ein Profilname.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> ist <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> ist null.- oder -<paramref name="version" /> ist null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Object)">
      <summary>Gibt einen Wert zurück, der angibt, ob diese <see cref="T:System.Runtime.Versioning.FrameworkName" />-Instanz die gleiche .NET Framework-Version wie ein angegebenes Objekt darstellt.</summary>
      <returns>true, wenn jede Komponente des aktuellen <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekts mit der entsprechenden Komponente von <paramref name="obj" /> übereinstimmt, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit der aktuellen Instanz verglichen werden soll.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Runtime.Versioning.FrameworkName)">
      <summary>Gibt einen Wert zurück, der angibt, ob diese <see cref="T:System.Runtime.Versioning.FrameworkName" />-Instanz die gleiche .NET Framework-Version wie eine angegebene <see cref="T:System.Runtime.Versioning.FrameworkName" />-Instanz darstellt.</summary>
      <returns>true, wenn jede Komponente des aktuellen <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekts mit der entsprechenden Komponente von <paramref name="other" /> übereinstimmt, andernfalls false.</returns>
      <param name="other">Das Objekt, das mit der aktuellen Instanz verglichen werden soll.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.FullName">
      <summary>Ruft den vollständigen Namen dieses <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekts ab.</summary>
      <returns>Der vollständige Name dieses <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekts.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.GetHashCode">
      <summary>Gibt den Hashcode für das <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekt zurück.</summary>
      <returns>Eine 32-Bit-Ganzzahl mit Vorzeichen, die den Hashcode für die Instanz darstellt.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Identifier">
      <summary>Ruft den Bezeichner dieses <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekts ab.</summary>
      <returns>Der Bezeichner dieses <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekts.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Equality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Gibt einen Wert zurück, der angibt, ob zwei <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekte die gleiche .NET Framework-Version darstellen.</summary>
      <returns>true, wenn der <paramref name="left" />-Parameter und der <paramref name="right" />-Parameter die gleiche .NET Framework-Version darstellen, andernfalls false.</returns>
      <param name="left">Das erste zu vergleichende Objekt.</param>
      <param name="right">Das zweite zu vergleichende Objekt.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Inequality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Gibt einen Wert zurück, der angibt, ob zwei <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekte unterschiedliche .NET Framework-Versionen darstellen.</summary>
      <returns>true, wenn der <paramref name="left" />-Parameter und der <paramref name="right" />-Parameter unterschiedliche .NET Framework-Versionen darstellen, andernfalls false.</returns>
      <param name="left">Das erste zu vergleichende Objekt.</param>
      <param name="right">Das zweite zu vergleichende Objekt.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Profile">
      <summary>Ruft den Profilnamen dieses <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekts ab.</summary>
      <returns>Der Profilname dieses <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekts.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.ToString">
      <summary>Gibt die Zeichenfolgendarstellung dieses <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekts zurück.</summary>
      <returns>Eine Zeichenfolge, die dieses <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekt darstellt.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Version">
      <summary>Ruft die Version dieses <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekts ab.</summary>
      <returns>Ein Objekt, das Versionsinformationen zu diesem <see cref="T:System.Runtime.Versioning.FrameworkName" />-Objekt enthält.</returns>
    </member>
  </members>
</doc>