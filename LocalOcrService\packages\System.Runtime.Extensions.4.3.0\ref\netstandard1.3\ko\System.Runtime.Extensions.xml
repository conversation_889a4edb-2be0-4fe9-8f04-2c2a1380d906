﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.BitConverter">
      <summary>기본 데이터 형식을 바이트의 배열로 변환하고, 바이트의 배열을 기본 데이터 형식으로 변환합니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.BitConverter.DoubleToInt64Bits(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자를 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 값에 해당하는 64비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Boolean)">
      <summary>지정된 부울 값을 바이트 배열로 반환합니다.</summary>
      <returns>길이가 1인 바이트 배열입니다.</returns>
      <param name="value">부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Char)">
      <summary>지정된 유니코드 문자 값을 바이트 배열로 반환합니다.</summary>
      <returns>길이가 2인 바이트 배열입니다.</returns>
      <param name="value">변환할 문자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 값을 바이트 배열로 반환합니다.</summary>
      <returns>길이가 8인 바이트 배열입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수 값을 바이트 배열로 반환합니다.</summary>
      <returns>길이가 2인 바이트 배열입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수 값을 바이트 배열로 반환합니다.</summary>
      <returns>길이가 4인 바이트 배열입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수 값을 바이트 배열로 반환합니다.</summary>
      <returns>길이가 8인 바이트 배열입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 값을 바이트 배열로 반환합니다.</summary>
      <returns>길이가 4인 바이트 배열입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수 값을 바이트 배열로 반환합니다.</summary>
      <returns>길이가 2인 바이트 배열입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수 값을 바이트 배열로 반환합니다.</summary>
      <returns>길이가 4인 바이트 배열입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수 값을 바이트 배열로 반환합니다.</summary>
      <returns>길이가 8인 바이트 배열입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.Int64BitsToDouble(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수를 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 수입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.BitConverter.IsLittleEndian">
      <summary>이 컴퓨터 아키텍처에서 데이터가 저장되는 바이트 순서("endianess")를 나타냅니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToBoolean(System.Byte[],System.Int32)">
      <summary>1바이트에서 변환된 부울 값을 바이트 배열의 지정된 위치에 반환합니다.</summary>
      <returns>
        <paramref name="value" />의 <paramref name="startIndex" />에 있는 바이트가 0이 아니면 true이고 그렇지 않으면 false입니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToChar(System.Byte[],System.Int32)">
      <summary>2바이트에서 변환된 유니코드 문자를 바이트 배열의 지정된 위치에 반환합니다.</summary>
      <returns>
        <paramref name="startIndex" />에서 시작하고 2바이트로 형성된 문자입니다.</returns>
      <param name="value">배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" />가 <paramref name="value" />의 길이에서 1을 뺀 값과 같은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToDouble(System.Byte[],System.Int32)">
      <summary>8바이트에서 변환된 배정밀도 부동 소수점 숫자를 바이트 배열의 지정된 위치에 반환합니다.</summary>
      <returns>
        <paramref name="startIndex" />에서 시작하고 8바이트로 형성된 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" />가 <paramref name="value" />의 길이에서 7을 뺀 값보다 크거나 같고 <paramref name="value" />의 길이에서 1을 뺀 값보다 작거나 같은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt16(System.Byte[],System.Int32)">
      <summary>2바이트에서 변환된 16비트 부호 있는 정수를 바이트 배열의 지정된 위치에 반환합니다.</summary>
      <returns>
        <paramref name="startIndex" />에서 시작하고 2바이트로 형성된 16비트 부호 있는 정수입니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" />가 <paramref name="value" />의 길이에서 1을 뺀 값과 같은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt32(System.Byte[],System.Int32)">
      <summary>4바이트에서 변환된 32비트 부호 있는 정수를 바이트 배열의 지정된 위치에 반환합니다.</summary>
      <returns>
        <paramref name="startIndex" />에서 시작하고 4바이트로 형성된 32비트 부호 있는 정수입니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" />가 <paramref name="value" />의 길이에서 3을 뺀 값보다 크거나 같고 <paramref name="value" />의 길이에서 1을 뺀 값보다 작거나 같은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt64(System.Byte[],System.Int32)">
      <summary>8바이트에서 변환된 64비트 부호 있는 정수를 바이트 배열의 지정된 위치에 반환합니다.</summary>
      <returns>
        <paramref name="startIndex" />에서 시작하고 8바이트로 형성된 64비트 부호 있는 정수입니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" />가 <paramref name="value" />의 길이에서 7을 뺀 값보다 크거나 같고 <paramref name="value" />의 길이에서 1을 뺀 값보다 작거나 같은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToSingle(System.Byte[],System.Int32)">
      <summary>4바이트에서 변환된 단정밀도 부동 소수점 숫자를 바이트 배열의 지정된 위치에 반환합니다.</summary>
      <returns>
        <paramref name="startIndex" />에서 시작하고 4바이트로 형성된 단정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" />가 <paramref name="value" />의 길이에서 3을 뺀 값보다 크거나 같고 <paramref name="value" />의 길이에서 1을 뺀 값보다 작거나 같은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[])">
      <summary>지정된 바이트 배열의 각 요소 숫자 값을 해당하는 16진수 문자열 표현으로 변환합니다.</summary>
      <returns>하이픈으로 구분된 16진수 쌍의 문자열이며 여기에서 각 쌍은 <paramref name="value" />;의 해당 요소(예: "7F-2C-4A-00")를 나타냅니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32)">
      <summary>지정된 바이트 하위 배열의 각 요소 숫자 값을 해당하는 16진수 문자열 표현으로 변환합니다.</summary>
      <returns>하이픈으로 구분된 16진수 쌍의 문자열이며 여기에서 각 쌍은 <paramref name="value" />;의 하위 배열에서 해당 요소(예: "7F-2C-4A-00")를 나타냅니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32,System.Int32)">
      <summary>지정된 바이트 하위 배열의 각 요소 숫자 값을 해당하는 16진수 문자열 표현으로 변환합니다.</summary>
      <returns>하이픈으로 구분된 16진수 쌍의 문자열이며 여기에서 각 쌍은 <paramref name="value" />;의 하위 배열에서 해당 요소(예: "7F-2C-4A-00")를 나타냅니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <param name="length">
        <paramref name="value" />의 배열 요소 중에서 변환할 배열 요소의 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 또는 <paramref name="length" />가 0보다 작은 경우또는<paramref name="startIndex" />가 0보다 크고 <paramref name="value" />의 길이보다 크거나 같은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" />와 <paramref name="length" />를 합하면 <paramref name="value" /> 내에 위치를 지정할 수 없는 경우. 즉, <paramref name="startIndex" /> 매개 변수가 <paramref name="value" />의 길이에서 <paramref name="length" /> 매개 변수를 뺀 값보다 큰 경우입니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt16(System.Byte[],System.Int32)">
      <summary>2바이트에서 변환된 16비트 부호 없는 정수를 바이트 배열의 지정된 위치에 반환합니다.</summary>
      <returns>
        <paramref name="startIndex" />에서 시작하고 2바이트로 형성된 16비트 부호 없는 정수입니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" />가 <paramref name="value" />의 길이에서 1을 뺀 값과 같은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt32(System.Byte[],System.Int32)">
      <summary>4바이트에서 변환된 32비트 부호 없는 정수를 바이트 배열의 지정된 위치에 반환합니다.</summary>
      <returns>
        <paramref name="startIndex" />에서 시작하고 4바이트로 형성된 32비트 부호 없는 정수입니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" />가 <paramref name="value" />의 길이에서 3을 뺀 값보다 크거나 같고 <paramref name="value" />의 길이에서 1을 뺀 값보다 작거나 같은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt64(System.Byte[],System.Int32)">
      <summary>8바이트에서 변환된 64비트 부호 없는 정수를 바이트 배열의 지정된 위치에 반환합니다.</summary>
      <returns>
        <paramref name="startIndex" />에서 시작하고 8바이트로 형성된 64비트 부호 없는 정수입니다.</returns>
      <param name="value">바이트 배열입니다. </param>
      <param name="startIndex">
        <paramref name="value" /> 내의 시작 위치입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" />가 <paramref name="value" />의 길이에서 7을 뺀 값보다 크거나 같고 <paramref name="value" />의 길이에서 1을 뺀 값보다 작거나 같은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 0보다 작거나 <paramref name="value" /> - 1의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Convert">
      <summary>기본 데이터 형식을 다른 데이터 형식으로 변환합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type)">
      <summary>값이 지정된 개체와 동일한 지정된 형식의 개체를 반환합니다.</summary>
      <returns>형식이 <paramref name="conversionType" />이고 값이 <paramref name="value" />와 동일한 개체입니다.또는<paramref name="value" />가 null이고 <paramref name="conversionType" />이 값 형식이 아니면 null 참조(Visual Basic의 경우 Nothing)입니다. </returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="conversionType">반환할 개체의 형식입니다. </param>
      <exception cref="T:System.InvalidCastException">이 변환은 지원되지 않습니다.  또는<paramref name="value" />가 null이고 <paramref name="conversionType" />이 값 형식인 경우또는<paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" />을를은 <paramref name="conversionType" />에 의해 인식되는 서식이 아닙니다.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <paramref name="conversionType" />의 범위를 벗어난 숫자를 나타내는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" />가 null인 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type,System.IFormatProvider)">
      <summary>지정된 개체와 동일한 값을 갖는 지정된 형식의 개체를 반환합니다.매개 변수에서 문화권별 서식 지정 정보를 제공합니다.</summary>
      <returns>형식이 <paramref name="conversionType" />이고 값이 <paramref name="value" />와 동일한 개체입니다.또는 <paramref name="value" />와 <paramref name="conversionType" />의 <see cref="T:System.Type" />이 같으면 <paramref name="value" />를 반환합니다.또는 <paramref name="value" />가 null이고 <paramref name="conversionType" />이 값 형식이 아니면 null 참조(Visual Basic의 경우 Nothing)입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="conversionType">반환할 개체의 형식입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.InvalidCastException">이 변환은 지원되지 않습니다. 또는<paramref name="value" />가 null이고 <paramref name="conversionType" />이 값 형식인 경우또는<paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 <paramref name="provider" />에서 인식되는 <paramref name="conversionType" />의 형식이 아닌 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <paramref name="conversionType" />의 범위를 벗어난 숫자를 나타내는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" />가 null인 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.TypeCode,System.IFormatProvider)">
      <summary>지정된 개체와 동일한 값을 갖는 지정된 형식의 개체를 반환합니다.매개 변수에서 문화권별 서식 지정 정보를 제공합니다.</summary>
      <returns>내부 형식이 <paramref name="typeCode" />이고 값이 <paramref name="value" />와 동일한 개체입니다.또는 <paramref name="value" />가 null이고 <paramref name="typeCode" />가 <see cref="F:System.TypeCode.Empty" />, <see cref="F:System.TypeCode.String" /> 또는 <see cref="F:System.TypeCode.Object" />이면 null 참조(Visual Basic의 경우 Nothing)입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="typeCode">반환할 개체의 형식입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.InvalidCastException">이 변환은 지원되지 않습니다.  또는<paramref name="value" />가 null이고 <paramref name="typeCode" />가 값 형식을 지정하는 경우또는<paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 <paramref name="provider" />에서 인식되는 <paramref name="typeCode" /> 형식에 대한 형식에 없는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <paramref name="typeCode" /> 형식의 범위를 벗어난 숫자를 나타내는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeCode" />이 잘못되었습니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64CharArray(System.Char[],System.Int32,System.Int32)">
      <summary>base-64 숫자의 이진 데이터를 해당하는 8비트 부호 없는 정수 배열로 인코딩하는 방법으로 유니코드 문자 배열의 하위 집합을 변환합니다.매개 변수에는 입력 배열의 하위 집합과 변환할 요소 수를 지정합니다.</summary>
      <returns>
        <paramref name="inArray" />의 <paramref name="offset" /> 위치에 있는 <paramref name="length" /> 요소에 해당하는 8비트 부호 없는 정수를 반환합니다.</returns>
      <param name="inArray">유니코드 문자 배열입니다. </param>
      <param name="offset">
        <paramref name="inArray" /> 내의 위치입니다. </param>
      <param name="length">
        <paramref name="inArray" />의 요소 중에서 변환할 요소의 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="length" />가 0 미만인 경우또는 <paramref name="offset" />과 <paramref name="length" />의 합이 <paramref name="inArray" /> 내에 있지 않은 위치를 나타내는 경우 </exception>
      <exception cref="T:System.FormatException">공백 문자를 제외한 <paramref name="inArray" />의 길이가 0이 아니거나 4의 배수가 아닌 경우 또는<paramref name="inArray" />의 형식이 유효하지 않은 경우<paramref name="inArray" />에는 기본 64 문자가 아닌 문자, 두 개 이상의 안쪽 여백 문자 또는 안쪽 여백 문자 중 공백 문자가 아닌 문자를 포함되어 있습니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64String(System.String)">
      <summary>base-64 숫자의 이진 데이터를 해당하는 8비트 부호 없는 정수 배열로 인코딩하는 방법으로 지정된 문자열을 변환합니다.</summary>
      <returns>
        <paramref name="s" />에 해당하는 8비트 부호 없는 정수 배열입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">공백 문자를 제외한 <paramref name="s" />의 길이가 0이 아니거나 4의 배수가 아닌 경우 또는<paramref name="s" />의 형식이 유효하지 않은 경우<paramref name="s" />에는 기본 64 문자가 아닌 문자, 두 개 이상의 패딩 문자 또는 패딩 문자 중 공백 문자가 아닌 문자를 포함되어 있습니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.GetTypeCode(System.Object)">
      <summary>지정된 개체의 <see cref="T:System.TypeCode" />를 반환합니다.</summary>
      <returns>
        <paramref name="value" />에 대해 <see cref="T:System.TypeCode" />를 반환하거나, <paramref name="value" />가 null이면 <see cref="F:System.TypeCode.Empty" />를 반환합니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64CharArray(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>8비트 부호 없는 정수 배열의 하위 집합을 base-64 숫자로 인코딩된 유니코드 문자 배열의 해당하는 하위 집합으로 변환합니다.매개 변수는 하위 집합을 입력 및 출력 배열의 오프셋으로 지정하고 변환할 입력 배열의 요소 수를 지정합니다.</summary>
      <returns>
        <paramref name="outArray" />의 바이트 수를 포함하는 32비트 부호 있는 정수를 반환합니다.</returns>
      <param name="inArray">8비트 부호 없는 정수로 구성된 입력 배열입니다. </param>
      <param name="offsetIn">
        <paramref name="inArray" /> 내의 위치입니다. </param>
      <param name="length">
        <paramref name="inArray" />의 요소 중에서 변환할 요소의 수입니다. </param>
      <param name="outArray">유니코드 문자로 구성된 출력 배열입니다. </param>
      <param name="offsetOut">
        <paramref name="outArray" /> 내의 위치입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> 또는 <paramref name="outArray" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offsetIn" />, <paramref name="offsetOut" /> 또는 <paramref name="length" />가 음수인 경우또는 <paramref name="offsetIn" />과 <paramref name="length" />의 합이 <paramref name="inArray" />의 길이보다 큰 경우또는 <paramref name="offsetOut" />과 반환할 요소 수의 합이 <paramref name="outArray" />의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[])">
      <summary>8비트 부호 없는 정수로 구성된 배열을 base-64 숫자로 인코딩된 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="inArray" />의 내용에 대한 base 64 숫자의 문자열 표현입니다.</returns>
      <param name="inArray">8비트 부호 없는 정수로 구성된 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" />가 null인 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[],System.Int32,System.Int32)">
      <summary>8비트 부호 없는 정수로 구성된 배열의 하위 집합을 base-64 숫자로 인코딩된 해당하는 문자열 표현으로 변환합니다.매개 변수는 하위 집합을 입력 배열의 오프셋으로 지정하고 변환할 배열의 요소 수를 지정합니다.</summary>
      <returns>
        <paramref name="offset" /> 위치에서 시작하는 <paramref name="inArray" />의 <paramref name="length" /> 요소에 대한 base 64 문자열 표현입니다.</returns>
      <param name="inArray">8비트 부호 없는 정수로 구성된 배열입니다. </param>
      <param name="offset">
        <paramref name="inArray" />에 있는 오프셋입니다. </param>
      <param name="length">
        <paramref name="inArray" />의 요소 중에서 변환할 요소의 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="length" />가 음수인 경우또는 <paramref name="offset" />과 <paramref name="length" />의 합이 <paramref name="inArray" />의 길이보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Boolean)">
      <summary>지정된 부울 값을 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">반환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수의 값을 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수의 값을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수의 값을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수의 값을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object)">
      <summary>지정된 개체의 값을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>true 또는 false이며, <paramref name="value" />의 내부 형식에 대해 <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> 메서드를 호출하여 반환된 값을 반영합니다.<paramref name="value" />가 null이면 메서드에서 false를 반환합니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />는 <see cref="F:System.Boolean.TrueString" /> 또는 <see cref="F:System.Boolean.FalseString" />과 동등하지 않은 문자열입니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우또는<paramref name="value" />에서 <see cref="T:System.Boolean" />로의 변환이 지원되지 않습니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>true 또는 false이며, <paramref name="value" />의 내부 형식에 대해 <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> 메서드를 호출하여 반환된 값을 반영합니다.<paramref name="value" />가 null이면 메서드에서 false를 반환합니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />는 <see cref="F:System.Boolean.TrueString" /> 또는 <see cref="F:System.Boolean.FalseString" />과 동등하지 않은 문자열입니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우또는<paramref name="value" />에서 <see cref="T:System.Boolean" />로의 변환이 지원되지 않습니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수의 값을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String)">
      <summary>논리 값의 지정된 문자열 표현을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 <see cref="F:System.Boolean.TrueString" />과 같으면 true를 반환하고, <paramref name="value" />가 <see cref="F:System.Boolean.FalseString" />과 같거나 null이면 false를 반환합니다.</returns>
      <param name="value">
        <see cref="F:System.Boolean.TrueString" /> 또는 <see cref="F:System.Boolean.FalseString" /> 값이 들어 있는 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 <see cref="F:System.Boolean.TrueString" /> 또는 <see cref="F:System.Boolean.FalseString" />과 같지 않은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여 논리 값의 지정된 문자열 표현을 해당하는 부울 값으로 변환합니다.</summary>
      <returns>true if <paramref name="value" /> equals <see cref="F:System.Boolean.TrueString" />, or false if <paramref name="value" /> equals <see cref="F:System.Boolean.FalseString" /> or null.</returns>
      <param name="value">
        <see cref="F:System.Boolean.TrueString" /> 또는 <see cref="F:System.Boolean.FalseString" /> 값이 들어 있는 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다.이 매개 변수는 무시됩니다.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 <see cref="F:System.Boolean.TrueString" /> 또는 <see cref="F:System.Boolean.FalseString" />과 같지 않은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수의 값을 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수의 값을 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수의 값을 부울 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 0이 아니면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">반환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Char)">
      <summary>지정된 유니코드 문자의 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 유니코드 문자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />는 <see cref="F:System.Byte.MaxValue" />보다 큰 수를 나타냅니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 8비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MaxValue" />보다 크거나 <see cref="F:System.Byte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 8비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MaxValue" />보다 크거나 <see cref="F:System.Byte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수의 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MinValue" />보다 작거나 <see cref="F:System.Byte.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수의 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MinValue" />보다 작거나 <see cref="F:System.Byte.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수의 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MinValue" />보다 작거나 <see cref="F:System.Byte.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object)">
      <summary>지정된 개체의 값을 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 <see cref="T:System.Byte" /> 값의 속성 서식에 없는 경우</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" />을 구현하지 않는 경우 또는<paramref name="value" />는 <see cref="T:System.Byte" />로의 형식 변환이 지원되지 않습니다.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MinValue" />보다 작거나 <see cref="F:System.Byte.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 <see cref="T:System.Byte" /> 값의 속성 서식에 없는 경우</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" />을 구현하지 않는 경우 또는<paramref name="value" />는 <see cref="T:System.Byte" />로의 형식 변환이 지원되지 않습니다.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MinValue" />보다 작거나 <see cref="F:System.Byte.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수의 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수입니다.</returns>
      <param name="value">변환될 8비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 8비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">단정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MaxValue" />보다 크거나 <see cref="F:System.Byte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MinValue" />보다 작거나 <see cref="F:System.Byte.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MinValue" />보다 작거나 <see cref="F:System.Byte.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.Int32)">
      <summary>지정된 기수로 나타낸 숫자의 문자열 표현을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 8비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="fromBase">
        <paramref name="value" />에 지정된 숫자의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" />가 2, 8, 10 또는16이 아닌 경우 또는기수가 10이 아닌 부호 없는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" />가 <see cref="F:System.String.Empty" />인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="fromBase" />에서 지정한 진수법에 해당하지 않는 문자가 <paramref name="value" />에 들어 있는 경우.<paramref name="value" />의 첫 번째 문자가 잘못된 경우 변환할 숫자가 없다는 예외 메시지가 표시되고, 그렇지 않은 경우 <paramref name="value" />에 잘못된 후행 문자가 들어 있다는 메시지가 표시됩니다.</exception>
      <exception cref="T:System.OverflowException">기수가 10인 부호 없는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우또는<paramref name="value" />가 <see cref="F:System.Byte.MinValue" />보다 작거나 <see cref="F:System.Byte.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수의 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수의 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수의 값을 해당하는 8비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Byte.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수의 값을 해당하는 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 유니코드 문자입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수의 값을 해당하는 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 유니코드 문자입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Char.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수의 값을 해당하는 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 유니코드 문자입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Char.MinValue" />보다 작거나 <see cref="F:System.Char.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수의 값을 해당하는 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 유니코드 문자입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Char.MinValue" />보다 작거나 <see cref="F:System.Char.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object)">
      <summary>지정된 개체의 값을 유니코드 문자로 변환합니다.</summary>
      <returns>value에 해당하는 유니코드 문자이거나, <paramref name="value" />가 null이면 <see cref="F:System.Char.MinValue" />입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />는 null 문자열입니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우또는<paramref name="value" />에서 <see cref="T:System.Char" />로의 변환이 지원되지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Char.MinValue" />보다 작거나 <see cref="F:System.Char.MaxValue" />보다 큰 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object,System.IFormatProvider)">
      <summary>문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 해당하는 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 유니코드 문자이거나, <paramref name="value" />가 null이면 <see cref="F:System.Char.MinValue" />입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />는 null 문자열입니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는<paramref name="value" />에서 <see cref="T:System.Char" />로의 변환이 지원되지 않습니다.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Char.MinValue" />보다 작거나 <see cref="F:System.Char.MaxValue" />보다 큰 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수의 값을 해당하는 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 유니코드 문자입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Char.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String)">
      <summary>지정된 문자열의 첫째 문자를 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 첫째 문자인 동시에 유일한 문자에 해당하는 유니코드 문자입니다.</returns>
      <param name="value">길이가 1인 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" />의 길이가 1이 아닌 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String,System.IFormatProvider)">
      <summary>문화권별 서식 지정 정보를 사용하여, 지정된 문자열의 첫째 문자를 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 첫째 문자인 동시에 유일한 문자에 해당하는 유니코드 문자입니다.</returns>
      <param name="value">길이가 1이거나 null인 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다.이 매개 변수는 무시됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" />의 길이가 1이 아닌 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수의 값을 해당하는 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 유니코드 문자입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수의 값을 해당하는 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 유니코드 문자입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Char.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수의 값을 해당하는 유니코드 문자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 유니코드 문자입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Char.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object)">
      <summary>지정된 개체의 값을 <see cref="T:System.DateTime" /> 개체로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 값에 해당하는 날짜와 시간이거나, <paramref name="value" />가 null이면 <see cref="F:System.DateTime.MinValue" />에 해당하는 날짜와 시간입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 유효한 날짜 및 시간 값이 아닙니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 <see cref="T:System.DateTime" /> 개체로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 값에 해당하는 날짜와 시간이거나, <paramref name="value" />가 null이면 <see cref="F:System.DateTime.MinValue" />에 해당하는 날짜와 시간입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 유효한 날짜 및 시간 값이 아닙니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String)">
      <summary>날짜와 시간의 지정된 문자열 표현을 해당하는 날짜와 시간 값으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 값에 해당하는 날짜와 시간이거나, <paramref name="value" />가 null이면 <see cref="F:System.DateTime.MinValue" />에 해당하는 날짜와 시간입니다.</returns>
      <param name="value">날짜 및 시간의 문자열 표현입니다.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 올바른 형식의 날짜 및 시간 문자열이 아닌 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 날짜와 시간으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 값에 해당하는 날짜와 시간이거나, <paramref name="value" />가 null이면 <see cref="F:System.DateTime.MinValue" />에 해당하는 날짜와 시간입니다.</returns>
      <param name="value">변환할 날짜 및 시간이 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 올바른 형식의 날짜 및 시간 문자열이 아닌 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Decimal)">
      <summary>지정된 10진수를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">10진수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수입니다. </returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Decimal.MaxValue" />보다 크거나 <see cref="F:System.Decimal.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object)">
      <summary>지정된 개체 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <see cref="T:System.Decimal" /> 형식을 위한 적절한 서식에 <paramref name="value" />가 없는 경우</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Decimal.MinValue" />보다 작거나 <see cref="F:System.Decimal.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <see cref="T:System.Decimal" /> 형식을 위한 적절한 서식에 <paramref name="value" />가 없는 경우</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우또는변환이 지원되지 않는 경우 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Decimal.MinValue" />보다 작거나 <see cref="F:System.Decimal.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수입니다. </returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Decimal.MaxValue" />보다 크거나 <see cref="F:System.Decimal.MinValue" />보다 작은 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 10진수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />의 형식이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Decimal.MinValue" />보다 작거나 <see cref="F:System.Decimal.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 10진수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />의 형식이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Decimal.MinValue" />보다 작거나 <see cref="F:System.Decimal.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수 값을 해당하는 10진수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 10진수입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">반환할 배정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />와 값이 같은 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object)">
      <summary>지정된 개체의 값을 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 숫자이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format for a <see cref="T:System.Double" /> type.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Double.MinValue" />보다 작거나 <see cref="F:System.Double.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 숫자이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format for a <see cref="T:System.Double" /> type.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Double.MinValue" />보다 작거나 <see cref="F:System.Double.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">단정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 배정밀도 부동 소수점 숫자이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />의 형식이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Double.MinValue" />보다 작거나 <see cref="F:System.Double.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 배정밀도 부동 소수점 숫자이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />의 형식이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Double.MinValue" />보다 작거나 <see cref="F:System.Double.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수 값을 해당하는 배정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수의 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Char)">
      <summary>지정된 유니코드 문자의 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 있는 정수입니다. </returns>
      <param name="value">변환할 유니코드 문자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 16비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MaxValue" />보다 크거나 <see cref="F:System.Int16.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 16비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MaxValue" />보다 크거나 <see cref="F:System.Int16.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">반환할 16비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수의 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 있는 정수를 반환합니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MaxValue" />보다 크거나 <see cref="F:System.Int16.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수의 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MaxValue" />보다 크거나 <see cref="F:System.Int16.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object)">
      <summary>지정된 개체의 값을 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <see cref="T:System.Int16" /> 형식을 위한 적절한 서식에 <paramref name="value" />가 없는 경우</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MinValue" />보다 작거나 <see cref="F:System.Int16.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <see cref="T:System.Int16" /> 형식을 위한 적절한 서식에 <paramref name="value" />가 없는 경우</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" />을 구현하지 않는 경우 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MinValue" />보다 작거나 <see cref="F:System.Int16.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수의 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 16비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MaxValue" />보다 크거나 <see cref="F:System.Int16.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 16비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MinValue" />보다 작거나 <see cref="F:System.Int16.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 16비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MinValue" />보다 작거나 <see cref="F:System.Int16.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.Int32)">
      <summary>지정된 기수로 나타낸 숫자에 대한 문자열 표현을 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 16비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="fromBase">
        <paramref name="value" />에 지정된 숫자의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" />가 2, 8, 10 또는16이 아닌 경우 또는기수가 10이 아닌 부호 있는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" />가 <see cref="F:System.String.Empty" />인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="fromBase" />에서 지정한 진수법에 해당하지 않는 문자가 <paramref name="value" />에 들어 있는 경우.<paramref name="value" />의 첫 번째 문자가 잘못된 경우 변환할 숫자가 없다는 예외 메시지가 표시되고, 그렇지 않은 경우 <paramref name="value" />에 잘못된 후행 문자가 들어 있다는 메시지가 표시됩니다.</exception>
      <exception cref="T:System.OverflowException">기수가 10이 아닌 부호 있는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우또는<paramref name="value" />가 <see cref="F:System.Int16.MinValue" />보다 작거나 <see cref="F:System.Int16.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수의 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수의 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수의 값을 해당하는 16비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수의 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Char)">
      <summary>지정된 유니코드 문자의 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 유니코드 문자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 32비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MaxValue" />보다 크거나 <see cref="F:System.Int32.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 32비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MaxValue" />보다 크거나 <see cref="F:System.Int32.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수의 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">반환할 32비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수의 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MaxValue" />보다 크거나 <see cref="F:System.Int32.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object)">
      <summary>지정된 개체의 값을 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MinValue" />보다 작거나 <see cref="F:System.Int32.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" />을 구현하지 않는 경우 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MinValue" />보다 작거나 <see cref="F:System.Int32.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수의 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 32비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MaxValue" />보다 크거나 <see cref="F:System.Int32.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 32비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MinValue" />보다 작거나 <see cref="F:System.Int32.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 32비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MinValue" />보다 작거나 <see cref="F:System.Int32.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.Int32)">
      <summary>지정된 기수로 나타낸 숫자에 대한 문자열 표현을 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 32비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="fromBase">
        <paramref name="value" />에 지정된 숫자의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" />가 2, 8, 10 또는16이 아닌 경우 또는기수가 10이 아닌 부호 있는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" />가 <see cref="F:System.String.Empty" />인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="fromBase" />에서 지정한 진수법에 해당하지 않는 문자가 <paramref name="value" />에 들어 있는 경우.<paramref name="value" />의 첫 번째 문자가 잘못된 경우 변환할 숫자가 없다는 예외 메시지가 표시되고, 그렇지 않은 경우 <paramref name="value" />에 잘못된 후행 문자가 들어 있다는 메시지가 표시됩니다.</exception>
      <exception cref="T:System.OverflowException">기수가 10이 아닌 부호 있는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우또는<paramref name="value" />가 <see cref="F:System.Int32.MinValue" />보다 작거나 <see cref="F:System.Int32.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수의 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수의 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수의 값을 해당하는 32비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수의 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Char)">
      <summary>지정된 유니코드 문자의 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 유니코드 문자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 64비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int64.MaxValue" />보다 크거나 <see cref="F:System.Int64.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 64비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int64.MaxValue" />보다 크거나 <see cref="F:System.Int64.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수의 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수의 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">64비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object)">
      <summary>지정된 개체의 값을 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int64.MinValue" />보다 작거나 <see cref="F:System.Int64.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우또는변환이 지원되지 않는 경우 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int64.MinValue" />보다 작거나 <see cref="F:System.Int64.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수의 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 64비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int64.MaxValue" />보다 크거나 <see cref="F:System.Int64.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 64비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int64.MinValue" />보다 작거나 <see cref="F:System.Int64.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 64비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int64.MinValue" />보다 작거나 <see cref="F:System.Int64.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.Int32)">
      <summary>지정된 기수로 나타낸 숫자에 대한 문자열 표현을 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 64비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="fromBase">
        <paramref name="value" />에 지정된 숫자의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" />가 2, 8, 10 또는16이 아닌 경우 또는기수가 10이 아닌 부호 있는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" />가 <see cref="F:System.String.Empty" />인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="fromBase" />에서 지정한 진수법에 해당하지 않는 문자가 <paramref name="value" />에 들어 있는 경우.<paramref name="value" />의 첫 번째 문자가 잘못된 경우 변환할 숫자가 없다는 예외 메시지가 표시되고, 그렇지 않은 경우 <paramref name="value" />에 잘못된 후행 문자가 들어 있다는 메시지가 표시됩니다.</exception>
      <exception cref="T:System.OverflowException">기수가 10이 아닌 부호 있는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우또는<paramref name="value" />가 <see cref="F:System.Int64.MinValue" />보다 작거나 <see cref="F:System.Int64.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수의 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수의 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수의 값을 해당하는 64비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int64.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수의 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Char)">
      <summary>지정된 유니코드 문자의 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 유니코드 문자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 8비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 크거나 <see cref="F:System.SByte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 8비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 크거나 <see cref="F:System.SByte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 8-bit signed integer.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 크거나 <see cref="F:System.SByte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수의 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 크거나 <see cref="F:System.SByte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수의 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 크거나 <see cref="F:System.SByte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object)">
      <summary>지정된 개체의 값을 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MinValue" />보다 작거나 <see cref="F:System.SByte.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MinValue" />보다 작거나 <see cref="F:System.SByte.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">반환할 8비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>가장 가까운 8비트 부호 있는 정수로 반올림된 <paramref name="value" />를 반환합니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 크거나 <see cref="F:System.SByte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 8비트 부호 있는 정수이거나, value가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MinValue" />보다 작거나 <see cref="F:System.SByte.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MinValue" />보다 작거나 <see cref="F:System.SByte.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.Int32)">
      <summary>지정된 기수로 나타낸 숫자에 대한 문자열 표현을 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 8비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="fromBase">
        <paramref name="value" />에 지정된 숫자의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" />가 2, 8, 10 또는16이 아닌 경우 또는기수가 10이 아닌 부호 있는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" />가 <see cref="F:System.String.Empty" />인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="fromBase" />에서 지정한 진수법에 해당하지 않는 문자가 <paramref name="value" />에 들어 있는 경우.<paramref name="value" />의 첫 번째 문자가 잘못된 경우 변환할 숫자가 없다는 예외 메시지가 표시되고, 그렇지 않은 경우 <paramref name="value" />에 잘못된 후행 문자가 들어 있다는 메시지가 표시됩니다.</exception>
      <exception cref="T:System.OverflowException">기수가 10이 아닌 부호 있는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우또는<paramref name="value" />가 <see cref="F:System.SByte.MinValue" />보다 작거나 <see cref="F:System.SByte.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수의 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수의 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 크거나 <see cref="F:System.SByte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수의 값을 해당하는 8비트 부호 있는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MaxValue" />보다 크거나 <see cref="F:System.SByte.MinValue" />보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자입니다.<paramref name="value" />는 반올림됩니다.예를 들어, 소수점 두 자리로 반올림하면 2.345는 2.34가 되고 2.355는 2.36이 됩니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자입니다.<paramref name="value" />는 반올림됩니다.예를 들어, 소수점 두 자리로 반올림하면 2.345는 2.34가 되고 2.355는 2.36이 됩니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object)">
      <summary>지정된 개체의 값을 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Single.MinValue" />보다 작거나 <see cref="F:System.Single.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" />을 구현하지 않는 경우 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Single.MinValue" />보다 작거나 <see cref="F:System.Single.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 8비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">반환할 단정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 단정밀도 부동 소수점 숫자이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />의 형식이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Single.MinValue" />보다 작거나 <see cref="F:System.Single.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 단정밀도 부동 소수점 숫자이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />의 형식이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Single.MinValue" />보다 작거나 <see cref="F:System.Single.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수 값을 해당하는 단정밀도 부동 소수점 숫자로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 단정밀도 부동 소수점 숫자입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean,System.IFormatProvider)">
      <summary>지정된 부울 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <param name="provider">개체의 인스턴스입니다.이 매개 변수는 무시됩니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 8비트 부호 없는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.Int32)">
      <summary>8비트 부호 없는 정수의 값을 특정 기수의 해당 문자열 표현으로 변환합니다.</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <param name="toBase">반환 값의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" />가 2, 8, 10 또는16이 아닌 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char)">
      <summary>지정된 유니코드 문자의 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 유니코드 문자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 유니코드 문자의 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 유니코드 문자입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다.이 매개 변수는 무시됩니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime)">
      <summary>지정된 <see cref="T:System.DateTime" />의 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 날짜 및 시간 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 <see cref="T:System.DateTime" />의 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 날짜 및 시간 값입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal)">
      <summary>지정된 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 10진수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double,System.IFormatProvider)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 16비트 부호 있는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.Int32)">
      <summary>16비트 부호 있는 정수 값을 지정된 기수의 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <param name="toBase">반환 값의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" />가 2, 8, 10 또는16이 아닌 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 32비트 부호 있는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.Int32)">
      <summary>32비트 부호 있는 정수 값을 지정된 기수의 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <param name="toBase">반환 값의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" />가 2, 8, 10 또는16이 아닌 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 64비트 부호 있는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.Int32)">
      <summary>64비트 부호 있는 정수 값을 지정된 기수의 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <param name="toBase">반환 값의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" />가 2, 8, 10 또는16이 아닌 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object)">
      <summary>지정된 개체의 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 값이 null인 개체인 경우 문자열 표시가 <paramref name="value" /> 또는 <see cref="F:System.String.Empty" />입니다.<paramref name="value" />가 null이면 메서드에서 null를 반환합니다.</returns>
      <param name="value">변환할 값을 제공하는 개체나 null입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 값이 null인 개체인 경우 문자열 표시가 <paramref name="value" /> 또는 <see cref="F:System.String.Empty" />입니다.<paramref name="value" />가 null이면 메서드에서 null를 반환합니다.</returns>
      <param name="value">변환할 값을 제공하는 개체나 null입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 8비트 부호 있는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 단정밀도 부동 소수점 숫자 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 16비트 부호 없는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 32비트 부호 없는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 64비트 부호 없는 정수 값을 해당하는 문자열 표현으로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 문자열 표현입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수의 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Char)">
      <summary>지정된 유니코드 문자의 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 없는 정수를 반환합니다.</returns>
      <param name="value">변환할 유니코드 문자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 16비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 16비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수의 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수의 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수의 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object)">
      <summary>지정된 개체의 값을 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt16.MinValue" />보다 작거나 <see cref="F:System.UInt16.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt16.MinValue" />보다 작거나 <see cref="F:System.UInt16.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수의 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 16비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 16비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt16.MinValue" />보다 작거나 <see cref="F:System.UInt16.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 16비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt16.MinValue" />보다 작거나 <see cref="F:System.UInt16.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.Int32)">
      <summary>지정된 기수로 나타낸 숫자의 문자열 표현을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 16비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="fromBase">
        <paramref name="value" />에 지정된 숫자의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" />가 2, 8, 10 또는16이 아닌 경우 또는기수가 10이 아닌 부호 없는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" />가 <see cref="F:System.String.Empty" />인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="fromBase" />에서 지정한 진수법에 해당하지 않는 문자가 <paramref name="value" />에 들어 있는 경우.<paramref name="value" />의 첫 번째 문자가 잘못된 경우 변환할 숫자가 없다는 예외 메시지가 표시되고, 그렇지 않은 경우 <paramref name="value" />에 잘못된 후행 문자가 들어 있다는 메시지가 표시됩니다.</exception>
      <exception cref="T:System.OverflowException">기수가 10이 아닌 부호 없는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우또는<paramref name="value" />가 <see cref="F:System.UInt16.MinValue" />보다 작거나 <see cref="F:System.UInt16.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">반환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수의 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수의 값을 해당하는 16비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 16비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt16.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수의 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Char)">
      <summary>지정된 유니코드 문자의 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 유니코드 문자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 32비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt32.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 32비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt32.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수의 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수의 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수의 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt32.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object)">
      <summary>지정된 개체의 값을 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt32.MinValue" />보다 작거나 <see cref="F:System.UInt32.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt32.MinValue" />보다 작거나 <see cref="F:System.UInt32.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수의 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 32비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt32.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 32비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt32.MinValue" />보다 작거나 <see cref="F:System.UInt32.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 32비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt32.MinValue" />보다 작거나 <see cref="F:System.UInt32.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.Int32)">
      <summary>지정된 기수로 나타낸 숫자의 문자열 표현을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 32비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="fromBase">
        <paramref name="value" />에 지정된 숫자의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" />가 2, 8, 10 또는16이 아닌 경우 또는기수가 10이 아닌 부호 없는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" />가 <see cref="F:System.String.Empty" />인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="fromBase" />에서 지정한 진수법에 해당하지 않는 문자가 <paramref name="value" />에 들어 있는 경우.<paramref name="value" />의 첫 번째 문자가 잘못된 경우 변환할 숫자가 없다는 예외 메시지가 표시되고, 그렇지 않은 경우 <paramref name="value" />에 잘못된 후행 문자가 들어 있다는 메시지가 표시됩니다.</exception>
      <exception cref="T:System.OverflowException">기수가 10이 아닌 부호 없는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우또는<paramref name="value" />가 <see cref="F:System.UInt32.MinValue" />보다 작거나 <see cref="F:System.UInt32.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수의 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt32)">
      <summary>지정된 32비트 부호 없는 정수를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">반환할 32비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수의 값을 해당하는 32비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 32비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 없는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt32.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Boolean)">
      <summary>지정된 부울 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />가 true이면 숫자 1을 반환하고, 그렇지 않으면 0을 반환합니다.</returns>
      <param name="value">변환할 부울 값입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Byte)">
      <summary>지정된 8비트 부호 없는 정수의 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 있는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Char)">
      <summary>지정된 유니코드 문자의 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 유니코드 문자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Decimal)">
      <summary>지정된 10진수 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 64비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 10진수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt64.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 64비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 배정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt64.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int16)">
      <summary>지정된 16비트 부호 있는 정수의 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int32)">
      <summary>지정된 32비트 부호 있는 정수의 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int64)">
      <summary>지정된 64비트 부호 있는 정수의 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 64비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object)">
      <summary>지정된 개체의 값을 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체나 null입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt64.MinValue" />보다 작거나 <see cref="F:System.UInt64.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 지정된 개체의 값을 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> 인터페이스를 구현하는 개체 </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />가 적절한 형식에 있지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" />가 <see cref="T:System.IConvertible" /> 인터페이스를 구현하지 않는 경우 또는변환이 지원되지 않는 경우</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt64.MinValue" />보다 작거나 <see cref="F:System.UInt64.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.SByte)">
      <summary>지정된 8비트 부호 있는 정수의 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 8비트 부호 있는 정수입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Single)">
      <summary>지정된 단정밀도 부동 소수점 숫자 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>가장 가까운 64비트 부호 없는 정수로 반올림된 <paramref name="value" />입니다.<paramref name="value" />가 두 정수 사이의 값이면 짝수 값을 반환합니다. 예를 들어, 4.5는 4로 변환되고 5.5는 6으로 변환됩니다.</returns>
      <param name="value">변환할 단정밀도 부동 소수점 숫자입니다. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 0보다 작거나 <see cref="F:System.UInt64.MaxValue" />보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String)">
      <summary>숫자의 지정된 문자열 표현을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 64비트 부호 있는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt64.MinValue" />보다 작거나 <see cref="F:System.UInt64.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.IFormatProvider)">
      <summary>지정된 문화권별 서식 지정 정보를 사용하여, 숫자의 지정된 문자열 표현을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 64비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="provider">문화권별 서식 지정 정보를 제공하는 개체입니다. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />에서는 자릿수(0부터 9까지) 시퀀스에 따라 선택 신호를 구성하지 않습니다. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.UInt64.MinValue" />보다 작거나 <see cref="F:System.UInt64.MaxValue" />보다 큰 숫자를 나타내는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.Int32)">
      <summary>지정된 기수로 나타낸 숫자의 문자열 표현을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />의 숫자에 해당하는 64비트 부호 없는 정수이거나, <paramref name="value" />가 null이면 0입니다.</returns>
      <param name="value">변환할 숫자가 포함된 문자열입니다. </param>
      <param name="fromBase">
        <paramref name="value" />에 지정된 숫자의 기수로서 2, 8, 10 또는 16이어야 합니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" />가 2, 8, 10 또는16이 아닌 경우 또는기수가 10이 아닌 부호 없는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" />가 <see cref="F:System.String.Empty" />인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="fromBase" />에서 지정한 진수법에 해당하지 않는 문자가 <paramref name="value" />에 들어 있는 경우.<paramref name="value" />의 첫 번째 문자가 잘못된 경우 변환할 숫자가 없다는 예외 메시지가 표시되고, 그렇지 않은 경우 <paramref name="value" />에 잘못된 후행 문자가 들어 있다는 메시지가 표시됩니다.</exception>
      <exception cref="T:System.OverflowException">기수가 10이 아닌 부호 없는 숫자를 나타내는 <paramref name="value" />의 접두사가 음수 기호인 경우또는<paramref name="value" />가 <see cref="F:System.UInt64.MinValue" />보다 작거나 <see cref="F:System.UInt64.MaxValue" />보다 큰 숫자를 나타내는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt16)">
      <summary>지정된 16비트 부호 없는 정수의 값을 해당하는 64비트 부호 없는 정수로 변환합니다.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 16비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 64-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />에 해당하는 64비트 부호 없는 정수입니다.</returns>
      <param name="value">변환할 32비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt64)">
      <summary>지정된 64비트 부호 없는 정수를 실제 변환 작업 없이 반환합니다.</summary>
      <returns>
        <paramref name="value" />를 변경하지 않고 반환합니다.</returns>
      <param name="value">반환할 64비트 부호 없는 정수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Environment">
      <summary>현재 환경 및 플랫폼에 대한 정보 및 조작 방법을 제공합니다.이 클래스는 상속될 수 없습니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.CurrentManagedThreadId">
      <summary>현재 관리되는 스레드의 고유 식별자를 가져옵니다.</summary>
      <returns>이 관리되는 스레드의 고유 식별자를 나타내는 정수입니다.</returns>
    </member>
    <member name="M:System.Environment.ExpandEnvironmentVariables(System.String)">
      <summary>지정된 문자열에 있는 각 환경 변수 이름을 해당 변수의 값에 해당하는 문자열로 바꾼 다음 결과 문자열을 반환합니다.</summary>
      <returns>각 환경 변수가 해당 값으로 바뀌는 문자열입니다.</returns>
      <param name="name">0개 이상의 환경 변수 이름이 포함된 문자열입니다.각 환경 변수는 백분율 기호(%)로 묶어야 합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.FailFast(System.String)">
      <summary>Windows 응용 프로그램 이벤트 로그에 메시지를 쓴 후 프로세스를 즉시 종료한 다음 해당 메시지를 Microsoft로 보내는 오류 보고에 포함시킵니다.</summary>
      <param name="message">프로세스가 종료된 이유를 설명하는 메시지이거나, 설명이 제공되지 않는 경우 null입니다.</param>
    </member>
    <member name="M:System.Environment.FailFast(System.String,System.Exception)">
      <summary>Windows 응용 프로그램 이벤트 로그에 메시지를 쓴 후 프로세스를 즉시 종료한 다음 해당 메시지와 예외 정보를 Microsoft로 보내는 오류 보고에 포함시킵니다.</summary>
      <param name="message">프로세스가 종료된 이유를 설명하는 메시지이거나, 설명이 제공되지 않는 경우 null입니다.</param>
      <param name="exception">종료의 원인이 된 오류를 나타내는 예외입니다.일반적으로 catch 블록의 예외입니다.</param>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariable(System.String)">
      <summary>현재 프로세스에서 환경 변수의 값을 검색합니다. </summary>
      <returns>
        <paramref name="variable" />로 지정한 환경 변수의 값이 반환되거나, 환경 변수를 찾을 수 없는 경우 null이 반환됩니다.</returns>
      <param name="variable">환경 변수의 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariables">
      <summary>현재 프로세스에서 모든 환경 변수 이름과 해당 값을 검색합니다.</summary>
      <returns>모든 환경 변수 이름과 해당 값을 포함하는 사전이 반환되고, 환경 변수를 찾을 수 없는 경우 빈 사전이 반환됩니다.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <exception cref="T:System.OutOfMemoryException">The buffer is out of memory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.HasShutdownStarted">
      <summary>현재 응용 프로그램 도메인이 언로드되고 있는지 또는 CLR(공용 언어 런타임)이 종료되고 있는지를 나타내는 값을 가져옵니다. </summary>
      <returns>현재 응용 프로그램 도메인이 언로드되거나 CLR이 종료되는 경우 true이고, 그렇지 않으면 false.입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.NewLine">
      <summary>이 환경에 대해 정의된 줄 바꿈 문자열을 가져옵니다.</summary>
      <returns>Unix 이외의 플랫폼의 경우 "\r\n"을 포함하는 문자열이거나 Unix 플랫폼의 경우 "\n"을 포함하는 문자열입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.ProcessorCount">
      <summary>현재 컴퓨터의 프로세서 수를 가져옵니다.</summary>
      <returns>현재 컴퓨터의 프로세서 수를 지정하는 32비트 부호 있는 정수입니다.기본값은 없습니다.현재 컴퓨터에 여러 프로세서 그룹이 포함되어 있으면 이 속성을 통해 CLR(공용 언어 런타임)에서 사용할 수 있는 논리 프로세서 수가 반환됩니다.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="NUMBER_OF_PROCESSORS" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.SetEnvironmentVariable(System.String,System.String)">
      <summary>현재 프로세스에 저장되어 있는 환경 변수를 생성, 수정 또는 삭제합니다.</summary>
      <param name="variable">환경 변수의 이름입니다.</param>
      <param name="value">
        <paramref name="variable" />에 할당할 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="variable" /> contains a zero-length string, an initial hexadecimal zero character (0x00), or an equal sign ("="). -or-The length of <paramref name="variable" /> or <paramref name="value" /> is greater than or equal to 32,767 characters.-or-An error occurred during the execution of this operation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.StackTrace">
      <summary>현재 스택 추적 정보를 가져옵니다.</summary>
      <returns>스택 추적 정보가 포함된 문자열입니다.이 값은 <see cref="F:System.String.Empty" />일 수 있습니다.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack trace information is out of range.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.TickCount">
      <summary>시스템 시작 이후 경과 시간(밀리초)을 가져옵니다.</summary>
      <returns>컴퓨터가 마지막으로 시작된 이후 경과된 시간(밀리초)을 포함하는 부호 있는 32비트 정수입니다. </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Math">
      <summary>삼각, 로그 및 기타 일반 수학 함수에 대한 상수 및 정적 메서드를 제공합니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Decimal)">
      <summary>
        <see cref="T:System.Decimal" /> 숫자의 절대값을 반환합니다.</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Decimal.MaxValue" />인 10진수 x입니다.</returns>
      <param name="value">
        <see cref="F:System.Decimal.MinValue" />보다 크거나 같지만 <see cref="F:System.Decimal.MaxValue" />보다 작거나 같은 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Double)">
      <summary>배정밀도 부동 소수점 수의 절대 값을 반환합니다.</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Double.MaxValue" /> 범위의 배정밀도 부동 소수점 숫자 x입니다.</returns>
      <param name="value">
        <see cref="F:System.Double.MinValue" />보다 크거나 같지만 <see cref="F:System.Double.MaxValue" />보다 작거나 같은 숫자입니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int16)">
      <summary>16비트 부호 있는 정수의 절대 값을 반환합니다.</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Int16.MaxValue" /> 범위의 16비트 부호 있는 정수 x입니다.</returns>
      <param name="value">
        <see cref="F:System.Int16.MinValue" />보다 크지만 <see cref="F:System.Int16.MaxValue" />보다 작거나 같은 숫자입니다.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int16.MinValue" />와 같습니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int32)">
      <summary>32비트 부호 있는 정수의 절대 값을 반환합니다.</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Int32.MaxValue" /> 범위의 32비트 부호 있는 정수 x입니다.</returns>
      <param name="value">
        <see cref="F:System.Int32.MinValue" />보다 크지만 <see cref="F:System.Int32.MaxValue" />보다 작거나 같은 숫자입니다.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int32.MinValue" />와 같습니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int64)">
      <summary>64비트 부호 있는 정수의 절대 값을 반환합니다.</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Int64.MaxValue" /> 범위의 64비트 부호 있는 정수 x입니다.</returns>
      <param name="value">
        <see cref="F:System.Int64.MinValue" />보다 크지만 <see cref="F:System.Int64.MaxValue" />보다 작거나 같은 숫자입니다.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.Int64.MinValue" />와 같습니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.SByte)">
      <summary>8비트 부호 있는 정수의 절대 값을 반환합니다.</summary>
      <returns>0 ≤ x ≤<see cref="F:System.SByte.MaxValue" /> 범위의 8비트 부호 있는 정수 x입니다.</returns>
      <param name="value">
        <see cref="F:System.SByte.MinValue" />보다 크지만 <see cref="F:System.SByte.MaxValue" />보다 작거나 같은 숫자입니다.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />가 <see cref="F:System.SByte.MinValue" />와 같습니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Single)">
      <summary>단정밀도 부동 소수점 수의 절대 값을 반환합니다.</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Single.MaxValue" /> 범위의 단정밀도 부동 소수점 숫자 x입니다.</returns>
      <param name="value">
        <see cref="F:System.Single.MinValue" />보다 크거나 같지만 <see cref="F:System.Single.MaxValue" />보다 작거나 같은 숫자입니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Acos(System.Double)">
      <summary>코사인을 적용했을 때 지정된 숫자가 나오는 각도를 반환합니다.</summary>
      <returns>0 ≤θ≤π 범위의 각도 θ(라디안)입니다.또는 <paramref name="d" /> &lt; -1이거나 <paramref name="d" /> &gt; 1이거나 <paramref name="d" />가 <see cref="F:System.Double.NaN" />과 같으면 <see cref="F:System.Double.NaN" />입니다.</returns>
      <param name="d">코사인을 나타내는 숫자입니다. <paramref name="d" />는 -1보다 크거나 같지만 1보다 작거나 같아야 합니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Asin(System.Double)">
      <summary>사인을 적용했을 때 지정된 숫자가 나오는 각도를 반환합니다.</summary>
      <returns>-π/2 ≤θ≤π/2 범위의 각도 θ(라디안)입니다. 또는 <paramref name="d" /> &lt; -1이거나 <paramref name="d" /> &gt; 1이거나 <paramref name="d" />가 <see cref="F:System.Double.NaN" />과 같으면 <see cref="F:System.Double.NaN" />입니다.</returns>
      <param name="d">사인을 나타내는 숫자입니다. <paramref name="d" />는 -1보다 크거나 같지만 1보다 작거나 같아야 합니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan(System.Double)">
      <summary>탄젠트를 적용했을 때 지정된 숫자가 나오는 각도를 반환합니다.</summary>
      <returns>-π2 ≤θ≤π/2 범위의 각도 θ(라디안)입니다.또는 <paramref name="d" />가 <see cref="F:System.Double.NaN" />과 같으면 <see cref="F:System.Double.NaN" />이고, <paramref name="d" />가 <see cref="F:System.Double.NegativeInfinity" />와 같으면 배정밀도로 반올림된 -π/2(-1.5707963267949)이고, <paramref name="d" />가 <see cref="F:System.Double.PositiveInfinity" />와 같으면 배정밀도로 반올림된 π/2(1.5707963267949)입니다.</returns>
      <param name="d">접선을 나타내는 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan2(System.Double,System.Double)">
      <summary>탄젠트를 적용했을 때 지정된 두 숫자의 몫이 나오는 각도를 반환합니다.</summary>
      <returns>π≤θ≤π 범위의 각도 θ(라디안) 및 tan(θ) = <paramref name="y" /> / <paramref name="x" />입니다(여기서 <paramref name="x" />,<paramref name="y" /> 는 데카르트 평면 상의 지점임).다음 사항이 적용됩니다.에 대 한 (<paramref name="x" />, <paramref name="y" />) 사분면 1, 0에서에서 &lt; θ &lt; π/2입니다.에 대 한 (<paramref name="x" />, <paramref name="y" />) 2, 사분면에 π/2 &lt; θ≤π.에 대 한 (<paramref name="x" />, <paramref name="y" />) 3 사분면에서-π &lt; θ &lt;-π/2입니다.에 대 한 (<paramref name="x" />, <paramref name="y" />) 사분면 4,-π/2 &lt; θ &lt; 0입니다.점이 사분면의 경계에 있는 경우 반환 값은 다음과 같습니다.y가 0이고 x가 음수가 아니면 θ = 0입니다.y가 0이고 x가 음수이면 θ = π입니다.y가 양수이고 x가 0이면 θ = π/2입니다.y가 음수이고 x가 0이면 θ = -π/2입니다.y가 0이고 x가 0이면 θ = 0입니다. <paramref name="x" /> 또는 <paramref name="y" />가 <see cref="F:System.Double.NaN" />이거나, <paramref name="x" /> 및 <paramref name="y" />가 <see cref="F:System.Double.PositiveInfinity" /> 또는 <see cref="F:System.Double.NegativeInfinity" />이면 이 메서드는 <see cref="F:System.Double.NaN" />을 반환합니다.</returns>
      <param name="y">점의 y 좌표입니다. </param>
      <param name="x">점의 x 좌표입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Decimal)">
      <summary>지정된 10진수보다 크거나 같은 최소 정수 값을 반환합니다.</summary>
      <returns>
        <paramref name="d" />보다 크거나 같은 최소 정수 값입니다.이 메서드는 정수 계열 형식이 아니라 <see cref="T:System.Decimal" />을 반환합니다.</returns>
      <param name="d">10진수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자보다 크거나 같은 최소 정수 값을 반환합니다.</summary>
      <returns>
        <paramref name="a" />보다 크거나 같은 최소 정수 값입니다.<paramref name="a" />가 <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> 또는 <see cref="F:System.Double.PositiveInfinity" />와 같으면, 해당 값이 반환됩니다.이 메서드는 정수 계열 형식이 아니라 <see cref="T:System.Double" />을 반환합니다.</returns>
      <param name="a">배정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cos(System.Double)">
      <summary>지정된 각도의 코사인을 반환합니다.</summary>
      <returns>
        <paramref name="d" />의 코사인입니다.<paramref name="d" />가 <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> 또는 <see cref="F:System.Double.PositiveInfinity" />와 같으면 이 메서드는 <see cref="F:System.Double.NaN" />을 반환합니다.</returns>
      <param name="d">라디안 단위의 각도입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cosh(System.Double)">
      <summary>지정된 각도의 하이퍼볼릭 코사인을 반환합니다.</summary>
      <returns>
        <paramref name="value" />의 쌍곡선 코사인입니다.<paramref name="value" />가 <see cref="F:System.Double.NegativeInfinity" /> 또는 <see cref="F:System.Double.PositiveInfinity" />와 같으면 <see cref="F:System.Double.PositiveInfinity" />가 반환됩니다.<paramref name="value" />가 <see cref="F:System.Double.NaN" />과 같으면 <see cref="F:System.Double.NaN" />이 반환됩니다.</returns>
      <param name="value">라디안 단위의 각도입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.E">
      <summary>상수, e로 지정된 자연 로그의 밑을 나타냅니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Exp(System.Double)">
      <summary>e를 지정된 수만큼 거듭제곱하여 반환합니다.</summary>
      <returns>e의 <paramref name="d" />승입니다.<paramref name="d" />가 <see cref="F:System.Double.NaN" /> 또는 <see cref="F:System.Double.PositiveInfinity" />와 같으면, 해당 값이 반환됩니다.<paramref name="d" />가 <see cref="F:System.Double.NegativeInfinity" />와 같으면 0이 반환됩니다.</returns>
      <param name="d">거듭제곱을 지정하는 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Decimal)">
      <summary>지정된 10진수보다 작거나 같은 최대 정수를 반환합니다.</summary>
      <returns>
        <paramref name="d" />보다 작거나 같은 최대 정수입니다.메서드는 형식 <see cref="T:System.Math" />의 정수 값을 반환합니다.</returns>
      <param name="d">10진수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Double)">
      <summary>지정된 배정밀도 부동 소수점 숫자보다 작거나 같은 최대 정수를 반환합니다.</summary>
      <returns>
        <paramref name="d" />보다 작거나 같은 최대 정수입니다.<paramref name="d" />가 <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> 또는 <see cref="F:System.Double.PositiveInfinity" />와 같으면, 해당 값이 반환됩니다.</returns>
      <param name="d">배정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.IEEERemainder(System.Double,System.Double)">
      <summary>지정된 수를 지정된 다른 수로 나눈 나머지를 반환합니다.</summary>
      <returns>
        <paramref name="x" /> - (<paramref name="y" /> Q)와 같은 숫자입니다. 여기서 Q는 가장 가까운 정수로 반올림된 <paramref name="x" /> / <paramref name="y" />의 몫입니다. <paramref name="x" /> / <paramref name="y" />가 두 수의 중간이면, 짝수가 반환됩니다.<paramref name="x" /> - (<paramref name="y" /> Q)가 0인 경우 <paramref name="x" />가 양수이면 값 +0이, <paramref name="x" />가 음수이면 값 -0이 반환됩니다.<paramref name="y" /> = 0인 경우 <see cref="F:System.Double.NaN" />이 반환됩니다.</returns>
      <param name="x">피제수입니다. </param>
      <param name="y">제수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double)">
      <summary>지정된 숫자의 자연(밑 e) 로그를 반환합니다.</summary>
      <returns>다음 표에 나와 있는 값 중 하나입니다. <paramref name="d" /> 매개 변수반환 값 양수 자연 로그 <paramref name="d" />; 즉, ln <paramref name="d" />, 또는 로그 e<paramref name="d" />0 <see cref="F:System.Double.NegativeInfinity" />음수 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />과 같습니다.<see cref="F:System.Double.NaN" /><see cref="F:System.Double.PositiveInfinity" />과 같습니다.<see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">로그가 있는 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double,System.Double)">
      <summary>지정된 밑을 사용하여 지정된 숫자의 로그를 반환합니다.</summary>
      <returns>다음 표에 나와 있는 값 중 하나입니다.+Infinity는 <see cref="F:System.Double.PositiveInfinity" />, -Infinity는 <see cref="F:System.Double.NegativeInfinity" />,NaN은 <see cref="F:System.Double.NaN" />을 의미합니다.<paramref name="a" /><paramref name="newBase" />반환 값<paramref name="a" />&gt; 0(0 &lt;<paramref name="newBase" />&lt; 1) -또는-(<paramref name="newBase" />&gt; 1)lognewBase(a)<paramref name="a" />&lt; 0(모든 값)NaN(모든 값)<paramref name="newBase" />&lt; 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = +InfinityNaN<paramref name="a" /> = NaN(모든 값)NaN(모든 값)<paramref name="newBase" /> = NaNNaN(모든 값)<paramref name="newBase" /> = 1NaN<paramref name="a" /> = 00 &lt;<paramref name="newBase" />&lt; 1 +Infinity<paramref name="a" /> = 0<paramref name="newBase" />&gt; 1-Infinity<paramref name="a" /> = + 무한대0 &lt;<paramref name="newBase" />&lt; 1-Infinity<paramref name="a" /> = + 무한대<paramref name="newBase" />&gt; 1+Infinity<paramref name="a" /> = 1<paramref name="newBase" /> = 00<paramref name="a" /> = 1<paramref name="newBase" /> = +Infinity0</returns>
      <param name="a">로그가 있는 숫자입니다. </param>
      <param name="newBase">로그의 밑입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log10(System.Double)">
      <summary>밑을 10으로 사용하여 지정된 숫자의 로그를 반환합니다.</summary>
      <returns>다음 표에 나와 있는 값 중 하나입니다. <paramref name="d" /> 매개 변수 반환 값 양수 기본 10 로그 <paramref name="d" />; 즉, 로그 10<paramref name="d" />. 0 <see cref="F:System.Double.NegativeInfinity" />음수 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />과 같습니다.<see cref="F:System.Double.NaN" /><see cref="F:System.Double.PositiveInfinity" />과 같습니다.<see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">로그가 있는 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Byte,System.Byte)">
      <summary>두 개의 8비트 부호 없는 정수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.</returns>
      <param name="val1">비교할 두 개의 8비트 부호 없는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 8비트 부호 없는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Decimal,System.Decimal)">
      <summary>두 개의 10진수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.</returns>
      <param name="val1">비교할 두 개의 10진수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 10진수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Double,System.Double)">
      <summary>두 개의 배정밀도 부동 소수점 수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.<paramref name="val1" />, <paramref name="val2" /> 또는 <paramref name="val1" />과 <paramref name="val2" />가 모두 <see cref="F:System.Double.NaN" />과 같은 경우 <see cref="F:System.Double.NaN" />이 반환됩니다.</returns>
      <param name="val1">비교할 두 개의 배정밀도 부동 소수점 수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 배정밀도 부동 소수점 수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int16,System.Int16)">
      <summary>두 개의 16비트 부호 있는 정수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.</returns>
      <param name="val1">비교할 두 개의 16비트 부호 있는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 16비트 부호 있는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int32,System.Int32)">
      <summary>두 개의 32비트 부호 있는 정수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.</returns>
      <param name="val1">비교할 두 개의 32비트 부호 있는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 32비트 부호 있는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int64,System.Int64)">
      <summary>두 개의 64비트 부호 있는 정수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.</returns>
      <param name="val1">비교할 두 개의 64비트 부호 있는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 64비트 부호 있는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.SByte,System.SByte)">
      <summary>두 개의 8비트 부호 있는 정수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.</returns>
      <param name="val1">비교할 두 개의 8비트 부호 있는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 8비트 부호 있는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Single,System.Single)">
      <summary>두 개의 단정밀도 부동 소수점 수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.<paramref name="val1" />이나 <paramref name="val2" /> 또는 <paramref name="val1" />과 <paramref name="val2" />가 모두 <see cref="F:System.Single.NaN" />과 같은 경우 <see cref="F:System.Single.NaN" />이 반환됩니다.</returns>
      <param name="val1">비교할 두 개의 단정밀도 부동 소수점 수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 단정밀도 부동 소수점 수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt16,System.UInt16)">
      <summary>두 개의 16비트 부호 없는 정수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.</returns>
      <param name="val1">비교할 두 개의 16비트 부호 없는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 16비트 부호 없는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt32,System.UInt32)">
      <summary>두 개의 32비트 부호 없는 정수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.</returns>
      <param name="val1">비교할 두 개의 32비트 부호 없는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 32비트 부호 없는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt64,System.UInt64)">
      <summary>두 개의 64비트 부호 없는 정수 중 더 큰 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 중 더 큰 매개 변수입니다.</returns>
      <param name="val1">비교할 두 개의 64비트 부호 없는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 64비트 부호 없는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Byte,System.Byte)">
      <summary>두 개의 8비트 부호 없는 정수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.</returns>
      <param name="val1">비교할 두 개의 8비트 부호 없는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 8비트 부호 없는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Decimal,System.Decimal)">
      <summary>두 개의 10진수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.</returns>
      <param name="val1">비교할 두 개의 10진수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 10진수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Double,System.Double)">
      <summary>두 개의 배정밀도 부동 소수점 수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.<paramref name="val1" />, <paramref name="val2" /> 또는 <paramref name="val1" />과 <paramref name="val2" />가 모두 <see cref="F:System.Double.NaN" />과 같은 경우 <see cref="F:System.Double.NaN" />이 반환됩니다.</returns>
      <param name="val1">비교할 두 개의 배정밀도 부동 소수점 수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 배정밀도 부동 소수점 수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int16,System.Int16)">
      <summary>두 개의 16비트 부호 있는 정수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.</returns>
      <param name="val1">비교할 두 개의 16비트 부호 있는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 16비트 부호 있는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int32,System.Int32)">
      <summary>두 개의 32비트 부호 있는 정수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.</returns>
      <param name="val1">비교할 두 개의 32비트 부호 있는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 32비트 부호 있는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int64,System.Int64)">
      <summary>두 개의 64비트 부호 있는 정수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.</returns>
      <param name="val1">비교할 두 개의 64비트 부호 있는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 64비트 부호 있는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.SByte,System.SByte)">
      <summary>두 개의 8비트 부호 있는 정수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.</returns>
      <param name="val1">비교할 두 개의 8비트 부호 있는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 8비트 부호 있는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Single,System.Single)">
      <summary>두 개의 단정밀도 부동 소수점 수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.<paramref name="val1" />, <paramref name="val2" /> 또는 <paramref name="val1" />과 <paramref name="val2" />가 모두 <see cref="F:System.Single.NaN" />과 같은 경우 <see cref="F:System.Single.NaN" />이 반환됩니다.</returns>
      <param name="val1">비교할 두 개의 단정밀도 부동 소수점 수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 단정밀도 부동 소수점 수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt16,System.UInt16)">
      <summary>두 개의 16비트 부호 없는 정수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.</returns>
      <param name="val1">비교할 두 개의 16비트 부호 없는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 16비트 부호 없는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt32,System.UInt32)">
      <summary>두 개의 32비트 부호 없는 정수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.</returns>
      <param name="val1">비교할 두 개의 32비트 부호 없는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 32비트 부호 없는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt64,System.UInt64)">
      <summary>두 개의 64비트 부호 없는 정수 중 더 작은 숫자를 반환합니다.</summary>
      <returns>
        <paramref name="val1" /> 또는 <paramref name="val2" /> 매개 변수 중에서 더 작은 수입니다.</returns>
      <param name="val1">비교할 두 개의 64비트 부호 없는 정수 중 첫 번째 숫자입니다. </param>
      <param name="val2">비교할 두 개의 64비트 부호 없는 정수 중 두 번째 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.PI">
      <summary>상수(π)로 지정된 원주율을 나타냅니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Pow(System.Double,System.Double)">
      <summary>지정된 숫자의 지정된 거듭제곱을 반환합니다.</summary>
      <returns>
        <paramref name="x" />의 <paramref name="y" />승입니다.</returns>
      <param name="x">거듭제곱할 배정밀도 부동 소수점 숫자입니다. </param>
      <param name="y">거듭제곱을 지정하는 배정밀도 부동 소수점 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal)">
      <summary>10진수 값을 가장 가까운 정수 값으로 반올림합니다.</summary>
      <returns>매개 변수 <paramref name="d" />에 가장 가까운 정수입니다.<paramref name="d" />의 소수 부분이 하나는 짝수이고 다른 하나는 홀수인 두 정수의 중간인 경우에는 짝수가 반환됩니다.이 메서드는 정수 계열 형식이 아니라 <see cref="T:System.Decimal" />을 반환합니다.</returns>
      <param name="d">반올림할 10진수입니다. </param>
      <exception cref="T:System.OverflowException">결과가 <see cref="T:System.Decimal" /> 범위를 벗어나는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32)">
      <summary>10진수 값을 지정된 소수 자릿수로 반올림합니다.</summary>
      <returns>
        <paramref name="decimals" />와(과) 일치하는 소수 자릿수가 포함된 <paramref name="d" />에 가장 가까운 수입니다. </returns>
      <param name="d">반올림할 10진수입니다. </param>
      <param name="decimals">반환 값의 소수 자릿수입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> 가 0 보다 작거나 28 보다 큰 경우 </exception>
      <exception cref="T:System.OverflowException">결과가 <see cref="T:System.Decimal" /> 범위를 벗어나는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32,System.MidpointRounding)">
      <summary>10진수 값을 지정된 소수 자릿수로 반올림합니다.두 숫자의 중간에 있는 값을 반올림하는 방법을 지정하는 매개 변수입니다.</summary>
      <returns>
        <paramref name="decimals" />와(과) 일치하는 소수 자릿수가 포함된 <paramref name="d" />에 가장 가까운 수입니다.<paramref name="d" />의 소수 자릿수가 <paramref name="decimals" />보다 작은 경우, <paramref name="d" />가 변경되지 않은 상태로 반환됩니다.</returns>
      <param name="d">반올림할 10진수입니다. </param>
      <param name="decimals">반환 값의 소수 자릿수입니다. </param>
      <param name="mode">서로 다른 두 숫자 중간에 있는 <paramref name="d" />의 반올림 방법에 대한 사양입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> 가 0 보다 작거나 28 보다 큰 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" />가 유효한 <see cref="T:System.MidpointRounding" /> 값이 아닌 경우</exception>
      <exception cref="T:System.OverflowException">결과가 <see cref="T:System.Decimal" /> 범위를 벗어나는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.MidpointRounding)">
      <summary>10진 값을 가장 가까운 정수로 반올림합니다.두 숫자의 중간에 있는 값을 반올림하는 방법을 지정하는 매개 변수입니다.</summary>
      <returns>
        <paramref name="d" />에 가장 가까운 정수입니다.<paramref name="d" />가 두 숫자의 중간이고, 이 숫자 중 하나는 짝수이고 다른 하나는 홀수이면, <paramref name="mode" />에 의해 두 숫자 중 하나가 반환됩니다.</returns>
      <param name="d">반올림할 10진수입니다. </param>
      <param name="mode">서로 다른 두 숫자 중간에 있는 <paramref name="d" />의 반올림 방법에 대한 사양입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" />가 유효한 <see cref="T:System.MidpointRounding" /> 값이 아닌 경우</exception>
      <exception cref="T:System.OverflowException">결과가 <see cref="T:System.Decimal" /> 범위를 벗어나는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double)">
      <summary>배정밀도 부동 소수점 값을 가장 가까운 정수 값으로 반올림합니다.</summary>
      <returns>
        <paramref name="a" />에 가장 가까운 정수입니다.<paramref name="a" />의 소수 부분이 하나는 짝수이고 다른 하나는 홀수인 두 정수의 중간인 경우에는 짝수가 반환됩니다.이 메서드는 정수 계열 형식이 아니라 <see cref="T:System.Double" />을 반환합니다.</returns>
      <param name="a">반올림할 배정밀도 부동 소수점 수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32)">
      <summary>배정밀도 부동 소수점 값을 지정된 소수 자릿수로 반올림합니다.</summary>
      <returns>
        <paramref name="digits" />와(과) 일치하는 소수 자릿수가 포함된 <paramref name="value" />에 가장 가까운 수입니다.</returns>
      <param name="value">반올림할 배정밀도 부동 소수점 수입니다. </param>
      <param name="digits">반환 값의 소수 자릿수입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> 가 0 보다 작거나 15 보다 큰 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32,System.MidpointRounding)">
      <summary>배정밀도 부동 소수점 값을 지정된 소수 자릿수로 반올림합니다.두 숫자의 중간에 있는 값을 반올림하는 방법을 지정하는 매개 변수입니다.</summary>
      <returns>
        <paramref name="digits" />와(과) 일치하는 소수 자릿수가 포함된 <paramref name="value" />에 가장 가까운 수입니다.<paramref name="value" />의 소수 자릿수가 <paramref name="digits" />보다 작은 경우, <paramref name="value" />가 변경되지 않은 상태로 반환됩니다.</returns>
      <param name="value">반올림할 배정밀도 부동 소수점 수입니다. </param>
      <param name="digits">반환 값의 소수 자릿수입니다. </param>
      <param name="mode">서로 다른 두 숫자 중간에 있는 <paramref name="value" />의 반올림 방법에 대한 사양입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> 가 0 보다 작거나 15 보다 큰 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" />가 유효한 <see cref="T:System.MidpointRounding" /> 값이 아닌 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.MidpointRounding)">
      <summary>배정밀도 부동 소수점 값을 가장 가까운 정수로 반올림합니다.두 숫자의 중간에 있는 값을 반올림하는 방법을 지정하는 매개 변수입니다.</summary>
      <returns>
        <paramref name="value" />에 가장 가까운 정수입니다.<paramref name="value" />가 두 정수의 중간이고, 이 숫자 중 하나는 짝수이고 다른 하나는 홀수이면, <paramref name="mode" />에 의해 두 숫자 중 하나가 반환됩니다.</returns>
      <param name="value">반올림할 배정밀도 부동 소수점 수입니다. </param>
      <param name="mode">서로 다른 두 숫자 중간에 있는 <paramref name="value" />의 반올림 방법에 대한 사양입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" />가 유효한 <see cref="T:System.MidpointRounding" /> 값이 아닌 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Decimal)">
      <summary>10진수의 부호를 나타내는 값을 반환합니다.</summary>
      <returns>다음 테이블과 같이 <paramref name="value" />의 부호를 나타내는 숫자입니다.반환 값 의미 -1 <paramref name="value" />가 0보다 작은 경우 0 <paramref name="value" />가 0과 같습니다. 1 <paramref name="value" />가 0보다 큽니다. </returns>
      <param name="value">부호 있는 10진수입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Double)">
      <summary>배정밀도 부동 소수점 수의 부호를 나타내는 값을 반환합니다.</summary>
      <returns>다음 테이블과 같이 <paramref name="value" />의 부호를 나타내는 숫자입니다.반환 값 의미 -1 <paramref name="value" />가 0보다 작은 경우 0 <paramref name="value" />가 0과 같습니다. 1 <paramref name="value" />가 0보다 큽니다. </returns>
      <param name="value">부호 있는 숫자입니다. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" />가 <see cref="F:System.Double.NaN" />와 같은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int16)">
      <summary>16비트 부호 있는 정수의 부호를 나타내는 값을 반환합니다.</summary>
      <returns>다음 테이블과 같이 <paramref name="value" />의 부호를 나타내는 숫자입니다.반환 값 의미 -1 <paramref name="value" />가 0보다 작은 경우 0 <paramref name="value" />가 0과 같습니다. 1 <paramref name="value" />가 0보다 큽니다. </returns>
      <param name="value">부호 있는 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int32)">
      <summary>32비트 부호 있는 정수의 부호를 나타내는 값을 반환합니다.</summary>
      <returns>다음 테이블과 같이 <paramref name="value" />의 부호를 나타내는 숫자입니다.반환 값 의미 -1 <paramref name="value" />가 0보다 작은 경우 0 <paramref name="value" />가 0과 같습니다. 1 <paramref name="value" />가 0보다 큽니다. </returns>
      <param name="value">부호 있는 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int64)">
      <summary>64비트 부호 있는 정수의 부호를 나타내는 값을 반환합니다.</summary>
      <returns>다음 테이블과 같이 <paramref name="value" />의 부호를 나타내는 숫자입니다.반환 값 의미 -1 <paramref name="value" />가 0보다 작은 경우 0 <paramref name="value" />가 0과 같습니다. 1 <paramref name="value" />가 0보다 큽니다. </returns>
      <param name="value">부호 있는 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.SByte)">
      <summary>8비트의 부호 있는 정수의 부호를 나타내는 값을 반환합니다.</summary>
      <returns>다음 테이블과 같이 <paramref name="value" />의 부호를 나타내는 숫자입니다.반환 값 의미 -1 <paramref name="value" />가 0보다 작은 경우 0 <paramref name="value" />가 0과 같습니다. 1 <paramref name="value" />가 0보다 큽니다. </returns>
      <param name="value">부호 있는 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Single)">
      <summary>단정밀도 부동 소수점 수의 부호를 나타내는 값을 반환합니다.</summary>
      <returns>다음 테이블과 같이 <paramref name="value" />의 부호를 나타내는 숫자입니다.반환 값 의미 -1 <paramref name="value" />가 0보다 작은 경우 0 <paramref name="value" />가 0과 같습니다. 1 <paramref name="value" />가 0보다 큽니다. </returns>
      <param name="value">부호 있는 숫자입니다. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" />가 <see cref="F:System.Single.NaN" />와 같은 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sin(System.Double)">
      <summary>지정된 각도의 사인을 반환합니다.</summary>
      <returns>
        <paramref name="a" />의 사인입니다.<paramref name="a" />가 <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> 또는 <see cref="F:System.Double.PositiveInfinity" />와 같으면 이 메서드는 <see cref="F:System.Double.NaN" />을 반환합니다.</returns>
      <param name="a">라디안 단위의 각도입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sinh(System.Double)">
      <summary>지정된 각도의 하이퍼볼릭 사인을 반환합니다.</summary>
      <returns>
        <paramref name="value" />의 쌍곡선 사인입니다.<paramref name="value" />가 <see cref="F:System.Double.NegativeInfinity" />, <see cref="F:System.Double.PositiveInfinity" /> 또는 <see cref="F:System.Double.NaN" />과 같으면 이 메서드는 <paramref name="value" />와 같은 <see cref="T:System.Double" />을 반환합니다.</returns>
      <param name="value">라디안 단위의 각도입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sqrt(System.Double)">
      <summary>지정된 숫자의 제곱근을 반환합니다.</summary>
      <returns>다음 표에 나와 있는 값 중 하나입니다. <paramref name="d" /> 매개 변수 반환 값 0 또는 양수 <paramref name="d" />의 양의 제곱근 음수 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />과 같음<see cref="F:System.Double.NaN" /><see cref="F:System.Double.PositiveInfinity" />과 같음<see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">제곱근을 구할 숫자입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tan(System.Double)">
      <summary>지정된 각도의 탄젠트를 반환합니다.</summary>
      <returns>
        <paramref name="a" />의 접선입니다.<paramref name="a" />가 <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> 또는 <see cref="F:System.Double.PositiveInfinity" />와 같으면 이 메서드는 <see cref="F:System.Double.NaN" />을 반환합니다.</returns>
      <param name="a">라디안 단위의 각도입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tanh(System.Double)">
      <summary>지정된 각도의 하이퍼볼릭 탄젠트를 반환합니다.</summary>
      <returns>
        <paramref name="value" />의 쌍곡선 접선입니다.<paramref name="value" />가 <see cref="F:System.Double.NegativeInfinity" />와 같으면 이 메서드는 -1을 반환하고값이 <see cref="F:System.Double.PositiveInfinity" />와 같으면 이 메서드는 1을 반환합니다.<paramref name="value" />가 <see cref="F:System.Double.NaN" />과 같으면 이 메서드는 <see cref="F:System.Double.NaN" />을 반환합니다.</returns>
      <param name="value">라디안 단위의 각도입니다. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Decimal)">
      <summary>지정된 10진수에서 정수 부분을 계산합니다. </summary>
      <returns>
        <paramref name="d" />의 정수 부분으로, 소수 자릿수를 삭제한 후 남은 숫자입니다.</returns>
      <param name="d">잘라낼 숫자입니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Double)">
      <summary>지정한 배정밀도 부동 소수점 숫자의 정수 부분을 계산합니다. </summary>
      <returns>
        <paramref name="d" />의 정수 부분으로, 소수 자릿수를 삭제한 후 남은 숫자 또는 다음 테이블에 나열된 값 중 하나입니다. <paramref name="d" />반환 값<see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">잘라낼 숫자입니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.MidpointRounding">
      <summary>수학적 반올림 메서드가 두 수의 중간에 있는 수를 처리하는 방식을 지정합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.MidpointRounding.AwayFromZero">
      <summary>두 수의 중간에 있는 수는 0에서 먼 쪽의 가장 가까운 수로 반올림됩니다.</summary>
    </member>
    <member name="F:System.MidpointRounding.ToEven">
      <summary>두 수의 중간에 있는 수는 가장 가까운 짝수로 반올림됩니다.</summary>
    </member>
    <member name="T:System.Progress`1">
      <summary>보고된 각 진행률 값에 대한 콜백을 호출하는 <see cref="T:System.IProgress`1" />을 제공합니다.</summary>
      <typeparam name="T">진행 보고서 값의 형식을 지정합니다.</typeparam>
    </member>
    <member name="M:System.Progress`1.#ctor">
      <summary>
        <see cref="T:System.Progress`1" /> 개체를 초기화합니다.</summary>
    </member>
    <member name="M:System.Progress`1.#ctor(System.Action{`0})">
      <summary>지정된 콜백을 사용하여 <see cref="T:System.Progress`1" /> 개체를 초기화합니다.</summary>
      <param name="handler">보고된 각 진행률 값에 대해 호출할 처리기입니다.이 처리기는 <see cref="E:System.Progress`1.ProgressChanged" /> 이벤트에 등록된 대리자에 대해서도 호출됩니다.생성 시 <see cref="T:System.Progress`1" />가 캡처하는 <see cref="T:System.Threading.SynchronizationContext" /> 인스턴스에 따라 이 처리기 인스턴스가 자신과 동시에 호출될 수도 있습니다.</param>
    </member>
    <member name="M:System.Progress`1.OnReport(`0)">
      <summary>진행률 변경을 보고합니다.</summary>
      <param name="value">업데이트된 진행률 값입니다.</param>
    </member>
    <member name="E:System.Progress`1.ProgressChanged">
      <summary>보고된 각 진행률 값에 대해 발생시킵니다.</summary>
    </member>
    <member name="M:System.Progress`1.System#IProgress{T}#Report(`0)">
      <summary>진행률 변경을 보고합니다.</summary>
      <param name="value">업데이트된 진행률 값입니다.</param>
    </member>
    <member name="T:System.Random">
      <summary>의사(pseudo) 난수 생성기를 나타냅니다. 이 장치는 무작위성에 대한 통계적인 특정 요구 사항과 일치하는 숫자 시퀀스를 생성합니다.이 유형의 .NET Framework 소스 코드를 찾아보려면 참조 소스를 참조하세요.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.#ctor">
      <summary>시간에 따라 달라지는 시드 값을 사용하여 <see cref="T:System.Random" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Random.#ctor(System.Int32)">
      <summary>지정된 시드 값을 사용하여 <see cref="T:System.Random" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="Seed">의사(pseudo) 난수 시퀀스의 시작 값을 계산하는 데 사용되는 숫자입니다.음수를 지정하면 이 숫자의 절대 값이 사용됩니다.</param>
    </member>
    <member name="M:System.Random.Next">
      <summary>음수가 아닌 임의의 정수를 반환합니다.</summary>
      <returns>0보다 크거나 같고 <see cref="F:System.Int32.MaxValue" />보다 작은 부호 있는 32비트 정수입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32)">
      <summary>지정된 최대값보다 작은 음수가 아닌 임의의 정수를 반환합니다.</summary>
      <returns>0보다 크거나 같고 <paramref name="maxValue" />보다 작은 부호 있는 32비트 정수이므로 반환 값의 범위에는 대개 이 포함되지만 <paramref name="maxValue" />는 포함되지 않습니다.하지만 <paramref name="maxValue" />가 0과 같으면 <paramref name="maxValue" />가 반환됩니다.</returns>
      <param name="maxValue">생성될 난수의 상한(제외)입니다.<paramref name="maxValue" />는 0보다 크거나 같아야 합니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxValue" /> is less than 0. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32,System.Int32)">
      <summary>지정된 범위 내의 임의의 정수를 반환합니다.</summary>
      <returns>
        <paramref name="minValue" />보다 크거나 같고 <paramref name="maxValue" />보다 작은 부호 있는 32비트 정수이므로 반환 값의 범위에는 <paramref name="minValue" />가 포함되지만 <paramref name="maxValue" />는 포함되지 않습니다.<paramref name="minValue" />가 <paramref name="maxValue" />와 같으면 <paramref name="minValue" />가 반환됩니다.</returns>
      <param name="minValue">반환되는 난수의 하한(포함)입니다. </param>
      <param name="maxValue">반환되는 난수의 상한(제외)입니다.<paramref name="maxValue" />는 <paramref name="minValue" />보다 크거나 같아야 합니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minValue" /> is greater than <paramref name="maxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextBytes(System.Byte[])">
      <summary>지정된 바이트 배열의 요소를 난수로 채웁니다.</summary>
      <param name="buffer">난수를 포함하는 바이트 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextDouble">
      <summary>0.0보다 크거나 같고 1.0보다 작은 부동 소수점 난수입니다.</summary>
      <returns>0.0보다 크거나 같고 1.0보다 작은 배정밀도 부동 소수점 숫자입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Sample">
      <summary>0.0과 1.0 사이의 임의의 부동 소수점 숫자를 반환합니다.</summary>
      <returns>0.0보다 크거나 같고 1.0보다 작은 배정밀도 부동 소수점 숫자입니다.</returns>
    </member>
    <member name="T:System.StringComparer">
      <summary>특정 대/소문자 및 문화권 기반 또는 서수 비교 규칙을 사용하는 문자열 비교 연산을 나타냅니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.StringComparer.#ctor">
      <summary>
        <see cref="T:System.StringComparer" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.StringComparer.Compare(System.String,System.String)">
      <summary>파생 클래스에서 재정의된 경우 두 문자열을 비교하고 상대적 정렬 순서를 나타내는 값을 반환합니다.</summary>
      <returns>다음 표와 같이 <paramref name="x" /> 및 <paramref name="y" />의 상대 값을 나타내는 부호 있는 정수입니다.값의미0보다 작음<paramref name="x" />가 정렬 순서에서 <paramref name="y" /> 앞에 오는 경우또는<paramref name="x" />가 null이고 <paramref name="y" />가 null이 아닌 경우0<paramref name="x" />가 <paramref name="y" />와 같은 경우또는<paramref name="x" />와 <paramref name="y" />가 둘 다 null인 경우 0보다 큼<paramref name="x" />가 정렬 순서에서 <paramref name="y" /> 뒤에 오는 경우또는<paramref name="y" />가 null이고 <paramref name="x" />가 null이 아닌 경우 </returns>
      <param name="x">
        <paramref name="y" />와 비교할 문자열입니다.</param>
      <param name="y">
        <paramref name="x" />와 비교할 문자열입니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.CurrentCulture">
      <summary>현재 문화권의 단어 비교 규칙을 사용하여 대/소문자를 구분하는 문자열을 비교하는 <see cref="T:System.StringComparer" /> 개체를 가져옵니다.</summary>
      <returns>새 <see cref="T:System.StringComparer" /> 개체입니다.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.StringComparer.CurrentCultureIgnoreCase">
      <summary>현재 문화권의 단어 비교 규칙을 사용하여 대/소문자를 구분하지 않는 문자열을 비교하는 <see cref="T:System.StringComparer" /> 개체를 가져옵니다.</summary>
      <returns>새 <see cref="T:System.StringComparer" /> 개체입니다.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.StringComparer.Equals(System.String,System.String)">
      <summary>파생 클래스에서 재정의된 경우 두 문자열이 같은지를 나타냅니다.</summary>
      <returns>true와 <paramref name="x" />가 같은 개체를 참조하거나, <paramref name="y" />와 <paramref name="x" />가 같거나, <paramref name="y" />와 <paramref name="x" />가 <paramref name="y" />이면 null이고, 그렇지 않으면 false입니다.</returns>
      <param name="x">
        <paramref name="y" />와 비교할 문자열입니다.</param>
      <param name="y">
        <paramref name="x" />와 비교할 문자열입니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.GetHashCode(System.String)">
      <summary>파생 클래스에서 재정의된 경우 지정된 문자열의 해시 코드를 가져옵니다.</summary>
      <returns>
        <paramref name="obj" /> 매개 변수의 값에서 계산된 32비트 부호 있는 해시 코드입니다.</returns>
      <param name="obj">문자열</param>
      <exception cref="T:System.ArgumentException">메모리가 부족하여 해시 코드를 계산하는 데 필요한 버퍼를 할당할 수 없습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" />가 null인 경우 </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.StringComparer.Ordinal">
      <summary>대/소문자를 구분하는 서수 문자열을 비교하는 <see cref="T:System.StringComparer" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.StringComparer" /> 개체입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.OrdinalIgnoreCase">
      <summary>대/소문자를 구분하지 않는 서수 문자열을 비교하는 <see cref="T:System.StringComparer" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.StringComparer" /> 개체입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>두 개체를 비교한 다음 한 개체가 다른 개체보다 작은지, 큰지 또는 두 개체가 같은지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>다음 표와 같이 <paramref name="x" /> 및 <paramref name="y" />의 상대 값을 나타내는 부호 있는 정수입니다.값의미0보다 작음<paramref name="x" />가 <paramref name="y" />보다 작은 경우0<paramref name="x" />가 <paramref name="y" />와 같습니다.0보다 큼<paramref name="x" />가 <paramref name="y" />보다 큰 경우</returns>
      <param name="x">비교할 첫 번째 개체입니다.</param>
      <param name="y">비교할 두 번째 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> 또는 <paramref name="y" /> 모두 <see cref="T:System.IComparable" /> 인터페이스를 구현하지 않는 경우또는<paramref name="x" />와 <paramref name="y" />가 형식이 다르므로 서로 비교할 수 없는 경우</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>지정한 개체가 같은지를 확인합니다.</summary>
      <returns>지정한 개체가 같으면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="x">비교할 첫 번째 개체입니다.</param>
      <param name="y">비교할 두 번째 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" />와 <paramref name="y" />가 형식이 다르므로 서로 비교할 수 없는 경우 </exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>지정한 개체의 해시 코드를 반환합니다.</summary>
      <returns>지정한 개체의 해시 코드입니다. </returns>
      <param name="obj">해시 코드가 반환될 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> 형식이 참조 형식이고 <paramref name="obj" />가 null인 경우 </exception>
    </member>
    <member name="T:System.UriBuilder">
      <summary>URI(Uniform Resource Indentifier)에 대한 사용자 지정 생성자를 제공하고 <see cref="T:System.Uri" /> 클래스의 URI를 수정합니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.#ctor">
      <summary>
        <see cref="T:System.UriBuilder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String)">
      <summary>지정된 URI를 사용하는 <see cref="T:System.UriBuilder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="uri">URI 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" />가 null입니다. </exception>
      <exception cref="T:System.UriFormatException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.FormatException" />를 catch합니다.<paramref name="uri" />가 길이가 0인 문자열이거나 공백만 포함하는 경우또는 구문 분석 루틴이 잘못된 형식에서 체계를 발견한 경우또는 파서가 "file" 체계를 사용하지 않는 URI에서 두 개 이상의 연속된 슬래시를 발견한 경우또는 <paramref name="uri" />가 올바른 URI가 아닌 경우 </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String)">
      <summary>지정된 체계 및 호스트를 사용하여 <see cref="T:System.UriBuilder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="schemeName">인터넷 액세스 프로토콜입니다. </param>
      <param name="hostName">DNS 스타일의 도메인 이름 또는 IP 주소입니다. </param>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32)">
      <summary>지정된 체계, 호스트 및 포트를 사용하여 <see cref="T:System.UriBuilder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="scheme">인터넷 액세스 프로토콜입니다. </param>
      <param name="host">DNS 스타일의 도메인 이름 또는 IP 주소입니다. </param>
      <param name="portNumber">서비스의 IP 포트 번호입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portNumber" />가 -1보다 작거나 65,535보다 큰 경우 </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String)">
      <summary>지정된 체계, 호스트, 포트 번호 및 경로를 사용하여 <see cref="T:System.UriBuilder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="scheme">인터넷 액세스 프로토콜입니다. </param>
      <param name="host">DNS 스타일의 도메인 이름 또는 IP 주소입니다. </param>
      <param name="port">서비스의 IP 포트 번호입니다. </param>
      <param name="pathValue">인터넷 리소스의 경로입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" />가 -1보다 작거나 65,535보다 큰 경우 </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String,System.String)">
      <summary>지정된 체계, 호스트, 포트 번호, 경로 및 쿼리 문자열이나 단편 식별자를 사용하여 <see cref="T:System.UriBuilder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="scheme">인터넷 액세스 프로토콜입니다. </param>
      <param name="host">DNS 스타일의 도메인 이름 또는 IP 주소입니다. </param>
      <param name="port">서비스의 IP 포트 번호입니다. </param>
      <param name="path">인터넷 리소스의 경로입니다. </param>
      <param name="extraValue">쿼리 문자열 또는 단편 식별자입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="extraValue" />가 null, <see cref="F:System.String.Empty" />, 번호 기호(#)로 시작하는 유효한 단편 식별자 또는 물음표(?)로 시작하는 유효한 쿼리 문자열이 아닌 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" />가 -1보다 작거나 65,535보다 큰 경우 </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.Uri)">
      <summary>지정된 <see cref="T:System.Uri" /> 인스턴스를 사용하여 <see cref="T:System.UriBuilder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="uri">
        <see cref="T:System.Uri" /> 클래스의 인스턴스입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" />가 null입니다. </exception>
    </member>
    <member name="M:System.UriBuilder.Equals(System.Object)">
      <summary>기존 <see cref="T:System.Uri" /> 인스턴스와 <see cref="T:System.UriBuilder" />의 콘텐츠가 같은지 비교합니다.</summary>
      <returns>
        <paramref name="rparam" />이 이 <see cref="T:System.UriBuilder" /> 인스턴스에서 생성된 <see cref="T:System.Uri" />와 같은 <see cref="T:System.Uri" />를 나타내면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="rparam">현재 인스턴스와 비교할 개체입니다. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Fragment">
      <summary>URI의 단편 부분을 가져오거나 설정합니다.</summary>
      <returns>URI의 단편 부분입니다.단편 시작 부분에 단편 식별자("#")가 추가됩니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.GetHashCode">
      <summary>URI의 해시 코드를 반환합니다.</summary>
      <returns>URI용으로 생성된 해시 코드입니다.</returns>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Host">
      <summary>서버의 DNS(Domain Name System) 호스트 이름이나 IP 주소를 가져오거나 설정합니다.</summary>
      <returns>서버의 DNS 호스트 이름 또는 IP 주소입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Password">
      <summary>URI에 액세스하는 사용자와 관련된 암호를 가져오거나 설정합니다.</summary>
      <returns>URI에 액세스하는 사용자의 암호입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Path">
      <summary>URI가 참조하는 리소스에 대한 경로를 가져오거나 설정합니다.</summary>
      <returns>URI가 참조하는 리소스에 대한 경로입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Port">
      <summary>URI의 포트 번호를 가져오거나 설정합니다.</summary>
      <returns>URI의 포트 번호입니다.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">포트는 -1보다 작거나 65,535보다 큰 값으로 설정할 수 없습니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Query">
      <summary>URI에 포함된 쿼리 정보를 가져오거나 설정합니다.</summary>
      <returns>URI에 포함된 쿼리 정보입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Scheme">
      <summary>URI의 체계 이름을 가져오거나 설정합니다.</summary>
      <returns>URI의 체계입니다.</returns>
      <exception cref="T:System.ArgumentException">구성표는 잘못된 구성표 이름으로 설정할 수 없습니다. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.UriBuilder.ToString">
      <summary>지정된 <see cref="T:System.UriBuilder" /> 인스턴스의 표시 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.UriBuilder" />의 이스케이프되지 않은 표시 문자열이 포함된 문자열입니다.</returns>
      <exception cref="T:System.UriFormatException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.FormatException" />를 catch합니다.<see cref="T:System.UriBuilder" /> 인스턴스에 잘못된 암호가 있습니다. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Uri">
      <summary>지정된 <see cref="T:System.UriBuilder" /> 인스턴스가 만든 <see cref="T:System.Uri" /> 인스턴스를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.UriBuilder" />에서 생성한 URI가 포함된 <see cref="T:System.Uri" />입니다.</returns>
      <exception cref="T:System.UriFormatException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.FormatException" />를 catch합니다.<see cref="T:System.UriBuilder" /> 속성에 의해 만들어진 URI가 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.UserName">
      <summary>URI에 액세스하는 사용자와 관련된 사용자 이름입니다.</summary>
      <returns>URI에 액세스하는 사용자의 사용자 이름입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Diagnostics.Stopwatch">
      <summary>경과 시간을 정확하게 측정하는 데 사용할 수 있는 일련의 메서드와 속성을 제공합니다.이 유형에 대한 .NET Framework 소스 코드를 검색하려면 참조 소스를 참조하세요.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Stopwatch" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.Elapsed">
      <summary>현재 인스턴스가 측정한 총 경과 시간을 가져옵니다.</summary>
      <returns>현재 인스턴스가 측정한 총 경과 시간을 나타내는 읽기 전용 <see cref="T:System.TimeSpan" />입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedMilliseconds">
      <summary>현재 인스턴스가 측정한 밀리초 단위의 총 경과 시간을 가져옵니다.</summary>
      <returns>현재 인스턴스가 측정한 총 밀리초 수를 나타내는 읽기 전용 정수(Long)입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedTicks">
      <summary>현재 인스턴스가 측정한 총 경과 시간(타이머 틱 수)을 가져옵니다.</summary>
      <returns>현재 인스턴스가 측정한 총 타이머 틱 수를 나타내는 읽기 전용 정수(Long)입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.Frequency">
      <summary>초당 틱 수로 나타낸 타이머의 빈도를 가져옵니다.이 필드는 읽기 전용입니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.GetTimestamp">
      <summary>타이머 메커니즘에서 현재 틱 수를 가져옵니다.</summary>
      <returns>내부 타이머 메커니즘의 틱 카운터 값을 나타내는 정수(Long)입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.IsHighResolution">
      <summary>타이머가 고해상도 성능 카운터를 기반으로 하는지를 나타냅니다.이 필드는 읽기 전용입니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.IsRunning">
      <summary>
        <see cref="T:System.Diagnostics.Stopwatch" /> 타이머가 실행 중인지를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Diagnostics.Stopwatch" /> 인스턴스가 간격의 경과 시간을 측정하면서 실행되고 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Reset">
      <summary>시간 간격 측정을 중지하고 경과 시간을 0으로 다시 설정합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Restart">
      <summary>시간 간격 측정을 중지하고 경과 시간 값을 0으로 다시 설정한 다음 경과 시간 측정을 시작합니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Start">
      <summary>간격에 대한 경과 시간 측정을 시작하거나 다시 시작합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.StartNew">
      <summary>새 <see cref="T:System.Diagnostics.Stopwatch" /> 인스턴스를 초기화하고 경과 시간 속성을 0으로 설정한 다음 경과 시간 측정을 시작합니다.</summary>
      <returns>경과 시간 측정을 방금 시작한 <see cref="T:System.Diagnostics.Stopwatch" />입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Stop">
      <summary>간격에 대한 경과 시간 측정을 중지합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.Path">
      <summary>파일이나 디렉터리 경로 정보를 포함하는 <see cref="T:System.String" /> 인스턴스에 대한 작업을 수행합니다.이러한 작업은 플랫폼 간에 호환되는 방식으로 수행됩니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.AltDirectorySeparatorChar">
      <summary>계층적 파일 시스템 구조를 반영하는 경로 문자열에서 디렉터리 수준을 구분하는 데 사용되는 플랫폼 특정 대체 문자를 제공합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.ChangeExtension(System.String,System.String)">
      <summary>경로 문자열의 확장명을 변경합니다.</summary>
      <returns>수정된 경로 정보입니다.Windows 기반 데스크톱 플랫폼에서 <paramref name="path" />이(가) null이거나 빈 문자열("")이면 경로 정보가 수정되지 않고 반환됩니다.<paramref name="extension" />이(가) null이면 반환된 문자열에 확장명이 제거된 지정된 경로가 포함되어 있습니다.<paramref name="path" />에 확장명이 없고 <paramref name="extension" />이(가) null이 아니면 반환된 경로 문자열에는 <paramref name="path" /> 끝에 추가된 <paramref name="extension" />이(가) 포함되어 있습니다.</returns>
      <param name="path">수정할 경로 정보입니다.경로에는 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 문자가 포함될 수 없습니다.</param>
      <param name="extension">앞에 마침표가 있거나 없는 새 확장명입니다.<paramref name="path" />에서 기존 확장명을 제거하려면 null을(를) 지정하세요.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />에 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 잘못된 문자가 하나 이상 포함되어 있는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String)">
      <summary>두 문자열을 한 경로로 결합합니다.</summary>
      <returns>결합된 경로입니다.지정된 경로 중 하나가 0 길이의 문자열이면 이 메서드는 다른 경로를 반환합니다.<paramref name="path2" />에 절대 경로가 포함되어 있으면 이 메서드는 <paramref name="path2" />을(를) 반환합니다.</returns>
      <param name="path1">결합할 첫 번째 경로입니다. </param>
      <param name="path2">결합할 두 번째 경로입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" /> 또는 <paramref name="path2" />에 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 잘못된 문자가 하나 이상 포함되어 있는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" /> 또는 <paramref name="path2" />가 null인 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String,System.String)">
      <summary>세 문자열을 한 경로로 결합합니다.</summary>
      <returns>결합된 경로입니다.</returns>
      <param name="path1">결합할 첫 번째 경로입니다. </param>
      <param name="path2">결합할 두 번째 경로입니다. </param>
      <param name="path3">결합할 세 번째 경로입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" />, <paramref name="path2" /> 또는 <paramref name="path3" />에 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 잘못된 문자가 하나 이상 포함되어 있는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" />, <paramref name="path2" /> 또는 <paramref name="path3" />가 null인 경우 </exception>
    </member>
    <member name="M:System.IO.Path.Combine(System.String[])">
      <summary>문자열 배열을 한 경로로 결합합니다.</summary>
      <returns>결합된 경로입니다.</returns>
      <param name="paths">경로 각 부분의 배열입니다.</param>
      <exception cref="T:System.ArgumentException">배열의 문자열 중 하나에 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 잘못된 문자가 하나 이상 포함된 경우 </exception>
      <exception cref="T:System.ArgumentNullException">배열의 문자열 중 하나가 null인 경우 </exception>
    </member>
    <member name="F:System.IO.Path.DirectorySeparatorChar">
      <summary>계층적 파일 시스템 구조를 반영하는 경로 문자열에서 디렉터리 수준을 구분하는 데 사용되는 플랫폼 특정 문자를 제공합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetDirectoryName(System.String)">
      <summary>지정된 경로 문자열에 대한 디렉터리 정보를 반환합니다.</summary>
      <returns>
        <paramref name="path" />에 대한 디렉터리 정보이며, <paramref name="path" />이(가) 루트 디렉터리를 나타내거나 null인 경우 null입니다.<paramref name="path" />에 디렉터리 정보가 포함되어 있지 않으면 <see cref="F:System.String.Empty" />이(가) 반환됩니다.</returns>
      <param name="path">파일 또는 디렉터리의 경로입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 매개 변수가 유효하지 않은 문자를 포함하거나, 비어 있거나, 공백만 포함하는 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">에 .NET for Windows Store apps 또는 이식 가능한 클래스 라이브러리, 기본 클래스 예외를 catch <see cref="T:System.IO.IOException" />, 대신 합니다.<paramref name="path" /> 매개 변수가 시스템에 정의된 최대 길이보다 긴 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetExtension(System.String)">
      <summary>지정된 경로 문자열에서 확장명을 반환합니다.</summary>
      <returns>"."(마침표)를 포함한 지정된 경로의 확장명, null 또는 <see cref="F:System.String.Empty" />입니다.<paramref name="path" />이(가) null이면, <see cref="M:System.IO.Path.GetExtension(System.String)" />은(는) null을(를) 반환합니다.<paramref name="path" />에 확장명 정보가 없는 경우 <see cref="M:System.IO.Path.GetExtension(System.String)" />은(는) <see cref="F:System.String.Empty" />을(를) 반환합니다.</returns>
      <param name="path">확장명을 가져올 경로 문자열입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />에 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 잘못된 문자가 하나 이상 포함되어 있는 경우  </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileName(System.String)">
      <summary>지정된 경로 문자열에서 파일 이름과 확장명을 반환합니다.</summary>
      <returns>
        <paramref name="path" />에서 마지막 디렉터리 문자 다음의 문자입니다.<paramref name="path" />의 마지막 문자가 디렉터리나 볼륨 구분 문자이면 이 메서드는 <see cref="F:System.String.Empty" />을(를) 반환합니다.<paramref name="path" />이(가) null이면 이 메서드는 null을(를) 반환합니다.</returns>
      <param name="path">파일 이름과 확장명을 가져올 경로 문자열입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />에 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 잘못된 문자가 하나 이상 포함되어 있는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileNameWithoutExtension(System.String)">
      <summary>확장명 없이 지정된 경로 문자열의 파일 이름을 반환합니다.</summary>
      <returns>마지막 마침표(.)와 그 다음에 나오는 모든 문자인 <see cref="M:System.IO.Path.GetFileName(System.String)" />에서 반환한 문자열입니다.</returns>
      <param name="path">파일의 경로입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />에 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 잘못된 문자가 하나 이상 포함되어 있는 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFullPath(System.String)">
      <summary>지정된 경로 문자열에 대한 절대 경로를 반환합니다.</summary>
      <returns>"C:\MyFile.txt"처럼 <paramref name="path" />의 정규화된 위치입니다.</returns>
      <param name="path">절대 경로 정보를 가져올 파일 또는 디렉터리입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />가 길이가 0인 문자열이거나 공백만 포함하거나 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 하나 이상의 잘못된 문자를 포함하는 경우또는 절대 경로를 검색할 수 없는 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />가 null인 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" />에 볼륨 식별자(예: "c:\")의 일부가 아닌 콜론(":")이 포함된 경우 </exception>
      <exception cref="T:System.IO.PathTooLongException">지정된 경로 또는 파일 이름이 시스템에 정의된 최대 길이를 초과하는 경우.예를 들어, Windows 기반 플랫폼에서는 경로에 248자 미만의 문자를 사용해야 하며 파일 이름에는 260자 미만의 문자를 사용해야 합니다.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetInvalidFileNameChars">
      <summary>파일 이름에 사용할 수 없는 문자가 포함된 배열을 가져옵니다.</summary>
      <returns>파일 이름에 사용할 수 없는 문자가 포함된 배열입니다.</returns>
    </member>
    <member name="M:System.IO.Path.GetInvalidPathChars">
      <summary>경로 이름에 사용할 수 없는 문자가 포함된 배열을 가져옵니다.</summary>
      <returns>파일 이름에 사용할 수 없는 문자가 포함된 배열입니다.</returns>
    </member>
    <member name="M:System.IO.Path.GetPathRoot(System.String)">
      <summary>지정된 경로의 루트 디렉터리 정보를 가져옵니다.</summary>
      <returns>
        <paramref name="path" />의 루트 디렉터리(예: "C:\")이며, <paramref name="path" />이(가) null인 경우 null이며 <paramref name="path" />에 루트 디렉터리 정보가 포함되어 있지 않은 경우 빈 문자열입니다.</returns>
      <param name="path">루트 디렉터리 정보를 가져올 경로입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />에 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 잘못된 문자가 하나 이상 포함되어 있는 경우또는 <see cref="F:System.String.Empty" />에 <paramref name="path" />가 전달된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetRandomFileName">
      <summary>임의의 폴더 이름 또는 파일 이름을 반환합니다.</summary>
      <returns>임의의 폴더 이름 또는 파일 이름입니다.</returns>
    </member>
    <member name="M:System.IO.Path.GetTempFileName">
      <summary>디스크에 크기가 0바이트인 고유한 이름의 임시 파일을 만들고 해당 파일의 전체 경로를 반환합니다.</summary>
      <returns>임시 파일의 전체 경로입니다.</returns>
      <exception cref="T:System.IO.IOException">사용할 수 있는 고유한 임시 파일 이름이 없는 경우와 같은 I/O 오류가 발생한 경우또는이 메서드로 임시 파일을 만들지 못한 경우</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetTempPath">
      <summary>현재 사용자의 임시 폴더 경로를 반환합니다.</summary>
      <returns>백슬래시로 끝나는 임시 폴더 경로입니다.</returns>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.HasExtension(System.String)">
      <summary>경로에 파일 확장명이 포함된지를 확인합니다.</summary>
      <returns>경로의 마지막 디렉터리 구분 문자(\\ 또는 /) 또는 볼륨 구분 기호 문자(:) 다음에 나오는 문자에 마침표(.)와 하나 이상의 문자가 포함되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="path">확장명을 검색할 경로입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />에 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 잘못된 문자가 하나 이상 포함되어 있는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.IsPathRooted(System.String)">
      <summary>지정된 경로 문자열에 루트가 포함된지를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <paramref name="path" />에 루트가 포함되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="path">테스트할 경로입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />에 <see cref="M:System.IO.Path.GetInvalidPathChars" />에 정의된 잘못된 문자가 하나 이상 포함되어 있는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.PathSeparator">
      <summary>환경 변수에서 경로 문자열을 구분하는 데 사용되는 플랫폼 특정 구분 문자입니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.VolumeSeparatorChar">
      <summary>플랫폼 특정 볼륨 구분 기호 문자를 제공합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Net.WebUtility">
      <summary>웹 요청을 처리할 경우 URL의 인코딩 및 디코딩 메서드를 제공합니다. </summary>
    </member>
    <member name="M:System.Net.WebUtility.HtmlDecode(System.String)">
      <summary>HTTP 전송을 위해 HTML 인코딩된 문자열을 디코딩된 문자열로 변환합니다.</summary>
      <returns>디코딩된 문자열입니다.</returns>
      <param name="value">디코딩할 문자열입니다.</param>
    </member>
    <member name="M:System.Net.WebUtility.HtmlEncode(System.String)">
      <summary>문자열을 HTML로 인코딩된 문자열로 변환합니다.</summary>
      <returns>인코딩된 문자열입니다.</returns>
      <param name="value">인코딩할 문자열입니다.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecode(System.String)">
      <summary>URL 전송을 위해 인코딩된 문자열을 디코딩된 문자열로 변환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.디코딩된 문자열입니다.</returns>
      <param name="encodedValue">디코딩할 URL 인코딩된 문자열입니다.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>URL에서 전송을 위해 인코딩된 바이트 문자열을 디코딩된 바이트 배열로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Byte" />를 반환합니다.디코딩된 <see cref="T:System.Byte" /> 문자열입니다.</returns>
      <param name="encodedValue">디코딩할 URL 인코딩된 <see cref="T:System.Byte" /> 배열입니다.</param>
      <param name="offset">디코딩할 <see cref="T:System.Byte" /> 배열의 시작 부분부터의 오프셋(바이트)입니다.</param>
      <param name="count">
        <see cref="T:System.Byte" /> 배열에서 디코딩할 개수입니다(바이트 단위).</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncode(System.String)">
      <summary>텍스트 문자열을 URL 인코딩된 문자열로 변환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.URL 인코딩된 문자열입니다.</returns>
      <param name="value">URL로 인코딩할 텍스트입니다.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>바이트 배열을 URL 인코딩된 바이트 배열로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Byte" />를 반환합니다.인코딩된 <see cref="T:System.Byte" /> 배열입니다.</returns>
      <param name="value">URL 인코딩할 <see cref="T:System.Byte" /> 배열입니다.</param>
      <param name="offset">인코딩할 <see cref="T:System.Byte" /> 배열의 시작 부분부터의 오프셋(바이트)입니다.</param>
      <param name="count">
        <see cref="T:System.Byte" /> 배열에서 인코딩할 개수입니다(바이트 단위).</param>
    </member>
    <member name="T:System.Runtime.Versioning.FrameworkName">
      <summary>.NET Framework 버전의 이름을 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String)">
      <summary>.NET Framework 버전에 대한 정보가 포함된 문자열에서 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="frameworkName">.NET Framework 버전 정보가 포함된 문자열입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="frameworkName" />가 <see cref="F:System.String.Empty" />입니다.또는<paramref name="frameworkName" />에 두 개 미만 또는 세 개 이상의 구성 요소가 들어 있는 경우또는<paramref name="frameworkName" />에 주 버전 번호와 부 버전 번호가 포함되어 있지 않습니다.또는<paramref name="frameworkName " />에 유효한 버전 번호가 포함되어 있지 않습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="frameworkName" />가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version)">
      <summary>문자열과 .NET Framework 버전을 식별하는 <see cref="T:System.Version" /> 개체에서 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identifier">.NET Framework 버전을 식별하는 문자열입니다. </param>
      <param name="version">.NET Framework 버전 정보가 포함된 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" />가 <see cref="F:System.String.Empty" />입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" />가 null입니다.또는<paramref name="version" />가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version,System.String)">
      <summary>문자열, .NET Framework 버전을 식별하는 <see cref="T:System.Version" /> 개체 및 프로필 이름에서 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identifier">.NET Framework 버전을 식별하는 문자열입니다.</param>
      <param name="version">.NET Framework 버전 정보가 포함된 개체입니다.</param>
      <param name="profile">프로필 이름입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" />가 <see cref="F:System.String.Empty" />입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" />가 null입니다.또는<paramref name="version" />가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Object)">
      <summary>이 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 인스턴스가 지정된 개체와 같은 .NET Framework 버전을 표시하는지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>현재 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체의 모든 구성 요소가 <paramref name="obj" />의 해당 구성 요소와 일치하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 인스턴스와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Runtime.Versioning.FrameworkName)">
      <summary>이 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 인스턴스가 지정된 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 인스턴스와 같은 .NET Framework 버전을 표시하는지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>현재 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체의 모든 구성 요소가 <paramref name="other" />의 해당 구성 요소와 일치하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 인스턴스와 비교할 개체입니다.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.FullName">
      <summary>이 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체의 전체 이름을 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체의 전체 이름입니다.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.GetHashCode">
      <summary>
        <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체의 해시 코드를 반환합니다.</summary>
      <returns>이 인스턴스의 해시 코드를 나타내는 32비트 부호 있는 정수입니다.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Identifier">
      <summary>이 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체의 식별자를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체의 식별자입니다.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Equality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>두 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체가 같은 .NET Framework 버전을 표시하는지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="left" /> 및 <paramref name="right" /> 매개 변수가 같은 .NET Framework 버전을 표시하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">비교할 첫 번째 개체입니다.</param>
      <param name="right">비교할 두 번째 개체입니다.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Inequality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>두 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체가 다른 .NET Framework 버전을 표시하는지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="left" /> 및 <paramref name="right" /> 매개 변수가 다른 .NET Framework 버전을 표시하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">비교할 첫 번째 개체입니다.</param>
      <param name="right">비교할 두 번째 개체입니다.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Profile">
      <summary>이 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체의 프로필 이름을 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체의 프로필 이름입니다.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.ToString">
      <summary>이 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체의 문자열 표현을 반환합니다.</summary>
      <returns>이 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Version">
      <summary>이 <see cref="T:System.Runtime.Versioning.FrameworkName" />의 버전을 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 개체에 대한 버전 정보가 포함된 개체입니다.</returns>
    </member>
  </members>
</doc>