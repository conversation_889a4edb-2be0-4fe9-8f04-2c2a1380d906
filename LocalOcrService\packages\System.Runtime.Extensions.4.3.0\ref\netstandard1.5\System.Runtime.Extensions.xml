﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.BitConverter">
      <summary>Converts base data types to an array of bytes, and an array of bytes to base data types.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.BitConverter.DoubleToInt64Bits(System.Double)">
      <summary>Converts the specified double-precision floating point number to a 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer whose value is equivalent to <paramref name="value" />.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Boolean)">
      <summary>Returns the specified Boolean value as an array of bytes.</summary>
      <returns>An array of bytes with length 1.</returns>
      <param name="value">A Boolean value. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Char)">
      <summary>Returns the specified Unicode character value as an array of bytes.</summary>
      <returns>An array of bytes with length 2.</returns>
      <param name="value">A character to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Double)">
      <summary>Returns the specified double-precision floating point value as an array of bytes.</summary>
      <returns>An array of bytes with length 8.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int16)">
      <summary>Returns the specified 16-bit signed integer value as an array of bytes.</summary>
      <returns>An array of bytes with length 2.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int32)">
      <summary>Returns the specified 32-bit signed integer value as an array of bytes.</summary>
      <returns>An array of bytes with length 4.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int64)">
      <summary>Returns the specified 64-bit signed integer value as an array of bytes.</summary>
      <returns>An array of bytes with length 8.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Single)">
      <summary>Returns the specified single-precision floating point value as an array of bytes.</summary>
      <returns>An array of bytes with length 4.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt16)">
      <summary>Returns the specified 16-bit unsigned integer value as an array of bytes.</summary>
      <returns>An array of bytes with length 2.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt32)">
      <summary>Returns the specified 32-bit unsigned integer value as an array of bytes.</summary>
      <returns>An array of bytes with length 4.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt64)">
      <summary>Returns the specified 64-bit unsigned integer value as an array of bytes.</summary>
      <returns>An array of bytes with length 8.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.Int64BitsToDouble(System.Int64)">
      <summary>Converts the specified 64-bit signed integer to a double-precision floating point number.</summary>
      <returns>A double-precision floating point number whose value is equivalent to <paramref name="value" />.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.BitConverter.IsLittleEndian">
      <summary>Indicates the byte order ("endianness") in which data is stored in this computer architecture.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToBoolean(System.Byte[],System.Int32)">
      <summary>Returns a Boolean value converted from one byte at a specified position in a byte array.</summary>
      <returns>true if the byte at <paramref name="startIndex" /> in <paramref name="value" /> is nonzero; otherwise, false.</returns>
      <param name="value">An array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToChar(System.Byte[],System.Int32)">
      <summary>Returns a Unicode character converted from two bytes at a specified position in a byte array.</summary>
      <returns>A character formed by two bytes beginning at <paramref name="startIndex" />.</returns>
      <param name="value">An array. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> equals the length of <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToDouble(System.Byte[],System.Int32)">
      <summary>Returns a double-precision floating point number converted from eight bytes at a specified position in a byte array.</summary>
      <returns>A double precision floating point number formed by eight bytes beginning at <paramref name="startIndex" />.</returns>
      <param name="value">An array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> is greater than or equal to the length of <paramref name="value" /> minus 7, and is less than or equal to the length of <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt16(System.Byte[],System.Int32)">
      <summary>Returns a 16-bit signed integer converted from two bytes at a specified position in a byte array.</summary>
      <returns>A 16-bit signed integer formed by two bytes beginning at <paramref name="startIndex" />.</returns>
      <param name="value">An array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> equals the length of <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt32(System.Byte[],System.Int32)">
      <summary>Returns a 32-bit signed integer converted from four bytes at a specified position in a byte array.</summary>
      <returns>A 32-bit signed integer formed by four bytes beginning at <paramref name="startIndex" />.</returns>
      <param name="value">An array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> is greater than or equal to the length of <paramref name="value" /> minus 3, and is less than or equal to the length of <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt64(System.Byte[],System.Int32)">
      <summary>Returns a 64-bit signed integer converted from eight bytes at a specified position in a byte array.</summary>
      <returns>A 64-bit signed integer formed by eight bytes beginning at <paramref name="startIndex" />.</returns>
      <param name="value">An array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> is greater than or equal to the length of <paramref name="value" /> minus 7, and is less than or equal to the length of <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToSingle(System.Byte[],System.Int32)">
      <summary>Returns a single-precision floating point number converted from four bytes at a specified position in a byte array.</summary>
      <returns>A single-precision floating point number formed by four bytes beginning at <paramref name="startIndex" />.</returns>
      <param name="value">An array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> is greater than or equal to the length of <paramref name="value" /> minus 3, and is less than or equal to the length of <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[])">
      <summary>Converts the numeric value of each element of a specified array of bytes to its equivalent hexadecimal string representation.</summary>
      <returns>A string of hexadecimal pairs separated by hyphens, where each pair represents the corresponding element in <paramref name="value" />; for example, "7F-2C-4A-00".</returns>
      <param name="value">An array of bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32)">
      <summary>Converts the numeric value of each element of a specified subarray of bytes to its equivalent hexadecimal string representation.</summary>
      <returns>A string of hexadecimal pairs separated by hyphens, where each pair represents the corresponding element in a subarray of <paramref name="value" />; for example, "7F-2C-4A-00".</returns>
      <param name="value">An array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32,System.Int32)">
      <summary>Converts the numeric value of each element of a specified subarray of bytes to its equivalent hexadecimal string representation.</summary>
      <returns>A string of hexadecimal pairs separated by hyphens, where each pair represents the corresponding element in a subarray of <paramref name="value" />; for example, "7F-2C-4A-00".</returns>
      <param name="value">An array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <param name="length">The number of array elements in <paramref name="value" /> to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> or <paramref name="length" /> is less than zero.-or-<paramref name="startIndex" /> is greater than zero and is greater than or equal to the length of <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentException">The combination of <paramref name="startIndex" /> and <paramref name="length" /> does not specify a position within <paramref name="value" />; that is, the <paramref name="startIndex" /> parameter is greater than the length of <paramref name="value" /> minus the <paramref name="length" /> parameter.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt16(System.Byte[],System.Int32)">
      <summary>Returns a 16-bit unsigned integer converted from two bytes at a specified position in a byte array.</summary>
      <returns>A 16-bit unsigned integer formed by two bytes beginning at <paramref name="startIndex" />.</returns>
      <param name="value">The array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> equals the length of <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt32(System.Byte[],System.Int32)">
      <summary>Returns a 32-bit unsigned integer converted from four bytes at a specified position in a byte array.</summary>
      <returns>A 32-bit unsigned integer formed by four bytes beginning at <paramref name="startIndex" />.</returns>
      <param name="value">An array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> is greater than or equal to the length of <paramref name="value" /> minus 3, and is less than or equal to the length of <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt64(System.Byte[],System.Int32)">
      <summary>Returns a 64-bit unsigned integer converted from eight bytes at a specified position in a byte array.</summary>
      <returns>A 64-bit unsigned integer formed by the eight bytes beginning at <paramref name="startIndex" />.</returns>
      <param name="value">An array of bytes. </param>
      <param name="startIndex">The starting position within <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> is greater than or equal to the length of <paramref name="value" /> minus 7, and is less than or equal to the length of <paramref name="value" /> minus 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is less than zero or greater than the length of <paramref name="value" /> minus 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Convert">
      <summary>Converts a base data type to another base data type.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type)">
      <summary>Returns an object of the specified type and whose value is equivalent to the specified object.</summary>
      <returns>An object whose type is <paramref name="conversionType" /> and whose value is equivalent to <paramref name="value" />.-or-A null reference (Nothing in Visual Basic), if <paramref name="value" /> is null and <paramref name="conversionType" /> is not a value type. </returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="conversionType">The type of object to return. </param>
      <exception cref="T:System.InvalidCastException">This conversion is not supported.  -or-<paramref name="value" /> is null and <paramref name="conversionType" /> is a value type.-or-<paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in a format recognized by <paramref name="conversionType" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is out of the range of <paramref name="conversionType" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> is null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type,System.IFormatProvider)">
      <summary>Returns an object of the specified type whose value is equivalent to the specified object. A parameter supplies culture-specific formatting information.</summary>
      <returns>An object whose type is <paramref name="conversionType" /> and whose value is equivalent to <paramref name="value" />.-or- <paramref name="value" />, if the <see cref="T:System.Type" /> of <paramref name="value" /> and <paramref name="conversionType" /> are equal.-or- A null reference (Nothing in Visual Basic), if <paramref name="value" /> is null and <paramref name="conversionType" /> is not a value type.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="conversionType">The type of object to return. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.InvalidCastException">This conversion is not supported. -or-<paramref name="value" /> is null and <paramref name="conversionType" /> is a value type.-or-<paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in a format for <paramref name="conversionType" /> recognized by <paramref name="provider" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is out of the range of <paramref name="conversionType" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> is null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.TypeCode,System.IFormatProvider)">
      <summary>Returns an object of the specified type whose value is equivalent to the specified object. A parameter supplies culture-specific formatting information.</summary>
      <returns>An object whose underlying type is <paramref name="typeCode" /> and whose value is equivalent to <paramref name="value" />.-or- A null reference (Nothing in Visual Basic), if <paramref name="value" /> is null and <paramref name="typeCode" /> is <see cref="F:System.TypeCode.Empty" />, <see cref="F:System.TypeCode.String" />, or <see cref="F:System.TypeCode.Object" />.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="typeCode">The type of object to return. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.InvalidCastException">This conversion is not supported.  -or-<paramref name="value" /> is null and <paramref name="typeCode" /> specifies a value type.-or-<paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in a format for the <paramref name="typeCode" /> type recognized by <paramref name="provider" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is out of the range of the <paramref name="typeCode" /> type.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeCode" /> is invalid. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64CharArray(System.Char[],System.Int32,System.Int32)">
      <summary>Converts a subset of a Unicode character array, which encodes binary data as base-64 digits, to an equivalent 8-bit unsigned integer array. Parameters specify the subset in the input array and the number of elements to convert.</summary>
      <returns>An array of 8-bit unsigned integers equivalent to <paramref name="length" /> elements at position <paramref name="offset" /> in <paramref name="inArray" />.</returns>
      <param name="inArray">A Unicode character array. </param>
      <param name="offset">A position within <paramref name="inArray" />. </param>
      <param name="length">The number of elements in <paramref name="inArray" /> to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="length" /> is less than 0.-or- <paramref name="offset" /> plus <paramref name="length" /> indicates a position not within <paramref name="inArray" />. </exception>
      <exception cref="T:System.FormatException">The length of <paramref name="inArray" />, ignoring white-space characters, is not zero or a multiple of 4. -or-The format of <paramref name="inArray" /> is invalid. <paramref name="inArray" /> contains a non-base-64 character, more than two padding characters, or a non-white-space character among the padding characters. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64String(System.String)">
      <summary>Converts the specified string, which encodes binary data as base-64 digits, to an equivalent 8-bit unsigned integer array.</summary>
      <returns>An array of 8-bit unsigned integers that is equivalent to <paramref name="s" />.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">The length of <paramref name="s" />, ignoring white-space characters, is not zero or a multiple of 4. -or-The format of <paramref name="s" /> is invalid. <paramref name="s" /> contains a non-base-64 character, more than two padding characters, or a non-white space-character among the padding characters.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.GetTypeCode(System.Object)">
      <summary>Returns the <see cref="T:System.TypeCode" /> for the specified object.</summary>
      <returns>The <see cref="T:System.TypeCode" /> for <paramref name="value" />, or <see cref="F:System.TypeCode.Empty" /> if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64CharArray(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Converts a subset of an 8-bit unsigned integer array to an equivalent subset of a Unicode character array encoded with base-64 digits. Parameters specify the subsets as offsets in the input and output arrays, and the number of elements in the input array to convert.</summary>
      <returns>A 32-bit signed integer containing the number of bytes in <paramref name="outArray" />.</returns>
      <param name="inArray">An input array of 8-bit unsigned integers. </param>
      <param name="offsetIn">A position within <paramref name="inArray" />. </param>
      <param name="length">The number of elements of <paramref name="inArray" /> to convert. </param>
      <param name="outArray">An output array of Unicode characters. </param>
      <param name="offsetOut">A position within <paramref name="outArray" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> or <paramref name="outArray" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offsetIn" />, <paramref name="offsetOut" />, or <paramref name="length" /> is negative.-or- <paramref name="offsetIn" /> plus <paramref name="length" /> is greater than the length of <paramref name="inArray" />.-or- <paramref name="offsetOut" /> plus the number of elements to return is greater than the length of <paramref name="outArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[])">
      <summary>Converts an array of 8-bit unsigned integers to its equivalent string representation that is encoded with base-64 digits.</summary>
      <returns>The string representation, in base 64, of the contents of <paramref name="inArray" />.</returns>
      <param name="inArray">An array of 8-bit unsigned integers. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[],System.Int32,System.Int32)">
      <summary>Converts a subset of an array of 8-bit unsigned integers to its equivalent string representation that is encoded with base-64 digits. Parameters specify the subset as an offset in the input array, and the number of elements in the array to convert.</summary>
      <returns>The string representation in base 64 of <paramref name="length" /> elements of <paramref name="inArray" />, starting at position <paramref name="offset" />.</returns>
      <param name="inArray">An array of 8-bit unsigned integers. </param>
      <param name="offset">An offset in <paramref name="inArray" />. </param>
      <param name="length">The number of elements of <paramref name="inArray" /> to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="length" /> is negative.-or- <paramref name="offset" /> plus <paramref name="length" /> is greater than the length of <paramref name="inArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Boolean)">
      <summary>Returns the specified Boolean value; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">The Boolean value to return. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object)">
      <summary>Converts the value of a specified object to an equivalent Boolean value.</summary>
      <returns>true or false, which reflects the value returned by invoking the <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> method for the underlying type of <paramref name="value" />. If <paramref name="value" /> is null, the method returns false. </returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is a string that does not equal <see cref="F:System.Boolean.TrueString" /> or <see cref="F:System.Boolean.FalseString" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface.-or-The conversion of <paramref name="value" /> to a <see cref="T:System.Boolean" /> is not supported.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to an equivalent Boolean value, using the specified culture-specific formatting information.</summary>
      <returns>true or false, which reflects the value returned by invoking the <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> method for the underlying type of <paramref name="value" />. If <paramref name="value" /> is null, the method returns false.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is a string that does not equal <see cref="F:System.Boolean.TrueString" /> or <see cref="F:System.Boolean.FalseString" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface.-or-The conversion of <paramref name="value" /> to a <see cref="T:System.Boolean" /> is not supported. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String)">
      <summary>Converts the specified string representation of a logical value to its Boolean equivalent.</summary>
      <returns>true if <paramref name="value" /> equals <see cref="F:System.Boolean.TrueString" />, or false if <paramref name="value" /> equals <see cref="F:System.Boolean.FalseString" /> or null.</returns>
      <param name="value">A string that contains the value of either <see cref="F:System.Boolean.TrueString" /> or <see cref="F:System.Boolean.FalseString" />. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not equal to <see cref="F:System.Boolean.TrueString" /> or <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a logical value to its Boolean equivalent, using the specified culture-specific formatting information.</summary>
      <returns>true if <paramref name="value" /> equals <see cref="F:System.Boolean.TrueString" />, or false if <paramref name="value" /> equals <see cref="F:System.Boolean.FalseString" /> or null.</returns>
      <param name="value">A string that contains the value of either <see cref="F:System.Boolean.TrueString" /> or <see cref="F:System.Boolean.FalseString" />. </param>
      <param name="provider">An object that supplies culture-specific formatting information. This parameter is ignored.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not equal to <see cref="F:System.Boolean.TrueString" /> or <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent Boolean value.</summary>
      <returns>true if <paramref name="value" /> is not zero; otherwise, false.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent 8-bit unsigned integer.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Byte)">
      <summary>Returns the specified 8-bit unsigned integer; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">The 8-bit unsigned integer to return. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Char)">
      <summary>Converts the value of the specified Unicode character to the equivalent 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The Unicode character to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is greater than <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent 8-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 8-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Byte.MaxValue" /> or less than <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent 8-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 8-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Byte.MaxValue" /> or less than <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object)">
      <summary>Converts the value of the specified object to an 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in the property format for a <see cref="T:System.Byte" /> value.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement <see cref="T:System.IConvertible" />. -or-Conversion from <paramref name="value" /> to the <see cref="T:System.Byte" /> type is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to an 8-bit unsigned integer, using the specified culture-specific formatting information.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in the property format for a <see cref="T:System.Byte" /> value.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement <see cref="T:System.IConvertible" />. -or-Conversion from <paramref name="value" /> to the <see cref="T:System.Byte" /> type is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to be converted. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to an equivalent 8-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 8-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">A single-precision floating-point number. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Byte.MaxValue" /> or less than <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent 8-bit unsigned integer, using specified culture-specific formatting information.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.Int32)">
      <summary>Converts the string representation of a number in a specified base to an equivalent 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="fromBase">The base of the number in <paramref name="value" />, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> is not 2, 8, 10, or 16. -or-<paramref name="value" />, which represents a non-base 10 unsigned number, is prefixed with a negative sign. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> is <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contains a character that is not a valid digit in the base specified by <paramref name="fromBase" />. The exception message indicates that there are no digits to convert if the first character in <paramref name="value" /> is invalid; otherwise, the message indicates that <paramref name="value" /> contains invalid trailing characters.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, which represents a base 10 unsigned number, is prefixed with a negative sign.-or-<paramref name="value" /> represents a number that is less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>An 8-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to its equivalent Unicode character.</summary>
      <returns>A Unicode character that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to its equivalent Unicode character.</summary>
      <returns>A Unicode character that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to its equivalent Unicode character.</summary>
      <returns>A Unicode character that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Char.MinValue" /> or greater than <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to its equivalent Unicode character.</summary>
      <returns>A Unicode character that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Char.MinValue" /> or greater than <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object)">
      <summary>Converts the value of the specified object to a Unicode character.</summary>
      <returns>A Unicode character that is equivalent to value, or <see cref="F:System.Char.MinValue" /> if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is a null string.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface.-or-The conversion of <paramref name="value" /> to a <see cref="T:System.Char" /> is not supported. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Char.MinValue" /> or greater than <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to its equivalent Unicode character, using the specified culture-specific formatting information.</summary>
      <returns>A Unicode character that is equivalent to <paramref name="value" />, or <see cref="F:System.Char.MinValue" /> if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is a null string.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion of <paramref name="value" /> to a <see cref="T:System.Char" /> is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Char.MinValue" /> or greater than <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to its equivalent Unicode character.</summary>
      <returns>A Unicode character that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String)">
      <summary>Converts the first character of a specified string to a Unicode character.</summary>
      <returns>A Unicode character that is equivalent to the first and only character in <paramref name="value" />.</returns>
      <param name="value">A string of length 1. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.FormatException">The length of <paramref name="value" /> is not 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String,System.IFormatProvider)">
      <summary>Converts the first character of a specified string to a Unicode character, using specified culture-specific formatting information.</summary>
      <returns>A Unicode character that is equivalent to the first and only character in <paramref name="value" />.</returns>
      <param name="value">A string of length 1 or null. </param>
      <param name="provider">An object that supplies culture-specific formatting information. This parameter is ignored.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.FormatException">The length of <paramref name="value" /> is not 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to its equivalent Unicode character.</summary>
      <returns>A Unicode character that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to its equivalent Unicode character.</summary>
      <returns>A Unicode character that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to its equivalent Unicode character.</summary>
      <returns>A Unicode character that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object)">
      <summary>Converts the value of the specified object to a <see cref="T:System.DateTime" /> object.</summary>
      <returns>The date and time equivalent of the value of <paramref name="value" />, or a date and time equivalent of <see cref="F:System.DateTime.MinValue" /> if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not a valid date and time value.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to a <see cref="T:System.DateTime" /> object, using the specified culture-specific formatting information.</summary>
      <returns>The date and time equivalent of the value of <paramref name="value" />, or the date and time equivalent of <see cref="F:System.DateTime.MinValue" /> if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not a valid date and time value.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String)">
      <summary>Converts the specified string representation of a date and time to an equivalent date and time value.</summary>
      <returns>The date and time equivalent of the value of <paramref name="value" />, or the date and time equivalent of <see cref="F:System.DateTime.MinValue" /> if <paramref name="value" /> is null.</returns>
      <param name="value">The string representation of a date and time.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not a properly formatted date and time string. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent date and time, using the specified culture-specific formatting information.</summary>
      <returns>The date and time equivalent of the value of <paramref name="value" />, or the date and time equivalent of <see cref="F:System.DateTime.MinValue" /> if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains a date and time to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not a properly formatted date and time string. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent decimal number.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent decimal number.</summary>
      <returns>The decimal number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Decimal)">
      <summary>Returns the specified decimal number; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">A decimal number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent decimal number.</summary>
      <returns>A decimal number that is equivalent to <paramref name="value" />. </returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Decimal.MaxValue" /> or less than <see cref="F:System.Decimal.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent decimal number.</summary>
      <returns>A decimal number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent decimal number.</summary>
      <returns>A decimal number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent decimal number.</summary>
      <returns>A decimal number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object)">
      <summary>Converts the value of the specified object to an equivalent decimal number.</summary>
      <returns>A decimal number that is equivalent to <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format for a <see cref="T:System.Decimal" /> type.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to an equivalent decimal number, using the specified culture-specific formatting information.</summary>
      <returns>A decimal number that is equivalent to <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format for a <see cref="T:System.Decimal" /> type.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface.-or-The conversion is not supported. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to the equivalent decimal number.</summary>
      <returns>A decimal number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to the equivalent decimal number.</summary>
      <returns>A decimal number that is equivalent to <paramref name="value" />. </returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Decimal.MaxValue" /> or less than <see cref="F:System.Decimal.MinValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent decimal number.</summary>
      <returns>A decimal number that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains a number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not a number in a valid format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent decimal number, using the specified culture-specific formatting information.</summary>
      <returns>A decimal number that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains a number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not a number in a valid format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to an equivalent decimal number.</summary>
      <returns>The decimal number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent decimal number.</summary>
      <returns>A decimal number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent decimal number.</summary>
      <returns>A decimal number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent double-precision floating-point number.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent double-precision floating-point number.</summary>
      <returns>The double-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The decimal number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Double)">
      <summary>Returns the specified double-precision floating-point number; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">The double-precision floating-point number to return. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object)">
      <summary>Converts the value of the specified object to a double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format for a <see cref="T:System.Double" /> type.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to an double-precision floating-point number, using the specified culture-specific formatting information.</summary>
      <returns>A double-precision floating-point number that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format for a <see cref="T:System.Double" /> type.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to the equivalent double-precision floating-point number.</summary>
      <returns>The 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to an equivalent double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The single-precision floating-point number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not a number in a valid format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent double-precision floating-point number, using the specified culture-specific formatting information.</summary>
      <returns>A double-precision floating-point number that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not a number in a valid format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to the equivalent double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent 16-bit signed integer.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 16-bit signed integer.</summary>
      <returns>A 16-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Char)">
      <summary>Converts the value of the specified Unicode character to the equivalent 16-bit signed integer.</summary>
      <returns>A 16-bit signed integer that is equivalent to <paramref name="value" />. </returns>
      <param name="value">The Unicode character to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent 16-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 16-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The decimal number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" /> or less than <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent 16-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 16-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" /> or less than <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int16)">
      <summary>Returns the specified 16-bit signed integer; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">The 16-bit signed integer to return. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent 16-bit signed integer.</summary>
      <returns>The 16-bit signed integer equivalent of <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" /> or less than <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 16-bit signed integer.</summary>
      <returns>A 16-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" /> or less than <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object)">
      <summary>Converts the value of the specified object to a 16-bit signed integer.</summary>
      <returns>A 16-bit signed integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format for an <see cref="T:System.Int16" /> type.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to a 16-bit signed integer, using the specified culture-specific formatting information.</summary>
      <returns>A 16-bit signed integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format for an <see cref="T:System.Int16" /> type.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to the equivalent 16-bit signed integer.</summary>
      <returns>A 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to an equivalent 16-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 16-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" /> or less than <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent 16-bit signed integer.</summary>
      <returns>A 16-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent 16-bit signed integer, using the specified culture-specific formatting information.</summary>
      <returns>A 16-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.Int32)">
      <summary>Converts the string representation of a number in a specified base to an equivalent 16-bit signed integer.</summary>
      <returns>A 16-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="fromBase">The base of the number in <paramref name="value" />, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> is not 2, 8, 10, or 16. -or-<paramref name="value" />, which represents a non-base 10 signed number, is prefixed with a negative sign. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> is <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contains a character that is not a valid digit in the base specified by <paramref name="fromBase" />. The exception message indicates that there are no digits to convert if the first character in <paramref name="value" /> is invalid; otherwise, the message indicates that <paramref name="value" /> contains invalid trailing characters.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, which represents a non-base 10 signed number, is prefixed with a negative sign.-or-<paramref name="value" /> represents a number that is less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to the equivalent 16-bit signed integer.</summary>
      <returns>A 16-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 16-bit signed integer.</summary>
      <returns>A 16-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 16-bit signed integer.</summary>
      <returns>A 16-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent 32-bit signed integer.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Char)">
      <summary>Converts the value of the specified Unicode character to the equivalent 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The Unicode character to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent 32-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 32-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The decimal number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int32.MaxValue" /> or less than <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent 32-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 32-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int32.MaxValue" /> or less than <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int32)">
      <summary>Returns the specified 32-bit signed integer; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">The 32-bit signed integer to return. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int32.MaxValue" /> or less than <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object)">
      <summary>Converts the value of the specified object to a 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the  <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to a 32-bit signed integer, using the specified culture-specific formatting information.</summary>
      <returns>A 32-bit signed integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to the equivalent 32-bit signed integer.</summary>
      <returns>A 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to an equivalent 32-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 32-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int32.MaxValue" /> or less than <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent 32-bit signed integer, using the specified culture-specific formatting information.</summary>
      <returns>A 32-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.Int32)">
      <summary>Converts the string representation of a number in a specified base to an equivalent 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="fromBase">The base of the number in <paramref name="value" />, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> is not 2, 8, 10, or 16. -or-<paramref name="value" />, which represents a non-base 10 signed number, is prefixed with a negative sign. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> is <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contains a character that is not a valid digit in the base specified by <paramref name="fromBase" />. The exception message indicates that there are no digits to convert if the first character in <paramref name="value" /> is invalid; otherwise, the message indicates that <paramref name="value" /> contains invalid trailing characters.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, which represents a non-base 10 signed number, is prefixed with a negative sign.-or-<paramref name="value" /> represents a number that is less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to the equivalent 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent 64-bit signed integer.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Char)">
      <summary>Converts the value of the specified Unicode character to the equivalent 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The Unicode character to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent 64-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 64-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The decimal number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int64.MaxValue" /> or less than <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent 64-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 64-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int64.MaxValue" /> or less than <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int64)">
      <summary>Returns the specified 64-bit signed integer; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">A 64-bit signed integer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object)">
      <summary>Converts the value of the specified object to a 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to a 64-bit signed integer, using the specified culture-specific formatting information.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface.-or-The conversion is not supported. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to the equivalent 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to an equivalent 64-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 64-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int64.MaxValue" /> or less than <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains a number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent 64-bit signed integer, using the specified culture-specific formatting information.</summary>
      <returns>A 64-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.Int32)">
      <summary>Converts the string representation of a number in a specified base to an equivalent 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="fromBase">The base of the number in <paramref name="value" />, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> is not 2, 8, 10, or 16. -or-<paramref name="value" />, which represents a non-base 10 signed number, is prefixed with a negative sign. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> is <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contains a character that is not a valid digit in the base specified by <paramref name="fromBase" />. The exception message indicates that there are no digits to convert if the first character in <paramref name="value" /> is invalid; otherwise, the message indicates that <paramref name="value" /> contains invalid trailing characters.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, which represents a non-base 10 signed number, is prefixed with a negative sign.-or-<paramref name="value" /> represents a number that is less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to the equivalent 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent 8-bit signed integer.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Char)">
      <summary>Converts the value of the specified Unicode character to the equivalent 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The Unicode character to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent 8-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 8-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The decimal number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" /> or less than <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent 8-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 8-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" /> or less than <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" /> or less than <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" /> or less than <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" /> or less than <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object)">
      <summary>Converts the value of the specified object to an 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to an 8-bit signed integer, using the specified culture-specific formatting information.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.SByte)">
      <summary>Returns the specified 8-bit signed integer; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">The 8-bit signed integer to return. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to an equivalent 8-bit signed integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 8-bit signed integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" /> or less than <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if value is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent 8-bit signed integer, using the specified culture-specific formatting information.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.Int32)">
      <summary>Converts the string representation of a number in a specified base to an equivalent 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="fromBase">The base of the number in <paramref name="value" />, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> is not 2, 8, 10, or 16. -or-<paramref name="value" />, which represents a non-base 10 signed number, is prefixed with a negative sign. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> is <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contains a character that is not a valid digit in the base specified by <paramref name="fromBase" />. The exception message indicates that there are no digits to convert if the first character in <paramref name="value" /> is invalid; otherwise, the message indicates that <paramref name="value" /> contains invalid trailing characters.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, which represents a non-base 10 signed number, is prefixed with a negative sign.-or-<paramref name="value" /> represents a number that is less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to the equivalent 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" /> or less than <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" /> or less than <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent single-precision floating-point number.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />.<paramref name="value" /> is rounded using rounding to nearest. For example, when rounded to two decimals, the value 2.345 becomes 2.34 and the value 2.355 becomes 2.36.</returns>
      <param name="value">The decimal number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />.<paramref name="value" /> is rounded using rounding to nearest. For example, when rounded to two decimals, the value 2.345 becomes 2.34 and the value 2.355 becomes 2.36.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object)">
      <summary>Converts the value of the specified object to a single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to an single-precision floating-point number, using the specified culture-specific formatting information.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to the equivalent single-precision floating-point number.</summary>
      <returns>An 8-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Single)">
      <summary>Returns the specified single-precision floating-point number; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">The single-precision floating-point number to return. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not a number in a valid format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent single-precision floating-point number, using the specified culture-specific formatting information.</summary>
      <returns>A single-precision floating-point number that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not a number in a valid format.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to the equivalent single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean)">
      <summary>Converts the specified Boolean value to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean,System.IFormatProvider)">
      <summary>Converts the specified Boolean value to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The Boolean value to convert. </param>
      <param name="provider">An instance of an object. This parameter is ignored.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.IFormatProvider)">
      <summary>Converts the value of the specified 8-bit unsigned integer to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.Int32)">
      <summary>Converts the value of an 8-bit unsigned integer to its equivalent string representation in a specified base.</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <param name="toBase">The base of the return value, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> is not 2, 8, 10, or 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char)">
      <summary>Converts the value of the specified Unicode character to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The Unicode character to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char,System.IFormatProvider)">
      <summary>Converts the value of the specified Unicode character to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The Unicode character to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. This parameter is ignored. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime)">
      <summary>Converts the value of the specified <see cref="T:System.DateTime" /> to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The date and time value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime,System.IFormatProvider)">
      <summary>Converts the value of the specified <see cref="T:System.DateTime" /> to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The date and time value to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal)">
      <summary>Converts the value of the specified decimal number to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The decimal number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal,System.IFormatProvider)">
      <summary>Converts the value of the specified decimal number to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The decimal number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double,System.IFormatProvider)">
      <summary>Converts the value of the specified double-precision floating-point number to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.IFormatProvider)">
      <summary>Converts the value of the specified 16-bit signed integer to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.Int32)">
      <summary>Converts the value of a 16-bit signed integer to its equivalent string representation in a specified base.</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <param name="toBase">The base of the return value, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> is not 2, 8, 10, or 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.IFormatProvider)">
      <summary>Converts the value of the specified 32-bit signed integer to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.Int32)">
      <summary>Converts the value of a 32-bit signed integer to its equivalent string representation in a specified base.</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <param name="toBase">The base of the return value, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> is not 2, 8, 10, or 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.IFormatProvider)">
      <summary>Converts the value of the specified 64-bit signed integer to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.Int32)">
      <summary>Converts the value of a 64-bit signed integer to its equivalent string representation in a specified base.</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <param name="toBase">The base of the return value, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> is not 2, 8, 10, or 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object)">
      <summary>Converts the value of the specified object to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />, or <see cref="F:System.String.Empty" /> if <paramref name="value" /> is an object whose value is null. If <paramref name="value" /> is null, the method returns null.</returns>
      <param name="value">An object that supplies the value to convert, or null. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to its equivalent string representation using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />, or <see cref="F:System.String.Empty" /> if <paramref name="value" /> is an object whose value is null. If <paramref name="value" /> is null, the method returns null. </returns>
      <param name="value">An object that supplies the value to convert, or null. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte,System.IFormatProvider)">
      <summary>Converts the value of the specified 8-bit signed integer to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single,System.IFormatProvider)">
      <summary>Converts the value of the specified single-precision floating-point number to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16,System.IFormatProvider)">
      <summary>Converts the value of the specified 16-bit unsigned integer to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32,System.IFormatProvider)">
      <summary>Converts the value of the specified 32-bit unsigned integer to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to its equivalent string representation.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64,System.IFormatProvider)">
      <summary>Converts the value of the specified 64-bit unsigned integer to its equivalent string representation, using the specified culture-specific formatting information.</summary>
      <returns>The string representation of <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent 16-bit unsigned integer.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 16-bit unsigned integer.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Char)">
      <summary>Converts the value of the specified Unicode character to the equivalent 16-bit unsigned integer.</summary>
      <returns>The 16-bit unsigned integer equivalent to <paramref name="value" />.</returns>
      <param name="value">The Unicode character to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent 16-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 16-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The decimal number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent 16-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 16-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 16-bit unsigned integer.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent 16-bit unsigned integer.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 16-bit unsigned integer.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object)">
      <summary>Converts the value of the specified object to a 16-bit unsigned integer.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the  <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to a 16-bit unsigned integer, using the specified culture-specific formatting information.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the  <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to the equivalent 16-bit unsigned integer.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to an equivalent 16-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 16-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent 16-bit unsigned integer.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent 16-bit unsigned integer, using the specified culture-specific formatting information.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.Int32)">
      <summary>Converts the string representation of a number in a specified base to an equivalent 16-bit unsigned integer.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="fromBase">The base of the number in <paramref name="value" />, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> is not 2, 8, 10, or 16. -or-<paramref name="value" />, which represents a non-base 10 unsigned number, is prefixed with a negative sign. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> is <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contains a character that is not a valid digit in the base specified by <paramref name="fromBase" />. The exception message indicates that there are no digits to convert if the first character in <paramref name="value" /> is invalid; otherwise, the message indicates that <paramref name="value" /> contains invalid trailing characters.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, which represents a non-base 10 unsigned number, is prefixed with a negative sign.-or-<paramref name="value" /> represents a number that is less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt16)">
      <summary>Returns the specified 16-bit unsigned integer; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">The 16-bit unsigned integer to return. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 16-bit unsigned integer.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 16-bit unsigned integer.</summary>
      <returns>A 16-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent 32-bit unsigned integer.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Char)">
      <summary>Converts the value of the specified Unicode character to the equivalent 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The Unicode character to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent 32-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 32-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The decimal number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent 32-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 32-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object)">
      <summary>Converts the value of the specified object to a 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to a 32-bit unsigned integer, using the specified culture-specific formatting information.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to the equivalent 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to an equivalent 32-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 32-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent 32-bit unsigned integer, using the specified culture-specific formatting information.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.Int32)">
      <summary>Converts the string representation of a number in a specified base to an equivalent 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="fromBase">The base of the number in <paramref name="value" />, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> is not 2, 8, 10, or 16. -or-<paramref name="value" />, which represents a non-base 10 unsigned number, is prefixed with a negative sign. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> is <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contains a character that is not a valid digit in the base specified by <paramref name="fromBase" />. The exception message indicates that there are no digits to convert if the first character in <paramref name="value" /> is invalid; otherwise, the message indicates that <paramref name="value" /> contains invalid trailing characters.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, which represents a non-base 10 unsigned number, is prefixed with a negative sign.-or-<paramref name="value" /> represents a number that is less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to the equivalent 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt32)">
      <summary>Returns the specified 32-bit unsigned integer; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">The 32-bit unsigned integer to return. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 32-bit unsigned integer.</summary>
      <returns>A 32-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit unsigned integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Boolean)">
      <summary>Converts the specified Boolean value to the equivalent 64-bit unsigned integer.</summary>
      <returns>The number 1 if <paramref name="value" /> is true; otherwise, 0.</returns>
      <param name="value">The Boolean value to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 64-bit unsigned integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Char)">
      <summary>Converts the value of the specified Unicode character to the equivalent 64-bit unsigned integer.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The Unicode character to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Decimal)">
      <summary>Converts the value of the specified decimal number to an equivalent 64-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 64-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The decimal number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Double)">
      <summary>Converts the value of the specified double-precision floating-point number to an equivalent 64-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 64-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The double-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 64-bit unsigned integer.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int32)">
      <summary>Converts the value of the specified 32-bit signed integer to an equivalent 64-bit unsigned integer.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 64-bit unsigned integer.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 64-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object)">
      <summary>Converts the value of the specified object to a 64-bit unsigned integer.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface, or null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object,System.IFormatProvider)">
      <summary>Converts the value of the specified object to a 64-bit unsigned integer, using the specified culture-specific formatting information.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to <paramref name="value" />, or zero if <paramref name="value" /> is null.</returns>
      <param name="value">An object that implements the <see cref="T:System.IConvertible" /> interface. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in an appropriate format.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> does not implement the <see cref="T:System.IConvertible" /> interface. -or-The conversion is not supported.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.SByte)">
      <summary>Converts the value of the specified 8-bit signed integer to the equivalent 64-bit unsigned integer.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 8-bit signed integer to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Single)">
      <summary>Converts the value of the specified single-precision floating-point number to an equivalent 64-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" />, rounded to the nearest 64-bit unsigned integer. If <paramref name="value" /> is halfway between two whole numbers, the even number is returned; that is, 4.5 is converted to 4, and 5.5 is converted to 6.</returns>
      <param name="value">The single-precision floating-point number to convert. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than zero or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String)">
      <summary>Converts the specified string representation of a number to an equivalent 64-bit unsigned integer.</summary>
      <returns>A 64-bit signed integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.IFormatProvider)">
      <summary>Converts the specified string representation of a number to an equivalent 64-bit unsigned integer, using the specified culture-specific formatting information.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="provider">An object that supplies culture-specific formatting information. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not consist of an optional sign followed by a sequence of digits (0 through 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> represents a number that is less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.Int32)">
      <summary>Converts the string representation of a number in a specified base to an equivalent 64-bit unsigned integer.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to the number in <paramref name="value" />, or 0 (zero) if <paramref name="value" /> is null.</returns>
      <param name="value">A string that contains the number to convert. </param>
      <param name="fromBase">The base of the number in <paramref name="value" />, which must be 2, 8, 10, or 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> is not 2, 8, 10, or 16. -or-<paramref name="value" />, which represents a non-base 10 unsigned number, is prefixed with a negative sign. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> is <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contains a character that is not a valid digit in the base specified by <paramref name="fromBase" />. The exception message indicates that there are no digits to convert if the first character in <paramref name="value" /> is invalid; otherwise, the message indicates that <paramref name="value" /> contains invalid trailing characters.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, which represents a non-base 10 unsigned number, is prefixed with a negative sign.-or-<paramref name="value" /> represents a number that is less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to the equivalent 64-bit unsigned integer.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 16-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 64-bit unsigned integer.</summary>
      <returns>A 64-bit unsigned integer that is equivalent to <paramref name="value" />.</returns>
      <param name="value">The 32-bit unsigned integer to convert. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt64)">
      <summary>Returns the specified 64-bit unsigned integer; no actual conversion is performed.</summary>
      <returns>
        <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">The 64-bit unsigned integer to return. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Environment">
      <summary>Provides information about, and means to manipulate, the current environment and platform. This class cannot be inherited.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.CurrentManagedThreadId">
      <summary>Gets a unique identifier for the current managed thread.</summary>
      <returns>An integer that represents a unique identifier for this managed thread.</returns>
    </member>
    <member name="M:System.Environment.ExpandEnvironmentVariables(System.String)">
      <summary>Replaces the name of each environment variable embedded in the specified string with the string equivalent of the value of the variable, then returns the resulting string.</summary>
      <returns>A string with each environment variable replaced by its value.</returns>
      <param name="name">A string containing the names of zero or more environment variables. Each environment variable is quoted with the percent sign character (%).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.FailFast(System.String)">
      <summary>Immediately terminates a process after writing a message to the Windows Application event log, and then includes the message in error reporting to Microsoft.</summary>
      <param name="message">A message that explains why the process was terminated, or null if no explanation is provided.</param>
    </member>
    <member name="M:System.Environment.FailFast(System.String,System.Exception)">
      <summary>Immediately terminates a process after writing a message to the Windows Application event log, and then includes the message and exception information in error reporting to Microsoft.</summary>
      <param name="message">A message that explains why the process was terminated, or null if no explanation is provided.</param>
      <param name="exception">An exception that represents the error that caused the termination. This is typically the exception in a catch block.</param>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariable(System.String)">
      <summary>Retrieves the value of an environment variable from the current process. </summary>
      <returns>The value of the environment variable specified by <paramref name="variable" />, or null if the environment variable is not found.</returns>
      <param name="variable">The name of the environment variable.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariables">
      <summary>Retrieves all environment variable names and their values from the current process.</summary>
      <returns>A dictionary that contains all environment variable names and their values; otherwise, an empty dictionary if no environment variables are found.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <exception cref="T:System.OutOfMemoryException">The buffer is out of memory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.HasShutdownStarted">
      <summary>Gets a value that indicates whether the current application domain is being unloaded or the common language runtime (CLR) is shutting down. </summary>
      <returns>true if the current application domain is being unloaded or the CLR is shutting down; otherwise, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.NewLine">
      <summary>Gets the newline string defined for this environment.</summary>
      <returns>A string containing "\r\n" for non-Unix platforms, or a string containing "\n" for Unix platforms.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.ProcessorCount">
      <summary>Gets the number of processors on the current machine.</summary>
      <returns>The 32-bit signed integer that specifies the number of processors on the current machine. There is no default. If the current machine contains multiple processor groups, this property returns the number of logical processors that are available for use by the common language runtime (CLR).</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="NUMBER_OF_PROCESSORS" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.SetEnvironmentVariable(System.String,System.String)">
      <summary>Creates, modifies, or deletes an environment variable stored in the current process.</summary>
      <param name="variable">The name of an environment variable.</param>
      <param name="value">A value to assign to <paramref name="variable" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="variable" /> contains a zero-length string, an initial hexadecimal zero character (0x00), or an equal sign ("="). -or-The length of <paramref name="variable" /> or <paramref name="value" /> is greater than or equal to 32,767 characters.-or-An error occurred during the execution of this operation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.StackTrace">
      <summary>Gets current stack trace information.</summary>
      <returns>A string containing stack trace information. This value can be <see cref="F:System.String.Empty" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack trace information is out of range.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.TickCount">
      <summary>Gets the number of milliseconds elapsed since the system started.</summary>
      <returns>A 32-bit signed integer containing the amount of time in milliseconds that has passed since the last time the computer was started. </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Math">
      <summary>Provides constants and static methods for trigonometric, logarithmic, and other common mathematical functions.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Decimal)">
      <summary>Returns the absolute value of a <see cref="T:System.Decimal" /> number.</summary>
      <returns>A decimal number, x, such that 0 ≤ x ≤<see cref="F:System.Decimal.MaxValue" />.</returns>
      <param name="value">A number that is greater than or equal to <see cref="F:System.Decimal.MinValue" />, but less than or equal to <see cref="F:System.Decimal.MaxValue" />. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Double)">
      <summary>Returns the absolute value of a double-precision floating-point number.</summary>
      <returns>A double-precision floating-point number, x, such that 0 ≤ x ≤<see cref="F:System.Double.MaxValue" />.</returns>
      <param name="value">A number that is greater than or equal to <see cref="F:System.Double.MinValue" />, but less than or equal to <see cref="F:System.Double.MaxValue" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int16)">
      <summary>Returns the absolute value of a 16-bit signed integer.</summary>
      <returns>A 16-bit signed integer, x, such that 0 ≤ x ≤<see cref="F:System.Int16.MaxValue" />.</returns>
      <param name="value">A number that is greater than <see cref="F:System.Int16.MinValue" />, but less than or equal to <see cref="F:System.Int16.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> equals <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int32)">
      <summary>Returns the absolute value of a 32-bit signed integer.</summary>
      <returns>A 32-bit signed integer, x, such that 0 ≤ x ≤<see cref="F:System.Int32.MaxValue" />.</returns>
      <param name="value">A number that is greater than <see cref="F:System.Int32.MinValue" />, but less than or equal to <see cref="F:System.Int32.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> equals <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int64)">
      <summary>Returns the absolute value of a 64-bit signed integer.</summary>
      <returns>A 64-bit signed integer, x, such that 0 ≤ x ≤<see cref="F:System.Int64.MaxValue" />.</returns>
      <param name="value">A number that is greater than <see cref="F:System.Int64.MinValue" />, but less than or equal to <see cref="F:System.Int64.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> equals <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.SByte)">
      <summary>Returns the absolute value of an 8-bit signed integer.</summary>
      <returns>An 8-bit signed integer, x, such that 0 ≤ x ≤<see cref="F:System.SByte.MaxValue" />.</returns>
      <param name="value">A number that is greater than <see cref="F:System.SByte.MinValue" />, but less than or equal to <see cref="F:System.SByte.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> equals <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Single)">
      <summary>Returns the absolute value of a single-precision floating-point number.</summary>
      <returns>A single-precision floating-point number, x, such that 0 ≤ x ≤<see cref="F:System.Single.MaxValue" />.</returns>
      <param name="value">A number that is greater than or equal to <see cref="F:System.Single.MinValue" />, but less than or equal to <see cref="F:System.Single.MaxValue" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Acos(System.Double)">
      <summary>Returns the angle whose cosine is the specified number.</summary>
      <returns>An angle, θ, measured in radians, such that 0 ≤θ≤π-or- <see cref="F:System.Double.NaN" /> if <paramref name="d" /> &lt; -1 or <paramref name="d" /> &gt; 1 or <paramref name="d" /> equals <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">A number representing a cosine, where <paramref name="d" /> must be greater than or equal to -1, but less than or equal to 1. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Asin(System.Double)">
      <summary>Returns the angle whose sine is the specified number.</summary>
      <returns>An angle, θ, measured in radians, such that -π/2 ≤θ≤π/2 -or- <see cref="F:System.Double.NaN" /> if <paramref name="d" /> &lt; -1 or <paramref name="d" /> &gt; 1 or <paramref name="d" /> equals <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">A number representing a sine, where <paramref name="d" /> must be greater than or equal to -1, but less than or equal to 1. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan(System.Double)">
      <summary>Returns the angle whose tangent is the specified number.</summary>
      <returns>An angle, θ, measured in radians, such that -π/2 ≤θ≤π/2.-or- <see cref="F:System.Double.NaN" /> if <paramref name="d" /> equals <see cref="F:System.Double.NaN" />, -π/2 rounded to double precision (-1.5707963267949) if <paramref name="d" /> equals <see cref="F:System.Double.NegativeInfinity" />, or π/2 rounded to double precision (1.5707963267949) if <paramref name="d" /> equals <see cref="F:System.Double.PositiveInfinity" />.</returns>
      <param name="d">A number representing a tangent. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan2(System.Double,System.Double)">
      <summary>Returns the angle whose tangent is the quotient of two specified numbers.</summary>
      <returns>An angle, θ, measured in radians, such that -π≤θ≤π, and tan(θ) = <paramref name="y" /> / <paramref name="x" />, where (<paramref name="x" />, <paramref name="y" />) is a point in the Cartesian plane. Observe the following: For (<paramref name="x" />, <paramref name="y" />) in quadrant 1, 0 &lt; θ &lt; π/2.For (<paramref name="x" />, <paramref name="y" />) in quadrant 2, π/2 &lt; θ≤π.For (<paramref name="x" />, <paramref name="y" />) in quadrant 3, -π &lt; θ &lt; -π/2.For (<paramref name="x" />, <paramref name="y" />) in quadrant 4, -π/2 &lt; θ &lt; 0.For points on the boundaries of the quadrants, the return value is the following:If y is 0 and x is not negative, θ = 0.If y is 0 and x is negative, θ = π.If y is positive and x is 0, θ = π/2.If y is negative and x is 0, θ = -π/2.If y is 0 and x is 0, θ = 0. If <paramref name="x" /> or <paramref name="y" /> is <see cref="F:System.Double.NaN" />, or if <paramref name="x" /> and <paramref name="y" /> are either <see cref="F:System.Double.PositiveInfinity" /> or <see cref="F:System.Double.NegativeInfinity" />, the method returns <see cref="F:System.Double.NaN" />.</returns>
      <param name="y">The y coordinate of a point. </param>
      <param name="x">The x coordinate of a point. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Decimal)">
      <summary>Returns the smallest integral value that is greater than or equal to the specified decimal number.</summary>
      <returns>The smallest integral value that is greater than or equal to <paramref name="d" />. Note that this method returns a <see cref="T:System.Decimal" /> instead of an integral type.</returns>
      <param name="d">A decimal number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Double)">
      <summary>Returns the smallest integral value that is greater than or equal to the specified double-precision floating-point number.</summary>
      <returns>The smallest integral value that is greater than or equal to <paramref name="a" />. If <paramref name="a" /> is equal to <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" />, or <see cref="F:System.Double.PositiveInfinity" />, that value is returned. Note that this method returns a <see cref="T:System.Double" /> instead of an integral type.</returns>
      <param name="a">A double-precision floating-point number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cos(System.Double)">
      <summary>Returns the cosine of the specified angle.</summary>
      <returns>The cosine of <paramref name="d" />. If <paramref name="d" /> is equal to <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" />, or <see cref="F:System.Double.PositiveInfinity" />, this method returns <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">An angle, measured in radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cosh(System.Double)">
      <summary>Returns the hyperbolic cosine of the specified angle.</summary>
      <returns>The hyperbolic cosine of <paramref name="value" />. If <paramref name="value" /> is equal to <see cref="F:System.Double.NegativeInfinity" /> or <see cref="F:System.Double.PositiveInfinity" />, <see cref="F:System.Double.PositiveInfinity" /> is returned. If <paramref name="value" /> is equal to <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NaN" /> is returned.</returns>
      <param name="value">An angle, measured in radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.E">
      <summary>Represents the natural logarithmic base, specified by the constant, e.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Exp(System.Double)">
      <summary>Returns e raised to the specified power.</summary>
      <returns>The number e raised to the power <paramref name="d" />. If <paramref name="d" /> equals <see cref="F:System.Double.NaN" /> or <see cref="F:System.Double.PositiveInfinity" />, that value is returned. If <paramref name="d" /> equals <see cref="F:System.Double.NegativeInfinity" />, 0 is returned.</returns>
      <param name="d">A number specifying a power. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Decimal)">
      <summary>Returns the largest integer less than or equal to the specified decimal number.</summary>
      <returns>The largest integer less than or equal to <paramref name="d" />.  Note that the method returns an integral value of type <see cref="T:System.Math" />. </returns>
      <param name="d">A decimal number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Double)">
      <summary>Returns the largest integer less than or equal to the specified double-precision floating-point number.</summary>
      <returns>The largest integer less than or equal to <paramref name="d" />. If <paramref name="d" /> is equal to <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" />, or <see cref="F:System.Double.PositiveInfinity" />, that value is returned.</returns>
      <param name="d">A double-precision floating-point number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.IEEERemainder(System.Double,System.Double)">
      <summary>Returns the remainder resulting from the division of a specified number by another specified number.</summary>
      <returns>A number equal to <paramref name="x" /> - (<paramref name="y" /> Q), where Q is the quotient of <paramref name="x" /> / <paramref name="y" /> rounded to the nearest integer (if <paramref name="x" /> / <paramref name="y" /> falls halfway between two integers, the even integer is returned).If <paramref name="x" /> - (<paramref name="y" /> Q) is zero, the value +0 is returned if <paramref name="x" /> is positive, or -0 if <paramref name="x" /> is negative.If <paramref name="y" /> = 0, <see cref="F:System.Double.NaN" /> is returned.</returns>
      <param name="x">A dividend. </param>
      <param name="y">A divisor. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double)">
      <summary>Returns the natural (base e) logarithm of a specified number.</summary>
      <returns>One of the values in the following table. <paramref name="d" /> parameterReturn value Positive The natural logarithm of <paramref name="d" />; that is, ln <paramref name="d" />, or log e<paramref name="d" />Zero <see cref="F:System.Double.NegativeInfinity" />Negative <see cref="F:System.Double.NaN" />Equal to <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Equal to <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">The number whose logarithm is to be found. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double,System.Double)">
      <summary>Returns the logarithm of a specified number in a specified base.</summary>
      <returns>One of the values in the following table. (+Infinity denotes <see cref="F:System.Double.PositiveInfinity" />, -Infinity denotes <see cref="F:System.Double.NegativeInfinity" />, and NaN denotes <see cref="F:System.Double.NaN" />.)<paramref name="a" /><paramref name="newBase" />Return value<paramref name="a" />&gt; 0(0 &lt;<paramref name="newBase" />&lt; 1) -or-(<paramref name="newBase" />&gt; 1)lognewBase(a)<paramref name="a" />&lt; 0(any value)NaN(any value)<paramref name="newBase" />&lt; 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = +InfinityNaN<paramref name="a" /> = NaN(any value)NaN(any value)<paramref name="newBase" /> = NaNNaN(any value)<paramref name="newBase" /> = 1NaN<paramref name="a" /> = 00 &lt;<paramref name="newBase" />&lt; 1 +Infinity<paramref name="a" /> = 0<paramref name="newBase" />&gt; 1-Infinity<paramref name="a" /> =  +Infinity0 &lt;<paramref name="newBase" />&lt; 1-Infinity<paramref name="a" /> =  +Infinity<paramref name="newBase" />&gt; 1+Infinity<paramref name="a" /> = 1<paramref name="newBase" /> = 00<paramref name="a" /> = 1<paramref name="newBase" /> = +Infinity0</returns>
      <param name="a">The number whose logarithm is to be found. </param>
      <param name="newBase">The base of the logarithm. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log10(System.Double)">
      <summary>Returns the base 10 logarithm of a specified number.</summary>
      <returns>One of the values in the following table. <paramref name="d" /> parameter Return value Positive The base 10 log of <paramref name="d" />; that is, log 10<paramref name="d" />. Zero <see cref="F:System.Double.NegativeInfinity" />Negative <see cref="F:System.Double.NaN" />Equal to <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Equal to <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">A number whose logarithm is to be found. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Byte,System.Byte)">
      <summary>Returns the larger of two 8-bit unsigned integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger.</returns>
      <param name="val1">The first of two 8-bit unsigned integers to compare. </param>
      <param name="val2">The second of two 8-bit unsigned integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Decimal,System.Decimal)">
      <summary>Returns the larger of two decimal numbers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger.</returns>
      <param name="val1">The first of two decimal numbers to compare. </param>
      <param name="val2">The second of two decimal numbers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Double,System.Double)">
      <summary>Returns the larger of two double-precision floating-point numbers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger. If <paramref name="val1" />, <paramref name="val2" />, or both <paramref name="val1" /> and <paramref name="val2" /> are equal to <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NaN" /> is returned.</returns>
      <param name="val1">The first of two double-precision floating-point numbers to compare. </param>
      <param name="val2">The second of two double-precision floating-point numbers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int16,System.Int16)">
      <summary>Returns the larger of two 16-bit signed integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger.</returns>
      <param name="val1">The first of two 16-bit signed integers to compare. </param>
      <param name="val2">The second of two 16-bit signed integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int32,System.Int32)">
      <summary>Returns the larger of two 32-bit signed integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger.</returns>
      <param name="val1">The first of two 32-bit signed integers to compare. </param>
      <param name="val2">The second of two 32-bit signed integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int64,System.Int64)">
      <summary>Returns the larger of two 64-bit signed integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger.</returns>
      <param name="val1">The first of two 64-bit signed integers to compare. </param>
      <param name="val2">The second of two 64-bit signed integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.SByte,System.SByte)">
      <summary>Returns the larger of two 8-bit signed integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger.</returns>
      <param name="val1">The first of two 8-bit signed integers to compare. </param>
      <param name="val2">The second of two 8-bit signed integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Single,System.Single)">
      <summary>Returns the larger of two single-precision floating-point numbers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger. If <paramref name="val1" />, or <paramref name="val2" />, or both <paramref name="val1" /> and <paramref name="val2" /> are equal to <see cref="F:System.Single.NaN" />, <see cref="F:System.Single.NaN" /> is returned.</returns>
      <param name="val1">The first of two single-precision floating-point numbers to compare. </param>
      <param name="val2">The second of two single-precision floating-point numbers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt16,System.UInt16)">
      <summary>Returns the larger of two 16-bit unsigned integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger.</returns>
      <param name="val1">The first of two 16-bit unsigned integers to compare. </param>
      <param name="val2">The second of two 16-bit unsigned integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt32,System.UInt32)">
      <summary>Returns the larger of two 32-bit unsigned integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger.</returns>
      <param name="val1">The first of two 32-bit unsigned integers to compare. </param>
      <param name="val2">The second of two 32-bit unsigned integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt64,System.UInt64)">
      <summary>Returns the larger of two 64-bit unsigned integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is larger.</returns>
      <param name="val1">The first of two 64-bit unsigned integers to compare. </param>
      <param name="val2">The second of two 64-bit unsigned integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Byte,System.Byte)">
      <summary>Returns the smaller of two 8-bit unsigned integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller.</returns>
      <param name="val1">The first of two 8-bit unsigned integers to compare. </param>
      <param name="val2">The second of two 8-bit unsigned integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Decimal,System.Decimal)">
      <summary>Returns the smaller of two decimal numbers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller.</returns>
      <param name="val1">The first of two decimal numbers to compare. </param>
      <param name="val2">The second of two decimal numbers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Double,System.Double)">
      <summary>Returns the smaller of two double-precision floating-point numbers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller. If <paramref name="val1" />, <paramref name="val2" />, or both <paramref name="val1" /> and <paramref name="val2" /> are equal to <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NaN" /> is returned.</returns>
      <param name="val1">The first of two double-precision floating-point numbers to compare. </param>
      <param name="val2">The second of two double-precision floating-point numbers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int16,System.Int16)">
      <summary>Returns the smaller of two 16-bit signed integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller.</returns>
      <param name="val1">The first of two 16-bit signed integers to compare. </param>
      <param name="val2">The second of two 16-bit signed integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int32,System.Int32)">
      <summary>Returns the smaller of two 32-bit signed integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller.</returns>
      <param name="val1">The first of two 32-bit signed integers to compare. </param>
      <param name="val2">The second of two 32-bit signed integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int64,System.Int64)">
      <summary>Returns the smaller of two 64-bit signed integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller.</returns>
      <param name="val1">The first of two 64-bit signed integers to compare. </param>
      <param name="val2">The second of two 64-bit signed integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.SByte,System.SByte)">
      <summary>Returns the smaller of two 8-bit signed integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller.</returns>
      <param name="val1">The first of two 8-bit signed integers to compare. </param>
      <param name="val2">The second of two 8-bit signed integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Single,System.Single)">
      <summary>Returns the smaller of two single-precision floating-point numbers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller. If <paramref name="val1" />, <paramref name="val2" />, or both <paramref name="val1" /> and <paramref name="val2" /> are equal to <see cref="F:System.Single.NaN" />, <see cref="F:System.Single.NaN" /> is returned.</returns>
      <param name="val1">The first of two single-precision floating-point numbers to compare. </param>
      <param name="val2">The second of two single-precision floating-point numbers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt16,System.UInt16)">
      <summary>Returns the smaller of two 16-bit unsigned integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller.</returns>
      <param name="val1">The first of two 16-bit unsigned integers to compare. </param>
      <param name="val2">The second of two 16-bit unsigned integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt32,System.UInt32)">
      <summary>Returns the smaller of two 32-bit unsigned integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller.</returns>
      <param name="val1">The first of two 32-bit unsigned integers to compare. </param>
      <param name="val2">The second of two 32-bit unsigned integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt64,System.UInt64)">
      <summary>Returns the smaller of two 64-bit unsigned integers.</summary>
      <returns>Parameter <paramref name="val1" /> or <paramref name="val2" />, whichever is smaller.</returns>
      <param name="val1">The first of two 64-bit unsigned integers to compare. </param>
      <param name="val2">The second of two 64-bit unsigned integers to compare. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.PI">
      <summary>Represents the ratio of the circumference of a circle to its diameter, specified by the constant, π.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Pow(System.Double,System.Double)">
      <summary>Returns a specified number raised to the specified power.</summary>
      <returns>The number <paramref name="x" /> raised to the power <paramref name="y" />.</returns>
      <param name="x">A double-precision floating-point number to be raised to a power. </param>
      <param name="y">A double-precision floating-point number that specifies a power. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal)">
      <summary>Rounds a decimal value to the nearest integral value.</summary>
      <returns>The integer nearest parameter <paramref name="d" />. If the fractional component of <paramref name="d" /> is halfway between two integers, one of which is even and the other odd, the even number is returned. Note that this method returns a <see cref="T:System.Decimal" /> instead of an integral type.</returns>
      <param name="d">A decimal number to be rounded. </param>
      <exception cref="T:System.OverflowException">The result is outside the range of a <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32)">
      <summary>Rounds a decimal value to a specified number of fractional digits.</summary>
      <returns>The number nearest to <paramref name="d" /> that contains a number of fractional digits equal to <paramref name="decimals" />. </returns>
      <param name="d">A decimal number to be rounded. </param>
      <param name="decimals">The number of decimal places in the return value. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> is less than 0 or greater than 28. </exception>
      <exception cref="T:System.OverflowException">The result is outside the range of a <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32,System.MidpointRounding)">
      <summary>Rounds a decimal value to a specified number of fractional digits. A parameter specifies how to round the value if it is midway between two numbers.</summary>
      <returns>The number nearest to <paramref name="d" /> that contains a number of fractional digits equal to <paramref name="decimals" />. If <paramref name="d" /> has fewer fractional digits than <paramref name="decimals" />, <paramref name="d" /> is returned unchanged.</returns>
      <param name="d">A decimal number to be rounded. </param>
      <param name="decimals">The number of decimal places in the return value. </param>
      <param name="mode">Specification for how to round <paramref name="d" /> if it is midway between two other numbers.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> is less than 0 or greater than 28. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> is not a valid value of <see cref="T:System.MidpointRounding" />.</exception>
      <exception cref="T:System.OverflowException">The result is outside the range of a <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.MidpointRounding)">
      <summary>Rounds a decimal value to the nearest integer. A parameter specifies how to round the value if it is midway between two numbers.</summary>
      <returns>The integer nearest <paramref name="d" />. If <paramref name="d" /> is halfway between two numbers, one of which is even and the other odd, then <paramref name="mode" /> determines which of the two is returned. </returns>
      <param name="d">A decimal number to be rounded. </param>
      <param name="mode">Specification for how to round <paramref name="d" /> if it is midway between two other numbers.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> is not a valid value of <see cref="T:System.MidpointRounding" />.</exception>
      <exception cref="T:System.OverflowException">The result is outside the range of a <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double)">
      <summary>Rounds a double-precision floating-point value to the nearest integral value.</summary>
      <returns>The integer nearest <paramref name="a" />. If the fractional component of <paramref name="a" /> is halfway between two integers, one of which is even and the other odd, then the even number is returned. Note that this method returns a <see cref="T:System.Double" /> instead of an integral type.</returns>
      <param name="a">A double-precision floating-point number to be rounded. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32)">
      <summary>Rounds a double-precision floating-point value to a specified number of fractional digits.</summary>
      <returns>The number nearest to <paramref name="value" /> that contains a number of fractional digits equal to <paramref name="digits" />.</returns>
      <param name="value">A double-precision floating-point number to be rounded. </param>
      <param name="digits">The number of fractional digits in the return value. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> is less than 0 or greater than 15. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32,System.MidpointRounding)">
      <summary>Rounds a double-precision floating-point value to a specified number of fractional digits. A parameter specifies how to round the value if it is midway between two numbers.</summary>
      <returns>The number nearest to <paramref name="value" /> that has a number of fractional digits equal to <paramref name="digits" />. If <paramref name="value" /> has fewer fractional digits than <paramref name="digits" />, <paramref name="value" /> is returned unchanged.</returns>
      <param name="value">A double-precision floating-point number to be rounded. </param>
      <param name="digits">The number of fractional digits in the return value. </param>
      <param name="mode">Specification for how to round <paramref name="value" /> if it is midway between two other numbers.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> is less than 0 or greater than 15. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> is not a valid value of <see cref="T:System.MidpointRounding" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.MidpointRounding)">
      <summary>Rounds a double-precision floating-point value to the nearest integer. A parameter specifies how to round the value if it is midway between two numbers.</summary>
      <returns>The integer nearest <paramref name="value" />. If <paramref name="value" /> is halfway between two integers, one of which is even and the other odd, then <paramref name="mode" /> determines which of the two is returned.</returns>
      <param name="value">A double-precision floating-point number to be rounded. </param>
      <param name="mode">Specification for how to round <paramref name="value" /> if it is midway between two other numbers.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> is not a valid value of <see cref="T:System.MidpointRounding" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Decimal)">
      <summary>Returns a value indicating the sign of a decimal number.</summary>
      <returns>A number that indicates the sign of <paramref name="value" />, as shown in the following table.Return value Meaning -1 <paramref name="value" /> is less than zero. 0 <paramref name="value" /> is equal to zero. 1 <paramref name="value" /> is greater than zero. </returns>
      <param name="value">A signed decimal number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Double)">
      <summary>Returns a value indicating the sign of a double-precision floating-point number.</summary>
      <returns>A number that indicates the sign of <paramref name="value" />, as shown in the following table.Return value Meaning -1 <paramref name="value" /> is less than zero. 0 <paramref name="value" /> is equal to zero. 1 <paramref name="value" /> is greater than zero. </returns>
      <param name="value">A signed number. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> is equal to <see cref="F:System.Double.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int16)">
      <summary>Returns a value indicating the sign of a 16-bit signed integer.</summary>
      <returns>A number that indicates the sign of <paramref name="value" />, as shown in the following table.Return value Meaning -1 <paramref name="value" /> is less than zero. 0 <paramref name="value" /> is equal to zero. 1 <paramref name="value" /> is greater than zero. </returns>
      <param name="value">A signed number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int32)">
      <summary>Returns a value indicating the sign of a 32-bit signed integer.</summary>
      <returns>A number that indicates the sign of <paramref name="value" />, as shown in the following table.Return value Meaning -1 <paramref name="value" /> is less than zero. 0 <paramref name="value" /> is equal to zero. 1 <paramref name="value" /> is greater than zero. </returns>
      <param name="value">A signed number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int64)">
      <summary>Returns a value indicating the sign of a 64-bit signed integer.</summary>
      <returns>A number that indicates the sign of <paramref name="value" />, as shown in the following table.Return value Meaning -1 <paramref name="value" /> is less than zero. 0 <paramref name="value" /> is equal to zero. 1 <paramref name="value" /> is greater than zero. </returns>
      <param name="value">A signed number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.SByte)">
      <summary>Returns a value indicating the sign of an 8-bit signed integer.</summary>
      <returns>A number that indicates the sign of <paramref name="value" />, as shown in the following table.Return value Meaning -1 <paramref name="value" /> is less than zero. 0 <paramref name="value" /> is equal to zero. 1 <paramref name="value" /> is greater than zero. </returns>
      <param name="value">A signed number. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Single)">
      <summary>Returns a value indicating the sign of a single-precision floating-point number.</summary>
      <returns>A number that indicates the sign of <paramref name="value" />, as shown in the following table.Return value Meaning -1 <paramref name="value" /> is less than zero. 0 <paramref name="value" /> is equal to zero. 1 <paramref name="value" /> is greater than zero. </returns>
      <param name="value">A signed number. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> is equal to <see cref="F:System.Single.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sin(System.Double)">
      <summary>Returns the sine of the specified angle.</summary>
      <returns>The sine of <paramref name="a" />. If <paramref name="a" /> is equal to <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" />, or <see cref="F:System.Double.PositiveInfinity" />, this method returns <see cref="F:System.Double.NaN" />.</returns>
      <param name="a">An angle, measured in radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sinh(System.Double)">
      <summary>Returns the hyperbolic sine of the specified angle.</summary>
      <returns>The hyperbolic sine of <paramref name="value" />. If <paramref name="value" /> is equal to <see cref="F:System.Double.NegativeInfinity" />, <see cref="F:System.Double.PositiveInfinity" />, or <see cref="F:System.Double.NaN" />, this method returns a <see cref="T:System.Double" /> equal to <paramref name="value" />.</returns>
      <param name="value">An angle, measured in radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sqrt(System.Double)">
      <summary>Returns the square root of a specified number.</summary>
      <returns>One of the values in the following table. <paramref name="d" /> parameter Return value Zero or positive The positive square root of <paramref name="d" />. Negative <see cref="F:System.Double.NaN" />Equals <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Equals <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">The number whose square root is to be found. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tan(System.Double)">
      <summary>Returns the tangent of the specified angle.</summary>
      <returns>The tangent of <paramref name="a" />. If <paramref name="a" /> is equal to <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" />, or <see cref="F:System.Double.PositiveInfinity" />, this method returns <see cref="F:System.Double.NaN" />.</returns>
      <param name="a">An angle, measured in radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tanh(System.Double)">
      <summary>Returns the hyperbolic tangent of the specified angle.</summary>
      <returns>The hyperbolic tangent of <paramref name="value" />. If <paramref name="value" /> is equal to <see cref="F:System.Double.NegativeInfinity" />, this method returns -1. If value is equal to <see cref="F:System.Double.PositiveInfinity" />, this method returns 1. If <paramref name="value" /> is equal to <see cref="F:System.Double.NaN" />, this method returns <see cref="F:System.Double.NaN" />.</returns>
      <param name="value">An angle, measured in radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Decimal)">
      <summary>Calculates the integral part of a specified decimal number. </summary>
      <returns>The integral part of <paramref name="d" />; that is, the number that remains after any fractional digits have been discarded.</returns>
      <param name="d">A number to truncate.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Double)">
      <summary>Calculates the integral part of a specified double-precision floating-point number. </summary>
      <returns>The integral part of <paramref name="d" />; that is, the number that remains after any fractional digits have been discarded, or one of the values listed in the following table. <paramref name="d" />Return value<see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">A number to truncate.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.MidpointRounding">
      <summary>Specifies how mathematical rounding methods should process a number that is midway between two numbers.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.MidpointRounding.AwayFromZero">
      <summary>When a number is halfway between two others, it is rounded toward the nearest number that is away from zero.</summary>
    </member>
    <member name="F:System.MidpointRounding.ToEven">
      <summary>When a number is halfway between two others, it is rounded toward the nearest even number.</summary>
    </member>
    <member name="T:System.Progress`1">
      <summary>Provides an <see cref="T:System.IProgress`1" /> that invokes callbacks for each reported progress value.</summary>
      <typeparam name="T">Specifies the type of the progress report value.</typeparam>
    </member>
    <member name="M:System.Progress`1.#ctor">
      <summary>Initializes the <see cref="T:System.Progress`1" /> object.</summary>
    </member>
    <member name="M:System.Progress`1.#ctor(System.Action{`0})">
      <summary>Initializes the <see cref="T:System.Progress`1" /> object with the specified callback.</summary>
      <param name="handler">A handler to invoke for each reported progress value. This handler will be invoked in addition to any delegates registered with the <see cref="E:System.Progress`1.ProgressChanged" /> event. Depending on the <see cref="T:System.Threading.SynchronizationContext" /> instance captured by the <see cref="T:System.Progress`1" /> at construction, it is possible that this handler instance could be invoked concurrently with itself.</param>
    </member>
    <member name="M:System.Progress`1.OnReport(`0)">
      <summary>Reports a progress change.</summary>
      <param name="value">The value of the updated progress.</param>
    </member>
    <member name="E:System.Progress`1.ProgressChanged">
      <summary>Raised for each reported progress value.</summary>
    </member>
    <member name="M:System.Progress`1.System#IProgress{T}#Report(`0)">
      <summary>Reports a progress change.</summary>
      <param name="value">The value of the updated progress.</param>
    </member>
    <member name="T:System.Random">
      <summary>Represents a pseudo-random number generator, which is a device that produces a sequence of numbers that meet certain statistical requirements for randomness.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Random" /> class, using a time-dependent default seed value.</summary>
    </member>
    <member name="M:System.Random.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Random" /> class, using the specified seed value.</summary>
      <param name="Seed">A number used to calculate a starting value for the pseudo-random number sequence. If a negative number is specified, the absolute value of the number is used. </param>
    </member>
    <member name="M:System.Random.Next">
      <summary>Returns a non-negative random integer.</summary>
      <returns>A 32-bit signed integer that is greater than or equal to 0 and less than <see cref="F:System.Int32.MaxValue" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32)">
      <summary>Returns a non-negative random integer that is less than the specified maximum.</summary>
      <returns>A 32-bit signed integer that is greater than or equal to 0, and less than <paramref name="maxValue" />; that is, the range of return values ordinarily includes 0 but not <paramref name="maxValue" />. However, if <paramref name="maxValue" /> equals 0, <paramref name="maxValue" /> is returned.</returns>
      <param name="maxValue">The exclusive upper bound of the random number to be generated. <paramref name="maxValue" /> must be greater than or equal to 0. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxValue" /> is less than 0. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32,System.Int32)">
      <summary>Returns a random integer that is within a specified range.</summary>
      <returns>A 32-bit signed integer greater than or equal to <paramref name="minValue" /> and less than <paramref name="maxValue" />; that is, the range of return values includes <paramref name="minValue" /> but not <paramref name="maxValue" />. If <paramref name="minValue" /> equals <paramref name="maxValue" />, <paramref name="minValue" /> is returned.</returns>
      <param name="minValue">The inclusive lower bound of the random number returned. </param>
      <param name="maxValue">The exclusive upper bound of the random number returned. <paramref name="maxValue" /> must be greater than or equal to <paramref name="minValue" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minValue" /> is greater than <paramref name="maxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextBytes(System.Byte[])">
      <summary>Fills the elements of a specified array of bytes with random numbers.</summary>
      <param name="buffer">An array of bytes to contain random numbers. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextDouble">
      <summary>Returns a random floating-point number that is greater than or equal to 0.0, and less than 1.0.</summary>
      <returns>A double-precision floating point number that is greater than or equal to 0.0, and less than 1.0.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Sample">
      <summary>Returns a random floating-point number between 0.0 and 1.0.</summary>
      <returns>A double-precision floating point number that is greater than or equal to 0.0, and less than 1.0.</returns>
    </member>
    <member name="T:System.StringComparer">
      <summary>Represents a string comparison operation that uses specific case and culture-based or ordinal comparison rules.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.StringComparer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.StringComparer" /> class. </summary>
    </member>
    <member name="M:System.StringComparer.Compare(System.String,System.String)">
      <summary>When overridden in a derived class, compares two strings and returns an indication of their relative sort order.</summary>
      <returns>A signed integer that indicates the relative values of <paramref name="x" /> and <paramref name="y" />, as shown in the following table.ValueMeaningLess than zero<paramref name="x" /> precedes <paramref name="y" /> in the sort order.-or-<paramref name="x" /> is null and <paramref name="y" /> is not null.Zero<paramref name="x" /> is equal to <paramref name="y" />.-or-<paramref name="x" /> and <paramref name="y" /> are both null. Greater than zero<paramref name="x" /> follows <paramref name="y" /> in the sort order.-or-<paramref name="y" /> is null and <paramref name="x" /> is not null. </returns>
      <param name="x">A string to compare to <paramref name="y" />.</param>
      <param name="y">A string to compare to <paramref name="x" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.CurrentCulture">
      <summary>Gets a <see cref="T:System.StringComparer" /> object that performs a case-sensitive string comparison using the word comparison rules of the current culture.</summary>
      <returns>A new <see cref="T:System.StringComparer" /> object.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.StringComparer.CurrentCultureIgnoreCase">
      <summary>Gets a <see cref="T:System.StringComparer" /> object that performs case-insensitive string comparisons using the word comparison rules of the current culture.</summary>
      <returns>A new <see cref="T:System.StringComparer" /> object.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.StringComparer.Equals(System.String,System.String)">
      <summary>When overridden in a derived class, indicates whether two strings are equal.</summary>
      <returns>true if <paramref name="x" /> and <paramref name="y" /> refer to the same object, or <paramref name="x" /> and <paramref name="y" /> are equal, or <paramref name="x" /> and <paramref name="y" /> are null; otherwise, false.</returns>
      <param name="x">A string to compare to <paramref name="y" />.</param>
      <param name="y">A string to compare to <paramref name="x" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.GetHashCode(System.String)">
      <summary>When overridden in a derived class, gets the hash code for the specified string.</summary>
      <returns>A 32-bit signed hash code calculated from the value of the <paramref name="obj" /> parameter.</returns>
      <param name="obj">A string.</param>
      <exception cref="T:System.ArgumentException">Not enough memory is available to allocate the buffer that is required to compute the hash code.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> is null. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.StringComparer.Ordinal">
      <summary>Gets a <see cref="T:System.StringComparer" /> object that performs a case-sensitive ordinal string comparison.</summary>
      <returns>A <see cref="T:System.StringComparer" /> object.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.OrdinalIgnoreCase">
      <summary>Gets a <see cref="T:System.StringComparer" /> object that performs a case-insensitive ordinal string comparison.</summary>
      <returns>A <see cref="T:System.StringComparer" /> object.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Compares two objects and returns a value that indicates whether one is less than, equal to, or greater than the other.</summary>
      <returns>A signed integer that indicates the relative values of <paramref name="x" /> and <paramref name="y" />, as shown in the following table.ValueMeaningLess than zero<paramref name="x" /> is less than <paramref name="y" />.Zero<paramref name="x" /> equals <paramref name="y" />.Greater than zero<paramref name="x" /> is greater than <paramref name="y" />.</returns>
      <param name="x">The first object to compare.</param>
      <param name="y">The second object to compare.</param>
      <exception cref="T:System.ArgumentException">Neither <paramref name="x" /> nor <paramref name="y" /> implements the <see cref="T:System.IComparable" /> interface.-or-<paramref name="x" /> and <paramref name="y" /> are of different types and neither one can handle comparisons with the other.</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Determines whether the specified objects are equal.</summary>
      <returns>true if the specified objects are equal; otherwise, false. </returns>
      <param name="x">The first object to compare.</param>
      <param name="y">The second object to compare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> and <paramref name="y" /> are of different types, and neither one can handle comparisons with the other. </exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Returns a hash code for the specified object.</summary>
      <returns>A hash code for the specified object. </returns>
      <param name="obj">The object for which a hash code is to be returned. </param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null. </exception>
    </member>
    <member name="T:System.UriBuilder">
      <summary>Provides a custom constructor for uniform resource identifiers (URIs) and modifies URIs for the <see cref="T:System.Uri" /> class.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.UriBuilder" /> class.</summary>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.UriBuilder" /> class with the specified URI.</summary>
      <param name="uri">A URI string. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is null. </exception>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.<paramref name="uri" /> is a zero length string or contains only spaces.-or- The parsing routine detected a scheme in an invalid form.-or- The parser detected more than two consecutive slashes in a URI that does not use the "file" scheme.-or- <paramref name="uri" /> is not a valid URI. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.UriBuilder" /> class with the specified scheme and host.</summary>
      <param name="schemeName">An Internet access protocol. </param>
      <param name="hostName">A DNS-style domain name or IP address. </param>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.UriBuilder" /> class with the specified scheme, host, and port.</summary>
      <param name="scheme">An Internet access protocol. </param>
      <param name="host">A DNS-style domain name or IP address. </param>
      <param name="portNumber">An IP port number for the service. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portNumber" /> is less than -1 or greater than 65,535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.UriBuilder" /> class with the specified scheme, host, port number, and path.</summary>
      <param name="scheme">An Internet access protocol. </param>
      <param name="host">A DNS-style domain name or IP address. </param>
      <param name="port">An IP port number for the service. </param>
      <param name="pathValue">The path to the Internet resource. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is less than -1 or greater than 65,535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.UriBuilder" /> class with the specified scheme, host, port number, path and query string or fragment identifier.</summary>
      <param name="scheme">An Internet access protocol. </param>
      <param name="host">A DNS-style domain name or IP address. </param>
      <param name="port">An IP port number for the service. </param>
      <param name="path">The path to the Internet resource. </param>
      <param name="extraValue">A query string or fragment identifier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="extraValue" /> is neither null nor <see cref="F:System.String.Empty" />, nor does a valid fragment identifier begin with a number sign (#), nor a valid query string begin with a question mark (?). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is less than -1 or greater than 65,535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.UriBuilder" /> class with the specified <see cref="T:System.Uri" /> instance.</summary>
      <param name="uri">An instance of the <see cref="T:System.Uri" /> class. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is null. </exception>
    </member>
    <member name="M:System.UriBuilder.Equals(System.Object)">
      <summary>Compares an existing <see cref="T:System.Uri" /> instance with the contents of the <see cref="T:System.UriBuilder" /> for equality.</summary>
      <returns>true if <paramref name="rparam" /> represents the same <see cref="T:System.Uri" /> as the <see cref="T:System.Uri" /> constructed by this <see cref="T:System.UriBuilder" /> instance; otherwise, false.</returns>
      <param name="rparam">The object to compare with the current instance. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Fragment">
      <summary>Gets or sets the fragment portion of the URI.</summary>
      <returns>The fragment portion of the URI. The fragment identifier ("#") is added to the beginning of the fragment.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.GetHashCode">
      <summary>Returns the hash code for the URI.</summary>
      <returns>The hash code generated for the URI.</returns>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Host">
      <summary>Gets or sets the Domain Name System (DNS) host name or IP address of a server.</summary>
      <returns>The DNS host name or IP address of the server.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Password">
      <summary>Gets or sets the password associated with the user that accesses the URI.</summary>
      <returns>The password of the user that accesses the URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Path">
      <summary>Gets or sets the path to the resource referenced by the URI.</summary>
      <returns>The path to the resource referenced by the URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Port">
      <summary>Gets or sets the port number of the URI.</summary>
      <returns>The port number of the URI.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The port cannot be set to a value less than -1 or greater than 65,535. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Query">
      <summary>Gets or sets any query information included in the URI.</summary>
      <returns>The query information included in the URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Scheme">
      <summary>Gets or sets the scheme name of the URI.</summary>
      <returns>The scheme of the URI.</returns>
      <exception cref="T:System.ArgumentException">The scheme cannot be set to an invalid scheme name. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.UriBuilder.ToString">
      <summary>Returns the display string for the specified <see cref="T:System.UriBuilder" /> instance.</summary>
      <returns>The string that contains the unescaped display string of the <see cref="T:System.UriBuilder" />.</returns>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.The <see cref="T:System.UriBuilder" /> instance has a bad password. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Uri">
      <summary>Gets the <see cref="T:System.Uri" /> instance constructed by the specified <see cref="T:System.UriBuilder" /> instance.</summary>
      <returns>A <see cref="T:System.Uri" /> that contains the URI constructed by the <see cref="T:System.UriBuilder" />.</returns>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.The URI constructed by the <see cref="T:System.UriBuilder" /> properties is invalid. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.UserName">
      <summary>The user name associated with the user that accesses the URI.</summary>
      <returns>The user name of the user that accesses the URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Diagnostics.Stopwatch">
      <summary>Provides a set of methods and properties that you can use to accurately measure elapsed time.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Stopwatch" /> class.</summary>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.Elapsed">
      <summary>Gets the total elapsed time measured by the current instance.</summary>
      <returns>A read-only <see cref="T:System.TimeSpan" /> representing the total elapsed time measured by the current instance.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedMilliseconds">
      <summary>Gets the total elapsed time measured by the current instance, in milliseconds.</summary>
      <returns>A read-only long integer representing the total number of milliseconds measured by the current instance.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedTicks">
      <summary>Gets the total elapsed time measured by the current instance, in timer ticks.</summary>
      <returns>A read-only long integer representing the total number of timer ticks measured by the current instance.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.Frequency">
      <summary>Gets the frequency of the timer as the number of ticks per second. This field is read-only.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.GetTimestamp">
      <summary>Gets the current number of ticks in the timer mechanism.</summary>
      <returns>A long integer representing the tick counter value of the underlying timer mechanism.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.IsHighResolution">
      <summary>Indicates whether the timer is based on a high-resolution performance counter. This field is read-only.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.IsRunning">
      <summary>Gets a value indicating whether the <see cref="T:System.Diagnostics.Stopwatch" /> timer is running.</summary>
      <returns>true if the <see cref="T:System.Diagnostics.Stopwatch" /> instance is currently running and measuring elapsed time for an interval; otherwise, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Reset">
      <summary>Stops time interval measurement and resets the elapsed time to zero.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Restart">
      <summary>Stops time interval measurement, resets the elapsed time to zero, and starts measuring elapsed time.</summary>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Start">
      <summary>Starts, or resumes, measuring elapsed time for an interval.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.StartNew">
      <summary>Initializes a new <see cref="T:System.Diagnostics.Stopwatch" /> instance, sets the elapsed time property to zero, and starts measuring elapsed time.</summary>
      <returns>A <see cref="T:System.Diagnostics.Stopwatch" /> that has just begun measuring elapsed time.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Stop">
      <summary>Stops measuring elapsed time for an interval.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.Path">
      <summary>Performs operations on <see cref="T:System.String" /> instances that contain file or directory path information. These operations are performed in a cross-platform manner.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.AltDirectorySeparatorChar">
      <summary>Provides a platform-specific alternate character used to separate directory levels in a path string that reflects a hierarchical file system organization.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.ChangeExtension(System.String,System.String)">
      <summary>Changes the extension of a path string.</summary>
      <returns>The modified path information.On Windows-based desktop platforms, if <paramref name="path" /> is null or an empty string (""), the path information is returned unmodified. If <paramref name="extension" /> is null, the returned string contains the specified path with its extension removed. If <paramref name="path" /> has no extension, and <paramref name="extension" /> is not null, the returned path string contains <paramref name="extension" /> appended to the end of <paramref name="path" />.</returns>
      <param name="path">The path information to modify. The path cannot contain any of the characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />. </param>
      <param name="extension">The new extension (with or without a leading period). Specify null to remove an existing extension from <paramref name="path" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String)">
      <summary>Combines two strings into a path.</summary>
      <returns>The combined paths. If one of the specified paths is a zero-length string, this method returns the other path. If <paramref name="path2" /> contains an absolute path, this method returns <paramref name="path2" />.</returns>
      <param name="path1">The first path to combine. </param>
      <param name="path2">The second path to combine. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" /> or <paramref name="path2" /> contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" /> or <paramref name="path2" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String,System.String)">
      <summary>Combines three strings into a path.</summary>
      <returns>The combined paths.</returns>
      <param name="path1">The first path to combine. </param>
      <param name="path2">The second path to combine. </param>
      <param name="path3">The third path to combine.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" />, <paramref name="path2" />, or <paramref name="path3" /> contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" />, <paramref name="path2" />, or <paramref name="path3" /> is null. </exception>
    </member>
    <member name="M:System.IO.Path.Combine(System.String[])">
      <summary>Combines an array of strings into a path.</summary>
      <returns>The combined paths.</returns>
      <param name="paths">An array of parts of the path.</param>
      <exception cref="T:System.ArgumentException">One of the strings in the array contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">One of the strings in the array is null. </exception>
    </member>
    <member name="F:System.IO.Path.DirectorySeparatorChar">
      <summary>Provides a platform-specific character used to separate directory levels in a path string that reflects a hierarchical file system organization.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetDirectoryName(System.String)">
      <summary>Returns the directory information for the specified path string.</summary>
      <returns>Directory information for <paramref name="path" />, or null if <paramref name="path" /> denotes a root directory or is null. Returns <see cref="F:System.String.Empty" /> if <paramref name="path" /> does not contain directory information.</returns>
      <param name="path">The path of a file or directory. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> parameter contains invalid characters, is empty, or contains only white spaces. </exception>
      <exception cref="T:System.IO.PathTooLongException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.IO.IOException" />, instead.The <paramref name="path" /> parameter is longer than the system-defined maximum length.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetExtension(System.String)">
      <summary>Returns the extension of the specified path string.</summary>
      <returns>The extension of the specified path (including the period "."), or null, or <see cref="F:System.String.Empty" />. If <paramref name="path" /> is null, <see cref="M:System.IO.Path.GetExtension(System.String)" /> returns null. If <paramref name="path" /> does not have extension information, <see cref="M:System.IO.Path.GetExtension(System.String)" /> returns <see cref="F:System.String.Empty" />.</returns>
      <param name="path">The path string from which to get the extension. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />.  </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileName(System.String)">
      <summary>Returns the file name and extension of the specified path string.</summary>
      <returns>The characters after the last directory character in <paramref name="path" />. If the last character of <paramref name="path" /> is a directory or volume separator character, this method returns <see cref="F:System.String.Empty" />. If <paramref name="path" /> is null, this method returns null.</returns>
      <param name="path">The path string from which to obtain the file name and extension. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileNameWithoutExtension(System.String)">
      <summary>Returns the file name of the specified path string without the extension.</summary>
      <returns>The string returned by <see cref="M:System.IO.Path.GetFileName(System.String)" />, minus the last period (.) and all characters following it.</returns>
      <param name="path">The path of the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFullPath(System.String)">
      <summary>Returns the absolute path for the specified path string.</summary>
      <returns>The fully qualified location of <paramref name="path" />, such as "C:\MyFile.txt".</returns>
      <param name="path">The file or directory for which to obtain absolute path information. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />.-or- The system could not retrieve the absolute path. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permissions. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contains a colon (":") that is not part of a volume identifier (for example, "c:\"). </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetInvalidFileNameChars">
      <summary>Gets an array containing the characters that are not allowed in file names.</summary>
      <returns>An array containing the characters that are not allowed in file names.</returns>
    </member>
    <member name="M:System.IO.Path.GetInvalidPathChars">
      <summary>Gets an array containing the characters that are not allowed in path names.</summary>
      <returns>An array containing the characters that are not allowed in path names.</returns>
    </member>
    <member name="M:System.IO.Path.GetPathRoot(System.String)">
      <summary>Gets the root directory information of the specified path.</summary>
      <returns>The root directory of <paramref name="path" />, such as "C:\", or null if <paramref name="path" /> is null, or an empty string if <paramref name="path" /> does not contain root directory information.</returns>
      <param name="path">The path from which to obtain root directory information. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />.-or- <see cref="F:System.String.Empty" /> was passed to <paramref name="path" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetRandomFileName">
      <summary>Returns a random folder name or file name.</summary>
      <returns>A random folder name or file name.</returns>
    </member>
    <member name="M:System.IO.Path.GetTempFileName">
      <summary>Creates a uniquely named, zero-byte temporary file on disk and returns the full path of that file.</summary>
      <returns>The full path of the temporary file.</returns>
      <exception cref="T:System.IO.IOException">An I/O error occurs, such as no unique temporary file name is available.- or -This method was unable to create a temporary file.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetTempPath">
      <summary>Returns the path of the current user's temporary folder.</summary>
      <returns>The path to the temporary folder, ending with a backslash.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permissions. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.HasExtension(System.String)">
      <summary>Determines whether a path includes a file name extension.</summary>
      <returns>true if the characters that follow the last directory separator (\\ or /) or volume separator (:) in the path include a period (.) followed by one or more characters; otherwise, false.</returns>
      <param name="path">The path to search for an extension. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.IsPathRooted(System.String)">
      <summary>Gets a value indicating whether the specified path string contains a root.</summary>
      <returns>true if <paramref name="path" /> contains a root; otherwise, false.</returns>
      <param name="path">The path to test. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contains one or more of the invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.PathSeparator">
      <summary>A platform-specific separator character used to separate path strings in environment variables.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.VolumeSeparatorChar">
      <summary>Provides a platform-specific volume separator character.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Net.WebUtility">
      <summary>Provides methods for encoding and decoding URLs when processing Web requests. </summary>
    </member>
    <member name="M:System.Net.WebUtility.HtmlDecode(System.String)">
      <summary>Converts a string that has been HTML-encoded for HTTP transmission into a decoded string.</summary>
      <returns>A decoded string.</returns>
      <param name="value">The string to decode.</param>
    </member>
    <member name="M:System.Net.WebUtility.HtmlEncode(System.String)">
      <summary>Converts a string to an HTML-encoded string.</summary>
      <returns>An encoded string.</returns>
      <param name="value">The string to encode.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecode(System.String)">
      <summary>Converts a string that has been encoded for transmission in a URL into a decoded string.</summary>
      <returns>Returns <see cref="T:System.String" />.A decoded string.</returns>
      <param name="encodedValue">A URL-encoded string to decode.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Converts an encoded byte array that has been encoded for transmission in a URL into a decoded byte array.</summary>
      <returns>Returns <see cref="T:System.Byte" />.A decoded <see cref="T:System.Byte" /> array.</returns>
      <param name="encodedValue">A URL-encoded <see cref="T:System.Byte" /> array to decode.</param>
      <param name="offset">The offset, in bytes, from the start of the <see cref="T:System.Byte" /> array to decode.</param>
      <param name="count">The count, in bytes, to decode from the <see cref="T:System.Byte" /> array.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncode(System.String)">
      <summary>Converts a text string into a URL-encoded string.</summary>
      <returns>Returns <see cref="T:System.String" />.A URL-encoded string.</returns>
      <param name="value">The text to URL-encode.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Converts a byte array into a URL-encoded byte array.</summary>
      <returns>Returns <see cref="T:System.Byte" />.An encoded <see cref="T:System.Byte" /> array.</returns>
      <param name="value">The <see cref="T:System.Byte" /> array to URL-encode.</param>
      <param name="offset">The offset, in bytes, from the start of the <see cref="T:System.Byte" /> array to encode.</param>
      <param name="count">The count, in bytes, to encode from the <see cref="T:System.Byte" /> array.</param>
    </member>
    <member name="T:System.Runtime.Versioning.FrameworkName">
      <summary>Represents the name of a version of the .NET Framework.</summary>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Versioning.FrameworkName" /> class from a string that contains information about a version of the .NET Framework.</summary>
      <param name="frameworkName">A string that contains .NET Framework version information.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="frameworkName" /> is <see cref="F:System.String.Empty" />.-or-<paramref name="frameworkName" /> has fewer than two components or more than three components.-or-<paramref name="frameworkName" /> does not include a major and minor version number.-or-<paramref name="frameworkName " />does not include a valid version number.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="frameworkName" /> is null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Versioning.FrameworkName" /> class from a string and a <see cref="T:System.Version" /> object that identify a .NET Framework version.</summary>
      <param name="identifier">A string that identifies a .NET Framework version. </param>
      <param name="version">An object that contains .NET Framework version information.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> is null.-or-<paramref name="version" /> is null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Versioning.FrameworkName" /> class from a string, a <see cref="T:System.Version" /> object that identifies a .NET Framework version, and a profile name.</summary>
      <param name="identifier">A string that identifies a .NET Framework version.</param>
      <param name="version">An object that contains .NET Framework version information.</param>
      <param name="profile">A profile name.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> is null.-or-<paramref name="version" /> is null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Object)">
      <summary>Returns a value that indicates whether this <see cref="T:System.Runtime.Versioning.FrameworkName" /> instance represents the same .NET Framework version as a specified object.</summary>
      <returns>true if every component of the current <see cref="T:System.Runtime.Versioning.FrameworkName" /> object matches the corresponding component of <paramref name="obj" />; otherwise, false.</returns>
      <param name="obj">The object to compare to the current instance.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Runtime.Versioning.FrameworkName)">
      <summary>Returns a value that indicates whether this <see cref="T:System.Runtime.Versioning.FrameworkName" /> instance represents the same .NET Framework version as a specified <see cref="T:System.Runtime.Versioning.FrameworkName" /> instance.</summary>
      <returns>true if every component of the current <see cref="T:System.Runtime.Versioning.FrameworkName" /> object matches the corresponding component of <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The object to compare to the current instance.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.FullName">
      <summary>Gets the full name of this <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</summary>
      <returns>The full name of this <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.GetHashCode">
      <summary>Returns the hash code for the <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</summary>
      <returns>A 32-bit signed integer that represents the hash code of this instance.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Identifier">
      <summary>Gets the identifier of this <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</summary>
      <returns>The identifier of this <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Equality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Returns a value that indicates whether two <see cref="T:System.Runtime.Versioning.FrameworkName" /> objects represent the same .NET Framework version.</summary>
      <returns>true if the <paramref name="left" /> and <paramref name="right" /> parameters represent the same .NET Framework version; otherwise, false.</returns>
      <param name="left">The first object to compare.</param>
      <param name="right">The second object to compare.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Inequality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Returns a value that indicates whether two <see cref="T:System.Runtime.Versioning.FrameworkName" /> objects represent different .NET Framework versions.</summary>
      <returns>true if the <paramref name="left" /> and <paramref name="right" /> parameters represent different .NET Framework versions; otherwise, false.</returns>
      <param name="left">The first object to compare.</param>
      <param name="right">The second object to compare.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Profile">
      <summary>Gets the profile name of this <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</summary>
      <returns>The profile name of this <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.ToString">
      <summary>Returns the string representation of this <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</summary>
      <returns>A string that represents this <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Version">
      <summary>Gets the version of this <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</summary>
      <returns>An object that contains version information about this <see cref="T:System.Runtime.Versioning.FrameworkName" /> object.</returns>
    </member>
  </members>
</doc>