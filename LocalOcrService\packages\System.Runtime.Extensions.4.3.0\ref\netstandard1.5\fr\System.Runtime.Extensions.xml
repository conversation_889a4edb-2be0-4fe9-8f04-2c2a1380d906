﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.BitConverter">
      <summary>Convertit les types de données de base en tableau d'octets et un tableau d'octets en types de données de base.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.BitConverter.DoubleToInt64Bits(System.Double)">
      <summary>Convertit le nombre à virgule flottante double précision spécifié en entier 64 bits signé.</summary>
      <returns>Entier 64 bits signé dont la valeur équivaut à <paramref name="value" />.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Boolean)">
      <summary>Retourne la valeur booléenne spécifiée sous la forme d'un tableau d'octets.</summary>
      <returns>Tableau d'octets d'une longueur égale à 1.</returns>
      <param name="value">Valeur booléenne. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Char)">
      <summary>Retourne la valeur du caractère Unicode spécifié sous la forme d'un tableau d'octets.</summary>
      <returns>Tableau d'octets d'une longueur égale à 2.</returns>
      <param name="value">Caractère à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Double)">
      <summary>Retourne la valeur à virgule flottante double précision spécifiée sous la forme d'un tableau d'octets.</summary>
      <returns>Tableau d'octets d'une longueur égale à 8.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int16)">
      <summary>Retourne la valeur de l'entier 16 bits signé spécifié sous la forme d'un tableau d'octets.</summary>
      <returns>Tableau d'octets d'une longueur égale à 2.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int32)">
      <summary>Retourne la valeur de l'entier 32 bits signé spécifié sous la forme d'un tableau d'octets.</summary>
      <returns>Tableau d'octets d'une longueur égale à 4.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int64)">
      <summary>Retourne la valeur de l'entier 64 bits signé spécifié sous la forme d'un tableau d'octets.</summary>
      <returns>Tableau d'octets d'une longueur égale à 8.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Single)">
      <summary>Retourne la valeur à virgule flottante simple précision spécifiée sous la forme d'un tableau d'octets.</summary>
      <returns>Tableau d'octets d'une longueur égale à 4.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt16)">
      <summary>Retourne la valeur de l'entier 16 bits non signé spécifié sous la forme d'un tableau d'octets.</summary>
      <returns>Tableau d'octets d'une longueur égale à 2.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt32)">
      <summary>Retourne la valeur de l'entier 32 bits non signé spécifié sous la forme d'un tableau d'octets.</summary>
      <returns>Tableau d'octets d'une longueur égale à 4.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt64)">
      <summary>Retourne la valeur de l'entier 64 bits non signé spécifié sous la forme d'un tableau d'octets.</summary>
      <returns>Tableau d'octets d'une longueur égale à 8.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.Int64BitsToDouble(System.Int64)">
      <summary>Convertit l'entier 64 bits signé spécifié en nombre à virgule flottante double précision.</summary>
      <returns>Nombre à virgule flottante double précision dont la valeur équivaut à <paramref name="value" />.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.BitConverter.IsLittleEndian">
      <summary>Indique l'ordre d'octet (format « ordre de primauté des octets ») utilisé pour stocker les données dans l'architecture de l'ordinateur.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToBoolean(System.Byte[],System.Int32)">
      <summary>Retourne une valeur booléenne convertie à partir d'un octet à une position spécifiée dans un tableau d'octets.</summary>
      <returns>true si l'octet situé à <paramref name="startIndex" /> dans <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToChar(System.Byte[],System.Int32)">
      <summary>Retourne un caractère Unicode converti à partir de deux octets à une position spécifiée dans un tableau d'octets.</summary>
      <returns>Caractère composé de deux octets, à partir de <paramref name="startIndex" />.</returns>
      <param name="value">Tableau. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> est égal à la longueur de <paramref name="value" /> moins 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToDouble(System.Byte[],System.Int32)">
      <summary>Retourne un nombre à virgule flottante double précision converti à partir de huit octets à une position spécifiée dans un tableau d'octets.</summary>
      <returns>Nombre à virgule flottante double précision composé de huit octets, à partir de <paramref name="startIndex" />.</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> est supérieur ou égal à la longueur de <paramref name="value" /> moins 7, et inférieur ou égal à la longueur de <paramref name="value" /> moins 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt16(System.Byte[],System.Int32)">
      <summary>Retourne un entier 16 bits signé converti à partir de deux octets à une position spécifiée dans un tableau d'octets.</summary>
      <returns>Entier 16 bits signé composé de deux octets, à partir de <paramref name="startIndex" />.</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> est égal à la longueur de <paramref name="value" /> moins 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt32(System.Byte[],System.Int32)">
      <summary>Retourne un entier 32 bits signé converti à partir de quatre octets à une position spécifiée dans un tableau d'octets.</summary>
      <returns>Entier 32 bits signé composé de quatre octets, à partir de <paramref name="startIndex" />.</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> est supérieur ou égal à la longueur de <paramref name="value" /> moins 3, et inférieur ou égal à la longueur de <paramref name="value" /> moins 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt64(System.Byte[],System.Int32)">
      <summary>Retourne un entier 64 bits signé converti à partir de huit octets à une position spécifiée dans un tableau d'octets.</summary>
      <returns>Entier 64 bits signé composé de huit octets, à partir de <paramref name="startIndex" />.</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> est supérieur ou égal à la longueur de <paramref name="value" /> moins 7, et inférieur ou égal à la longueur de <paramref name="value" /> moins 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToSingle(System.Byte[],System.Int32)">
      <summary>Retourne un nombre à virgule flottante simple précision converti à partir de quatre octets à une position spécifiée dans un tableau d'octets.</summary>
      <returns>Nombre à virgule flottante simple précision composé de quatre octets, à partir de <paramref name="startIndex" />.</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> est supérieur ou égal à la longueur de <paramref name="value" /> moins 3, et inférieur ou égal à la longueur de <paramref name="value" /> moins 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[])">
      <summary>Convertit la valeur numérique de chaque élément contenu dans un tableau d'octets spécifié en sa représentation sous forme de chaîne hexadécimale équivalente.</summary>
      <returns>Une chaîne qui contient des paires hexadécimales séparées par des tirets, où chaque paire représente l'élément correspondant dans <paramref name="value" /> ; par exemple, "7F-2C-4A-00".</returns>
      <param name="value">Tableau d'octets. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32)">
      <summary>Convertit la valeur numérique de chaque élément contenu dans un sous-tableau d'octets spécifié en sa représentation sous forme de chaîne hexadécimale équivalente.</summary>
      <returns>Une chaîne qui contient des paires hexadécimales séparées par des tirets, où chaque paire représente l'élément correspondant dans un sous-tableau de <paramref name="value" /> ; par exemple, "7F-2C-4A-00".</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32,System.Int32)">
      <summary>Convertit la valeur numérique de chaque élément contenu dans un sous-tableau d'octets spécifié en sa représentation sous forme de chaîne hexadécimale équivalente.</summary>
      <returns>Une chaîne qui contient des paires hexadécimales séparées par des tirets, où chaque paire représente l'élément correspondant dans un sous-tableau de <paramref name="value" /> ; par exemple, "7F-2C-4A-00".</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <param name="length">Nombre d'éléments de tableau de <paramref name="value" /> à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> ou <paramref name="length" /> est inférieur à zéro.ou<paramref name="startIndex" /> est supérieur à zéro et supérieur ou égal à la longueur de <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentException">La combinaison de <paramref name="startIndex" /> et de <paramref name="length" /> ne spécifie pas de position dans <paramref name="value" /> ; en d'autres termes, le paramètre <paramref name="startIndex" /> est supérieur à la longueur de <paramref name="value" /> moins le paramètre <paramref name="length" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt16(System.Byte[],System.Int32)">
      <summary>Retourne un entier 16 bits non signé converti à partir de deux octets à une position spécifiée dans un tableau d'octets.</summary>
      <returns>Entier 16 bits non signé composé de deux octets, à partir de <paramref name="startIndex" />.</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> est égal à la longueur de <paramref name="value" /> moins 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt32(System.Byte[],System.Int32)">
      <summary>Retourne un entier 32 bits non signé converti à partir de quatre octets à une position spécifiée dans un tableau d'octets.</summary>
      <returns>Entier 32 bits non signé composé de quatre octets, à partir de <paramref name="startIndex" />.</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> est supérieur ou égal à la longueur de <paramref name="value" /> moins 3, et inférieur ou égal à la longueur de <paramref name="value" /> moins 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt64(System.Byte[],System.Int32)">
      <summary>Retourne un entier 64 bits non signé converti à partir de huit octets à une position spécifiée dans un tableau d'octets.</summary>
      <returns>Entier 64 bits non signé composé de huit octets, à partir de <paramref name="startIndex" />.</returns>
      <param name="value">Tableau d'octets. </param>
      <param name="startIndex">Point de départ dans <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> est supérieur ou égal à la longueur de <paramref name="value" /> moins 7, et inférieur ou égal à la longueur de <paramref name="value" /> moins 1.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est inférieur à zéro ou supérieur à la longueur de <paramref name="value" /> moins 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Convert">
      <summary>Convertit un type de données de base en un autre type de données de base.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type)">
      <summary>Retourne un objet du type spécifié dont la valeur est équivalente à celle de l'objet spécifié.</summary>
      <returns>Objet dont le type est <paramref name="conversionType" /> et dont la valeur équivaut à <paramref name="value" />.ouRéférence null (Nothing en Visual Basic), si <paramref name="value" /> est null et <paramref name="conversionType" /> n'est pas un type valeur. </returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="conversionType">Type d'objet à retourner. </param>
      <exception cref="T:System.InvalidCastException">Cette conversion n'est pas prise en charge.  ou<paramref name="value" /> est null et <paramref name="conversionType" /> est un type valeur.ou<paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format reconnu par <paramref name="conversionType" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre qui est en dehors de la plage de <paramref name="conversionType" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> a la valeur null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type,System.IFormatProvider)">
      <summary>Retourne un objet du type spécifié dont la valeur est équivalente à celle de l'objet spécifié.Un paramètre fournit des informations de mise en forme propres à la culture.</summary>
      <returns>Objet dont le type est <paramref name="conversionType" /> et dont la valeur équivaut à <paramref name="value" />.ou <paramref name="value" />, si le <see cref="T:System.Type" /> de <paramref name="value" /> et <paramref name="conversionType" /> sont égaux.ou Référence null (Nothing en Visual Basic), si <paramref name="value" /> est null et <paramref name="conversionType" /> n'est pas un type valeur.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="conversionType">Type d'objet à retourner. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.InvalidCastException">Cette conversion n'est pas prise en charge. ou<paramref name="value" /> est null et <paramref name="conversionType" /> est un type valeur.ou<paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format pour <paramref name="conversionType" /> reconnu par <paramref name="provider" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre qui est en dehors de la plage de <paramref name="conversionType" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> a la valeur null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.TypeCode,System.IFormatProvider)">
      <summary>Retourne un objet du type spécifié dont la valeur est équivalente à celle de l'objet spécifié.Un paramètre fournit des informations de mise en forme propres à la culture.</summary>
      <returns>Objet dont le type sous-jacent est <paramref name="typeCode" /> et dont la valeur équivaut à <paramref name="value" />.ou Référence null (Nothing en Visual Basic), si <paramref name="value" /> est null et <paramref name="typeCode" /> est <see cref="F:System.TypeCode.Empty" />, <see cref="F:System.TypeCode.String" />, ou <see cref="F:System.TypeCode.Object" />.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="typeCode">Type d'objet à retourner. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.InvalidCastException">Cette conversion n'est pas prise en charge.  ou<paramref name="value" /> est null et <paramref name="typeCode" /> spécifie un type valeur.ou<paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format pour le type <paramref name="typeCode" /> reconnu par <paramref name="provider" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre qui est en dehors de la plage du type <paramref name="typeCode" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeCode" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64CharArray(System.Char[],System.Int32,System.Int32)">
      <summary>Convertit un sous-ensemble d'un tableau de caractères Unicode, qui encode les données binaires en chiffres base 64, en un tableau équivalent d'entiers non signés 8 bits.Des paramètres spécifient le sous-ensemble du tableau d'entrée et le nombre d'éléments à convertir.</summary>
      <returns>Tableau d'entiers non signés 8 bits équivalant aux éléments <paramref name="length" /> situés à la position <paramref name="offset" /> dans <paramref name="inArray" />.</returns>
      <param name="inArray">Tableau de caractères Unicode. </param>
      <param name="offset">Position au sein de <paramref name="inArray" />. </param>
      <param name="length">Nombre d'éléments de <paramref name="inArray" /> à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ou <paramref name="length" /> est inférieur à 0.ou <paramref name="offset" /> plus <paramref name="length" /> indique une position en dehors de <paramref name="inArray" />. </exception>
      <exception cref="T:System.FormatException">La longueur de <paramref name="inArray" /> n'est pas zéro ou un multiple de 4 (en ignorant les espaces blancs). ouLe format de <paramref name="inArray" /> n'est pas valide.<paramref name="inArray" /> contient un caractère qui n'est pas en base 64, plus de deux caractères de remplissage ou un caractère autre qu'espace blanc parmi les caractères de remplissage.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64String(System.String)">
      <summary>Convertit la chaîne spécifiée, qui encode les données binaires en chiffres base 64, en tableau équivalent d'entiers non signés 8 bits.</summary>
      <returns>Tableau d'entiers non signés 8 bits qui est équivalent à <paramref name="s" />.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> a la valeur null. </exception>
      <exception cref="T:System.FormatException">La longueur de <paramref name="s" /> n'est pas zéro ou un multiple de 4 (en ignorant les espaces blancs). ouLe format de <paramref name="s" /> n'est pas valide.<paramref name="s" /> contient un caractère qui n'est pas en base 64, plus de deux caractères de remplissage ou un caractère autre qu'espace blanc parmi les caractères de remplissage.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.GetTypeCode(System.Object)">
      <summary>Retourne le <see cref="T:System.TypeCode" /> de l'objet spécifié.</summary>
      <returns>
        <see cref="T:System.TypeCode" /> de <paramref name="value" />, ou <see cref="F:System.TypeCode.Empty" /> si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64CharArray(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Convertit un sous-ensemble d'un tableau d'entiers non signés 8 bits en sous-ensemble équivalent d'un tableau de caractères Unicode encodé en chiffres base 64.Des paramètres spécifient les sous-ensembles en tant qu'offsets des tableaux d'entrée et de sortie ainsi que le nombre d'éléments du tableau d'entrée à convertir.</summary>
      <returns>Entier 32 bits signé contenant le nombre d'octets dans <paramref name="outArray" />.</returns>
      <param name="inArray">Tableau d'entrée d'entiers non signés 8 bits. </param>
      <param name="offsetIn">Position au sein de <paramref name="inArray" />. </param>
      <param name="length">Nombre d'éléments de <paramref name="inArray" /> à convertir. </param>
      <param name="outArray">Tableau de sortie de caractères Unicode. </param>
      <param name="offsetOut">Position au sein de <paramref name="outArray" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> ou <paramref name="outArray" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offsetIn" />, <paramref name="offsetOut" /> ou <paramref name="length" /> est négatif.ou <paramref name="offsetIn" /> plus <paramref name="length" /> est supérieur à la longueur de <paramref name="inArray" />.ou <paramref name="offsetOut" /> plus le nombre d'éléments à retourner est supérieur à la longueur de <paramref name="outArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[])">
      <summary>Convertit un tableau d'entiers non signés 8 bits en sa représentation sous forme de chaîne équivalente, encodée en chiffres base 64.</summary>
      <returns>Représentation sous forme de chaîne, en base 64, du contenu de <paramref name="inArray" />.</returns>
      <param name="inArray">Tableau d'entiers non signés 8 bits. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> a la valeur null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[],System.Int32,System.Int32)">
      <summary>Convertit un sous-ensemble d'un tableau d'entiers non signés 8 bits en sa représentation sous forme de chaîne équivalente, encodée en chiffres base 64.Des paramètres spécifient le sous-ensemble en tant qu'offset du tableau d'entrée et le nombre d'éléments du tableau à convertir.</summary>
      <returns>Représentation sous forme de chaîne, en base 64, des éléments <paramref name="length" /> de <paramref name="inArray" />, en commençant à la position <paramref name="offset" />.</returns>
      <param name="inArray">Tableau d'entiers non signés 8 bits. </param>
      <param name="offset">Offset dans <paramref name="inArray" />. </param>
      <param name="length">Nombre d'éléments de <paramref name="inArray" /> à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ou <paramref name="length" /> est négatif.ou <paramref name="offset" /> plus <paramref name="length" /> est supérieur à la longueur de <paramref name="inArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Boolean)">
      <summary>Retourne la valeur booléenne spécifiée. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Valeur booléenne à retourner. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Nombre à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int16)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int32)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int64)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object)">
      <summary>Convertit la valeur d'un objet spécifié en valeur booléenne équivalente.</summary>
      <returns>true ou false, qui reflète la valeur retourné en appelant la méthode <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> pour le type sous-jacent de <paramref name="value" />.Si <paramref name="value" /> est null, la méthode retourne false.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> est une chaîne qui n'égale pas <see cref="F:System.Boolean.TrueString" /> ou <see cref="F:System.Boolean.FalseString" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />.ouLa conversion de <paramref name="value" /> en <see cref="T:System.Boolean" /> n'est pas prise en charge.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en valeur booléenne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>true ou false, qui reflète la valeur retourné en appelant la méthode <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> pour le type sous-jacent de <paramref name="value" />.Si <paramref name="value" /> est null, la méthode retourne false.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> est une chaîne qui n'égale pas <see cref="F:System.Boolean.TrueString" /> ou <see cref="F:System.Boolean.FalseString" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />.ouLa conversion de <paramref name="value" /> en <see cref="T:System.Boolean" /> n'est pas prise en charge. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'une valeur logique en son équivalent booléen.</summary>
      <returns>true si <paramref name="value" /> équivaut à <see cref="F:System.Boolean.TrueString" /> ou false si <paramref name="value" /> équivaut à <see cref="F:System.Boolean.FalseString" /> ou null.</returns>
      <param name="value">Chaîne qui contient la valeur de <see cref="F:System.Boolean.TrueString" /> ou de <see cref="F:System.Boolean.FalseString" />. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas égal à <see cref="F:System.Boolean.TrueString" /> ni à <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'une valeur logique en son équivalent booléen à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>true si <paramref name="value" /> équivaut à <see cref="F:System.Boolean.TrueString" /> ou false si <paramref name="value" /> équivaut à <see cref="F:System.Boolean.FalseString" /> ou null.</returns>
      <param name="value">Chaîne qui contient la valeur de <see cref="F:System.Boolean.TrueString" /> ou de <see cref="F:System.Boolean.FalseString" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture.Ce paramètre est ignoré.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas égal à <see cref="F:System.Boolean.TrueString" /> ni à <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt32)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en valeur booléenne équivalente.</summary>
      <returns>true si <paramref name="value" /> est différent de zéro ; sinon, false.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en entier non signé 8 bits équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Byte)">
      <summary>Retourne l'entier non signé 8 bits spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Entier non signé 8 bits à retourner. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Char)">
      <summary>Convertit la valeur du caractère Unicode spécifié en entier non signé 8 bits équivalent.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Caractère Unicode à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre qui est supérieur à <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en entier non signé 8 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 8 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Byte.MaxValue" /> ou inférieur à <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en entier non signé 8 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 8 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Byte.MaxValue" /> ou inférieur à <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int16)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en entier non signé 8 bits équivalent.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à <see cref="F:System.Byte.MinValue" /> ou supérieur à <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int32)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en entier non signé 8 bits équivalent.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à <see cref="F:System.Byte.MinValue" /> ou supérieur à <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int64)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en entier non signé 8 bits équivalent.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à <see cref="F:System.Byte.MinValue" /> ou supérieur à <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en entier non signé 8 bits.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans le format de propriété pour une valeur <see cref="T:System.Byte" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas <see cref="T:System.IConvertible" />. ouLa conversion de <paramref name="value" /> en type <see cref="T:System.Byte" /> n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Byte.MinValue" /> ou supérieur à <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en entier non signé 8 bits à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans le format de propriété pour une valeur <see cref="T:System.Byte" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas <see cref="T:System.IConvertible" />. ouLa conversion de <paramref name="value" /> en type <see cref="T:System.Byte" /> n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Byte.MinValue" /> ou supérieur à <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en entier non signé 8 bits équivalent.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en entier non signé 8 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 8 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante simple précision. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Byte.MaxValue" /> ou inférieur à <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier non signé 8 bits équivalent.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Byte.MinValue" /> ou supérieur à <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier non signé 8 bits équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Byte.MinValue" /> ou supérieur à <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.Int32)">
      <summary>Convertit la représentation sous forme de chaîne d'un nombre dans une base spécifiée en entier non signé 8 bits équivalent.</summary>
      <returns>Entier non signé 8 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="fromBase">Base du nombre figurant dans <paramref name="value" />, qui doit correspondre à 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> n'est pas 2, 8, 10 ou 16. ou<paramref name="value" />, qui représente un nombre non signé de base autre que 10, est préfixé avec un signe moins. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> a la valeur <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contient un caractère qui n'est pas un chiffre valide dans la base spécifiée par <paramref name="fromBase" />.Le message d'exception indique qu'il n'y a pas de chiffres à convertir si le premier caractère de <paramref name="value" /> n'est pas valide ; sinon, il indique que <paramref name="value" /> contient des caractères de fin non valides.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, qui représente un nombre non signé de base 10, est préfixé avec un signe moins.ou<paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Byte.MinValue" /> ou supérieur à <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt16)">
      <summary>Convertit la valeur de l'entier 16 bits non signé spécifié en un entier 8 bits non signé équivalent.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt32)">
      <summary>Convertit la valeur de l'entier 32 bits non signé spécifié en un entier 8 bits non signé équivalent.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>Entier non signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en son caractère Unicode équivalent.</summary>
      <returns>Caractère Unicode équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int16)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en son caractère Unicode équivalent.</summary>
      <returns>Caractère Unicode équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int32)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en son caractère Unicode équivalent.</summary>
      <returns>Caractère Unicode équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à <see cref="F:System.Char.MinValue" /> ou supérieur à <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int64)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en son caractère Unicode équivalent.</summary>
      <returns>Caractère Unicode équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à <see cref="F:System.Char.MinValue" /> ou supérieur à <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en caractère Unicode.</summary>
      <returns>Caractère Unicode qui est équivalent à la valeur, ou <see cref="F:System.Char.MinValue" /> si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> est une chaîne Null.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />.ouLa conversion de <paramref name="value" /> en <see cref="T:System.Char" /> n'est pas prise en charge. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à <see cref="F:System.Char.MinValue" /> ou supérieur à <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en son caractère Unicode équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Caractère Unicode qui est équivalent à <paramref name="value" />, ou <see cref="F:System.Char.MinValue" /> si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> est une chaîne Null.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion de <paramref name="value" /> en <see cref="T:System.Char" /> n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à <see cref="F:System.Char.MinValue" /> ou supérieur à <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en son caractère Unicode équivalent.</summary>
      <returns>Caractère Unicode équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String)">
      <summary>Convertit le premier caractère d'une chaîne spécifiée en caractère Unicode.</summary>
      <returns>Caractère Unicode qui est équivalent au premier et unique caractère de <paramref name="value" />.</returns>
      <param name="value">Chaîne de longueur 1. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.FormatException">La longueur de <paramref name="value" /> n'est pas 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String,System.IFormatProvider)">
      <summary>Convertit le premier caractère d'une chaîne spécifiée en caractère Unicode à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Caractère Unicode qui est équivalent au premier et unique caractère de <paramref name="value" />.</returns>
      <param name="value">Chaîne de longueur 1 ou null. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture.Ce paramètre est ignoré.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.FormatException">La longueur de <paramref name="value" /> n'est pas 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en son caractère Unicode équivalent.</summary>
      <returns>Caractère Unicode équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt32)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en son caractère Unicode équivalent.</summary>
      <returns>Caractère Unicode équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en son caractère Unicode équivalent.</summary>
      <returns>Caractère Unicode équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en objet <see cref="T:System.DateTime" />.</summary>
      <returns>Équivalent de date et d'heure de la valeur de <paramref name="value" /> ou équivalent de date et d'heure de <see cref="F:System.DateTime.MinValue" /> si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas une date et une heure valides.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en objet <see cref="T:System.DateTime" /> à l'aide des informations de mise en forme propres à la culture spécifiées.</summary>
      <returns>Équivalent de date et heure de la valeur de <paramref name="value" /> ou équivalent de date et heure de <see cref="F:System.DateTime.MinValue" /> si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas une date et une heure valides.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'une valeur de date et heure en valeur de date et heure équivalente.</summary>
      <returns>Équivalent de date et heure de la valeur de <paramref name="value" /> ou équivalent de date et heure de <see cref="F:System.DateTime.MinValue" /> si <paramref name="value" /> est null.</returns>
      <param name="value">Représentation sous forme de chaîne d'une valeur de date et heure.</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas une chaîne de date et d'heure correctement mise en forme. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en valeur équivalente de date et heure à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Équivalent de date et heure de la valeur de <paramref name="value" /> ou équivalent de date et heure de <see cref="F:System.DateTime.MinValue" /> si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant une date et une heure à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas une chaîne de date et d'heure correctement mise en forme. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en nombre décimal équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal qui est équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Decimal)">
      <summary>Retourne le nombre décimal spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Nombre décimal. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal équivalent à <paramref name="value" />. </returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Decimal.MaxValue" /> ou inférieur à <see cref="F:System.Decimal.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int16)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int32)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int64)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal qui est équivalent à <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié pour un type <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Decimal.MinValue" /> ou supérieur à <see cref="F:System.Decimal.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en nombre décimal équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Nombre décimal qui est équivalent à <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié pour un type <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />.ouLa conversion n'est pas prise en charge. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Decimal.MinValue" /> ou supérieur à <see cref="F:System.Decimal.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal équivalent à <paramref name="value" />. </returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Decimal.MaxValue" /> ou inférieur à <see cref="F:System.Decimal.MinValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en nombre décimal équivalent.</summary>
      <returns>Nombre décimal qui est équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant un nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas un nombre au format valide.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Decimal.MinValue" /> ou supérieur à <see cref="F:System.Decimal.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en nombre décimal équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Nombre décimal qui est équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant un nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas un nombre au format valide.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Decimal.MinValue" /> ou supérieur à <see cref="F:System.Decimal.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal qui est équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt32)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en nombre décimal équivalent.</summary>
      <returns>Nombre décimal équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Nombre à virgule flottante double précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Nombre à virgule flottante double précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Double)">
      <summary>Retourne le nombre à virgule flottante double précision spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Nombre à virgule flottante double précision à retourner. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int16)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Nombre à virgule flottante double précision équivalant à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int32)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Nombre à virgule flottante double précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int64)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Nombre à virgule flottante double précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en nombre à virgule flottante double précision.</summary>
      <returns>Nombre à virgule flottante double précision qui est équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié pour un type <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Double.MinValue" /> ou supérieur à <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en nombre à virgule flottante double précision à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Nombre à virgule flottante double précision qui est équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié pour un type <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Double.MinValue" /> ou supérieur à <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Nombre à virgule flottante double précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Nombre à virgule flottante simple précision. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Nombre à virgule flottante double précision qui est équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas un nombre au format valide.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Double.MinValue" /> ou supérieur à <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en nombre à virgule flottante double précision équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Nombre à virgule flottante double précision qui est équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas un nombre au format valide.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Double.MinValue" /> ou supérieur à <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Nombre à virgule flottante double précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt32)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Nombre à virgule flottante double précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en nombre à virgule flottante double précision équivalent.</summary>
      <returns>Nombre à virgule flottante double précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en entier signé 16 bits équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 16-bit signed integer.</summary>
      <returns>Entier signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Char)">
      <summary>Convertit la valeur du caractère Unicode spécifié en entier signé 16 bits équivalent.</summary>
      <returns>Entier signé 16 bits équivalent à <paramref name="value" />. </returns>
      <param name="value">Caractère Unicode à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en entier signé 16 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 16 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int16.MaxValue" /> ou inférieur à <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en entier signé 16 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 16 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int16.MaxValue" /> ou inférieur à <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int16)">
      <summary>Retourne l'entier signé 16 bits spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Entier signé 16 bits à retourner. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int32)">
      <summary>Convertit la valeur de l'entier 32 bits signé spécifié en un entier 16 bits signé équivalent.</summary>
      <returns>Entier 16 bits signé équivalant à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int16.MaxValue" /> ou inférieur à <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int64)">
      <summary>Convertit la valeur de l'entier 64 bits signé spécifié en un entier 16 bits signé équivalent.</summary>
      <returns>Entier signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int16.MaxValue" /> ou inférieur à <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en entier signé 16 bits.</summary>
      <returns>Entier signé 16 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié pour un type <see cref="T:System.Int16" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int16.MinValue" /> ou supérieur à <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en entier signé 16 bits à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier signé 16 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié pour un type <see cref="T:System.Int16" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int16.MinValue" /> ou supérieur à <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en entier signé 16 bits équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en entier signé 16 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 16 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int16.MaxValue" /> ou inférieur à <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier signé 16 bits équivalent.</summary>
      <returns>Entier signé 16 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int16.MinValue" /> ou supérieur à <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier signé 16 bits équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier signé 16 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int16.MinValue" /> ou supérieur à <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.Int32)">
      <summary>Convertit la représentation sous forme de chaîne d'un nombre dans une base spécifiée en entier signé 16 bits équivalent.</summary>
      <returns>Entier signé 16 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="fromBase">Base du nombre figurant dans <paramref name="value" />, qui doit correspondre à 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> n'est pas 2, 8, 10 ou 16. ou<paramref name="value" />, qui représente un nombre signé de base autre que 10, est préfixé avec un signe moins. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> a la valeur <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contient un caractère qui n'est pas un chiffre valide dans la base spécifiée par <paramref name="fromBase" />.Le message d'exception indique qu'il n'y a pas de chiffres à convertir si le premier caractère de <paramref name="value" /> n'est pas valide ; sinon, il indique que <paramref name="value" /> contient des caractères de fin non valides.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, qui représente un nombre signé de base autre que 10, est préfixé avec un signe moins.ou<paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int16.MinValue" /> ou supérieur à <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en entier signé 16 bits équivalent.</summary>
      <returns>Entier signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt32)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en entier signé 16 bits équivalent.</summary>
      <returns>Entier signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en entier signé 16 bits équivalent.</summary>
      <returns>Entier signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en entier signé 32 bits équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en entier signé 32 bits équivalent.</summary>
      <returns>Entier signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Char)">
      <summary>Convertit la valeur du caractère Unicode spécifié en entier signé 32 bits équivalent.</summary>
      <returns>Entier signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Caractère Unicode à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en entier signé 32 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 32 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int32.MaxValue" /> ou inférieur à <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en entier signé 32 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 32 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int32.MaxValue" /> ou inférieur à <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>Entier signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int32)">
      <summary>Retourne l'entier signé 32 bits spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Entier signé 32 bits à retourner. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>Entier signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int32.MaxValue" /> ou inférieur à <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en entier signé 32 bits.</summary>
      <returns>Entier signé 32 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int32.MinValue" /> ou supérieur à <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en entier signé 32 bits à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier signé 32 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int32.MinValue" /> ou supérieur à <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en entier signé 32 bits équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en entier signé 32 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 32 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int32.MaxValue" /> ou inférieur à <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier signé 32 bits équivalent.</summary>
      <returns>Entier signé 32 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int32.MinValue" /> ou supérieur à <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier signé 32 bits équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier signé 32 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int32.MinValue" /> ou supérieur à <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.Int32)">
      <summary>Convertit la représentation sous forme de chaîne d'un nombre dans une base spécifiée en entier signé 32 bits équivalent.</summary>
      <returns>Entier signé 32 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="fromBase">Base du nombre figurant dans <paramref name="value" />, qui doit correspondre à 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> n'est pas 2, 8, 10 ou 16. ou<paramref name="value" />, qui représente un nombre signé de base autre que 10, est préfixé avec un signe moins. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> a la valeur <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contient un caractère qui n'est pas un chiffre valide dans la base spécifiée par <paramref name="fromBase" />.Le message d'exception indique qu'il n'y a pas de chiffres à convertir si le premier caractère de <paramref name="value" /> n'est pas valide ; sinon, il indique que <paramref name="value" /> contient des caractères de fin non valides.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, qui représente un nombre signé de base autre que 10, est préfixé avec un signe moins.ou<paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int32.MinValue" /> ou supérieur à <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en entier signé 32 bits équivalent.</summary>
      <returns>Entier signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt32)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en entier signé 32 bits équivalent.</summary>
      <returns>Entier signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en entier signé 32 bits équivalent.</summary>
      <returns>Entier signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en entier signé 64 bits équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en entier signé 64 bits équivalent.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Char)">
      <summary>Convertit la valeur du caractère Unicode spécifié en entier signé 64 bits équivalent.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Caractère Unicode à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en entier signé 64 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 64 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int64.MaxValue" /> ou inférieur à <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en entier signé 64 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 64 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int64.MaxValue" /> ou inférieur à <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int16)">
      <summary>Convertit la valeur de l'entier 16 bits signé spécifié en un entier 64 bits signé équivalent.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int32)">
      <summary>Convertit la valeur de l'entier 32 bits signé spécifié en un entier 64 bits signé équivalent.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int64)">
      <summary>Retourne l'entier signé 64 bits spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Entier signé 64 bits. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en entier signé 64 bits.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int64.MinValue" /> ou supérieur à <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en entier signé 64 bits à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />.ouLa conversion n'est pas prise en charge. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int64.MinValue" /> ou supérieur à <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en entier signé 64 bits équivalent.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en entier signé 64 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 64 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int64.MaxValue" /> ou inférieur à <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier signé 64 bits équivalent.</summary>
      <returns>Entier signé 64 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant un nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int64.MinValue" /> ou supérieur à <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier signé 64 bits équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier signé 64 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int64.MinValue" /> ou supérieur à <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.Int32)">
      <summary>Convertit la représentation sous forme de chaîne d'un nombre dans une base spécifiée en entier signé 64 bits équivalent.</summary>
      <returns>Entier signé 64 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="fromBase">Base du nombre figurant dans <paramref name="value" />, qui doit correspondre à 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> n'est pas 2, 8, 10 ou 16. ou<paramref name="value" />, qui représente un nombre signé de base autre que 10, est préfixé avec un signe moins. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> a la valeur <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contient un caractère qui n'est pas un chiffre valide dans la base spécifiée par <paramref name="fromBase" />.Le message d'exception indique qu'il n'y a pas de chiffres à convertir si le premier caractère de <paramref name="value" /> n'est pas valide ; sinon, il indique que <paramref name="value" /> contient des caractères de fin non valides.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, qui représente un nombre signé de base autre que 10, est préfixé avec un signe moins.ou<paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Int64.MinValue" /> ou supérieur à <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en entier signé 64 bits équivalent.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt32)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en entier signé 64 bits équivalent.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en entier signé 64 bits équivalent.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en entier signé 8 bits équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en entier signé 8 bits équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Char)">
      <summary>Convertit la valeur du caractère Unicode spécifié en entier signé 8 bits équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Caractère Unicode à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en entier signé 8 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 8 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" /> ou inférieur à <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en entier signé 8 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 8 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" /> ou inférieur à <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 8-bit signed integer.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" /> ou inférieur à <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int32)">
      <summary>Convertit la valeur de l'entier 32 bits signé spécifié en un entier 8 bits signé équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" /> ou inférieur à <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int64)">
      <summary>Convertit la valeur de l'entier 64 bits signé spécifié en un entier 8 bits signé équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" /> ou inférieur à <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en entier signé 8 bits.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.SByte.MinValue" /> ou supérieur à <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en entier signé 8 bits à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié. </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.SByte.MinValue" /> ou supérieur à <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.SByte)">
      <summary>Retourne l'entier signé 8 bits spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Entier signé 8 bits à retourner. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en entier signé 8 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier signé 8 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" /> ou inférieur à <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier signé 8 bits équivalent.</summary>
      <returns>Entier signé 8 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si la valeur est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.SByte.MinValue" /> ou supérieur à <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier signé 8 bits équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.SByte.MinValue" /> ou supérieur à <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.Int32)">
      <summary>Convertit la représentation sous forme de chaîne d'un nombre dans une base spécifiée en entier signé 8 bits équivalent.</summary>
      <returns>Entier signé 8 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="fromBase">Base du nombre figurant dans <paramref name="value" />, qui doit correspondre à 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> n'est pas 2, 8, 10 ou 16. ou<paramref name="value" />, qui représente un nombre signé de base autre que 10, est préfixé avec un signe moins. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> a la valeur <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contient un caractère qui n'est pas un chiffre valide dans la base spécifiée par <paramref name="fromBase" />.Le message d'exception indique qu'il n'y a pas de chiffres à convertir si le premier caractère de <paramref name="value" /> n'est pas valide ; sinon, il indique que <paramref name="value" /> contient des caractères de fin non valides.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, qui représente un nombre signé de base autre que 10, est préfixé avec un signe moins.ou<paramref name="value" /> représente un nombre inférieur à <see cref="F:System.SByte.MinValue" /> ou supérieur à <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en entier signé 8 bits équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt32)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en entier signé 8 bits équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" /> ou inférieur à <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en entier signé 8 bits équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.SByte.MaxValue" /> ou inférieur à <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Nombre à virgule flottante simple précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Nombre à virgule flottante simple précision équivalent à <paramref name="value" />.<paramref name="value" /> est arrondi selon le principe d'arrondi au plus près.Par exemple, lorsqu'elle est arrondie à deux décimales, la valeur 2,345 devient 2,34 et la valeur 2,355 devient 2,36.</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Nombre à virgule flottante simple précision équivalent à <paramref name="value" />.<paramref name="value" /> est arrondi selon le principe d'arrondi au plus près.Par exemple, lorsqu'elle est arrondie à deux décimales, la valeur 2,345 devient 2,34 et la valeur 2,355 devient 2,36.</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int16)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Nombre à virgule flottante simple précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int32)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Nombre à virgule flottante simple précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int64)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Nombre à virgule flottante simple précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en nombre à virgule flottante simple précision.</summary>
      <returns>Nombre à virgule flottante simple précision qui est équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Single.MinValue" /> ou supérieur à <see cref="F:System.Single.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en nombre à virgule flottante simple précision à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Nombre à virgule flottante simple précision qui est équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Single.MinValue" /> ou supérieur à <see cref="F:System.Single.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Entier signé 8 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Single)">
      <summary>Retourne le nombre à virgule flottante simple précision spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Nombre à virgule flottante simple précision à retourner. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Nombre à virgule flottante simple précision qui est équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas un nombre au format valide.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Single.MinValue" /> ou supérieur à <see cref="F:System.Single.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en nombre à virgule flottante simple précision équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Nombre à virgule flottante simple précision qui est équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas un nombre au format valide.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.Single.MinValue" /> ou supérieur à <see cref="F:System.Single.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Nombre à virgule flottante simple précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt32)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Nombre à virgule flottante simple précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en nombre à virgule flottante simple précision équivalent.</summary>
      <returns>Nombre à virgule flottante simple précision équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean,System.IFormatProvider)">
      <summary>Convertit la valeur booléenne spécifiée en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <param name="provider">Instance d'un objet.Ce paramètre est ignoré.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.IFormatProvider)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.Int32)">
      <summary>Convertit la valeur d'un entier non signé 8 bits en sa représentation sous forme de chaîne équivalente dans une base spécifiée.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" /> en base <paramref name="toBase" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <param name="toBase">Base de la valeur de retour, qui doit être 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> n'est pas 2, 8, 10 ou 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char)">
      <summary>Convertit la valeur du caractère Unicode spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Caractère Unicode à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char,System.IFormatProvider)">
      <summary>Convertit la valeur du caractère Unicode spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Caractère Unicode à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture.Ce paramètre est ignoré.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime)">
      <summary>Convertit la valeur du <see cref="T:System.DateTime" /> spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Valeur de date et heure à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime,System.IFormatProvider)">
      <summary>Convertit la valeur du <see cref="T:System.DateTime" /> spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme propres à la culture spécifiées.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Valeur de date et heure à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal,System.IFormatProvider)">
      <summary>Convertit la valeur du nombre décimal spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double,System.IFormatProvider)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.IFormatProvider)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.Int32)">
      <summary>Convertit la valeur d'un entier signé 16 bits en sa représentation sous forme de chaîne équivalente dans une base spécifiée.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" /> en base <paramref name="toBase" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <param name="toBase">Base de la valeur de retour, qui doit être 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> n'est pas 2, 8, 10 ou 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.IFormatProvider)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.Int32)">
      <summary>Convertit la valeur d'un entier signé 32 bits en sa représentation sous forme de chaîne équivalente dans une base spécifiée.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" /> en base <paramref name="toBase" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <param name="toBase">Base de la valeur de retour, qui doit être 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> n'est pas 2, 8, 10 ou 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.IFormatProvider)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.Int32)">
      <summary>Convertit la valeur d'un entier signé 64 bits en sa représentation sous forme de chaîne équivalente dans une base spécifiée.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" /> en base <paramref name="toBase" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <param name="toBase">Base de la valeur de retour, qui doit être 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> n'est pas 2, 8, 10 ou 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>La représentation sous forme de chaîne de <paramref name="value" />, ou <see cref="F:System.String.Empty" /> si la valeur <paramref name="value" /> est un objet dont la valeur est null.Si <paramref name="value" /> est null, la méthode retourne null.</returns>
      <param name="value">Objet qui fournit la valeur à convertir, ou null. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>La représentation sous forme de chaîne de <paramref name="value" />, ou <see cref="F:System.String.Empty" /> si la valeur <paramref name="value" /> est un objet dont la valeur est null.Si <paramref name="value" /> est null, la méthode retourne null.</returns>
      <param name="value">Objet qui fournit la valeur à convertir, ou null. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte,System.IFormatProvider)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single,System.IFormatProvider)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16,System.IFormatProvider)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32,System.IFormatProvider)">
      <summary>Convertit la valeur de l'entier non signé 32 bits spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en sa représentation sous forme de chaîne équivalente.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64,System.IFormatProvider)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en sa représentation sous forme de chaîne équivalente à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Représentation sous forme de chaîne de <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en entier non signé 16 bits équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en l'entier non signé 16 bits équivalent.</summary>
      <returns>Entier non signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Char)">
      <summary>Convertit la valeur du caractère Unicode spécifié en entier non signé 16 bits équivalent.</summary>
      <returns>Entier 16 bits non signé équivalent à <paramref name="value" />.</returns>
      <param name="value">Caractère Unicode à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en entier non signé 16 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 16 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en entier non signé 16 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 16 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int16)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en entier non signé 16 bits équivalent.</summary>
      <returns>Entier non signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int32)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en entier non signé 16 bits équivalent.</summary>
      <returns>Entier non signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int64)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en entier non signé 16 bits équivalent.</summary>
      <returns>Entier non signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en entier non signé 16 bits.</summary>
      <returns>Entier non signé 16 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt16.MinValue" /> ou supérieur à <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en entier non signé 16 bits à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier non signé 16 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt16.MinValue" /> ou supérieur à <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en entier non signé 16 bits équivalent.</summary>
      <returns>Entier non signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en entier non signé 16 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 16 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier non signé 16 bits équivalent.</summary>
      <returns>Entier non signé 16 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt16.MinValue" /> ou supérieur à <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier non signé 16 bits équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier non signé 16 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt16.MinValue" /> ou supérieur à <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.Int32)">
      <summary>Convertit la représentation sous forme de chaîne d'un nombre dans une base spécifiée en entier non signé 16 bits équivalent.</summary>
      <returns>Entier non signé 16 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="fromBase">Base du nombre figurant dans <paramref name="value" />, qui doit correspondre à 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> n'est pas 2, 8, 10 ou 16. ou<paramref name="value" />, qui représente un nombre non signé de base autre que 10, est préfixé avec un signe moins. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> a la valeur <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contient un caractère qui n'est pas un chiffre valide dans la base spécifiée par <paramref name="fromBase" />.Le message d'exception indique qu'il n'y a pas de chiffres à convertir si le premier caractère de <paramref name="value" /> n'est pas valide ; sinon, il indique que <paramref name="value" /> contient des caractères de fin non valides.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, qui représente un nombre non signé de base autre que 10, est préfixé avec un signe moins.ou<paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt16.MinValue" /> ou supérieur à <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt16)">
      <summary>Retourne l'entier non signé 16 bits spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Entier non signé 16 bits à retourner. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt32)">
      <summary>Convertit la valeur de l'entier 32 bits non signé spécifié en un entier 16 bits non signé équivalent.</summary>
      <returns>Entier non signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en un entier non signé 16 bits équivalent.</summary>
      <returns>Entier non signé 16 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en entier non signé 32 bits équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en l'entier non signé 32 bits équivalent.</summary>
      <returns>Entier non signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Char)">
      <summary>Convertit la valeur du caractère Unicode spécifié en entier non signé 32 bits équivalent.</summary>
      <returns>Entier non signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Caractère Unicode à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en entier non signé 32 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 32 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en entier non signé 32 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 32 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int16)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en entier non signé 32 bits équivalent.</summary>
      <returns>Entier non signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int32)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en entier non signé 32 bits équivalent.</summary>
      <returns>Entier non signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int64)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en entier non signé 32 bits équivalent.</summary>
      <returns>Entier non signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en entier non signé 32 bits.</summary>
      <returns>Entier non signé 32 bits équivalent à <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt32.MinValue" /> ou supérieur à <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en entier non signé 32 bits à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier non signé 32 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt32.MinValue" /> ou supérieur à <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en entier non signé 32 bits équivalent.</summary>
      <returns>Entier non signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en entier non signé 32 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 32 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier non signé 32 bits équivalent.</summary>
      <returns>Entier non signé 32 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt32.MinValue" /> ou supérieur à <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier non signé 32 bits équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier non signé 32 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt32.MinValue" /> ou supérieur à <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.Int32)">
      <summary>Convertit la représentation sous forme de chaîne d'un nombre dans une base spécifiée en entier non signé 32 bits équivalent.</summary>
      <returns>Entier non signé 32 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="fromBase">Base du nombre figurant dans <paramref name="value" />, qui doit correspondre à 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> n'est pas 2, 8, 10 ou 16. ou<paramref name="value" />, qui représente un nombre non signé de base autre que 10, est préfixé avec un signe moins. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> a la valeur <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contient un caractère qui n'est pas un chiffre valide dans la base spécifiée par <paramref name="fromBase" />.Le message d'exception indique qu'il n'y a pas de chiffres à convertir si le premier caractère de <paramref name="value" /> n'est pas valide ; sinon, il indique que <paramref name="value" /> contient des caractères de fin non valides.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, qui représente un nombre non signé de base autre que 10, est préfixé avec un signe moins.ou<paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt32.MinValue" /> ou supérieur à <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en l'entier non signé 32 bits équivalent.</summary>
      <returns>Entier non signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt32)">
      <summary>Retourne l'entier non signé 32 bits spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Entier non signé 32 bits à retourner. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt64)">
      <summary>Convertit la valeur de l'entier non signé 64 bits spécifié en un entier non signé 32 bits équivalent.</summary>
      <returns>Entier non signé 32 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est supérieur à <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Boolean)">
      <summary>Convertit la valeur booléenne spécifiée en entier non signé 64 bits équivalent.</summary>
      <returns>Chiffre 1, si <paramref name="value" /> est true ; sinon, 0.</returns>
      <param name="value">Valeur Boolean à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Byte)">
      <summary>Convertit la valeur de l'entier non signé 8 bits spécifié en l'entier non signé 64 bits équivalent.</summary>
      <returns>Entier signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 8 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Char)">
      <summary>Convertit la valeur du caractère Unicode spécifié en entier non signé 64 bits équivalent.</summary>
      <returns>Entier non signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Caractère Unicode à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Decimal)">
      <summary>Convertit la valeur du nombre décimal spécifié en entier non signé 64 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 64 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre décimal à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Double)">
      <summary>Convertit la valeur du nombre à virgule flottante double précision spécifié en entier non signé 64 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 64 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante double précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int16)">
      <summary>Convertit la valeur de l'entier signé 16 bits spécifié en entier non signé 64 bits équivalent.</summary>
      <returns>Entier non signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 16 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int32)">
      <summary>Convertit la valeur de l'entier signé 32 bits spécifié en entier non signé 64 bits équivalent.</summary>
      <returns>Entier non signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 32 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int64)">
      <summary>Convertit la valeur de l'entier signé 64 bits spécifié en entier non signé 64 bits équivalent.</summary>
      <returns>Entier non signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 64 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object)">
      <summary>Convertit la valeur de l'objet spécifié en entier non signé 64 bits.</summary>
      <returns>Entier non signé 64 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" /> ou null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt64.MinValue" /> ou supérieur à <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object,System.IFormatProvider)">
      <summary>Convertit la valeur de l'objet spécifié en entier non signé 64 bits à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier non signé 64 bits équivalent à <paramref name="value" />, ou zéro si <paramref name="value" /> est null.</returns>
      <param name="value">Objet qui implémente l'interface <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas dans un format approprié.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> n'implémente pas l'interface <see cref="T:System.IConvertible" />. ouLa conversion n'est pas prise en charge.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt64.MinValue" /> ou supérieur à <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.SByte)">
      <summary>Convertit la valeur de l'entier signé 8 bits spécifié en entier non signé 64 bits équivalent.</summary>
      <returns>Entier non signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier signé 8 bits à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Single)">
      <summary>Convertit la valeur du nombre à virgule flottante simple précision spécifié en entier non signé 64 bits équivalent.</summary>
      <returns>
        <paramref name="value" /> arrondi à l'entier non signé 64 bits le plus proche.Si <paramref name="value" /> se trouve entre deux nombres entiers, le nombre pair est retourné (par exemple, 4,5 est converti en 4 et 5,5 en 6).</returns>
      <param name="value">Nombre à virgule flottante simple précision à convertir. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est inférieur à zéro ou supérieur à <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier non signé 64 bits équivalent.</summary>
      <returns>Entier signé 64 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt64.MinValue" /> ou supérieur à <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.IFormatProvider)">
      <summary>Convertit la représentation sous forme de chaîne spécifiée d'un nombre en entier non signé 64 bits équivalent à l'aide des informations de mise en forme spécifiées propres à la culture.</summary>
      <returns>Entier non signé 64 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="provider">Objet qui fournit des informations de mise en forme propres à la culture. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> n'est pas constitué d'un signe facultatif suivi d'une séquence de chiffres (0 à 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt64.MinValue" /> ou supérieur à <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.Int32)">
      <summary>Convertit la représentation sous forme de chaîne d'un nombre dans une base spécifiée en entier non signé 64 bits équivalent.</summary>
      <returns>Entier non signé 64 bits équivalent au nombre dans <paramref name="value" />, ou 0 (zéro) si <paramref name="value" /> est null.</returns>
      <param name="value">Chaîne contenant le nombre à convertir. </param>
      <param name="fromBase">Base du nombre figurant dans <paramref name="value" />, qui doit correspondre à 2, 8, 10 ou 16. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> n'est pas 2, 8, 10 ou 16. ou<paramref name="value" />, qui représente un nombre non signé de base autre que 10, est préfixé avec un signe moins. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> a la valeur <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> contient un caractère qui n'est pas un chiffre valide dans la base spécifiée par <paramref name="fromBase" />.Le message d'exception indique qu'il n'y a pas de chiffres à convertir si le premier caractère de <paramref name="value" /> n'est pas valide ; sinon, il indique que <paramref name="value" /> contient des caractères de fin non valides.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />, qui représente un nombre non signé de base autre que 10, est préfixé avec un signe moins.ou<paramref name="value" /> représente un nombre inférieur à <see cref="F:System.UInt64.MinValue" /> ou supérieur à <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt16)">
      <summary>Convertit la valeur de l'entier non signé 16 bits spécifié en l'entier non signé 64 bits équivalent.</summary>
      <returns>Entier non signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 16 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt32)">
      <summary>Convertit la valeur de l'entier 32 bits non signé spécifié en un entier 64 bits non signé équivalent.</summary>
      <returns>Entier non signé 64 bits équivalent à <paramref name="value" />.</returns>
      <param name="value">Entier non signé 32 bits à convertir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt64)">
      <summary>Retourne l'entier non signé 64 bits spécifié. Aucune conversion n'est effectuée.</summary>
      <returns>
        <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Entier non signé 64 bits à retourner. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Environment">
      <summary>Fournit des informations concernant l'environnement et la plateforme actuels, ainsi que des moyens pour les manipuler.Cette classe ne peut pas être héritée.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.CurrentManagedThreadId">
      <summary>Obtient un identificateur unique pour le thread managé actuel.</summary>
      <returns>Entier représentant un identificateur unique pour ce thread managé.</returns>
    </member>
    <member name="M:System.Environment.ExpandEnvironmentVariables(System.String)">
      <summary>Remplace le nom de chaque variable d'environnement incorporée dans la chaîne spécifiée par la chaîne équivalente de la valeur de la variable, puis retourne la chaîne qui en résulte.</summary>
      <returns>Chaîne avec chaque variable d'environnement remplacée par sa valeur.</returns>
      <param name="name">Chaîne contenant les noms d'aucune ou plusieurs variables d'environnement.Chaque variable d'environnement est citée avec le signe de pourcentage (%).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.FailFast(System.String)">
      <summary>Met immédiatement fin à un processus après avoir écrit un message dans le journal des événements des applications Windows, puis inclut le message dans le rapport d'erreurs à Microsoft.</summary>
      <param name="message">Message qui explique pourquoi le processus s'est terminé, ou null si aucune explication n'est fournie.</param>
    </member>
    <member name="M:System.Environment.FailFast(System.String,System.Exception)">
      <summary>Met immédiatement fin à un processus après avoir écrit un message dans le journal des événements des applications Windows, puis inclut le message et les informations sur les exceptions dans le rapport d'erreurs à Microsoft.</summary>
      <param name="message">Message qui explique pourquoi le processus s'est terminé, ou null si aucune explication n'est fournie.</param>
      <param name="exception">Exception qui représente l'erreur à l'origine de l'arrêt.Il s'agit en général de l'exception dans un bloc catch.</param>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariable(System.String)">
      <summary>Récupère la valeur d'une variable d'environnement du processus en cours. </summary>
      <returns>La valeur de la variable d'environnement spécifiée par <paramref name="variable" />, ou null si la variable d'environnement n'est pas trouvée.</returns>
      <param name="variable">Nom de la variable d'environnement.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariables">
      <summary>Récupère tous les noms des variables d'environnement et leurs valeurs à partir du processus en cours.</summary>
      <returns>Dictionnaire qui contient tous les noms des variables d'environnement et leurs valeurs ; sinon, un dictionnaire vide si aucune variable d'environnement n'est trouvée.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <exception cref="T:System.OutOfMemoryException">The buffer is out of memory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.HasShutdownStarted">
      <summary>Obtient une valeur indiquant si le déchargement du domaine d'application actuel est en cours ou si le Common Language Runtime s'arrête. </summary>
      <returns>true si le domaine d'application actuel est déchargé ou si le CLR s'arrête ; sinon, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.NewLine">
      <summary>Obtient la chaîne de saut de ligne définie pour cet environnement.</summary>
      <returns>Chaîne contenant « \r\n » pour les plateformes non-Unix, ou une chaîne contenant « \n » pour les plateformes Unix.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.ProcessorCount">
      <summary>Obtient le nombre de processeurs de l'ordinateur actuel.</summary>
      <returns>Entier signé 32 bits qui spécifie le nombre de processeurs de l'ordinateur actuel.Il n'y a pas de valeur par défaut.Si l'ordinateur actuel contient plusieurs groupes de processeurs, cette propriété retourne le nombre de processeurs logiques disponibles pour le Common Language Runtime (CLR).</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="NUMBER_OF_PROCESSORS" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.SetEnvironmentVariable(System.String,System.String)">
      <summary>Crée, modifie ou supprime une variable d'environnement stockée dans le processus en cours.</summary>
      <param name="variable">Nom d'une variable d'environnement.</param>
      <param name="value">Valeur à assigner à <paramref name="variable" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="variable" /> contains a zero-length string, an initial hexadecimal zero character (0x00), or an equal sign ("="). -or-The length of <paramref name="variable" /> or <paramref name="value" /> is greater than or equal to 32,767 characters.-or-An error occurred during the execution of this operation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.StackTrace">
      <summary>Obtient les informations actuelles sur la trace de la pile.</summary>
      <returns>Chaîne contenant les informations de trace de la pile.Cette valeur peut être <see cref="F:System.String.Empty" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack trace information is out of range.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.TickCount">
      <summary>Obtient le nombre de millisecondes écoulées depuis le démarrage du système.</summary>
      <returns>Entier signé 32 bits contenant la durée écoulée en millisecondes depuis le dernier démarrage de l'ordinateur. </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Math">
      <summary>Fournit des constantes et des méthodes statiques pour des fonctions trigonométriques, logarithmiques et d'autres fonctions mathématiques courantes.Pour parcourir le code source de .NET Framework pour ce type, consultez la Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Decimal)">
      <summary>Retourne la valeur absolue d'un nombre <see cref="T:System.Decimal" />.</summary>
      <returns>Nombre décimal x, tel que 0 ≤ x ≤<see cref="F:System.Decimal.MaxValue" />.</returns>
      <param name="value">Nombre qui est supérieur ou égal à <see cref="F:System.Decimal.MinValue" />, mais inférieur ou égal à <see cref="F:System.Decimal.MaxValue" />. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Double)">
      <summary>Retourne la valeur absolue d'un nombre à virgule flottante double précision.</summary>
      <returns>Nombre à virgule flottante double précision x, tel que 0 ≤ x ≤<see cref="F:System.Double.MaxValue" />.</returns>
      <param name="value">Nombre qui est supérieur ou égal à <see cref="F:System.Double.MinValue" />, mais inférieur ou égal à <see cref="F:System.Double.MaxValue" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int16)">
      <summary>Retourne la valeur absolue d'un entier signé 16 bits.</summary>
      <returns>Entier signé 16 bits, x, tel que 0 ≤ x ≤<see cref="F:System.Int16.MaxValue" />.</returns>
      <param name="value">Nombre qui est supérieur à <see cref="F:System.Int16.MinValue" />, mais inférieur ou égal à <see cref="F:System.Int16.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est égal à <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int32)">
      <summary>Retourne la valeur absolue d'un entier signé 32 bits.</summary>
      <returns>Entier signé 32 bits, x, tel que 0 ≤ x ≤<see cref="F:System.Int32.MaxValue" />.</returns>
      <param name="value">Nombre qui est supérieur à <see cref="F:System.Int32.MinValue" />, mais inférieur ou égal à <see cref="F:System.Int32.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est égal à <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int64)">
      <summary>Retourne la valeur absolue d'un entier signé 64 bits.</summary>
      <returns>Entier signé 64 bits, x, tel que 0 ≤ x ≤<see cref="F:System.Int64.MaxValue" />.</returns>
      <param name="value">Nombre qui est supérieur à <see cref="F:System.Int64.MinValue" />, mais inférieur ou égal à <see cref="F:System.Int64.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est égal à <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.SByte)">
      <summary>Retourne la valeur absolue d'un entier signé 8 bits.</summary>
      <returns>Entier signé 8 bits x, tel que 0 ≤ x ≤<see cref="F:System.SByte.MaxValue" />.</returns>
      <param name="value">Nombre qui est supérieur à <see cref="F:System.SByte.MinValue" />, mais inférieur ou égal à <see cref="F:System.SByte.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> est égal à <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Single)">
      <summary>Retourne la valeur absolue d'un nombre à virgule flottante simple précision.</summary>
      <returns>Nombre à virgule flottante simple précision x, tel que 0 ≤ x ≤<see cref="F:System.Single.MaxValue" />.</returns>
      <param name="value">Nombre qui est supérieur ou égal à <see cref="F:System.Single.MinValue" />, mais inférieur ou égal à <see cref="F:System.Single.MaxValue" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Acos(System.Double)">
      <summary>Retourne l'angle dont le cosinus est le nombre spécifié.</summary>
      <returns>Angle, θ, mesuré en radians, tel que 0 ≤θ≤πou <see cref="F:System.Double.NaN" /> si <paramref name="d" /> &lt; -1 ou <paramref name="d" /> &gt; 1 ou <paramref name="d" /> est égal à <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Nombre représentant un cosinus, où <paramref name="d" /> doit être supérieur ou égal à -1, mais inférieur ou égal à 1. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Asin(System.Double)">
      <summary>Retourne l'angle dont le sinus est le nombre spécifié.</summary>
      <returns>Angle, θ, mesuré en radians, tel que -π/2 ≤θ≤π/2 ou <see cref="F:System.Double.NaN" /> si <paramref name="d" /> &lt; -1 ou <paramref name="d" /> &gt; 1 ou <paramref name="d" /> est égal à <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Nombre représentant un sinus, où <paramref name="d" /> doit être supérieur ou égal à -1, mais inférieur ou égal à 1. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan(System.Double)">
      <summary>Retourne l'angle dont la tangente est le nombre spécifié.</summary>
      <returns>Angle, θ, mesuré en radians, tel que -π/2 ≤θ≤π/2.ou <see cref="F:System.Double.NaN" /> si <paramref name="d" /> est égal à <see cref="F:System.Double.NaN" />, -π/2 arrondi à la double précision (-1,5707963267949) si <paramref name="d" /> est égal à <see cref="F:System.Double.NegativeInfinity" />, ou π/2 arrondi à la double précision (1,5707963267949) si <paramref name="d" /> est égal à <see cref="F:System.Double.PositiveInfinity" />.</returns>
      <param name="d">Nombre représentant une tangente. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan2(System.Double,System.Double)">
      <summary>Retourne l'angle dont la tangente est le quotient de deux nombres spécifiés.</summary>
      <returns>Angle, θ, mesuré en radians, tel que -π≤θ≤π, et tan(θ) = <paramref name="y" /> / <paramref name="x" />, où (<paramref name="x" />, <paramref name="y" />) est un point du plan cartésien.Observez ce qui suit :Pour (<paramref name="x" />, <paramref name="y" />) dans le quadrant 1, 0 &lt; θ &lt; π/2.Pour (<paramref name="x" />, <paramref name="y" />) dans le quadrant 2, π/2 &lt; θ≤π.Pour (<paramref name="x" />, <paramref name="y" />) dans le quadrant 3,-π &lt; θ &lt;-π/2.Pour (<paramref name="x" />, <paramref name="y" />) dans le quadrant 4,-π/2 &lt; θ &lt; 0.La valeur de retour des points situés aux limites des quadrants est la suivante :Si y est égal à 0 et si x n'est pas négatif, alors θ = 0.Si y est égal à 0 et si x est négatif, alors θ = π.Si y est positif et x est égal à 0, alors θ = π/2.Si y est négatif et si x est égal à 0, alors θ = -π/2.Si y est égal à 0 et si x est égal à 0, alors θ = 0. Si <paramref name="x" /> ou <paramref name="y" /> est <see cref="F:System.Double.NaN" />, ou si <paramref name="x" /> et <paramref name="y" /> sont <see cref="F:System.Double.PositiveInfinity" /> ou <see cref="F:System.Double.NegativeInfinity" />, la méthode retourne <see cref="F:System.Double.NaN" />.</returns>
      <param name="y">Coordonnée y d'un point. </param>
      <param name="x">Coordonnée x d'un point. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Decimal)">
      <summary>Retourne la plus petite valeur intégrale supérieure ou égale au nombre décimal spécifié.</summary>
      <returns>Plus petite valeur intégrale qui est supérieure ou égale à <paramref name="d" />.Notez que cette méthode retourne un objet <see cref="T:System.Decimal" /> plutôt qu'un type intégral.</returns>
      <param name="d">Nombre décimal. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Double)">
      <summary>Retourne la plus petite valeur intégrale supérieure ou égale au nombre à virgule flottante double précision spécifié.</summary>
      <returns>Plus petite valeur intégrale qui est supérieure ou égale à <paramref name="a" />.Si <paramref name="a" /> est égal à <see cref="F:System.Double.NaN" />, à <see cref="F:System.Double.NegativeInfinity" /> ou à <see cref="F:System.Double.PositiveInfinity" />, cette valeur est retournée.Notez que cette méthode retourne un objet <see cref="T:System.Double" /> plutôt qu'un type intégral.</returns>
      <param name="a">Nombre à virgule flottante double précision. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cos(System.Double)">
      <summary>Retourne le cosinus de l'angle spécifié.</summary>
      <returns>Cosinus de <paramref name="d" />.Si <paramref name="d" /> est égal à <see cref="F:System.Double.NaN" />, à <see cref="F:System.Double.NegativeInfinity" /> ou à <see cref="F:System.Double.PositiveInfinity" />, cette méthode retourne <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Angle, mesuré en radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cosh(System.Double)">
      <summary>Retourne le cosinus hyperbolique de l'angle spécifié.</summary>
      <returns>Cosinus hyperbolique de <paramref name="value" />.Si <paramref name="value" /> est égal à <see cref="F:System.Double.NegativeInfinity" /> ou <see cref="F:System.Double.PositiveInfinity" />, <see cref="F:System.Double.PositiveInfinity" /> est retourné.Si <paramref name="value" /> est égal à <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NaN" /> est retourné.</returns>
      <param name="value">Angle, mesuré en radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.E">
      <summary>Représente la base logarithmique naturelle spécifiée par la constante e.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Exp(System.Double)">
      <summary>Retourne e élevé à la puissance spécifiée.</summary>
      <returns>Nombre e élevé à la puissance <paramref name="d" />.Si <paramref name="d" /> est égal à <see cref="F:System.Double.NaN" /> ou <see cref="F:System.Double.PositiveInfinity" />, cette valeur est retournée.Si <paramref name="d" /> est égal à <see cref="F:System.Double.NegativeInfinity" />, 0 est retourné.</returns>
      <param name="d">Nombre spécifiant une puissance. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Decimal)">
      <summary>Retourne le plus grand entier inférieur ou égal au nombre décimal spécifié.</summary>
      <returns>Plus grand nombre entier inférieur ou égal à <paramref name="d" />.Notez que la méthode retourne une valeur intégrale de type <see cref="T:System.Math" />.</returns>
      <param name="d">Nombre décimal. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Double)">
      <summary>Retourne le plus grand entier inférieur ou égal au nombre à virgule flottante double précision spécifié.</summary>
      <returns>Plus grand nombre entier inférieur ou égal à <paramref name="d" />.Si <paramref name="d" /> est égal à <see cref="F:System.Double.NaN" />, à <see cref="F:System.Double.NegativeInfinity" /> ou à <see cref="F:System.Double.PositiveInfinity" />, cette valeur est retournée.</returns>
      <param name="d">Nombre à virgule flottante double précision. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.IEEERemainder(System.Double,System.Double)">
      <summary>Retourne le reste de la division d'un nombre spécifié par un autre.</summary>
      <returns>Nombre égal à <paramref name="x" /> - (<paramref name="y" /> Q), où Q est le quotient de <paramref name="x" /> / <paramref name="y" /> arrondi à l'entier le plus proche (si <paramref name="x" /> / <paramref name="y" /> se trouve à égale distance de deux entiers, l'entier pair est retourné).Si <paramref name="x" /> - (<paramref name="y" /> Q) est égal à zéro, la valeur retournée est +0 si <paramref name="x" /> est positif, ou -0 si <paramref name="x" /> est négatif.Si <paramref name="y" /> = 0, <see cref="F:System.Double.NaN" /> est retourné.</returns>
      <param name="x">Dividende. </param>
      <param name="y">Diviseur. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double)">
      <summary>Retourne le logarithme naturel (base e) d'un nombre spécifié.</summary>
      <returns>Une des valeurs du tableau suivant. Paramètre <paramref name="d" />Valeur de retour Positif Le logarithme népérien de <paramref name="d" />; autrement dit, ln <paramref name="d" />, ou journal e<paramref name="d" />Zéro <see cref="F:System.Double.NegativeInfinity" />Négatif <see cref="F:System.Double.NaN" />Égal à <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Égal à <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Nombre dont le logarithme doit être recherché. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double,System.Double)">
      <summary>Retourne le logarithme d'un nombre spécifié dans une base spécifiée.</summary>
      <returns>Une des valeurs du tableau suivant.(+Infini indique <see cref="F:System.Double.PositiveInfinity" />, -Infini indique <see cref="F:System.Double.NegativeInfinity" /> et NaN indique <see cref="F:System.Double.NaN" />.)<paramref name="a" /><paramref name="newBase" />Valeur de retour<paramref name="a" />&gt; 0(0 &lt;<paramref name="newBase" />&lt; 1) – ou – (<paramref name="newBase" />&gt; 1)lognewBase(a)<paramref name="a" />&lt; 0(toute valeur)NaN(toute valeur)<paramref name="newBase" />&lt; 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = +InfinityNaN<paramref name="a" /> = NaN(toute valeur)NaN(toute valeur)<paramref name="newBase" /> = NaNNaN(toute valeur)<paramref name="newBase" /> = 1NaN<paramref name="a" /> = 00 &lt;<paramref name="newBase" />&lt; 1 +Infini<paramref name="a" /> = 0<paramref name="newBase" />&gt; 1-Infini<paramref name="a" /> = + Infinity0 &lt;<paramref name="newBase" />&lt; 1-Infini<paramref name="a" /> = + Infinity<paramref name="newBase" />&gt; 1+Infini<paramref name="a" /> = 1<paramref name="newBase" /> = 00<paramref name="a" /> = 1<paramref name="newBase" /> = +Infinity0</returns>
      <param name="a">Nombre dont le logarithme doit être recherché. </param>
      <param name="newBase">Base du logarithme. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log10(System.Double)">
      <summary>Retourne le logarithme de base 10 d'un nombre spécifié.</summary>
      <returns>Une des valeurs du tableau suivant. Paramètre <paramref name="d" />Valeur de retour Positif Le logarithme base 10 de <paramref name="d" />; autrement dit, le journal 10<paramref name="d" />. Zéro <see cref="F:System.Double.NegativeInfinity" />Négatif <see cref="F:System.Double.NaN" />Égal à <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Égal à <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Nombre dont le logarithme doit être recherché. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Byte,System.Byte)">
      <summary>Retourne le plus grand de deux entiers non signés 8 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).</returns>
      <param name="val1">Premier des deux entiers non signés 8 bits à comparer. </param>
      <param name="val2">Second des deux entiers non signés 8 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Decimal,System.Decimal)">
      <summary>Retourne le plus grand de deux nombres décimaux.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).</returns>
      <param name="val1">Premier des deux nombres décimaux à comparer. </param>
      <param name="val2">Second des deux nombres décimaux à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Double,System.Double)">
      <summary>Retourne le plus grand de deux nombres à virgule flottante double précision.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).Si <paramref name="val1" />, <paramref name="val2" /> ou <paramref name="val1" /> et <paramref name="val2" /> sont égaux à <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NaN" /> est retourné.</returns>
      <param name="val1">Premier des deux nombres à virgule flottante double précision à comparer. </param>
      <param name="val2">Second des deux nombres à virgule flottante double précision à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int16,System.Int16)">
      <summary>Retourne le plus grand de deux entiers signés 16 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).</returns>
      <param name="val1">Premier des deux entiers signés 16 bits à comparer. </param>
      <param name="val2">Second des deux entiers signés 16 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int32,System.Int32)">
      <summary>Retourne le plus grand de deux entiers signés 32 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).</returns>
      <param name="val1">Premier des deux entiers signés 32 bits à comparer. </param>
      <param name="val2">Second des deux entiers signés 32 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int64,System.Int64)">
      <summary>Retourne le plus grand de deux entiers signés 64 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).</returns>
      <param name="val1">Premier des deux entiers signés 64 bits à comparer. </param>
      <param name="val2">Second des deux entiers signés 64 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.SByte,System.SByte)">
      <summary>Retourne le plus grand de deux entiers signés 8 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).</returns>
      <param name="val1">Premier des deux entiers signés 8 bits à comparer. </param>
      <param name="val2">Second des deux entiers signés 8 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Single,System.Single)">
      <summary>Retourne le plus grand de deux nombres à virgule flottante simple précision.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).Si <paramref name="val1" /> ou <paramref name="val2" />, ou <paramref name="val1" /> et <paramref name="val2" /> sont égaux à <see cref="F:System.Single.NaN" />, <see cref="F:System.Single.NaN" /> est retourné.</returns>
      <param name="val1">Premier des deux nombres à virgule flottante simple précision à comparer. </param>
      <param name="val2">Second des deux nombres à virgule flottante simple précision à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt16,System.UInt16)">
      <summary>Retourne le plus grand de deux entiers non signés 16 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).</returns>
      <param name="val1">Premier des deux entiers non signés 16 bits à comparer. </param>
      <param name="val2">Second des deux entiers non signés 16 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt32,System.UInt32)">
      <summary>Retourne le plus grand de deux entiers non signés 32 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).</returns>
      <param name="val1">Premier des deux entiers non signés 32 bits à comparer. </param>
      <param name="val2">Second des deux entiers non signés 32 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt64,System.UInt64)">
      <summary>Retourne le plus grand de deux entiers non signés 64 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus grand).</returns>
      <param name="val1">Premier des deux entiers non signés 64 bits à comparer. </param>
      <param name="val2">Second des deux entiers non signés 64 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Byte,System.Byte)">
      <summary>Retourne le plus petit de deux entiers non signés 8 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).</returns>
      <param name="val1">Premier des deux entiers non signés 8 bits à comparer. </param>
      <param name="val2">Second des deux entiers non signés 8 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Decimal,System.Decimal)">
      <summary>Retourne le plus petit de deux nombres décimaux.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).</returns>
      <param name="val1">Premier des deux nombres décimaux à comparer. </param>
      <param name="val2">Second des deux nombres décimaux à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Double,System.Double)">
      <summary>Retourne le plus petit de deux nombres à virgule flottante double précision.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).Si <paramref name="val1" />, <paramref name="val2" /> ou <paramref name="val1" /> et <paramref name="val2" /> sont égaux à <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NaN" /> est retourné.</returns>
      <param name="val1">Premier des deux nombres à virgule flottante double précision à comparer. </param>
      <param name="val2">Second des deux nombres à virgule flottante double précision à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int16,System.Int16)">
      <summary>Retourne le plus petit de deux entiers signés 16 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).</returns>
      <param name="val1">Premier des deux entiers signés 16 bits à comparer. </param>
      <param name="val2">Second des deux entiers signés 16 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int32,System.Int32)">
      <summary>Retourne le plus petit de deux entiers signés 32 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).</returns>
      <param name="val1">Premier des deux entiers signés 32 bits à comparer. </param>
      <param name="val2">Second des deux entiers signés 32 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int64,System.Int64)">
      <summary>Retourne le plus petit de deux entiers signés 64 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).</returns>
      <param name="val1">Premier des deux entiers signés 64 bits à comparer. </param>
      <param name="val2">Second des deux entiers signés 64 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.SByte,System.SByte)">
      <summary>Retourne le plus petit de deux entiers signés 8 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).</returns>
      <param name="val1">Premier des deux entiers signés 8 bits à comparer. </param>
      <param name="val2">Second des deux entiers signés 8 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Single,System.Single)">
      <summary>Retourne le plus petit de deux nombres à virgule flottante simple précision.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).Si <paramref name="val1" />, <paramref name="val2" /> ou <paramref name="val1" /> et <paramref name="val2" /> sont égaux à <see cref="F:System.Single.NaN" />, <see cref="F:System.Single.NaN" /> est retourné.</returns>
      <param name="val1">Premier des deux nombres à virgule flottante simple précision à comparer. </param>
      <param name="val2">Second des deux nombres à virgule flottante simple précision à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt16,System.UInt16)">
      <summary>Retourne le plus petit de deux entiers non signés 16 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).</returns>
      <param name="val1">Premier des deux entiers non signés 16 bits à comparer. </param>
      <param name="val2">Second des deux entiers non signés 16 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt32,System.UInt32)">
      <summary>Retourne le plus petit de deux entiers non signés 32 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).</returns>
      <param name="val1">Premier des deux entiers non signés 32 bits à comparer. </param>
      <param name="val2">Second des deux entiers non signés 32 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt64,System.UInt64)">
      <summary>Retourne le plus petit de deux entiers non signés 64 bits.</summary>
      <returns>Paramètre <paramref name="val1" /> ou <paramref name="val2" /> (selon celui qui est le plus petit).</returns>
      <param name="val1">Premier des deux entiers non signés 64 bits à comparer. </param>
      <param name="val2">Second des deux entiers non signés 64 bits à comparer. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.PI">
      <summary>Représente le rapport de la circonférence d'un cercle à son diamètre, spécifié par la constante π.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Pow(System.Double,System.Double)">
      <summary>Retourne un nombre spécifié élevé à la puissance spécifiée.</summary>
      <returns>Nombre <paramref name="x" /> élevé à la puissance <paramref name="y" />.</returns>
      <param name="x">Nombre à virgule flottante double précision à élever à une puissance. </param>
      <param name="y">Nombre à virgule flottante double précision. qui spécifie une puissance. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal)">
      <summary>Arrondit une valeur décimale à la valeur entière la plus proche.</summary>
      <returns>Paramètre <paramref name="d" /> de l'entier le plus proche.Si le composant fractionnaire de <paramref name="d" /> se trouve à égale distance de deux entiers, l'un pair et l'autre impair, le nombre pair est retourné.Notez que cette méthode retourne un objet <see cref="T:System.Decimal" /> plutôt qu'un type intégral.</returns>
      <param name="d">Nombre décimal à arrondir. </param>
      <exception cref="T:System.OverflowException">Le résultat est à l'extérieur de la plage d'un <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32)">
      <summary>Arrondit une valeur décimale au nombre de chiffres fractionnaires spécifié.</summary>
      <returns>Nombre le plus proche de <paramref name="d" /> contenant un nombre de chiffres fractionnaires égal à <paramref name="decimals" />. </returns>
      <param name="d">Nombre décimal à arrondir. </param>
      <param name="decimals">Nombre de décimales de la valeur de retour. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> est inférieur à 0 ou supérieure à 28. </exception>
      <exception cref="T:System.OverflowException">Le résultat est à l'extérieur de la plage d'un <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32,System.MidpointRounding)">
      <summary>Arrondit une valeur décimale au nombre de chiffres fractionnaires spécifié.Un paramètre spécifie comment arrondir la valeur qui se trouve à égale distance des deux nombres.</summary>
      <returns>Nombre le plus proche de <paramref name="d" /> contenant un nombre de chiffres fractionnaires égal à <paramref name="decimals" />.Si <paramref name="d" /> a moins de chiffres fractionnaires que <paramref name="decimals" />, <paramref name="d" /> est retourné sans modification.</returns>
      <param name="d">Nombre décimal à arrondir. </param>
      <param name="decimals">Nombre de décimales de la valeur de retour. </param>
      <param name="mode">Spécification sur la façon d'arrondir <paramref name="d" /> s'il se trouve à mi-chemin entre deux nombres.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> est inférieur à 0 ou supérieure à 28. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> n'est pas une valeur valide de <see cref="T:System.MidpointRounding" />.</exception>
      <exception cref="T:System.OverflowException">Le résultat est à l'extérieur de la plage d'un <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.MidpointRounding)">
      <summary>Arrondit une valeur décimale à l'entier le plus proche.Un paramètre spécifie comment arrondir la valeur qui se trouve à égale distance des deux nombres.</summary>
      <returns>Entier le plus proche de <paramref name="d" />.Si <paramref name="d" /> se trouve à égale distance de deux nombres, l'un pair et l'autre impair, <paramref name="mode" /> détermine le nombre qui sera retourné.</returns>
      <param name="d">Nombre décimal à arrondir. </param>
      <param name="mode">Spécification sur la façon d'arrondir <paramref name="d" /> s'il se trouve à mi-chemin entre deux nombres.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> n'est pas une valeur valide de <see cref="T:System.MidpointRounding" />.</exception>
      <exception cref="T:System.OverflowException">Le résultat est à l'extérieur de la plage d'un <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double)">
      <summary>Arrondit une valeur à virgule flottante double précision à la valeur entière la plus proche.</summary>
      <returns>Entier le plus proche de <paramref name="a" />.Si le composant fractionnaire de <paramref name="a" /> se trouve à égale distance de deux entiers, l'un pair et l'autre impair, le nombre pair est retourné.Notez que cette méthode retourne un objet <see cref="T:System.Double" /> plutôt qu'un type intégral.</returns>
      <param name="a">Nombre à virgule flottante double précision à arrondir. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32)">
      <summary>Arrondit une valeur à virgule flottante double précision au nombre de chiffres fractionnaires spécifié.</summary>
      <returns>Nombre le plus proche de <paramref name="value" /> contenant un nombre de chiffres fractionnaires égal à <paramref name="digits" />.</returns>
      <param name="value">Nombre à virgule flottante double précision à arrondir. </param>
      <param name="digits">Nombre de chiffres fractionnaires de la valeur de retour. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> est inférieur à 0 ou supérieure à 15. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32,System.MidpointRounding)">
      <summary>Arrondit une valeur à virgule flottante double précision au nombre de chiffres fractionnaires spécifié.Un paramètre spécifie comment arrondir la valeur qui se trouve à égale distance des deux nombres.</summary>
      <returns>Nombre le plus proche de <paramref name="value" /> contenant un nombre de chiffres fractionnaires égal à <paramref name="digits" />.Si <paramref name="value" /> a moins de chiffres fractionnaires que <paramref name="digits" />, <paramref name="value" /> est retourné sans modification.</returns>
      <param name="value">Nombre à virgule flottante double précision à arrondir. </param>
      <param name="digits">Nombre de chiffres fractionnaires de la valeur de retour. </param>
      <param name="mode">Spécification sur la façon d'arrondir <paramref name="value" /> s'il se trouve à mi-chemin entre deux nombres.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> est inférieur à 0 ou supérieure à 15. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> n'est pas une valeur valide de <see cref="T:System.MidpointRounding" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.MidpointRounding)">
      <summary>Arrondit une valeur à virgule flottante double précision à l'entier le plus proche.Un paramètre spécifie comment arrondir la valeur qui se trouve à égale distance des deux nombres.</summary>
      <returns>Entier le plus proche de <paramref name="value" />.Si <paramref name="value" /> se trouve à égale distance de deux entiers, l'un pair et l'autre impair, <paramref name="mode" /> détermine le nombre qui sera retourné.</returns>
      <param name="value">Nombre à virgule flottante double précision à arrondir. </param>
      <param name="mode">Spécification sur la façon d'arrondir <paramref name="value" /> s'il se trouve à mi-chemin entre deux nombres.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> n'est pas une valeur valide de <see cref="T:System.MidpointRounding" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Decimal)">
      <summary>Retourne une valeur indiquant le signe d'un nombre décimal.</summary>
      <returns>Nombre qui indique le signe de <paramref name="value" />, comme indiqué dans le tableau suivant.Valeur de retour Signification -1 <paramref name="value" /> est inférieur à zéro. 0 <paramref name="value" /> est égal à zéro. 1 <paramref name="value" /> est supérieur à zéro. </returns>
      <param name="value">Nombre décimal signé. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Double)">
      <summary>Retourne une valeur indiquant le signe d'un nombre à virgule flottante double précision.</summary>
      <returns>Nombre qui indique le signe de <paramref name="value" />, comme indiqué dans le tableau suivant.Valeur de retour Signification -1 <paramref name="value" /> est inférieur à zéro. 0 <paramref name="value" /> est égal à zéro. 1 <paramref name="value" /> est supérieur à zéro. </returns>
      <param name="value">Nombre signé. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> est égal à <see cref="F:System.Double.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int16)">
      <summary>Retourne une valeur indiquant le signe d'un entier signé 16 bits.</summary>
      <returns>Nombre qui indique le signe de <paramref name="value" />, comme indiqué dans le tableau suivant.Valeur de retour Signification -1 <paramref name="value" /> est inférieur à zéro. 0 <paramref name="value" /> est égal à zéro. 1 <paramref name="value" /> est supérieur à zéro. </returns>
      <param name="value">Nombre signé. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int32)">
      <summary>Retourne une valeur indiquant le signe d'un entier signé 32 bits.</summary>
      <returns>Nombre qui indique le signe de <paramref name="value" />, comme indiqué dans le tableau suivant.Valeur de retour Signification -1 <paramref name="value" /> est inférieur à zéro. 0 <paramref name="value" /> est égal à zéro. 1 <paramref name="value" /> est supérieur à zéro. </returns>
      <param name="value">Nombre signé. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int64)">
      <summary>Retourne une valeur indiquant le signe d'un entier signé 64 bits.</summary>
      <returns>Nombre qui indique le signe de <paramref name="value" />, comme indiqué dans le tableau suivant.Valeur de retour Signification -1 <paramref name="value" /> est inférieur à zéro. 0 <paramref name="value" /> est égal à zéro. 1 <paramref name="value" /> est supérieur à zéro. </returns>
      <param name="value">Nombre signé. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.SByte)">
      <summary>Retourne une valeur indiquant le signe d'un entier signé 8 bits.</summary>
      <returns>Nombre qui indique le signe de <paramref name="value" />, comme indiqué dans le tableau suivant.Valeur de retour Signification -1 <paramref name="value" /> est inférieur à zéro. 0 <paramref name="value" /> est égal à zéro. 1 <paramref name="value" /> est supérieur à zéro. </returns>
      <param name="value">Nombre signé. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Single)">
      <summary>Retourne une valeur indiquant le signe d'un nombre à virgule flottante simple précision.</summary>
      <returns>Nombre qui indique le signe de <paramref name="value" />, comme indiqué dans le tableau suivant.Valeur de retour Signification -1 <paramref name="value" /> est inférieur à zéro. 0 <paramref name="value" /> est égal à zéro. 1 <paramref name="value" /> est supérieur à zéro. </returns>
      <param name="value">Nombre signé. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> est égal à <see cref="F:System.Single.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sin(System.Double)">
      <summary>Retourne le sinus de l'angle spécifié.</summary>
      <returns>Sinus de <paramref name="a" />.Si <paramref name="a" /> est égal à <see cref="F:System.Double.NaN" />, à <see cref="F:System.Double.NegativeInfinity" /> ou à <see cref="F:System.Double.PositiveInfinity" />, cette méthode retourne <see cref="F:System.Double.NaN" />.</returns>
      <param name="a">Angle, mesuré en radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sinh(System.Double)">
      <summary>Retourne le sinus hyperbolique de l'angle spécifié.</summary>
      <returns>Sinus hyperbolique de <paramref name="value" />.Si <paramref name="value" /> est égal à <see cref="F:System.Double.NegativeInfinity" />, <see cref="F:System.Double.PositiveInfinity" /> ou <see cref="F:System.Double.NaN" />, cette méthode retourne un <see cref="T:System.Double" /> égal à <paramref name="value" />.</returns>
      <param name="value">Angle, mesuré en radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sqrt(System.Double)">
      <summary>Retourne la racine carrée d'un nombre spécifié.</summary>
      <returns>Une des valeurs du tableau suivant. Paramètre <paramref name="d" />Valeur de retour Zéro, ou une valeur positive Racine carrée positive de <paramref name="d" />. Négatif <see cref="F:System.Double.NaN" />Est égal à<see cref="F:System.Double.NaN" />.<see cref="F:System.Double.NaN" />Est égal à<see cref="F:System.Double.PositiveInfinity" />.<see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Nombre dont la racine carrée doit être recherchée. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tan(System.Double)">
      <summary>Retourne la tangente de l'angle spécifié.</summary>
      <returns>Tangente de <paramref name="a" />.Si <paramref name="a" /> est égal à <see cref="F:System.Double.NaN" />, à <see cref="F:System.Double.NegativeInfinity" /> ou à <see cref="F:System.Double.PositiveInfinity" />, cette méthode retourne <see cref="F:System.Double.NaN" />.</returns>
      <param name="a">Angle, mesuré en radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tanh(System.Double)">
      <summary>Retourne la tangente hyperbolique de l'angle spécifié.</summary>
      <returns>Tangente hyperbolique de <paramref name="value" />.Si <paramref name="value" /> est égal à <see cref="F:System.Double.NegativeInfinity" />, cette méthode retourne -1.Si la valeur est égale à <see cref="F:System.Double.PositiveInfinity" />, cette méthode retourne 1.Si <paramref name="value" /> est égal à <see cref="F:System.Double.NaN" />, cette méthode retourne <see cref="F:System.Double.NaN" />.</returns>
      <param name="value">Angle, mesuré en radians. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Decimal)">
      <summary>Calcule la partie entière d'un nombre décimal spécifié. </summary>
      <returns>Partie entière de <paramref name="d" />, c'est-à-dire le nombre qui reste après avoir ignoré tous les chiffres fractionnaires.</returns>
      <param name="d">Nombre à tronquer.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Double)">
      <summary>Calcule la partie entière d'un nombre à virgule flottante double précision spécifié. </summary>
      <returns>Partie entière de <paramref name="d" />, c'est-à-dire le nombre qui reste après avoir ignoré tous les chiffres fractionnaires, ou l'une des valeurs indiquées dans le tableau suivant. <paramref name="d" />Valeur de retour<see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Nombre à tronquer.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.MidpointRounding">
      <summary>Spécifie comment les méthodes d'arrondi mathématiques doivent traiter un nombre qui se trouve à mi-chemin entre deux nombres.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.MidpointRounding.AwayFromZero">
      <summary>Quand un nombre se trouve à mi-chemin entre deux autres, il est arrondi vers le nombre le plus proche qui s'éloigne de zéro.</summary>
    </member>
    <member name="F:System.MidpointRounding.ToEven">
      <summary>Quand un nombre est à mi-chemin entre deux autres, il est arrondi vers le nombre pair le plus proche.</summary>
    </member>
    <member name="T:System.Progress`1">
      <summary>Fournit un <see cref="T:System.IProgress`1" /> qui appelle des rappels pour chaque valeur de progression signalée.</summary>
      <typeparam name="T">Spécifie le type de la valeur de rapport progression.</typeparam>
    </member>
    <member name="M:System.Progress`1.#ctor">
      <summary>Initialise l'objet <see cref="T:System.Progress`1" />.</summary>
    </member>
    <member name="M:System.Progress`1.#ctor(System.Action{`0})">
      <summary>Initialise l'objet <see cref="T:System.Progress`1" /> avec le rappel spécifié.</summary>
      <param name="handler">Gestionnaire à appeler pour chaque valeur de progression signalée.Ce gestionnaire est appelé en plus de tous les délégués inscrits auprès de l'événement <see cref="E:System.Progress`1.ProgressChanged" />.Selon l'instance <see cref="T:System.Threading.SynchronizationContext" /> capturée par <see cref="T:System.Progress`1" /> lors de la construction, il est possible que cette instance du gestionnaire puisse être appelée simultanément avec elle-même.</param>
    </member>
    <member name="M:System.Progress`1.OnReport(`0)">
      <summary>Signale une modification de progression.</summary>
      <param name="value">Valeur de la progression mise à jour.</param>
    </member>
    <member name="E:System.Progress`1.ProgressChanged">
      <summary>Déclenché pour chaque valeur de progression signalée.</summary>
    </member>
    <member name="M:System.Progress`1.System#IProgress{T}#Report(`0)">
      <summary>Signale une modification de progression.</summary>
      <param name="value">Valeur de la progression mise à jour.</param>
    </member>
    <member name="T:System.Random">
      <summary>Représente un générateur de nombres pseudo-aléatoires. Il s'agit d'un périphérique qui produit une séquence de nombres conformes à certains prérequis statistiques liés à l'aspect aléatoire.Pour parcourir le code source .NET Framework correspondant à ce type, consultez la Source de référence.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Random" />, à l'aide d'une valeur initiale par défaut qui est fonction du temps.</summary>
    </member>
    <member name="M:System.Random.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Random" /> à l'aide de la valeur initiale spécifiée.</summary>
      <param name="Seed">Nombre utilisé pour calculer la valeur de départ de la séquence de nombres pseudo-aléatoires.Si un nombre négatif est spécifié, la valeur absolue du nombre est utilisée.</param>
    </member>
    <member name="M:System.Random.Next">
      <summary>Retourne un nombre aléatoire entier non négatif.</summary>
      <returns>Entier signé 32 bits supérieur ou égal à 0 et inférieur à <see cref="F:System.Int32.MaxValue" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32)">
      <summary>Retourne un nombre aléatoire entier non négatif, inférieur au nombre maximal spécifié.</summary>
      <returns>Entier signé 32 bits supérieur ou égal à 0 et inférieur à <paramref name="maxValue" /> ; autrement dit, la plage des valeurs de retour inclut généralement 0, mais pas <paramref name="maxValue" />.Toutefois, si <paramref name="maxValue" /> est égal à 0, <paramref name="maxValue" /> est retourné.</returns>
      <param name="maxValue">Limite supérieure (exclusive) du nombre aléatoire à générer.<paramref name="maxValue" /> doit être supérieur ou égal à 0 (zéro).</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxValue" /> is less than 0. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32,System.Int32)">
      <summary>Retourne un entier aléatoire qui se trouve dans une plage spécifiée.</summary>
      <returns>Entier signé 32 bits supérieur ou égal à <paramref name="minValue" /> et inférieur à <paramref name="maxValue" /> ; autrement dit, la plage des valeurs de retour inclut <paramref name="minValue" />, mais pas <paramref name="maxValue" />.Si <paramref name="minValue" /> est égal à <paramref name="maxValue" />, <paramref name="minValue" /> est retourné.</returns>
      <param name="minValue">Limite inférieure (incluse) du nombre aléatoire retourné. </param>
      <param name="maxValue">Limite supérieure (exclusive) du nombre aléatoire retourné.<paramref name="maxValue" /> doit être supérieur ou égal à <paramref name="minValue" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minValue" /> is greater than <paramref name="maxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextBytes(System.Byte[])">
      <summary>Remplit les éléments d'un tableau d'octets spécifié à l'aide de nombres aléatoires.</summary>
      <param name="buffer">Tableau d'octets contenant des nombres aléatoires. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextDouble">
      <summary>Retourne un nombre aléatoire à virgule flottante supérieur ou égal à 0,0 et inférieur à 1,0.</summary>
      <returns>Nombre à virgule flottante double précision supérieur ou égal à 0,0 et inférieur à 1,0.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Sample">
      <summary>Retourne un nombre aléatoire à virgule flottante compris entre 0,0 et 1,0.</summary>
      <returns>Nombre à virgule flottante double précision supérieur ou égal à 0,0 et inférieur à 1,0.</returns>
    </member>
    <member name="T:System.StringComparer">
      <summary>Représente une opération de comparaison de chaînes qui utilise des règles de tri spécifiques basées sur la casse et la culture ou des règles de comparaison ordinale.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.StringComparer.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.StringComparer" />. </summary>
    </member>
    <member name="M:System.StringComparer.Compare(System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, compare deux chaînes et retourne une indication de leur ordre de tri relatif.</summary>
      <returns>Entier signé qui indique les valeurs relatives de <paramref name="x" /> et <paramref name="y" />, comme indiqué dans le tableau suivant.ValeurSignificationInférieure à zéro<paramref name="x" /> précède <paramref name="y" /> dans l'ordre de tri.ou<paramref name="x" /> a la valeur null et <paramref name="y" /> n'a pas la valeur null.Zéro<paramref name="x" /> est égal à <paramref name="y" />.ou<paramref name="x" /> et <paramref name="y" /> ont la valeur null. Supérieure à zéro<paramref name="x" /> suit <paramref name="y" /> dans l'ordre de tri.ou<paramref name="y" /> a la valeur null et <paramref name="x" /> n'a pas la valeur null. </returns>
      <param name="x">Chaîne à comparer à <paramref name="y" />.</param>
      <param name="y">Chaîne à comparer à <paramref name="x" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.CurrentCulture">
      <summary>Obtient un objet <see cref="T:System.StringComparer" /> qui exécute une comparaison de chaînes respectant la casse à l'aide des règles de comparaison de mots de la culture actuelle.</summary>
      <returns>Nouvel objet <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.StringComparer.CurrentCultureIgnoreCase">
      <summary>Obtient un objet <see cref="T:System.StringComparer" /> qui effectue des comparaisons de chaînes ne respectant pas la casse à l'aide des règles de comparaison de mots de la culture actuelle.</summary>
      <returns>Nouvel objet <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.StringComparer.Equals(System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, indique si deux chaînes sont égales.</summary>
      <returns>true si <paramref name="x" /> et <paramref name="y" /> font référence au même objet ou <paramref name="x" /> et <paramref name="y" /> sont égaux ou <paramref name="x" /> et <paramref name="y" /> ont la valeur null ; sinon, false.</returns>
      <param name="x">Chaîne à comparer à <paramref name="y" />.</param>
      <param name="y">Chaîne à comparer à <paramref name="x" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.GetHashCode(System.String)">
      <summary>En cas de substitution dans une classe dérivée, obtient le code de hachage pour la chaîne spécifiée.</summary>
      <returns>Code de hachage signé de 32 bits calculé à partir de la valeur du paramètre <paramref name="obj" />.</returns>
      <param name="obj">Une chaîne.</param>
      <exception cref="T:System.ArgumentException">La mémoire disponible n'est pas suffisante pour allouer de la mémoire tampon nécessaire pour calculer le code de hachage.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> a la valeur null. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.StringComparer.Ordinal">
      <summary>Obtient un objet <see cref="T:System.StringComparer" /> qui effectue une comparaison de chaînes ordinale respectant la casse.</summary>
      <returns>Objet <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.OrdinalIgnoreCase">
      <summary>Obtient un objet <see cref="T:System.StringComparer" /> qui effectue une comparaison de chaînes ordinale ne respectant pas la casse.</summary>
      <returns>Objet <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Compare deux objets et retourne une valeur qui indique si l'un d'entre eux est inférieur, égal ou supérieur à l'autre.</summary>
      <returns>Entier signé qui indique les valeurs relatives de <paramref name="x" /> et <paramref name="y" />, comme indiqué dans le tableau suivant.ValeurSignificationInférieure à zéro<paramref name="x" /> est inférieur à <paramref name="y" />.Zéro<paramref name="x" /> est égal à <paramref name="y" />.Supérieure à zéro<paramref name="x" /> est supérieur à <paramref name="y" />.</returns>
      <param name="x">Premier objet à comparer.</param>
      <param name="y">Deuxième objet à comparer.</param>
      <exception cref="T:System.ArgumentException">Ni <paramref name="x" /> ni <paramref name="y" /> n'implémente l'interface <see cref="T:System.IComparable" />.ou<paramref name="x" /> et <paramref name="y" /> sont de types différents et aucun d'entre eux ne peut effectuer de comparaisons avec l'autre.</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Détermine si les objets spécifiés sont égaux.</summary>
      <returns>true si les objets spécifiés sont égaux ; sinon, false. </returns>
      <param name="x">Premier objet à comparer.</param>
      <param name="y">Deuxième objet à comparer.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> et <paramref name="y" /> sont de types différents et aucun d'entre eux ne peut effectuer de comparaisons avec l'autre. </exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Retourne un code de hachage pour l'objet spécifié.</summary>
      <returns>Code de hachage pour l'objet spécifié. </returns>
      <param name="obj">Objet pour lequel un code de hachage doit être retourné. </param>
      <exception cref="T:System.ArgumentNullException">Le type de <paramref name="obj" /> est un type référence et <paramref name="obj" /> est null. </exception>
    </member>
    <member name="T:System.UriBuilder">
      <summary>Fournit un constructeur personnalisé pour les URI (uniform resource identifier) et les modifie pour la classe <see cref="T:System.Uri" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.UriBuilder" />.</summary>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.UriBuilder" /> avec l'URI spécifié.</summary>
      <param name="uri">Chaîne d'identificateur URI. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> a la valeur null. </exception>
      <exception cref="T:System.UriFormatException">Dans les .NET pour applications Windows Store  ou la Bibliothèque de classes portable, intercepte l'exception de classe de base, <see cref="T:System.FormatException" />, sinon.<paramref name="uri" /> est une chaîne de longueur zéro ou contient uniquement des espaces.ou La routine d'analyse a détecté un schéma dont le format n'est pas valide.ou L'analyseur a détecté plus de deux barres obliques consécutives dans un identificateur URI n'utilisant pas le schéma « file ».ou <paramref name="uri" /> n'est pas une URI valide. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.UriBuilder" /> avec le schéma et l'hôte spécifiés.</summary>
      <param name="schemeName">Protocole d'accès à Internet. </param>
      <param name="hostName">Nom de domaine de type DNS ou adresse IP. </param>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.UriBuilder" /> avec le schéma, l'hôte et le port spécifiés.</summary>
      <param name="scheme">Protocole d'accès à Internet. </param>
      <param name="host">Nom de domaine de type DNS ou adresse IP. </param>
      <param name="portNumber">Numéro de port IP pour le service. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portNumber" /> est inférieur à -1 ou supérieur à 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.UriBuilder" /> avec le schéma, l'hôte, le numéro de port et le chemin d'accès spécifiés.</summary>
      <param name="scheme">Protocole d'accès à Internet. </param>
      <param name="host">Nom de domaine de type DNS ou adresse IP. </param>
      <param name="port">Numéro de port IP pour le service. </param>
      <param name="pathValue">Chemin d'accès à la ressource Internet. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> est inférieur à -1 ou supérieur à 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.UriBuilder" /> avec le schéma, l'hôte, le numéro de port, le chemin d'accès et la chaîne de requête spécifiés.</summary>
      <param name="scheme">Protocole d'accès à Internet. </param>
      <param name="host">Nom de domaine de type DNS ou adresse IP. </param>
      <param name="port">Numéro de port IP pour le service. </param>
      <param name="path">Chemin d'accès à la ressource Internet. </param>
      <param name="extraValue">Chaîne de requête ou identificateur de fragment. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="extraValue" /> n'est ni null, ni <see cref="F:System.String.Empty" />. Un identificateur de fragment valide ne commence pas par un symbole dièse (#), une chaîne de requête valide ne commence pas par un point d'interrogation (?). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> est inférieur à -1 ou supérieur à 65.535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.Uri)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.UriBuilder" /> avec l'instance de <see cref="T:System.Uri" /> spécifiée.</summary>
      <param name="uri">Instance de la classe <see cref="T:System.Uri" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> a la valeur null. </exception>
    </member>
    <member name="M:System.UriBuilder.Equals(System.Object)">
      <summary>Compare l'égalité d'une instance <see cref="T:System.Uri" /> existante au contenu de <see cref="T:System.UriBuilder" />.</summary>
      <returns>true si <paramref name="rparam" /> représente le même <see cref="T:System.Uri" /> que le <see cref="T:System.Uri" /> construit par cette instance de <see cref="T:System.UriBuilder" /> ; sinon false.</returns>
      <param name="rparam">Objet à comparer avec l'instance actuelle. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Fragment">
      <summary>Obtient ou définit la partie fragment de l'identificateur URI.</summary>
      <returns>Partie fragment de l'identificateur URI.L'identificateur du fragment (« # ») est ajouté au début du fragment.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.GetHashCode">
      <summary>Retourne le code de hachage pour l'identificateur URI.</summary>
      <returns>Code de hachage généré pour l'identificateur URI.</returns>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Host">
      <summary>Obtient ou définit le nom de l'hôte DNS (Domain Name System) ou l'adresse IP d'un serveur.</summary>
      <returns>Nom de l'hôte DNS ou adresse IP du serveur.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Password">
      <summary>Obtient ou définit le mot de passe associé à l'utilisateur qui accède à l'URI.</summary>
      <returns>Mot de passe de l'utilisateur qui accède à l'URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Path">
      <summary>Obtient ou définit le chemin d'accès à la ressource référencée par l'identificateur URI.</summary>
      <returns>Chemin d'accès à la ressource référencée par l'identificateur URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Port">
      <summary>Obtient ou définit le numéro de port de l'identificateur URI.</summary>
      <returns>Numéro de port de l'identificateur URI.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Le port ne peut pas avoir de valeur inférieure à -1 ou supérieure à 65 535. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Query">
      <summary>Obtient ou définit les informations de requête incluses dans l'identificateur URI.</summary>
      <returns>Informations de requête incluses dans l'identificateur URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Scheme">
      <summary>Obtient ou définit le nom de schéma de l'identificateur URI.</summary>
      <returns>Schéma de l'identificateur URI.</returns>
      <exception cref="T:System.ArgumentException">Le schéma ne peut pas avoir pour valeur un nom de schéma non valide. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.UriBuilder.ToString">
      <summary>Retourne la  chaîne d'affichage de l'instance de <see cref="T:System.UriBuilder" /> spécifiée.</summary>
      <returns>Chaîne qui contient la chaîne d'affichage ne faisant pas l'objet d'une séquence d'échappement de <see cref="T:System.UriBuilder" />.</returns>
      <exception cref="T:System.UriFormatException">Dans les .NET pour applications Windows Store  ou la Bibliothèque de classes portable, intercepte l'exception de classe de base, <see cref="T:System.FormatException" />, sinon.L'instance <see cref="T:System.UriBuilder" /> a un mot de passe incorrect. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Uri">
      <summary>Obtient l'instance de <see cref="T:System.Uri" /> construite par l'instance de <see cref="T:System.UriBuilder" /> spécifiée.</summary>
      <returns>
        <see cref="T:System.Uri" /> qui contient l'URI construit par <see cref="T:System.UriBuilder" />.</returns>
      <exception cref="T:System.UriFormatException">Dans les .NET pour applications Windows Store  ou la Bibliothèque de classes portable, intercepte l'exception de classe de base, <see cref="T:System.FormatException" />, sinon.L'identificateur URI construit par les propriétés <see cref="T:System.UriBuilder" /> n'est pas valide. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.UserName">
      <summary>Nom d'utilisateur associé à l'utilisateur qui accède à l'URI.</summary>
      <returns>Nom de l'utilisateur qui accède à l'URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Diagnostics.Stopwatch">
      <summary>Fournit un jeu de méthodes et de propriétés que vous pouvez utiliser pour mesurer le temps écoulé précisément.Pour parcourir le code source .NET Framework correspondant à ce type, consultez la Source de référence.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Diagnostics.Stopwatch" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.Elapsed">
      <summary>Obtient le temps total écoulé mesuré par l'instance actuelle.</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> en lecture seule qui représente le temps total écoulé mesuré par l'instance actuelle.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedMilliseconds">
      <summary>Obtient le temps total écoulé mesuré par l'instance actuelle, en millisecondes.</summary>
      <returns>Entier long en lecture seule qui représente le nombre total de millisecondes mesuré par l'instance actuelle.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedTicks">
      <summary>Obtient le temps total écoulé mesuré par l'instance actuelle, en graduations de minuterie.</summary>
      <returns>Entier long en lecture seule qui représente le nombre total de graduations de minuterie mesuré par l'instance actuelle.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.Frequency">
      <summary>Obtient la fréquence de la minuterie en nombre de graduations par seconde.Ce champ est en lecture seule.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.GetTimestamp">
      <summary>Obtient le nombre actuel de graduations dans le mécanisme de minuterie.</summary>
      <returns>Entier long qui représente la valeur du compteur de graduations du mécanisme de minuterie sous-jacent.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.IsHighResolution">
      <summary>Indique si la minuterie est basée sur un compteur de performance haute résolution.Ce champ est en lecture seule.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.IsRunning">
      <summary>Obtient une valeur indiquant si la minuterie <see cref="T:System.Diagnostics.Stopwatch" /> s'exécute.</summary>
      <returns>true si l'instance <see cref="T:System.Diagnostics.Stopwatch" /> est en cours d'exécution et en train de mesurer le temps écoulé pour un intervalle ; sinon, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Reset">
      <summary>Arrête la mesure d'intervalle de temps et remet le temps écoulé à zéro.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Restart">
      <summary>Arrête la mesure d'intervalle de temps, réinitialise le temps écoulé sur zéro puis commence à le mesurer.</summary>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Start">
      <summary>Démarre ou reprend la mesure du temps écoulé pour un intervalle.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.StartNew">
      <summary>Initialise une nouvelle instance <see cref="T:System.Diagnostics.Stopwatch" />, affecte une valeur de zéro à la propriété de temps écoulé et commence à mesurer le temps écoulé.</summary>
      <returns>
        <see cref="T:System.Diagnostics.Stopwatch" /> qui vient de commencer à mesurer le temps écoulé.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Stop">
      <summary>Cesse de mesurer le temps écoulé pour un intervalle.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.Path">
      <summary>Effectue des opérations sur des instances de <see cref="T:System.String" /> qui contiennent des informations relatives au chemin d'accès d'un fichier ou d'un répertoire.Ces opérations sont effectuées différemment selon la plateforme.Pour parcourir le code source de .NET Framework pour ce type, consultez la Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.AltDirectorySeparatorChar">
      <summary>Fournit un caractère de remplacement spécifique à la plateforme, utilisé pour séparer les niveaux de répertoire dans une chaîne de chemin d'accès qui reflète une organisation de système de fichiers hiérarchique.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.ChangeExtension(System.String,System.String)">
      <summary>Modifie l'extension d'une chaîne de chemin d'accès.</summary>
      <returns>Informations relatives au chemin d'accès modifiées.Sur les plateformes de bureau Windows, si <paramref name="path" /> est null ou est une chaîne vide (""), les informations relatives au chemin d'accès sont retournées inchangées.Si <paramref name="extension" /> est null, la chaîne retournée contient le chemin d'accès spécifié avec l'extension supprimée.Si <paramref name="path" /> ne possède pas d'extension et si <paramref name="extension" /> n'est pas null, la chaîne du chemin d'accès retournée contient <paramref name="extension" />, ajouté à la fin de <paramref name="path" />.</returns>
      <param name="path">Informations relatives au chemin d'accès à modifier.Le chemin d'accès ne peut contenir aucun des caractères définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />.</param>
      <param name="extension">Nouvelle extension (avec ou sans point de début).Spécifiez null pour supprimer une extension existante de <paramref name="path" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contient un ou plusieurs caractères non valides définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String)">
      <summary>Combine deux chaînes en un chemin d'accès.</summary>
      <returns>Chemins d'accès combinés.Si un des chemins d'accès spécifiés est une chaîne de longueur zéro, cette méthode retourne l'autre chemin d'accès.Si <paramref name="path2" /> contient un chemin d'accès absolu, cette méthode retourne <paramref name="path2" />.</returns>
      <param name="path1">Premier chemin d'accès à combiner. </param>
      <param name="path2">Deuxième chemin d'accès à combiner. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" /> ou <paramref name="path2" /> contient un ou plusieurs caractères non valides définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" /> ou <paramref name="path2" /> a la valeur null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String,System.String)">
      <summary>Combine trois chaînes en un chemin d'accès.</summary>
      <returns>Chemins d'accès combinés.</returns>
      <param name="path1">Premier chemin d'accès à combiner. </param>
      <param name="path2">Deuxième chemin d'accès à combiner. </param>
      <param name="path3">Troisième chemin d'accès à combiner.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" />, <paramref name="path2" /> ou  <paramref name="path3" /> contient un ou plusieurs caractères non valides définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" />, <paramref name="path2" /> ou <paramref name="path3" /> a la valeur null. </exception>
    </member>
    <member name="M:System.IO.Path.Combine(System.String[])">
      <summary>Combine un tableau de chaînes en un chemin d'accès.</summary>
      <returns>Chemins d'accès combinés.</returns>
      <param name="paths">Tableau de parties du chemin d'accès.</param>
      <exception cref="T:System.ArgumentException">L'une des chaînes du tableau contient un ou plusieurs des caractères non valides définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">L'une des chaînes du tableau est null. </exception>
    </member>
    <member name="F:System.IO.Path.DirectorySeparatorChar">
      <summary>Fournit un caractère spécifique à la plateforme, utilisé pour séparer les niveaux de répertoire dans une chaîne de chemin d'accès qui reflète une organisation de système de fichiers hiérarchique.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetDirectoryName(System.String)">
      <summary>Retourne les informations relatives au répertoire pour la chaîne de chemin d'accès spécifiée.</summary>
      <returns>Informations relatives au répertoire pour <paramref name="path" />, ou null si <paramref name="path" /> indique un répertoire racine ou est null.Retourne un élément <see cref="F:System.String.Empty" /> si <paramref name="path" /> ne contient pas d'informations relatives au répertoire.</returns>
      <param name="path">Chemin d'accès d'un fichier ou d'un répertoire. </param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="path" /> contient des caractères non valides, est vide ou contient uniquement des espaces blancs. </exception>
      <exception cref="T:System.IO.PathTooLongException">Dans le .NET for Windows Store apps ou bibliothèque de classes Portable, intercepter l'exception de la classe de base, <see cref="T:System.IO.IOException" />, à la place.Le paramètre <paramref name="path" /> est plus long que la longueur maximale définie par le système.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetExtension(System.String)">
      <summary>Retourne l'extension de la chaîne de chemin d'accès spécifiée.</summary>
      <returns>Extension du chemin d'accès spécifié (y compris le point ".") ou null ou <see cref="F:System.String.Empty" />.Si <paramref name="path" /> est null, <see cref="M:System.IO.Path.GetExtension(System.String)" /> retourne null.Si <paramref name="path" /> ne contient pas d'informations relatives à l'extension, <see cref="M:System.IO.Path.GetExtension(System.String)" /> retourne <see cref="F:System.String.Empty" />.</returns>
      <param name="path">Chaîne de chemin d'accès pour laquelle obtenir l'extension. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contient un ou plusieurs caractères non valides définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />.  </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileName(System.String)">
      <summary>Retourne le nom et l'extension de fichier de la chaîne de chemin d'accès spécifiée.</summary>
      <returns>Caractères situés après le dernier caractère du répertoire dans <paramref name="path" />.Si le dernier caractère de <paramref name="path" /> est un caractère de séparation de répertoire ou de volume, cette méthode retourne <see cref="F:System.String.Empty" />.Si <paramref name="path" /> est null, cette méthode retourne null.</returns>
      <param name="path">Chaîne de chemin d'accès à partir de laquelle obtenir le nom et l'extension de fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contient un ou plusieurs caractères non valides définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileNameWithoutExtension(System.String)">
      <summary>Retourne le nom de fichier de la chaîne de chemin d'accès spécifiée sans l'extension.</summary>
      <returns>Chaîne retournée par <see cref="M:System.IO.Path.GetFileName(System.String)" />, moins le dernier point (.) et tous les caractères après celui-ci.</returns>
      <param name="path">Chemin d'accès au fichier. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contient un ou plusieurs caractères non valides définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFullPath(System.String)">
      <summary>Retourne le chemin d'accès absolu de la chaîne de chemin d'accès spécifiée.</summary>
      <returns>Emplacement qualifié complet de <paramref name="path" />, par exemple « C:\MonFichier.txt ».</returns>
      <param name="path">Fichier ou répertoire pour lequel obtenir les informations sur le chemin d'accès absolu. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs des caractères non valides définis par <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou Le système n'a pas pu récupérer le chemin d'accès absolu. </exception>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas les autorisations nécessaires. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> a la valeur null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contient un caractère deux-points (« : ») qui ne fait pas partie d'un identificateur de volume (par exemple, « c:\ »). </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetInvalidFileNameChars">
      <summary>Obtient un tableau contenant les caractères qui ne sont pas autorisés dans les noms de fichiers.</summary>
      <returns>Tableau contenant les caractères qui ne sont pas autorisés dans les noms de fichiers.</returns>
    </member>
    <member name="M:System.IO.Path.GetInvalidPathChars">
      <summary>Obtient un tableau contenant les caractères qui ne sont pas autorisés dans les noms de chemins d'accès.</summary>
      <returns>Tableau contenant les caractères qui ne sont pas autorisés dans les noms de chemins d'accès.</returns>
    </member>
    <member name="M:System.IO.Path.GetPathRoot(System.String)">
      <summary>Obtient les informations relatives au répertoire racine du chemin d'accès spécifié.</summary>
      <returns>Répertoire racine de <paramref name="path" />, par exemple « C:\ », ou null si <paramref name="path" /> est null, ou une chaîne vide si <paramref name="path" /> ne contient pas d'informations relatives au répertoire racine.</returns>
      <param name="path">Chemin d'accès à partir duquel obtenir les informations relatives au répertoire racine. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contient un ou plusieurs caractères non valides définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />.ou <see cref="F:System.String.Empty" /> a été passé à <paramref name="path" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetRandomFileName">
      <summary>Retourne un nom de dossier ou de fichier aléatoire.</summary>
      <returns>Nom de dossier ou de fichier aléatoire.</returns>
    </member>
    <member name="M:System.IO.Path.GetTempFileName">
      <summary>Crée un fichier temporaire de zéro octet nommé de façon univoque sur le disque et retourne le chemin d'accès complet de ce fichier.</summary>
      <returns>Chemin d'accès complet du fichier temporaire.</returns>
      <exception cref="T:System.IO.IOException">Une erreur d'E/S se produit, par exemple si aucun nom de fichier temporaire unique n'est disponible.ouCette méthode n'a pas pu créer un fichier temporaire.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetTempPath">
      <summary>Retourne le chemin d'accès du dossier temporaire de l'utilisateur actuel.</summary>
      <returns>Chemin d'accès au dossier temporaire, se terminant par une barre oblique inverse.</returns>
      <exception cref="T:System.Security.SecurityException">L'appelant n'a pas les autorisations nécessaires. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.HasExtension(System.String)">
      <summary>Détermine si un chemin d'accès inclut une extension de nom de fichier.</summary>
      <returns>true si les caractères qui suivent le dernier séparateur de répertoire (\\ ou /) ou le dernier séparateur de volume (:) dans le chemin d'accès incluent un point (.) suivi d'un ou plusieurs caractères ; sinon, false.</returns>
      <param name="path">Chemin d'accès où rechercher une extension. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contient un ou plusieurs caractères non valides définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.IsPathRooted(System.String)">
      <summary>Obtient une valeur indiquant si la chaîne de chemin d'accès spécifiée contient une racine.</summary>
      <returns>true si <paramref name="path" /> contient une racine ; sinon, false.</returns>
      <param name="path">Chemin d'accès à tester. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contient un ou plusieurs caractères non valides définis dans <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.PathSeparator">
      <summary>Caractère de séparation spécifique à la plateforme, utilisé pour séparer les chaînes de chemin d'accès dans les variables d'environnement.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.VolumeSeparatorChar">
      <summary>Fournit un caractère de séparation de volume spécifique à la plateforme.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Net.WebUtility">
      <summary>Fournit des méthodes pour l'encodage et le décodage d'URL lors du traitement de demandes Web. </summary>
    </member>
    <member name="M:System.Net.WebUtility.HtmlDecode(System.String)">
      <summary>Convertit une chaîne qui a été encodée en HTML pour une transmission HTTP en chaîne décodée.</summary>
      <returns>Chaîne décodée.</returns>
      <param name="value">Chaîne à décoder.</param>
    </member>
    <member name="M:System.Net.WebUtility.HtmlEncode(System.String)">
      <summary>Convertit une chaîne en chaîne encodée en HTML.</summary>
      <returns>Chaîne encodée.</returns>
      <param name="value">Chaîne à encoder.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecode(System.String)">
      <summary>Convertit en chaîne décodée une chaîne qui a été encodée pour une transmission dans une URL.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne décodée.</returns>
      <param name="encodedValue">Chaîne encodée en URL à décoder.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Convertit un tableau d'octets encodé qui a été encodé pour être transmis dans une URL dans un tableau d'octets décodé.</summary>
      <returns>retourne <see cref="T:System.Byte" /> ;Tableau décodé <see cref="T:System.Byte" />.</returns>
      <param name="encodedValue">Tableau <see cref="T:System.Byte" /> encodé en URL à décoder.</param>
      <param name="offset">Offset, en octets, depuis le début du tableau <see cref="T:System.Byte" /> à décoder.</param>
      <param name="count">Nombre, en octets, à décoder depuis le tableau <see cref="T:System.Byte" />.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncode(System.String)">
      <summary>Convertit une chaîne de texte en chaîne encodée en URL.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne encodée en URL.</returns>
      <param name="value">Texte à encoder en URL.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Convertit un tableau d'octets en tableau d'octets encodé URL.</summary>
      <returns>retourne <see cref="T:System.Byte" /> ;Tableau <see cref="T:System.Byte" /> encodé.</returns>
      <param name="value">Tableau <see cref="T:System.Byte" /> à encoder au format URL.</param>
      <param name="offset">Offset, en octets, depuis le début du tableau <see cref="T:System.Byte" /> à encoder.</param>
      <param name="count">Nombre, en octets, à encoder depuis le tableau <see cref="T:System.Byte" />.</param>
    </member>
    <member name="T:System.Runtime.Versioning.FrameworkName">
      <summary>Représente le nom d'une version du .NET Framework.</summary>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.Versioning.FrameworkName" /> à partir d'une chaîne qui contient les informations de version du .NET Framework.</summary>
      <param name="frameworkName">Chaîne qui contient les informations de version du .NET Framework.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="frameworkName" /> a la valeur <see cref="F:System.String.Empty" />.ou<paramref name="frameworkName" /> est constitué de moins de deux composants ou de plus de trois composants.ou<paramref name="frameworkName" /> n'inclut pas un numéro de version principale et secondaire.ou<paramref name="frameworkName " />n'inclut pas un numéro de version valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="frameworkName" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.Versioning.FrameworkName" /> à partir d'une chaîne et d'un objet <see cref="T:System.Version" /> qui identifient la version du .NET Framework.</summary>
      <param name="identifier">Chaîne qui identifie la version du .NET Framework. </param>
      <param name="version">Objet qui contient les informations de version du .NET Framework.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> a la valeur <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> a la valeur null.ou<paramref name="version" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.Versioning.FrameworkName" /> à partir d'une chaîne, d'un objet <see cref="T:System.Version" /> qui identifie la version du .NET Framework et d'un nom de profil.</summary>
      <param name="identifier">Chaîne qui identifie la version du .NET Framework.</param>
      <param name="version">Objet qui contient les informations de version du .NET Framework.</param>
      <param name="profile">Nom de profil.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> a la valeur <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> a la valeur null.ou<paramref name="version" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Object)">
      <summary>Retourne une valeur qui indique si cette instance de <see cref="T:System.Runtime.Versioning.FrameworkName" /> représente la même version du .NET Framework qu'un objet spécifique.</summary>
      <returns>true si chaque composant de l'objet <see cref="T:System.Runtime.Versioning.FrameworkName" /> actif coïncide avec le composant correspondant de <paramref name="obj" /> ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'instance en cours.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Runtime.Versioning.FrameworkName)">
      <summary>Retourne une valeur qui indique si cette instance de <see cref="T:System.Runtime.Versioning.FrameworkName" /> représente la même version du .NET Framework qu'une instance spécifique de <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>true si chaque composant de l'objet <see cref="T:System.Runtime.Versioning.FrameworkName" /> actif coïncide avec le composant correspondant de <paramref name="other" /> ; sinon, false.</returns>
      <param name="other">Objet à comparer avec l'instance en cours.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.FullName">
      <summary>Obtient le nom complet de cet objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Nom complet de cet objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.GetHashCode">
      <summary>Retourne le code de hachage pour l'objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Entier signé 32 bits qui représente le code de hachage de cette instance.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Identifier">
      <summary>Obtient l'identificateur de cet objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Identificateur de cet objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Equality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Retourne une valeur qui indique si deux objets <see cref="T:System.Runtime.Versioning.FrameworkName" /> représentent la même version du .NET Framework.</summary>
      <returns>true si les paramètres <paramref name="left" /> et <paramref name="right" /> représentent la même version du .NET Framework ; sinon, false.</returns>
      <param name="left">Premier objet à comparer.</param>
      <param name="right">Second objet à comparer.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Inequality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Retourne une valeur qui indique si deux objets <see cref="T:System.Runtime.Versioning.FrameworkName" /> représentent des versions différentes du .NET Framework.</summary>
      <returns>true si les paramètres <paramref name="left" /> et <paramref name="right" /> représentent des versions différentes du .NET Framework ; sinon, false.</returns>
      <param name="left">Premier objet à comparer.</param>
      <param name="right">Second objet à comparer.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Profile">
      <summary>Obtient le nom de profil de cet objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Nom de profil de cet objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.ToString">
      <summary>Retourne la représentation sous forme de chaîne de cet objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Chaîne qui représente l'objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Version">
      <summary>Obtient la version de cet objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Objet qui contient des informations de version sur cet objet <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
  </members>
</doc>