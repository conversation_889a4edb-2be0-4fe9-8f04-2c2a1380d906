﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.BitConverter">
      <summary>Преобразует базовые типы данных в массив байтов и массив байтов в базовые типы данных.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.BitConverter.DoubleToInt64Bits(System.Double)">
      <summary>Преобразует указанное число двойной точности с плавающей запятой в 64-битовое целое число со знаком.</summary>
      <returns>Значение 64-битового знакового целого числа эквивалентно <paramref name="value" />.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Boolean)">
      <summary>Возвращает указанное логическое значение в виде массива байтов.</summary>
      <returns>Массив байтов длиной 1.</returns>
      <param name="value">Логическое значение. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Char)">
      <summary>Возвращает указанное значение знака Юникода в виде массива байтов.</summary>
      <returns>Массив байтов длиной 2.</returns>
      <param name="value">Преобразуемый знак. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Double)">
      <summary>Возвращает указанное значение двойной точности с плавающей запятой в виде массива байтов.</summary>
      <returns>Массив байтов длиной 8.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int16)">
      <summary>Возвращает указанное значение 16-битового целого числа со знаком в виде массива байтов.</summary>
      <returns>Массив байтов длиной 2.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int32)">
      <summary>Возвращает указанное значение 32-битового целого числа со знаком в виде массива байтов.</summary>
      <returns>Массив байтов длиной 4.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int64)">
      <summary>Возвращает указанное значение 64-битового целого числа со знаком в виде массива байтов.</summary>
      <returns>Массив байтов длиной 8.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Single)">
      <summary>Возвращает указанное значение с плавающей запятой с обычной точностью в виде массива байтов.</summary>
      <returns>Массив байтов длиной 4.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt16)">
      <summary>Возвращает указанное значение 16-битового целого числа без знака в виде массива байтов.</summary>
      <returns>Массив байтов длиной 2.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt32)">
      <summary>Возвращает указанное значение 32-битового целого числа без знака в виде массива байтов.</summary>
      <returns>Массив байтов длиной 4.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt64)">
      <summary>Возвращает указанное значение 64-битового целого числа без знака в виде массива байтов.</summary>
      <returns>Массив байтов длиной 8.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.Int64BitsToDouble(System.Int64)">
      <summary>Преобразует указанное 64-битовое целое число со знаком в число двойной точности с плавающей запятой.</summary>
      <returns>Значение числа двойной точности с плавающей запятой эквивалентно <paramref name="value" />.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.BitConverter.IsLittleEndian">
      <summary>Указывает порядок байтов, в котором данные хранятся в архитектуре данного компьютера.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToBoolean(System.Byte[],System.Int32)">
      <summary>Возвращает логическое значение, преобразованное из одного байта с указанной позицией в массиве байтов.</summary>
      <returns>Значение true, если байт в <paramref name="startIndex" /> в параметре <paramref name="value" /> отличен от нуля; в противном случае — значение false.</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToChar(System.Byte[],System.Int32)">
      <summary>Возвращает знак Юникода, преобразованный из двух байтов с указанной позицией в массив байтов.</summary>
      <returns>Знак, образованный двумя байтами, начинающимися с <paramref name="startIndex" />.</returns>
      <param name="value">Массив. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="startIndex" /> равен длине <paramref name="value" /> минус 1.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToDouble(System.Byte[],System.Int32)">
      <summary>Возвращает число двойной точности с плавающей запятой, преобразованное из восьми байтов с указанной позицией в массив байтов.</summary>
      <returns>Число двойной точности с плавающей запятой, образованное восемью байтами, начинающимися с <paramref name="startIndex" />.</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="startIndex" /> больше или равно длине параметра <paramref name="value" /> минус 7 и меньше или равно длине параметра <paramref name="value" /> минус 1.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt16(System.Byte[],System.Int32)">
      <summary>Возвращает 16-битовое целое число со знаком, преобразованное из двух байтов с указанной позицией в массив байтов.</summary>
      <returns>16-битовое знаковое целое число, образованное двумя байтами, начинающимися с <paramref name="startIndex" />.</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="startIndex" /> равен длине <paramref name="value" /> минус 1.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt32(System.Byte[],System.Int32)">
      <summary>Возвращает 32-битовое целое число со знаком, преобразованное из четырех байтов с указанной позицией в массив байтов.</summary>
      <returns>32-битовое знаковое целое число, образованное четырьмя байтами, начинающимися с <paramref name="startIndex" />.</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="startIndex" /> больше или равно длине параметра <paramref name="value" /> минус 3 и меньше или равно длине параметра <paramref name="value" /> минус 1.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt64(System.Byte[],System.Int32)">
      <summary>Возвращает 64-битовое целое число со знаком, преобразованное из восьми байт с указанной позицией в массив байтов.</summary>
      <returns>64-битовое знаковое целое число, сформированное восемью байтами, начинающимися с <paramref name="startIndex" />.</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="startIndex" /> больше или равно длине параметра <paramref name="value" /> минус 7 и меньше или равно длине параметра <paramref name="value" /> минус 1.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToSingle(System.Byte[],System.Int32)">
      <summary>Возвращает число одинарной точности с плавающей запятой, преобразованное из четырех байтов с указанной позицией в массив байтов.</summary>
      <returns>Число одинарной точности с плавающей запятой, образованное четырьмя байтами, начинающимися с <paramref name="startIndex" />.</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="startIndex" /> больше или равно длине параметра <paramref name="value" /> минус 3 и меньше или равно длине параметра <paramref name="value" /> минус 1.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[])">
      <summary>Преобразует числовое значение каждого элемента заданного массива байтов в эквивалентное ему шестнадцатеричное строковое представление.</summary>
      <returns>Строка, состоящая из шестнадцатеричных пар, разделенных дефисами, где каждая пара предоставляет соответствующий элемент в <paramref name="value" />; например: "7F-2C-4A-00".</returns>
      <param name="value">Массив байтов. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32)">
      <summary>Преобразует числовое значение каждого элемента заданного подмассива байтов в эквивалентное ему шестнадцатеричное строковое представление.</summary>
      <returns>Строка, состоящая из шестнадцатеричных пар, разделенных дефисами, где каждая пара предоставляет соответствующий элемент в подмассиве <paramref name="value" />; например, "7F-2C-4A-00".</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32,System.Int32)">
      <summary>Преобразует числовое значение каждого элемента заданного подмассива байтов в эквивалентное ему шестнадцатеричное строковое представление.</summary>
      <returns>Строка, состоящая из шестнадцатеричных пар, разделенных дефисами, где каждая пара предоставляет соответствующий элемент в подмассиве <paramref name="value" />; например, "7F-2C-4A-00".</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <param name="length">Количество преобразуемых элементов в массиве <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="startIndex" /> или <paramref name="length" /> меньше нуля.– или –Значение параметра <paramref name="startIndex" /> меньше нуля и больше или равно длине <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentException">Сочетание <paramref name="startIndex" /> и <paramref name="length" /> не задает позицию в <paramref name="value" />; то есть, параметр <paramref name="startIndex" /> больше, чем длина <paramref name="value" /> минус параметр <paramref name="length" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt16(System.Byte[],System.Int32)">
      <summary>Возвращает 16-битовое целое число без знака, преобразованное из двух байтов с указанной позицией в массив байтов.</summary>
      <returns>16-битовое целое число без знака, образованное двумя байтами, начинающимися с <paramref name="startIndex" />.</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="startIndex" /> равен длине <paramref name="value" /> минус 1.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt32(System.Byte[],System.Int32)">
      <summary>Возвращает 32-битовое целое число без знака, преобразованное из четырех байтов с указанной позицией в массив байтов.</summary>
      <returns>32-битовое целое число без знака, образованное четырьмя байтами, начинающимися с <paramref name="startIndex" />.</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="startIndex" /> больше или равно длине параметра <paramref name="value" /> минус 3 и меньше или равно длине параметра <paramref name="value" /> минус 1.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt64(System.Byte[],System.Int32)">
      <summary>Возвращает 64-битовое целое число без знака, преобразованное из восьми байтов с указанной позицией в массив байтов.</summary>
      <returns>64-битовое целое число без знака, образованное восемью байтами, начинающимися с <paramref name="startIndex" />.</returns>
      <param name="value">Массив байтов. </param>
      <param name="startIndex">Начальная позиция в <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="startIndex" /> больше или равно длине параметра <paramref name="value" /> минус 7 и меньше или равно длине параметра <paramref name="value" /> минус 1.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> меньше нуля или больше, чем длина <paramref name="value" /> минус 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Convert">
      <summary>Преобразует значение одного базового типа данных к другому базовому типу данных.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type)">
      <summary>Возвращает объект указанного типа, значение которого эквивалентно заданному объекту.</summary>
      <returns>Объект, тип которого равен <paramref name="conversionType" />, а значение эквивалентно <paramref name="value" />.-или-Пустая ссылка (Nothing в Visual Basic), если <paramref name="value" /> равняется null, а <paramref name="conversionType" /> не является типом значения. </returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="conversionType">Тип возвращаемого объекта. </param>
      <exception cref="T:System.InvalidCastException">Данное преобразование не поддерживается.  -или-Параметр <paramref name="value" /> имеет значение null, и <paramref name="conversionType" /> является типом значения.-или-Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> находится не в формате, распознаваемом <paramref name="conversionType" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет номер, который находится вне диапазона <paramref name="conversionType" />.</exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="conversionType" /> имеет значение null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type,System.IFormatProvider)">
      <summary>Возвращает объект указанного типа, чье значение эквивалентно заданному объекту.Параметр предоставляет сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Объект, тип которого равен <paramref name="conversionType" />, а значение эквивалентно <paramref name="value" />.-или- <paramref name="value" />, если <see cref="T:System.Type" /> параметра <paramref name="value" /> равен <paramref name="conversionType" />.-или- Пустая ссылка (Nothing в Visual Basic), если <paramref name="value" /> равняется null, а <paramref name="conversionType" /> не является типом значения.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="conversionType">Тип возвращаемого объекта. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.InvalidCastException">Данное преобразование не поддерживается. -или-Параметр <paramref name="value" /> имеет значение null, и <paramref name="conversionType" /> является типом значения.-или-Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> имеет формат для <paramref name="conversionType" />, не распознаваемый <paramref name="provider" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет номер, который находится вне диапазона <paramref name="conversionType" />.</exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="conversionType" /> имеет значение null.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.TypeCode,System.IFormatProvider)">
      <summary>Возвращает объект указанного типа, чье значение эквивалентно заданному объекту.Параметр предоставляет сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Объект, базовый тип которого равен <paramref name="typeCode" />, а значение эквивалентно <paramref name="value" />.-или- Пустая ссылка (Nothing в Visual Basic), если <paramref name="value" /> равняется null, а <paramref name="typeCode" /> равняется <see cref="F:System.TypeCode.Empty" />, <see cref="F:System.TypeCode.String" /> или <see cref="F:System.TypeCode.Object" />.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="typeCode">Тип возвращаемого объекта. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.InvalidCastException">Данное преобразование не поддерживается.  -или-Параметр <paramref name="value" /> имеет значение null, и <paramref name="typeCode" /> задает тип значения.-или-Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> имеет формат для <paramref name="typeCode" />, не распознаваемый <paramref name="provider" />.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет номер, который находится вне диапазона для типа <paramref name="typeCode" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeCode" /> недопустим. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64CharArray(System.Char[],System.Int32,System.Int32)">
      <summary>Преобразует подмножество массива знаков Юникода, представляющих двоичные данные в виде цифр в кодировке Base64, в эквивалентный массив 8-разрядных целых чисел без знака.Параметры задают подмножество входного массива и количество преобразуемых элементов.</summary>
      <returns>Массив 8-битовых целых чисел без знака, эквивалентный числу элементов <paramref name="length" /> с позиции <paramref name="offset" /> в массиве <paramref name="inArray" />.</returns>
      <param name="inArray">Массив знаков Юникода. </param>
      <param name="offset">Позиция в массиве <paramref name="inArray" />. </param>
      <param name="length">Число преобразуемых элементов массива <paramref name="inArray" />. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="inArray" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="offset" /> или <paramref name="length" /> меньше 0.-или- <paramref name="offset" /> плюс <paramref name="length" /> указывает на позицию вне массива <paramref name="inArray" />. </exception>
      <exception cref="T:System.FormatException">Длина массива <paramref name="inArray" />, исключая пробелы, не равна нулю и не кратна 4. -или-Формат параметра <paramref name="inArray" /> недопустим.<paramref name="inArray" /> содержит знак не из base 64, более чем два символа заполнения, или другой символ (не пробел) среди символов заполнения.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64String(System.String)">
      <summary>Преобразует заданную строку, представляющую двоичные данные в виде цифр в кодировке Base64, в эквивалентный массив 8-разрядных целых чисел без знака.</summary>
      <returns>Массив 8-разрядных целых чисел без знака, эквивалентный <paramref name="s" />.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="s" /> имеет значение null. </exception>
      <exception cref="T:System.FormatException">Длина массива <paramref name="s" />, исключая пробелы, не равна нулю и не кратна 4. -или-Формат параметра <paramref name="s" /> недопустим.<paramref name="s" /> содержит знак не из base 64, более чем два символа заполнения, или другой символ (не пробел) среди символов заполнения.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.GetTypeCode(System.Object)">
      <summary>Возвращает <see cref="T:System.TypeCode" /> для заданного объекта.</summary>
      <returns>
        <see cref="T:System.TypeCode" /> для <paramref name="value" /> или <see cref="F:System.TypeCode.Empty" />, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64CharArray(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>Преобразует подмножество массива 8-разрядных целых чисел без знака в эквивалентное подмножество массива знаков Юникода, состоящее из цифр в кодировке Base64.Подмножества задаются с помощью параметров как смещение во входном и выходном массивах и количеством преобразуемых элементов входного массива.</summary>
      <returns>32-битовое целое число со знаком, представляющее число байтов в массиве <paramref name="outArray" />.</returns>
      <param name="inArray">Входной массив 8-битовых целых чисел без знака. </param>
      <param name="offsetIn">Позиция в массиве <paramref name="inArray" />. </param>
      <param name="length">Число преобразуемых элементов <paramref name="inArray" />. </param>
      <param name="outArray">Выходной массив знаков Юникода. </param>
      <param name="offsetOut">Позиция в массиве <paramref name="outArray" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="inArray" /> или <paramref name="outArray" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="offsetIn" />, <paramref name="offsetOut" /> или <paramref name="length" /> является отрицательным.-или- <paramref name="offsetIn" /> плюс <paramref name="length" /> превышает длину массива <paramref name="inArray" />.-или- <paramref name="offsetOut" /> плюс количество возвращаемых элементов превышает длину массива <paramref name="outArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[])">
      <summary>Преобразует массив 8-разрядных целых чисел без знака в эквивалентное строковое представление, состоящее из цифр в кодировке Base64.</summary>
      <returns>Строковое представление содержимого массива <paramref name="inArray" /> в кодировке Base64.</returns>
      <param name="inArray">Массив 8-битовых целых чисел без знака. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="inArray" /> имеет значение null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[],System.Int32,System.Int32)">
      <summary>Преобразует подмножество массива 8-разрядных целых чисел без знака в эквивалентное строковое представление, состоящее из цифр в кодировке Base64.В параметрах задается подмножество как смещение во входном массиве и количество преобразуемых элементов этого массива.</summary>
      <returns>Строковое представление <paramref name="length" /> элементов массива <paramref name="inArray" /> в кодировке Base64, начиная с позиции <paramref name="offset" />.</returns>
      <param name="inArray">Массив 8-битовых целых чисел без знака. </param>
      <param name="offset">Смещение в массиве <paramref name="inArray" />. </param>
      <param name="length">Число преобразуемых элементов <paramref name="inArray" />. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="inArray" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="offset" /> или <paramref name="length" /> является отрицательным.-или- <paramref name="offset" /> плюс <paramref name="length" /> превышает длину массива <paramref name="inArray" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Boolean)">
      <summary>Возвращает заданное логическое значение; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Возвращаемое логическое значение. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Byte)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">Преобразуемое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Double)">
      <summary>Преобразует значение заданного числа двойной точности с плавающей запятой в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int16)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int64)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object)">
      <summary>Преобразует значение заданного объекта в эквивалентное логическое значение.</summary>
      <returns>true или false, отражающее значение, возвращаемое методом <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> при вызове для базового типа параметра <paramref name="value" />.Если значением параметра <paramref name="value" /> является null, метод возвращает false.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> — это строка, которая не равна <see cref="F:System.Boolean.TrueString" /> или <see cref="F:System.Boolean.FalseString" />.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />.-или-Соглашение <paramref name="value" /> для <see cref="T:System.Boolean" /> не поддерживается.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентное логическое значение, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>true или false, отражающее значение, возвращаемое методом <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> при вызове для базового типа параметра <paramref name="value" />.Если значением параметра <paramref name="value" /> является null, метод возвращает false.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> — это строка, которая не равна <see cref="F:System.Boolean.TrueString" /> или <see cref="F:System.Boolean.FalseString" />.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />.-или-Соглашение <paramref name="value" /> для <see cref="T:System.Boolean" /> не поддерживается. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.SByte)">
      <summary>Преобразует значение заданного 8-битового целого числа со знаком в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String)">
      <summary>Преобразует заданное строковое представление логического значения в эквивалентное логическое значение.</summary>
      <returns>true, если параметр <paramref name="value" /> имеет значение <see cref="F:System.Boolean.TrueString" />, или false, если параметр <paramref name="value" /> имеет значение <see cref="F:System.Boolean.FalseString" /> либо null.</returns>
      <param name="value">Строка, содержащая значение <see cref="F:System.Boolean.TrueString" /> или <see cref="F:System.Boolean.FalseString" />. </param>
      <exception cref="T:System.FormatException">Значение параметра <paramref name="value" /> не равно <see cref="F:System.Boolean.TrueString" /> или <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление логического значения в эквивалентное логическое значение, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>true, если параметр <paramref name="value" /> имеет значение <see cref="F:System.Boolean.TrueString" />, или false, если параметр <paramref name="value" /> имеет значение <see cref="F:System.Boolean.FalseString" /> либо null.</returns>
      <param name="value">Строка, содержащая значение <see cref="F:System.Boolean.TrueString" /> или <see cref="F:System.Boolean.FalseString" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров.Этот параметр не учитывается.</param>
      <exception cref="T:System.FormatException">Значение параметра <paramref name="value" /> не равно <see cref="F:System.Boolean.TrueString" /> или <see cref="F:System.Boolean.FalseString" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt16)">
      <summary>Преобразует значение заданного 16-битового целого числа без знака в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt32)">
      <summary>Преобразует значение заданного 32-битового целого числа без знака в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt64)">
      <summary>Преобразует значение заданного 64-битового целого числа без знака в эквивалентное логическое значение.</summary>
      <returns>true, если значение параметра <paramref name="value" /> не равно нулю; в противном случае — false.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное 8-битовое целое число без знака.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Byte)">
      <summary>Возвращает заданное 8-битовое целое число без знака; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Возвращаемое 8-разрядное целое число без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Char)">
      <summary>Преобразует значение заданного символа Юникода в эквивалентное 8-битовое целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">Знак Юникода, который необходимо преобразовать. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, больше <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное 8-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 8-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Преобразуемое число. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Byte.MaxValue" /> или меньше значения <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное 8-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 8-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Byte.MaxValue" /> или меньше значения <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int16)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное 8-битовое целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше <see cref="F:System.Byte.MinValue" /> или больше <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное 8-битовое целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше <see cref="F:System.Byte.MinValue" /> или больше <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int64)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное 8-битовое целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше <see cref="F:System.Byte.MinValue" /> или больше <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object)">
      <summary>Преобразует значение заданного объекта в 8-разрядное целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в формате свойства для значения <see cref="T:System.Byte" /> распознаваемом .</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование из <paramref name="value" /> в тип <see cref="T:System.Byte" /> не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Byte.MinValue" /> или больше <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентное 8-разрядное целое число без знака, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>8-разрядное целое число без знака, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в формате свойства для значения <see cref="T:System.Byte" /> распознаваемом .</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование из <paramref name="value" /> в тип <see cref="T:System.Byte" /> не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Byte.MinValue" /> или больше <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.SByte)">
      <summary>Преобразует значение заданного 8-битового целого числа со знаком в эквивалентное 8-битовое целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />является менее <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное 8-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 8-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число одинарной точности с плавающей запятой. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Byte.MaxValue" /> или меньше значения <see cref="F:System.Byte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 8-битовое целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Byte.MinValue" /> или больше <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 8-разрядное целое число без знака, учитывая сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>8-разрядное целое число без знака, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Byte.MinValue" /> или больше <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.Int32)">
      <summary>Преобразует строковое представление числа с указанным основанием системы счисления в эквивалентное ему 8-битовое целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="fromBase">Основание системы счисления, используемой для представления числа, заданного в параметре <paramref name="value" />, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fromBase" /> не равно 2, 8, 10 или 16. -или-Значение параметра <paramref name="value" />, представляющее число без знака в системе счисления, основание которой отлично от 10, предваряется знаком минус. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойство <paramref name="value" /> имеет значение <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> содержит знак, не являющийся допустимой цифрой в системе счисления, основание которой задано в параметре <paramref name="fromBase" />.Если первый знак в параметре <paramref name="value" /> недопустим, в сообщении об исключении указывается, что отсутствуют цифры для преобразования; в противном случае в этом сообщении указывается, что <paramref name="value" /> содержит недействительные замыкающие знаки.</exception>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" />, представляющее число без знака в системе счисления, основание которой отлично от 10, предваряется знаком минус.-или-<paramref name="value" /> представляет число, которое меньше <see cref="F:System.Byte.MinValue" /> или больше <see cref="F:System.Byte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt16)">
      <summary>Преобразует значение заданного 16-разрядного целого числа без знака в эквивалентное 8-разрядное целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt32)">
      <summary>Преобразует значение заданного 32-разрядного целого числа без знака в эквивалентное 8-разрядное целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt64)">
      <summary>Преобразует значение заданного 64-разрядного целого числа без знака в эквивалентное 8-разрядное целое число без знака.</summary>
      <returns>8-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Byte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Byte)">
      <summary>Преобразует значение заданного 8-разрядного целого числа без знака в эквивалентный символ Юникода.</summary>
      <returns>Знак Юникода, который эквивалентен значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int16)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентный символ Юникода.</summary>
      <returns>Знак Юникода, который эквивалентен значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />является менее <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентный символ Юникода.</summary>
      <returns>Знак Юникода, который эквивалентен значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше <see cref="F:System.Char.MinValue" /> или больше <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int64)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентный символ Юникода.</summary>
      <returns>Знак Юникода, который эквивалентен значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше <see cref="F:System.Char.MinValue" /> или больше <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object)">
      <summary>Преобразует значение заданного объекта в знак Юникода.</summary>
      <returns>Знак Юникода, который эквивалентен значению, или <see cref="F:System.Char.MinValue" />, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> является пустой строкой.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />.-или-Соглашение <paramref name="value" /> для <see cref="T:System.Char" /> не поддерживается. </exception>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше <see cref="F:System.Char.MinValue" /> или больше <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентный знак Юникода, используя указанные сведения о форматировании, связанные с языком и региональными параметрами.</summary>
      <returns>Знак Юникода, который эквивалентен значению <paramref name="value" />, или <see cref="F:System.Char.MinValue" />, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> является пустой строкой.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Соглашение <paramref name="value" /> для <see cref="T:System.Char" /> не поддерживается.</exception>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше <see cref="F:System.Char.MinValue" /> или больше <see cref="F:System.Char.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.SByte)">
      <summary>Преобразует значение заданного 8-битового целого числа со знаком в эквивалентный символ Юникода.</summary>
      <returns>Знак Юникода, который эквивалентен значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />является менее <see cref="F:System.Char.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String)">
      <summary>Преобразует первый знак указанной строки в знак Юникода.</summary>
      <returns>Знак Юникода, эквивалентный первому и единственному знаку в <paramref name="value" />.</returns>
      <param name="value">Строка длиной в 1 знак. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.FormatException">Длина <paramref name="value" /> не равна 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String,System.IFormatProvider)">
      <summary>Преобразует первый знак заданной строки в знак Юникода, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Знак Юникода, эквивалентный первому и единственному знаку в <paramref name="value" />.</returns>
      <param name="value">Строка длиной в 1 знак или null. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров.Этот параметр не учитывается.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.FormatException">Длина <paramref name="value" /> не равна 1. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt16)">
      <summary>Преобразует значение заданного 16-разрядного целого числа без знака в эквивалентный символ Юникода.</summary>
      <returns>Знак Юникода, который эквивалентен значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt32)">
      <summary>Преобразует значение заданного 32-разрядного целого числа без знака в эквивалентный символ Юникода.</summary>
      <returns>Знак Юникода, который эквивалентен значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt64)">
      <summary>Преобразует значение заданного 64-разрядного целого числа без знака в эквивалентный символ Юникода.</summary>
      <returns>Знак Юникода, который эквивалентен значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Char.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object)">
      <summary>Преобразует значение указанного объекта в объект <see cref="T:System.DateTime" />.</summary>
      <returns>Дата и время, эквивалентные значению <paramref name="value" />, или дата и время, эквивалентные значению <see cref="F:System.DateTime.MinValue" />, если значение <paramref name="value" /> равно null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не является допустимым значением даты или времени.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в объект <see cref="T:System.DateTime" />, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Дата и время, эквивалентные значению <paramref name="value" />, или дата и время, эквивалентные значению <see cref="F:System.DateTime.MinValue" />, если значение <paramref name="value" /> равно null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не является допустимым значением даты или времени.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String)">
      <summary>Преобразует заданное строковое представление даты и времени в эквивалентное значение даты и времени.</summary>
      <returns>Дата и время, эквивалентные значению <paramref name="value" />, или дата и время, эквивалентные значению <see cref="F:System.DateTime.MinValue" />, если значение <paramref name="value" /> равно null.</returns>
      <param name="value">Строковое представление даты и времени.</param>
      <exception cref="T:System.FormatException">Значение параметра <paramref name="value" /> не является правильно отформатированной строкой, представляющей дату и время. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное значение даты и времени, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Дата и время, эквивалентные значению <paramref name="value" />, или дата и время, эквивалентные значению <see cref="F:System.DateTime.MinValue" />, если значение <paramref name="value" /> равно null.</returns>
      <param name="value">Строка, содержащая дату и время, которые нужно преобразовать. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">Значение параметра <paramref name="value" /> не является правильно отформатированной строкой, представляющей дату и время. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное десятичное число.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Byte)">
      <summary>Преобразует значение заданного 8-разрядного целого число без знака в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Decimal)">
      <summary>Возвращает заданное десятичное число; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Десятичное число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, которое эквивалентно значению <paramref name="value" />. </returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Decimal.MaxValue" /> или меньше значения <see cref="F:System.Decimal.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int16)">
      <summary>Преобразует значение заданного 16-разрядного знакового целого числа в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int32)">
      <summary>Преобразует значение заданного 32-разрядного знакового целого числа в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int64)">
      <summary>Преобразует значение заданного 64-разрядного знакового целого числа в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object)">
      <summary>Преобразует значение заданного объекта в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, эквивалентное <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> равняется null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в подходящем формате для типа <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Decimal.MinValue" /> или больше <see cref="F:System.Decimal.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентное десятичное число, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Десятичное число, эквивалентное <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> равняется null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в подходящем формате для типа <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />.-или-Преобразование не поддерживается. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Decimal.MinValue" /> или больше <see cref="F:System.Decimal.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.SByte)">
      <summary>Преобразует значение заданного 8-разрядного знакового целого числа в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, которое эквивалентно значению <paramref name="value" />. </returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Decimal.MaxValue" /> или меньше значения <see cref="F:System.Decimal.MinValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, эквивалентное числу <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> равняется null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">Параметр <paramref name="value" /> не является числом в допустимом формате.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Decimal.MinValue" /> или больше <see cref="F:System.Decimal.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное десятичное число, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Десятичное число, эквивалентное числу <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> равняется null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">Параметр <paramref name="value" /> не является числом в допустимом формате.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Decimal.MinValue" /> или больше <see cref="F:System.Decimal.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt16)">
      <summary>Преобразует значение заданного 16-разрядного целого числа без знака в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt32)">
      <summary>Преобразует значение заданного 32-разрядного целого числа без знака в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt64)">
      <summary>Преобразует значение заданного 64-разрядного целого числа без знака в эквивалентное десятичное число.</summary>
      <returns>Десятичное число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Byte)">
      <summary>Преобразует значение заданного 8-разрядного целого числа без знака в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>Число с плавающей запятой двойной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>Число с плавающей запятой двойной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Double)">
      <summary>Возвращает заданное число с плавающей запятой двойной точности; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Возвращаемое число с плавающей запятой двойной точности. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int16)">
      <summary>Преобразует значение заданного 16-разрядного знакового целого числа в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>Число с плавающей запятой удвоенной точности эквивалентное значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int32)">
      <summary>Преобразует значение заданного 32-разрядного знакового целого числа в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>Число с плавающей запятой двойной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int64)">
      <summary>Преобразует значение заданного 64-разрядного знакового целого числа в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>Число с плавающей запятой двойной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object)">
      <summary>Преобразует значение заданного объекта в число с плавающей запятой двойной точности.</summary>
      <returns>Число с плавающей запятой двойной точности, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в подходящем формате для типа <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Double.MinValue" /> или больше <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в число с плавающей запятой двойной точности, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Число с плавающей запятой двойной точности, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в подходящем формате для типа <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Double.MinValue" /> или больше <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.SByte)">
      <summary>Преобразует значение заданного 8-разрядного знакового целого числа в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одинарной точности в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>Число с плавающей запятой двойной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">Число с плавающей запятой одиночной точности. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>Число с плавающей запятой двойной точности, эквивалентное числу <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">Параметр <paramref name="value" /> не является числом в допустимом формате.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Double.MinValue" /> или больше <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное число с плавающей запятой двойной точности, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Число с плавающей запятой двойной точности, эквивалентное числу <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">Параметр <paramref name="value" /> не является числом в допустимом формате.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Double.MinValue" /> или больше <see cref="F:System.Double.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt16)">
      <summary>Преобразует значение заданного 16-разрядного целого числа без знака в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>Число с плавающей запятой двойной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt32)">
      <summary>Преобразует значение заданного 32-разрядного целого числа без знака в эквивалентное число двойной точности с плавающей запятой.</summary>
      <returns>Число с плавающей запятой двойной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt64)">
      <summary>Преобразует значение заданного 64-разрядного целого числа без знака в эквивалентное число двойной точности с плавающей запятой.</summary>
      <returns>Число с плавающей запятой двойной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Byte)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>16-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Char)">
      <summary>Преобразует значение заданного символа Юникода в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>16-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />. </returns>
      <param name="value">Знак Юникода, который необходимо преобразовать. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 16-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int16.MaxValue" /> или меньше значения <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 16-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int16.MaxValue" /> или меньше значения <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int16)">
      <summary>Возвращает заданное 16-битовое целое число со знаком; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Возвращаемое 16-разрядное знаковое целое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>16-битовое целое число со знаком, эквивалентное <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int16.MaxValue" /> или меньше значения <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int64)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>16-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int16.MaxValue" /> или меньше значения <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object)">
      <summary>Преобразует значение заданного объекта в 16-битовое целое число со знаком.</summary>
      <returns>16-разрядное знаковое целое число, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в подходящем формате для типа <see cref="T:System.Int16" />.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int16.MinValue" /> или больше <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентное 16-битовое целое число со знаком, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>16-разрядное знаковое целое число, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в подходящем формате для типа <see cref="T:System.Int16" />.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int16.MinValue" /> или больше <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.SByte)">
      <summary>Преобразует значение заданного 8-разрядного целого числа со знаком в эквивалентное 16-разрядное целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 16-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int16.MaxValue" /> или меньше значения <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>16-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int16.MinValue" /> или больше <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 16-битовое целое число со знаком, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>16-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int16.MinValue" /> или больше <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.Int32)">
      <summary>Преобразует строковое представление числа с указанным основанием системы счисления в эквивалентное ему 16-битовое целое число со знаком.</summary>
      <returns>16-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="fromBase">Основание системы счисления, используемой для представления числа, заданного в параметре <paramref name="value" />, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fromBase" /> не равно 2, 8, 10 или 16. -или-Значение параметра <paramref name="value" />, представляющее число со знаком в системе счисления, основание которой отлично от 10, предваряется знаком минус. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойство <paramref name="value" /> имеет значение <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> содержит знак, не являющийся допустимой цифрой в системе счисления, основание которой задано в параметре <paramref name="fromBase" />.Если первый знак в параметре <paramref name="value" /> недопустим, в сообщении об исключении указывается, что отсутствуют цифры для преобразования; в противном случае в этом сообщении указывается, что <paramref name="value" /> содержит недействительные замыкающие знаки.</exception>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" />, представляющее число со знаком в системе счисления, основание которой отлично от 10, предваряется знаком минус.-или-<paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int16.MinValue" /> или больше <see cref="F:System.Int16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt16)">
      <summary>Преобразует значение заданного 16-битового целого числа без знака в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>16-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt32)">
      <summary>Преобразует значение заданного 32-битового целого числа без знака в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>16-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt64)">
      <summary>Преобразует значение заданного 64-битового целого числа без знака в эквивалентное 16-битовое целое число со знаком.</summary>
      <returns>16-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Int16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное 32-битовое целое число со знаком.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Byte)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное 32-битовое целое число со знаком.</summary>
      <returns>32-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Char)">
      <summary>Преобразует значение заданного символа Юникода в эквивалентное 32-битовое целое число со знаком.</summary>
      <returns>32-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">Знак Юникода, который необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное 32-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 32-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int32.MaxValue" /> или меньше значения <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное 32-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 32-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int32.MaxValue" /> или меньше значения <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int16)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное 32-битовое целое число со знаком.</summary>
      <returns>32-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int32)">
      <summary>Возвращает заданное 32-битовое целое число со знаком; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Возвращаемое 32-разрядное знаковое целое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>32-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int32.MaxValue" /> или меньше значения <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object)">
      <summary>Преобразует значение заданного объекта в 32-битовое целое число со знаком.</summary>
      <returns>32-разрядное знаковое целое число, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int32.MinValue" /> или больше <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентное 32-битовое целое число со знаком, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>32-разрядное знаковое целое число, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int32.MinValue" /> или больше <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.SByte)">
      <summary>Преобразует значение заданного 8-разрядного целого числа со знаком в эквивалентное 32-разрядное целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное 32-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 32-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int32.MaxValue" /> или меньше значения <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 32-битовое целое число со знаком.</summary>
      <returns>32-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int32.MinValue" /> или больше <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 32-битовое целое число со знаком, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>32-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int32.MinValue" /> или больше <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.Int32)">
      <summary>Преобразует строковое представление числа с указанным основанием системы счисления в эквивалентное ему 32-битовое целое число со знаком.</summary>
      <returns>32-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="fromBase">Основание системы счисления, используемой для представления числа, заданного в параметре <paramref name="value" />, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fromBase" /> не равно 2, 8, 10 или 16. -или-Значение параметра <paramref name="value" />, представляющее число со знаком в системе счисления, основание которой отлично от 10, предваряется знаком минус. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойство <paramref name="value" /> имеет значение <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> содержит знак, не являющийся допустимой цифрой в системе счисления, основание которой задано в параметре <paramref name="fromBase" />.Если первый знак в параметре <paramref name="value" /> недопустим, в сообщении об исключении указывается, что отсутствуют цифры для преобразования; в противном случае в этом сообщении указывается, что <paramref name="value" /> содержит недействительные замыкающие знаки.</exception>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" />, представляющее число со знаком в системе счисления, основание которой отлично от 10, предваряется знаком минус.-или-<paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int32.MinValue" /> или больше <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt16)">
      <summary>Преобразует значение заданного 16-битового целого числа без знака в эквивалентное 32-битовое целое число со знаком.</summary>
      <returns>32-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt32)">
      <summary>Преобразует значение заданного 32-битового целого числа без знака в эквивалентное 32-битовое целое число со знаком.</summary>
      <returns>32-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 32-bit signed integer.</summary>
      <returns>32-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Byte)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Char)">
      <summary>Преобразует значение заданного символа Юникода в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">Знак Юникода, который необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 64-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int64.MaxValue" /> или меньше значения <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 64-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int64.MaxValue" /> или меньше значения <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int16)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int64)">
      <summary>Возвращает заданное 64-битовое целое число со знаком; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">64-разрядное знаковое целое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object)">
      <summary>Преобразует значение заданного объекта в 64-битовое целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int64.MinValue" /> или больше <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентное 64-битовое целое число со знаком, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>64-разрядное знаковое целое число, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />.-или-Преобразование не поддерживается. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int64.MinValue" /> или больше <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.SByte)">
      <summary>Преобразует значение заданного 8-разрядного целого числа со знаком в эквивалентное 64-разрядное целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 64-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.Int64.MaxValue" /> или меньше значения <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int64.MinValue" /> или больше <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 64-битовое целое число со знаком, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int64.MinValue" /> или больше <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.Int32)">
      <summary>Преобразует строковое представление числа с указанным основанием системы счисления в эквивалентное ему 64-битовое целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="fromBase">Основание системы счисления, используемой для представления числа, заданного в параметре <paramref name="value" />, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fromBase" /> не равно 2, 8, 10 или 16. -или-Значение параметра <paramref name="value" />, представляющее число со знаком в системе счисления, основание которой отлично от 10, предваряется знаком минус. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойство <paramref name="value" /> имеет значение <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> содержит знак, не являющийся допустимой цифрой в системе счисления, основание которой задано в параметре <paramref name="fromBase" />.Если первый знак в параметре <paramref name="value" /> недопустим, в сообщении об исключении указывается, что отсутствуют цифры для преобразования; в противном случае в этом сообщении указывается, что <paramref name="value" /> содержит недействительные замыкающие знаки.</exception>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" />, представляющее число со знаком в системе счисления, основание которой отлично от 10, предваряется знаком минус.-или-<paramref name="value" /> представляет число, которое меньше <see cref="F:System.Int64.MinValue" /> или больше <see cref="F:System.Int64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt16)">
      <summary>Преобразует значение заданного 16-битового целого числа без знака в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt32)">
      <summary>Преобразует значение заданного 32-битового целого числа без знака в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt64)">
      <summary>Преобразует значение заданного 64-битового целого числа без знака в эквивалентное 64-битовое целое число со знаком.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.Int64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Byte)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Char)">
      <summary>Преобразует значение заданного символа Юникода в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">Знак Юникода, который необходимо преобразовать. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 8-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" /> или меньше значения <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 8-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" /> или меньше значения <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 8-bit signed integer.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" /> или меньше значения <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" /> или меньше значения <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int64)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" /> или меньше значения <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object)">
      <summary>Преобразует значение заданного объекта в 8-битовое целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате. </exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.SByte.MinValue" /> или больше <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентное 8-разрядное знаковое целое число, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>8-разрядное знаковое целое число, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате. </exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.SByte.MinValue" /> или больше <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.SByte)">
      <summary>Возвращает заданное 8-битовое целое число со знаком; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Возвращаемое 8-разрядное знаковое целое число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 8-разрядного знакового целого числа.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" /> или меньше значения <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если value имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.SByte.MinValue" /> или больше <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 8-битовое целое число со знаком, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="value" /> имеет значение null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.SByte.MinValue" /> или больше <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.Int32)">
      <summary>Преобразует строковое представление числа с указанным основанием системы счисления в эквивалентное ему 8-битовое целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="fromBase">Основание системы счисления, используемой для представления числа, заданного в параметре <paramref name="value" />, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fromBase" /> не равно 2, 8, 10 или 16. -или-Значение параметра <paramref name="value" />, представляющее число со знаком в системе счисления, основание которой отлично от 10, предваряется знаком минус. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойство <paramref name="value" /> имеет значение <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> содержит знак, не являющийся допустимой цифрой в системе счисления, основание которой задано в параметре <paramref name="fromBase" />.Если первый знак в параметре <paramref name="value" /> недопустим, в сообщении об исключении указывается, что отсутствуют цифры для преобразования; в противном случае в этом сообщении указывается, что <paramref name="value" /> содержит недействительные замыкающие знаки.</exception>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" />, представляющее число со знаком в системе счисления, основание которой отлично от 10, предваряется знаком минус.-или-<paramref name="value" /> представляет число, которое меньше <see cref="F:System.SByte.MinValue" /> или больше <see cref="F:System.SByte.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt16)">
      <summary>Преобразует значение заданного 16-битового целого числа без знака в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt32)">
      <summary>Преобразует значение заданного 32-битового целого числа без знака в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" /> или меньше значения <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt64)">
      <summary>Преобразует значение заданного 64-битового целого числа без знака в эквивалентное 8-битовое целое число со знаком.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> больше значения <see cref="F:System.SByte.MaxValue" /> или меньше значения <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Byte)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, которое эквивалентно значению <paramref name="value" />.<paramref name="value" /> округляется до ближайшего числа.Например, при округлении до второго знака после десятичной запятой значение 2,345 преобразуется в 2,34, а значение 2,355 — в 2,36.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное число с плавающей запятой двойной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, которое эквивалентно значению <paramref name="value" />.<paramref name="value" /> округляется до ближайшего числа.Например, при округлении до второго знака после десятичной запятой значение 2,345 преобразуется в 2,34, а значение 2,355 — в 2,36.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int16)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int64)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object)">
      <summary>Преобразует значение заданного объекта в число с плавающей запятой одиночной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Single.MinValue" /> или больше <see cref="F:System.Single.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в число с плавающей запятой одиночной точности, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Число с плавающей запятой одиночной точности, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Single.MinValue" /> или больше <see cref="F:System.Single.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.SByte)">
      <summary>Преобразует значение заданного 8-битового целого числа со знаком в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>8-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Single)">
      <summary>Возвращает заданное число с плавающей запятой одиночной точности; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Возвращаемое число с плавающей запятой одиночной точности. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, эквивалентное числу <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">Параметр <paramref name="value" /> не является числом в допустимом формате.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Single.MinValue" /> или больше <see cref="F:System.Single.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное число с плавающей запятой одиночной точности, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Число с плавающей запятой одиночной точности, эквивалентное числу <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">Параметр <paramref name="value" /> не является числом в допустимом формате.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.Single.MinValue" /> или больше <see cref="F:System.Single.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt16)">
      <summary>Преобразует значение заданного 16-битового целого числа без знака в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt32)">
      <summary>Преобразует значение заданного 32-битового целого числа без знака в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt64)">
      <summary>Преобразует значение заданного 64-битового целого числа без знака в эквивалентное число с плавающей запятой одиночной точности.</summary>
      <returns>Число с плавающей запятой одиночной точности, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean)">
      <summary>Преобразует указанное логическое значение в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean,System.IFormatProvider)">
      <summary>Преобразует указанное логическое значение в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <param name="provider">Экземпляр объекта.Этот параметр не учитывается.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.IFormatProvider)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное строковое представление, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.Int32)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное строковое представление в указанной системе счисления.</summary>
      <returns>Строковое представление значения параметра <paramref name="value" /> в системе счисления с основанием <paramref name="toBase" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <param name="toBase">Основание системы счисления возвращаемого значения, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="toBase" /> не равно 2, 8, 10 или 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char)">
      <summary>Преобразует значение заданного знака Юникода в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Знак Юникода, который необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char,System.IFormatProvider)">
      <summary>Преобразует значение заданного знака Юникода в эквивалентное строковое представление, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Знак Юникода, который необходимо преобразовать. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров.Этот параметр не учитывается.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime)">
      <summary>Преобразует значение заданного объекта <see cref="T:System.DateTime" /> в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Значение даты и времени для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта <see cref="T:System.DateTime" /> в эквивалентное строковое представление с использованием указанных сведений об особенностях форматирования для данного языка и региональных параметров.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Значение даты и времени для преобразования. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal,System.IFormatProvider)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное строковое представление, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double,System.IFormatProvider)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.IFormatProvider)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное строковое представление, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.Int32)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное строковое представление в указанной системе счисления.</summary>
      <returns>Строковое представление значения параметра <paramref name="value" /> в системе счисления с основанием <paramref name="toBase" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <param name="toBase">Основание системы счисления возвращаемого значения, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="toBase" /> не равно 2, 8, 10 или 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.IFormatProvider)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное строковое представление, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное строковое представление в указанной системе счисления.</summary>
      <returns>Строковое представление значения параметра <paramref name="value" /> в системе счисления с основанием <paramref name="toBase" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <param name="toBase">Основание системы счисления возвращаемого значения, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="toBase" /> не равно 2, 8, 10 или 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.IFormatProvider)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное строковое представление, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.Int32)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное строковое представление в указанной системе счисления.</summary>
      <returns>Строковое представление значения параметра <paramref name="value" /> в системе счисления с основанием <paramref name="toBase" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <param name="toBase">Основание системы счисления возвращаемого значения, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="toBase" /> не равно 2, 8, 10 или 16. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object)">
      <summary>Преобразует значение заданного объекта в эквивалентное строковое представление.</summary>
      <returns>Строковое представление значения параметра <paramref name="value" /> или <see cref="F:System.String.Empty" />, если <paramref name="value" /> представляет собой объект, значение которого равно null.Если значением параметра <paramref name="value" /> является null, метод возвращает null.</returns>
      <param name="value">Объект, содержащий значение для преобразования, или null. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение указанного объекта в эквивалентное строковое представление с использованием указанных сведений об особенностях форматирования для данного языка и региональных параметров.</summary>
      <returns>Строковое представление значения параметра <paramref name="value" /> или <see cref="F:System.String.Empty" />, если <paramref name="value" /> представляет собой объект, значение которого равно null.Если значением параметра <paramref name="value" /> является null, метод возвращает null.</returns>
      <param name="value">Объект, содержащий значение для преобразования, или null. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte)">
      <summary>Преобразует значение заданного 8-битового целого числа со знаком в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte,System.IFormatProvider)">
      <summary>Преобразует значение заданного 8-битового целого числа со знаком в эквивалентное строковое представление, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single,System.IFormatProvider)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное строковое представление, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16)">
      <summary>Преобразует значение заданного 16-битового целого числа без знака в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16,System.IFormatProvider)">
      <summary>Преобразует значение заданного 16-битового целого числа без знака в эквивалентное строковое представление, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32)">
      <summary>Преобразует значение заданного 32-битового целого числа без знака в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32,System.IFormatProvider)">
      <summary>Преобразует значение заданного 32-битового целого числа без знака в эквивалентное строковое представление, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64)">
      <summary>Преобразует значение заданного 64-битового целого числа без знака в эквивалентное строковое представление.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64,System.IFormatProvider)">
      <summary>Преобразует значение заданного 64-битового целого числа без знака в эквивалентное строковое представление, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>Строковое представление параметра <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Byte)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Char)">
      <summary>Преобразует значение заданного символа Юникода в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, эквивалентное <paramref name="value" />.</returns>
      <param name="value">Знак Юникода, который необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 16-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 16-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int16)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> меньше нуля. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int64)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object)">
      <summary>Преобразует значение заданного объекта в 16-битовое целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt16.MinValue" /> или больше <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентное 16-битовое целое число без знака, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>16-разрядное целое число без знака, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt16.MinValue" /> или больше <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.SByte)">
      <summary>Преобразует значение заданного 8-битового целого числа со знаком в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> меньше нуля. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 16-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 16-битовое целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt16.MinValue" /> или больше <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 16-битовое целое число без знака, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>16-разрядное целое число без знака, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt16.MinValue" /> или больше <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.Int32)">
      <summary>Преобразует строковое представление числа с указанным основанием системы счисления в эквивалентное ему 16-битовое целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="fromBase">Основание системы счисления, используемой для представления числа, заданного в параметре <paramref name="value" />, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fromBase" /> не равно 2, 8, 10 или 16. -или-Значение параметра <paramref name="value" />, представляющее число без знака в системе счисления, основание которой отлично от 10, предваряется знаком минус. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойство <paramref name="value" /> имеет значение <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> содержит знак, не являющийся допустимой цифрой в системе счисления, основание которой задано в параметре <paramref name="fromBase" />.Если первый знак в параметре <paramref name="value" /> недопустим, в сообщении об исключении указывается, что отсутствуют цифры для преобразования; в противном случае в этом сообщении указывается, что <paramref name="value" /> содержит недействительные замыкающие знаки.</exception>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" />, представляющее число без знака в системе счисления, основание которой отлично от 10, предваряется знаком минус.-или-<paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt16.MinValue" /> или больше <see cref="F:System.UInt16.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt16)">
      <summary>Возвращает заданное 16-битовое целое число без знака; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Возвращаемое 16-разрядное целое число без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt32)">
      <summary>Преобразует значение заданного 32-разрядного целого числа без знака в эквивалентное 16-разрядное целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt64)">
      <summary>Преобразует значение заданного 64-разрядного целого числа без знака в эквивалентное 16-разрядное целое число без знака.</summary>
      <returns>16-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.UInt16.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Byte)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Char)">
      <summary>Преобразует значение заданного символа Юникода в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">Знак Юникода, который необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 32-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 32-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int16)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> меньше нуля. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> меньше нуля. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int64)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object)">
      <summary>Преобразует значение заданного объекта в 32-битовое целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, эквивалентное значению <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt32.MinValue" /> или больше <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентное 32-битовое целое число без знака, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>32-разрядное целое число без знака, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt32.MinValue" /> или больше <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.SByte)">
      <summary>Преобразует значение заданного 8-битового целого числа со знаком в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> меньше нуля. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 32-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt32.MinValue" /> или больше <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 32-битовое целое число без знака, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt32.MinValue" /> или больше <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.Int32)">
      <summary>Преобразует строковое представление числа с указанным основанием системы счисления в эквивалентное ему 32-битовое целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="fromBase">Основание системы счисления, используемой для представления числа, заданного в параметре <paramref name="value" />, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fromBase" /> не равно 2, 8, 10 или 16. -или-Значение параметра <paramref name="value" />, представляющее число без знака в системе счисления, основание которой отлично от 10, предваряется знаком минус. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойство <paramref name="value" /> имеет значение <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> содержит знак, не являющийся допустимой цифрой в системе счисления, основание которой задано в параметре <paramref name="fromBase" />.Если первый знак в параметре <paramref name="value" /> недопустим, в сообщении об исключении указывается, что отсутствуют цифры для преобразования; в противном случае в этом сообщении указывается, что <paramref name="value" /> содержит недействительные замыкающие знаки.</exception>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" />, представляющее число без знака в системе счисления, основание которой отлично от 10, предваряется знаком минус.-или-<paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt32.MinValue" /> или больше <see cref="F:System.UInt32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt16)">
      <summary>Преобразует значение заданного 16-битового целого числа без знака в эквивалентное 32-битовое целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt32)">
      <summary>Возвращает заданное 32-битовое целое число без знака; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Возвращаемое 32-разрядное целое число без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt64)">
      <summary>Преобразует значение заданного 64-разрядного целого числа без знака в эквивалентное 32-разрядное целое число без знака.</summary>
      <returns>32-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное целое число без знака для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение <paramref name="value" /> больше значения <see cref="F:System.UInt32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Boolean)">
      <summary>Преобразует заданное логическое значение в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>Число 1, если <paramref name="value" /> имеет значение true; в противном случае — 0.</returns>
      <param name="value">Логическое значение, которое необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Byte)">
      <summary>Преобразует значение заданного 8-битового целого числа без знака в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Char)">
      <summary>Преобразует значение заданного символа Юникода в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>64-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">Знак Юникода, который необходимо преобразовать. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Decimal)">
      <summary>Преобразует значение заданного десятичного числа в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 64-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Десятичное число для преобразования. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Double)">
      <summary>Преобразует значение заданного числа с плавающей запятой двойной точности в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 64-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой двойной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int16)">
      <summary>Преобразует значение заданного 16-битового целого числа со знаком в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>64-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> меньше нуля. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int32)">
      <summary>Преобразует значение заданного 32-битового целого числа со знаком в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>64-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> меньше нуля. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int64)">
      <summary>Преобразует значение заданного 64-битового целого числа со знаком в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>64-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">64-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> меньше нуля. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object)">
      <summary>Преобразует значение заданного объекта в 64-битовое целое число без знака.</summary>
      <returns>64-разрядное целое число без знака, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />, или значение null. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt64.MinValue" /> или больше <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object,System.IFormatProvider)">
      <summary>Преобразует значение заданного объекта в эквивалентное 64-битовое целое число без знака, используя указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>64-разрядное целое число без знака, эквивалентное значению <paramref name="value" />, или нуль, если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Объект, реализующий интерфейс <see cref="T:System.IConvertible" />. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не находится в соответствующем формате.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="value" /> не реализует интерфейс <see cref="T:System.IConvertible" />. -или-Преобразование не поддерживается.</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt64.MinValue" /> или больше <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.SByte)">
      <summary>Преобразует значение заданного 8-битового целого числа со знаком в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>64-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">8-разрядное знаковое целое число для преобразования. </param>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" /> меньше нуля. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Single)">
      <summary>Преобразует значение заданного числа с плавающей запятой одиночной точности в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>Значение <paramref name="value" />, округленное до ближайшего 64-разрядного целого числа без знака.Если <paramref name="value" /> имеет среднее значение между двумя целыми числами, будет возвращено четное число; так, значение 4,5 преобразуется в 4, а 5,5 — в 6.</returns>
      <param name="value">Число с плавающей запятой одиночной точности, подлежащее преобразованию. </param>
      <exception cref="T:System.OverflowException">Параметр <paramref name="value" /> меньше нуля или больше <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>64-разрядное знаковое целое число, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt64.MinValue" /> или больше <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.IFormatProvider)">
      <summary>Преобразует заданное строковое представление числа в эквивалентное 64-битовое целое число без знака, учитывая указанные сведения об особенностях форматирования, связанных с языком и региональными параметрами.</summary>
      <returns>64-разрядное целое число без знака, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="provider">Объект, предоставляющий сведения о форматировании для определенного языка и региональных параметров. </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> не состоит из необязательного знака и следующей за ним последовательности цифр (от 0 до 9). </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt64.MinValue" /> или больше <see cref="F:System.UInt64.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.Int32)">
      <summary>Преобразует строковое представление числа с указанным основанием системы счисления в эквивалентное ему 64-битовое целое число без знака.</summary>
      <returns>64-разрядное целое число без знака, которое эквивалентно значению параметра <paramref name="value" />, или 0 (нуль), если <paramref name="value" /> имеет значение null.</returns>
      <param name="value">Строка, содержащая преобразуемое число. </param>
      <param name="fromBase">Основание системы счисления, используемой для представления числа, заданного в параметре <paramref name="value" />, равное 2, 8, 10 или 16. </param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="fromBase" /> не равно 2, 8, 10 или 16. -или-Значение параметра <paramref name="value" />, представляющее число без знака в системе счисления, основание которой отлично от 10, предваряется знаком минус. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойство <paramref name="value" /> имеет значение <see cref="F:System.String.Empty" />. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> содержит знак, не являющийся допустимой цифрой в системе счисления, основание которой задано в параметре <paramref name="fromBase" />.Если первый знак в параметре <paramref name="value" /> недопустим, в сообщении об исключении указывается, что отсутствуют цифры для преобразования; в противном случае в этом сообщении указывается, что <paramref name="value" /> содержит недействительные замыкающие знаки.</exception>
      <exception cref="T:System.OverflowException">Значение параметра <paramref name="value" />, представляющее число без знака в системе счисления, основание которой отлично от 10, предваряется знаком минус.-или-<paramref name="value" /> представляет число, которое меньше <see cref="F:System.UInt64.MinValue" /> или больше <see cref="F:System.UInt64.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt16)">
      <summary>Преобразует значение заданного 16-битового целого числа без знака в эквивалентное 64-битовое целое число без знака.</summary>
      <returns>64-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">16-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 64-bit unsigned integer.</summary>
      <returns>64-разрядное целое число без знака, которое эквивалентно значению <paramref name="value" />.</returns>
      <param name="value">32-разрядное целое число без знака для преобразования. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt64)">
      <summary>Возвращает заданное 64-битовое целое число без знака; фактическое преобразование не производится.</summary>
      <returns>Параметр <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Возвращаемое 64-разрядное целое число без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Environment">
      <summary>Предоставляет сведения о текущей среде и платформе, а также необходимые для управления ими средства.Этот класс не наследуется.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.CurrentManagedThreadId">
      <summary>Возвращает уникальный идентификатор текущего управляемого потока.</summary>
      <returns>Целочисленное значение, представляющее уникальный идентификатор для этого управляемого потока.</returns>
    </member>
    <member name="M:System.Environment.ExpandEnvironmentVariables(System.String)">
      <summary>Замещает имя каждой переменной среды, внедренной в указанную строку, строчным эквивалентом значения переменной, а затем возвращает результирующую строку.</summary>
      <returns>Строка, в которой каждая переменная среды замещена ее значением.</returns>
      <param name="name">Строка, содержащая либо не содержащая имена переменных среды.Каждая переменная среды с двух сторон окружена знаками процента (%).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.FailFast(System.String)">
      <summary>Завершает процесс сразу после записи сообщения в журнал событий приложений Windows, после чего включает сообщение в отчет об ошибках, отправляемый в корпорацию Майкрософт.</summary>
      <param name="message">Сообщение, в котором объясняется причина завершения процесса или содержится значение null, если объяснение отсутствует.</param>
    </member>
    <member name="M:System.Environment.FailFast(System.String,System.Exception)">
      <summary>Завершает процесс сразу после записи сообщения в журнал событий приложений Windows, после чего включает сообщение и сведения об исключении в отчет об ошибках, отправляемый в корпорацию Майкрософт.</summary>
      <param name="message">Сообщение, в котором объясняется причина завершения процесса или содержится значение null, если объяснение отсутствует.</param>
      <param name="exception">Исключение, представляющее ошибку, вызвавшую завершение процесса.Обычно это исключение в блоке catch.</param>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariable(System.String)">
      <summary>Возвращает из текущего процесса значение переменной среды. </summary>
      <returns>Значение переменной среды, заданное параметром <paramref name="variable" /> или значение null, если переменная среды не найдена.</returns>
      <param name="variable">Имя переменной среды.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariables">
      <summary>Возвращает из текущего процесса имена всех переменных среды и их значения.</summary>
      <returns>Словарь, в котором содержатся имена всех переменных среды и их значения; в противном случае, если переменные среды не найдены, — пустой словарь.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <exception cref="T:System.OutOfMemoryException">The buffer is out of memory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.HasShutdownStarted">
      <summary>Возвращает значение, указывающее, выгружается ли текущий домен приложения или среда CLR завершает работу. </summary>
      <returns>Значение true, если текущий домен приложения выгружается или среда CLR завершает работу; в противном случае — значение false..</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.NewLine">
      <summary>Возвращает строку, обозначающую в данной среде начало новой строки.</summary>
      <returns>Строка, содержащая "\r\n" для платформ, отличных от Unix, или строка, содержащая "\n" для платформ Unix.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.ProcessorCount">
      <summary>Возвращает число процессоров на текущем компьютере.</summary>
      <returns>32-битовое целое число со знаком, которое задает количество процессоров на текущем компьютере.Значение по умолчанию отсутствует.Если текущий компьютер содержит несколько групп процессоров, данное свойство возвращает число логических процессоров, доступных для использования средой CLR.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="NUMBER_OF_PROCESSORS" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.SetEnvironmentVariable(System.String,System.String)">
      <summary>Создает, изменяет или удаляет переменную среды, хранящуюся в текущем процессе.</summary>
      <param name="variable">Имя переменной среды.</param>
      <param name="value">Значение, которое необходимо присвоить параметру <paramref name="variable" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="variable" /> contains a zero-length string, an initial hexadecimal zero character (0x00), or an equal sign ("="). -or-The length of <paramref name="variable" /> or <paramref name="value" /> is greater than or equal to 32,767 characters.-or-An error occurred during the execution of this operation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.StackTrace">
      <summary>Возвращает текущие сведения о трассировке стека.</summary>
      <returns>Строка, содержащая сведения о трассировке стека.Это значение может быть равно <see cref="F:System.String.Empty" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack trace information is out of range.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.TickCount">
      <summary>Возвращает время, истекшее с момента загрузки системы (в миллисекундах).</summary>
      <returns>32-битовое целое число со знаком, содержащее время, истекшее с момента с последней загрузки системы (в миллисекундах). </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Math">
      <summary>Предоставляет константы и статические методы для тригонометрических, логарифмических и иных общих математических функций.Чтобы просмотреть исходный код .NET Framework для этого типа, см. Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Decimal)">
      <summary>Возвращает абсолютное значение числа <see cref="T:System.Decimal" />.</summary>
      <returns>Десятичное число x, такое что 0 ≤ x ≤<see cref="F:System.Decimal.MaxValue" />.</returns>
      <param name="value">Число, которое больше или равно значению <see cref="F:System.Decimal.MinValue" />, но меньше или равно значению <see cref="F:System.Decimal.MaxValue" />. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Double)">
      <summary>Возвращает абсолютное значение числа двойной точности с плавающей запятой.</summary>
      <returns>Число х двойной точности с плавающей запятой такое, что 0 ≤ x ≤<see cref="F:System.Double.MaxValue" />.</returns>
      <param name="value">Число, которое больше или равно значению <see cref="F:System.Double.MinValue" />, но меньше или равно значению <see cref="F:System.Double.MaxValue" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int16)">
      <summary>Возвращает абсолютное значение 16-битового целого числа со знаком.</summary>
      <returns>16-разрядное целое число х со знаком, такое что 0 ≤ x ≤<see cref="F:System.Int16.MaxValue" />.</returns>
      <param name="value">Число, которое больше значения <see cref="F:System.Int16.MinValue" />, но меньше или равно значению <see cref="F:System.Int16.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> равняется <see cref="F:System.Int16.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int32)">
      <summary>Возвращает абсолютное значение 32-битового целого числа со знаком.</summary>
      <returns>32-разрядное целое число х со знаком, такое что 0 ≤ x ≤<see cref="F:System.Int32.MaxValue" />.</returns>
      <param name="value">Число, которое больше значения <see cref="F:System.Int32.MinValue" />, но меньше или равно значению <see cref="F:System.Int32.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> равняется <see cref="F:System.Int32.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int64)">
      <summary>Возвращает абсолютное значение 64-битового целого числа со знаком.</summary>
      <returns>64-разрядное целое число х со знаком, такое что 0 ≤ x ≤<see cref="F:System.Int64.MaxValue" />.</returns>
      <param name="value">Число, которое больше значения <see cref="F:System.Int64.MinValue" />, но меньше или равно значению <see cref="F:System.Int64.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> равняется <see cref="F:System.Int64.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.SByte)">
      <summary>Возвращает абсолютное значение 8-битового целого числа со знаком.</summary>
      <returns>8-разрядное целое число х со знаком, такое что 0 ≤ x ≤<see cref="F:System.SByte.MaxValue" />.</returns>
      <param name="value">Число, которое больше значения <see cref="F:System.SByte.MinValue" />, но меньше или равно значению <see cref="F:System.SByte.MaxValue" />.</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> равняется <see cref="F:System.SByte.MinValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Single)">
      <summary>Возвращает абсолютное значение числа одинарной точности с плавающей запятой.</summary>
      <returns>Число х одинарной точности с плавающей запятой, такое что 0 ≤ x ≤<see cref="F:System.Single.MaxValue" />.</returns>
      <param name="value">Число, которое больше или равно значению <see cref="F:System.Single.MinValue" />, но меньше или равно значению <see cref="F:System.Single.MaxValue" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Acos(System.Double)">
      <summary>Возвращает угол, косинус которого равен указанному числу.</summary>
      <returns>Угол θ, измеренный в радианах, такой что 0 ≤θ≤π-или- значение <see cref="F:System.Double.NaN" />, если <paramref name="d" /> &lt; -1, <paramref name="d" /> &gt; 1 или значение параметра <paramref name="d" /> равно <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Число, представляющее косинус, где значение параметра <paramref name="d" /> должно быть больше или равно -1, но меньше или равно 1. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Asin(System.Double)">
      <summary>Возвращает угол, синус которого равен указанному числу.</summary>
      <returns>Угол θ, измеренный в радианах, такой что -π/2 ≤θ≤π/2 -или- значение <see cref="F:System.Double.NaN" />, если <paramref name="d" /> &lt; -1, <paramref name="d" /> &gt; 1 или значение параметра <paramref name="d" /> равно <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Число, представляющее синус, где значение параметра <paramref name="d" /> должно быть больше или равно -1, но меньше или равно 1. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan(System.Double)">
      <summary>Возвращает угол, тангенс которого равен указанному числу.</summary>
      <returns>Угол θ, измеренный в радианах, такой что -π/2 ≤θ≤π/2.-или- значение <see cref="F:System.Double.NaN" />, если <paramref name="d" /> равно <see cref="F:System.Double.NaN" />, -π/2, округленное до двойной точности (-1,5707963267949), если <paramref name="d" /> равно <see cref="F:System.Double.NegativeInfinity" />, или π/2, округленное до двойной точности (1,5707963267949), если <paramref name="d" /> равно <see cref="F:System.Double.PositiveInfinity" />.</returns>
      <param name="d">Число, представляющее тангенс. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan2(System.Double,System.Double)">
      <summary>Возвращает угол, тангенс которого равен отношению двух указанных чисел.</summary>
      <returns>Угол θ, измеренный в радианах, такой что -π≤θ≤π, и tg(θ) = <paramref name="y" />/<paramref name="x" />, где (<paramref name="x" />, <paramref name="y" />) — это точка в декартовой системе координат.Обратите внимание на следующее.Для (<paramref name="x" />, <paramref name="y" />) в первом квадранте, 0 &lt; θ &lt; π/2.Для (<paramref name="x" />, <paramref name="y" />) во втором квадранте, π/2 &lt; θ≤π.Для (<paramref name="x" />, <paramref name="y" />) в квадранте,-π &lt; θ &lt;-π/2.Для (<paramref name="x" />, <paramref name="y" />) в квадранте,-π/2 &lt; θ &lt; 0.Для точек за пределами указанных квадрантов возвращаемое значение указано ниже.Если y равно 0 и x не является отрицательным, θ = 0.Если y равно 0 и x не является отрицательным, θ = π.Если y — положительное число, а x равно 0, θ = π/2.Если y является отрицательным и х равно 0, θ = -π/2.Если y равен 0 и х равен 0, то θ = -π/2. Если значение параметра <paramref name="x" /> или <paramref name="y" /> равно <see cref="F:System.Double.NaN" /> либо если значения параметров <paramref name="x" /> и <paramref name="y" /> равны значению <see cref="F:System.Double.PositiveInfinity" /> или <see cref="F:System.Double.NegativeInfinity" />, метод возвращает значение <see cref="F:System.Double.NaN" />.</returns>
      <param name="y">Координата y точки. </param>
      <param name="x">Координата х точки. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Decimal)">
      <summary>Возвращает наименьшее целое число, которое больше или равно заданному десятичному числу.</summary>
      <returns>Наименьшее целое число, которое больше или равно <paramref name="d" />.Обратите внимание, что данный метод возвращает не целочисленное значение, а значение типа <see cref="T:System.Decimal" />.</returns>
      <param name="d">Десятичное число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Double)">
      <summary>Возвращает наименьшее целое число, которое больше или равно заданному числу с плавающей запятой двойной точности.</summary>
      <returns>Наименьшее целочисленное значение, которое больше или равно <paramref name="a" />.Если значение параметра <paramref name="a" /> равно  <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> или <see cref="F:System.Double.PositiveInfinity" />, возвращается это значение.Обратите внимание, что данный метод возвращает не целочисленное значение, а значение типа <see cref="T:System.Double" />.</returns>
      <param name="a">Число двойной точности с плавающей запятой. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cos(System.Double)">
      <summary>Возвращает косинус указанного угла.</summary>
      <returns>Косинус <paramref name="d" />.Если значение параметра <paramref name="d" /> равно <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> или <see cref="F:System.Double.PositiveInfinity" />, то данный метод возвращает <see cref="F:System.Double.NaN" />.</returns>
      <param name="d">Угол, измеряемый в радианах. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cosh(System.Double)">
      <summary>Возвращает гиперболический косинус указанного угла.</summary>
      <returns>Гиперболический косинус <paramref name="value" />.Если значение параметра <paramref name="value" /> равно <see cref="F:System.Double.NegativeInfinity" /> или <see cref="F:System.Double.PositiveInfinity" />, возвращается значение <see cref="F:System.Double.PositiveInfinity" />.Если значение параметра <paramref name="value" /> равно <see cref="F:System.Double.NaN" />, возвращается значение <see cref="F:System.Double.NaN" />.</returns>
      <param name="value">Угол, измеряемый в радианах. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.E">
      <summary>Представляет основание натурального логарифма, определяемое константой e.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Exp(System.Double)">
      <summary>Возвращает значение e, возведенное в указанную степень.</summary>
      <returns>Число e, возведенное в степень <paramref name="d" />.Если значение параметра <paramref name="d" /> равно <see cref="F:System.Double.NaN" /> или <see cref="F:System.Double.PositiveInfinity" />, возвращается это значение.Если значение параметра <paramref name="d" /> равно <see cref="F:System.Double.NegativeInfinity" />, возвращается значение 0.</returns>
      <param name="d">Число, определяющее степень. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Decimal)">
      <summary>Возвращает наибольшее целое число, которое меньше или равно указанному десятичному числу.</summary>
      <returns>Наибольшее целое число, меньшее или равное <paramref name="d" />.Обратите внимание, что этот метод возвращает целочисленное значение типа <see cref="T:System.Math" />.</returns>
      <param name="d">Десятичное число. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Double)">
      <summary>Возвращает наибольшее целое число, которое меньше или равно заданному числу двойной точности с плавающей запятой.</summary>
      <returns>Наибольшее целое число, меньшее или равное <paramref name="d" />.Если значение параметра <paramref name="d" /> равно <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> или <see cref="F:System.Double.PositiveInfinity" />, возвращается это значение.</returns>
      <param name="d">Число двойной точности с плавающей запятой. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.IEEERemainder(System.Double,System.Double)">
      <summary>Возвращает остаток от деления одного указанного числа на другое указанное число.</summary>
      <returns>Число, равное <paramref name="x" /> - (<paramref name="y" />Q), где Q является частным <paramref name="x" />/<paramref name="y" />, округленным до ближайшего целого числа (если <paramref name="x" />/<paramref name="y" /> находится на равном расстоянии от двух целых чисел, выбирается четное число).Если значение <paramref name="x" /> - (<paramref name="y" />Q) равно нулю, возвращается значение +0 при положительном <paramref name="x" /> или значение -0 при отрицательном <paramref name="x" />.Если значение параметра <paramref name="y" /> равно 0, возвращается значение <see cref="F:System.Double.NaN" />.</returns>
      <param name="x">Делимое. </param>
      <param name="y">Делитель. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double)">
      <summary>Возвращает натуральный логарифм (с основанием e) указанного числа.</summary>
      <returns>Одно из значений, перечисленных в следующей таблице. Параметр <paramref name="d" />Возвращаемое значение Положительное число Натуральный логарифм <paramref name="d" />; то есть, ln <paramref name="d" />, или журнал e<paramref name="d" />Нуль <see cref="F:System.Double.NegativeInfinity" />Отрицательное число <see cref="F:System.Double.NaN" />Равно значению <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Равно значению <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Число, логарифм которого требуется найти. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double,System.Double)">
      <summary>Возвращает логарифм указанного числа в системе счисления с указанным основанием.</summary>
      <returns>Одно из значений, перечисленных в следующей таблице.(+бесконечность обозначает <see cref="F:System.Double.PositiveInfinity" />, -бесконечность обозначает <see cref="F:System.Double.NegativeInfinity" />, а нечисловое значение обозначает <see cref="F:System.Double.NaN" />.)<paramref name="a" /><paramref name="newBase" />Возвращаемое значение<paramref name="a" />&gt; 0(0 &lt;<paramref name="newBase" />&lt; 1) -или-(<paramref name="newBase" />&gt; 1)lognewBase(a)<paramref name="a" />&lt; 0(любое значение)NaN(любое значение)<paramref name="newBase" />&lt; 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = +бесконечностьNaN<paramref name="a" /> = не число(любое значение)NaN(любое значение)<paramref name="newBase" /> = не числоNaN(любое значение)<paramref name="newBase" /> = 1NaN<paramref name="a" /> = 00 &lt;<paramref name="newBase" />&lt; 1 +бесконечность<paramref name="a" /> = 0<paramref name="newBase" />&gt; 1-бесконечность<paramref name="a" /> = + Infinity0 &lt;<paramref name="newBase" />&lt; 1-бесконечность<paramref name="a" /> = + Infinity<paramref name="newBase" />&gt; 1+бесконечность<paramref name="a" /> = 1<paramref name="newBase" /> = 00<paramref name="a" /> = 1<paramref name="newBase" /> = +бесконечность0</returns>
      <param name="a">Число, логарифм которого требуется найти. </param>
      <param name="newBase">Основание логарифма. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log10(System.Double)">
      <summary>Возвращает логарифм с основанием 10 указанного числа.</summary>
      <returns>Одно из значений, перечисленных в следующей таблице. Параметр <paramref name="d" />Возвращаемое значение Положительное число Основание логарифма 10 <paramref name="d" />; то есть журнал 10<paramref name="d" />. Нуль <see cref="F:System.Double.NegativeInfinity" />Отрицательное число <see cref="F:System.Double.NaN" />Равно значению <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Равно значению <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Число, логарифм которого должен быть найден. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Byte,System.Byte)">
      <summary>Возвращает большее из двух 8-битовых целых чисел без знака.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 8-разрядных целых чисел без знака. </param>
      <param name="val2">Второе из двух сравниваемых 8-разрядных целых чисел без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Decimal,System.Decimal)">
      <summary>Возвращает большее из двух десятичных чисел.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых десятичных чисел. </param>
      <param name="val2">Второе из двух сравниваемых десятичных чисел. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Double,System.Double)">
      <summary>Возвращает большее из двух чисел двойной точности с плавающей запятой.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.Если <paramref name="val1" />, <paramref name="val2" /> или оба параметра <paramref name="val1" /> и <paramref name="val2" /> равны <see cref="F:System.Double.NaN" />, возвращается значение <see cref="F:System.Double.NaN" />.</returns>
      <param name="val1">Первое из двух сравниваемых чисел двойной точности с плавающей запятой. </param>
      <param name="val2">Второе из двух сравниваемых чисел двойной точности с плавающей запятой. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int16,System.Int16)">
      <summary>Возвращает большее из двух 16-битовых целых чисел со знаком.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 16-разрядных целых чисел со знаком. </param>
      <param name="val2">Второе из двух сравниваемых 16-разрядных целых чисел со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int32,System.Int32)">
      <summary>Возвращает большее из двух 32-битовых целых чисел со знаком.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 32-разрядных целых чисел со знаком. </param>
      <param name="val2">Второе из двух сравниваемых 32-разрядных целых чисел со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int64,System.Int64)">
      <summary>Возвращает большее из двух 64-битовых целых чисел со знаком.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 64-разрядных целых чисел со знаком. </param>
      <param name="val2">Второе из двух сравниваемых 64-разрядных целых чисел со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.SByte,System.SByte)">
      <summary>Возвращает большее из двух 8-битовых целых чисел со знаком.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 8-разрядных целых чисел со знаком. </param>
      <param name="val2">Второе из двух сравниваемых 8-разрядных целых чисел со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Single,System.Single)">
      <summary>Возвращает большее из двух чисел одинарной точности с плавающей запятой.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.Если <paramref name="val1" />, <paramref name="val2" /> или оба параметра <paramref name="val1" /> и <paramref name="val2" /> равны <see cref="F:System.Single.NaN" />, возвращается значение <see cref="F:System.Single.NaN" />.</returns>
      <param name="val1">Первое из двух сравниваемых чисел одинарной точности с плавающей запятой. </param>
      <param name="val2">Второе из двух сравниваемых чисел одинарной точности с плавающей запятой. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt16,System.UInt16)">
      <summary>Возвращает большее из двух 16-битовых целых чисел без знака.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 16-разрядных целых чисел без знака. </param>
      <param name="val2">Второе из двух сравниваемых 16-разрядных целых чисел без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt32,System.UInt32)">
      <summary>Возвращает большее из двух 32-битовых целых чисел без знака.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 32-разрядных целых чисел без знака. </param>
      <param name="val2">Второе из двух сравниваемых 32-разрядных целых чисел без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt64,System.UInt64)">
      <summary>Возвращает большее из двух 64-битовых целых чисел без знака.</summary>
      <returns>Большее из значений двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 64-разрядных целых чисел без знака. </param>
      <param name="val2">Второе из двух сравниваемых 64-разрядных целых чисел без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Byte,System.Byte)">
      <summary>Возвращает меньшее из двух 8-битовых целых чисел без знака.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 8-разрядных целых чисел без знака. </param>
      <param name="val2">Второе из двух сравниваемых 8-разрядных целых чисел без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Decimal,System.Decimal)">
      <summary>Возвращает меньшее из двух десятичных чисел.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых десятичных чисел. </param>
      <param name="val2">Второе из двух сравниваемых десятичных чисел. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Double,System.Double)">
      <summary>Возвращает меньшее из двух чисел двойной точности с плавающей запятой.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.Если <paramref name="val1" />, <paramref name="val2" /> или оба параметра <paramref name="val1" /> и <paramref name="val2" /> равны <see cref="F:System.Double.NaN" />, возвращается значение <see cref="F:System.Double.NaN" />.</returns>
      <param name="val1">Первое из двух сравниваемых чисел двойной точности с плавающей запятой. </param>
      <param name="val2">Второе из двух сравниваемых чисел двойной точности с плавающей запятой. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int16,System.Int16)">
      <summary>Возвращает меньшее из двух 16-битовых целых чисел со знаком.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 16-разрядных целых чисел со знаком. </param>
      <param name="val2">Второе из двух сравниваемых 16-разрядных целых чисел со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int32,System.Int32)">
      <summary>Возвращает меньшее из двух 32-битовых целых чисел со знаком.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 32-разрядных целых чисел со знаком. </param>
      <param name="val2">Второе из двух сравниваемых 32-разрядных целых чисел со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int64,System.Int64)">
      <summary>Возвращает меньшее из двух 64-битовых целых чисел со знаком.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 64-разрядных целых чисел со знаком. </param>
      <param name="val2">Второе из двух сравниваемых 64-разрядных целых чисел со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.SByte,System.SByte)">
      <summary>Возвращает меньшее из двух 8-битовых целых чисел со знаком.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 8-разрядных целых чисел со знаком. </param>
      <param name="val2">Второе из двух сравниваемых 8-разрядных целых чисел со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Single,System.Single)">
      <summary>Возвращает меньшее из двух чисел одинарной точности с плавающей запятой.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.Если <paramref name="val1" />, <paramref name="val2" /> или оба параметра <paramref name="val1" /> и <paramref name="val2" /> равны <see cref="F:System.Single.NaN" />, возвращается значение <see cref="F:System.Single.NaN" />.</returns>
      <param name="val1">Первое из двух сравниваемых чисел одинарной точности с плавающей запятой. </param>
      <param name="val2">Второе из двух сравниваемых чисел одинарной точности с плавающей запятой. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt16,System.UInt16)">
      <summary>Возвращает меньшее из двух 16-битовых целых чисел без знака.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 16-разрядных целых чисел без знака. </param>
      <param name="val2">Второе из двух сравниваемых 16-разрядных целых чисел без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt32,System.UInt32)">
      <summary>Возвращает меньшее из двух 32-битовых целых чисел без знака.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 32-разрядных целых чисел без знака. </param>
      <param name="val2">Второе из двух сравниваемых 32-разрядных целых чисел без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt64,System.UInt64)">
      <summary>Возвращает меньшее из двух 64-битовых целых чисел без знака.</summary>
      <returns>Меньший из двух параметров, <paramref name="val1" /> или <paramref name="val2" />.</returns>
      <param name="val1">Первое из двух сравниваемых 64-разрядных целых чисел без знака. </param>
      <param name="val2">Второе из двух сравниваемых 64-разрядных целых чисел без знака. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.PI">
      <summary>Представляет отношение длины окружности к ее диаметру, определяемое константой π.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Pow(System.Double,System.Double)">
      <summary>Возвращает указанное число, возведенное в указанную степень.</summary>
      <returns>Число <paramref name="x" />, возведенное в степень <paramref name="y" />.</returns>
      <param name="x">Число двойной точности с плавающей запятой, возводимое в степень. </param>
      <param name="y">Число двойной точности с плавающей запятой, задающее степень. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal)">
      <summary>Округляет десятичное значение до ближайшего целого.</summary>
      <returns>Целое число, ближайшее к значению параметра <paramref name="d" />.Если дробная часть <paramref name="d" /> находится на равном расстоянии от двух целых чисел (четного и нечетного), возвращается четное число.Обратите внимание, что данный метод возвращает не целочисленное значение, а значение типа <see cref="T:System.Decimal" />.</returns>
      <param name="d">Округляемое десятичное число. </param>
      <exception cref="T:System.OverflowException">Полученное значение находится вне допустимого диапазона типа <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32)">
      <summary>Округляет десятичное значение до указанного числа дробных разрядов.</summary>
      <returns>Число, ближайшее к параметру <paramref name="d" />, количество цифр дробной части которого равно <paramref name="decimals" />. </returns>
      <param name="d">Округляемое десятичное число. </param>
      <param name="decimals">Количество десятичных разрядов в возвращаемом значении. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> значение меньше 0 или больше 28. </exception>
      <exception cref="T:System.OverflowException">Полученное значение находится вне допустимого диапазона типа <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32,System.MidpointRounding)">
      <summary>Округляет десятичное значение до указанного числа дробных разрядов.Параметр задает правило округления значения, если оно находится ровно посредине между двумя числами.</summary>
      <returns>Число, ближайшее к параметру <paramref name="d" />, содержащему количество дробных разрядов, которое равно <paramref name="decimals" />.Если <paramref name="d" /> имеет меньшее количество цифр дробной части, чем <paramref name="decimals" />, то <paramref name="d" /> возвращается без изменений.</returns>
      <param name="d">Округляемое десятичное число. </param>
      <param name="decimals">Количество десятичных разрядов в возвращаемом значении. </param>
      <param name="mode">Значение, задающее правило округления параметра <paramref name="d" />, если его значение находится ровно посредине между двумя другими числами.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> значение меньше 0 или больше 28. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="mode" /> не является допустимым значением объекта <see cref="T:System.MidpointRounding" />.</exception>
      <exception cref="T:System.OverflowException">Полученное значение находится вне допустимого диапазона типа <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.MidpointRounding)">
      <summary>Округляет десятичное значение до ближайшего целого.Параметр задает правило округления значения, если оно находится ровно посредине между двумя числами.</summary>
      <returns>Целое число, ближайшее к значению параметра <paramref name="d" />.Если <paramref name="d" /> находится на равном расстоянии от двух чисел (четного и нечетного), то возвращаемое число определяется по значению параметра <paramref name="mode" />.</returns>
      <param name="d">Округляемое десятичное число. </param>
      <param name="mode">Значение, задающее правило округления параметра <paramref name="d" />, если его значение находится ровно посредине между двумя другими числами.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="mode" /> не является допустимым значением объекта <see cref="T:System.MidpointRounding" />.</exception>
      <exception cref="T:System.OverflowException">Полученное значение находится вне допустимого диапазона типа <see cref="T:System.Decimal" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double)">
      <summary>Округляет заданное число с плавающей запятой двойной точности до ближайшего целого.</summary>
      <returns>Целое число, ближайшее к значению параметра <paramref name="a" />.Если дробная часть <paramref name="a" /> находится на равном расстоянии от двух целых чисел (четного и нечетного), возвращается четное число.Обратите внимание, что данный метод возвращает не целочисленное значение, а значение типа <see cref="T:System.Double" />.</returns>
      <param name="a">Округляемое число двойной точности с плавающей запятой. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32)">
      <summary>Округляет значение двойной точности с плавающей запятой до заданного количества дробных разрядов.</summary>
      <returns>Число, ближайшее к параметру <paramref name="value" />, количество цифр дробной части которого равно <paramref name="digits" />.</returns>
      <param name="value">Округляемое число двойной точности с плавающей запятой. </param>
      <param name="digits">Количество цифр дробной части в возвращаемом значении. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> значение меньше 0 или больше 15. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32,System.MidpointRounding)">
      <summary>Округляет значение двойной точности с плавающей запятой до заданного количества дробных разрядов.Параметр задает правило округления значения, если оно находится ровно посредине между двумя числами.</summary>
      <returns>Число, ближайшее к параметру <paramref name="value" />, количество цифр дробной части которого равно <paramref name="digits" />.Если <paramref name="value" /> имеет меньшее количество цифр дробной части, чем <paramref name="digits" />, то <paramref name="value" /> возвращается без изменений.</returns>
      <param name="value">Округляемое число двойной точности с плавающей запятой. </param>
      <param name="digits">Количество цифр дробной части в возвращаемом значении. </param>
      <param name="mode">Значение, задающее правило округления параметра <paramref name="value" />, если его значение находится ровно посредине между двумя другими числами.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> значение меньше 0 или больше 15. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="mode" /> не является допустимым значением объекта <see cref="T:System.MidpointRounding" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.MidpointRounding)">
      <summary>Округляет заданное значение число двойной точности с плавающей запятой до ближайшего целого.Параметр задает правило округления значения, если оно находится ровно посредине между двумя числами.</summary>
      <returns>Целое число, ближайшее к значению параметра <paramref name="value" />.Если <paramref name="value" /> находится на равном расстоянии от двух целых чисел (четного и нечетного), то возвращаемое число определяется по значению параметра <paramref name="mode" />.</returns>
      <param name="value">Округляемое число двойной точности с плавающей запятой. </param>
      <param name="mode">Значение, задающее правило округления параметра <paramref name="value" />, если его значение находится ровно посредине между двумя другими числами.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="mode" /> не является допустимым значением объекта <see cref="T:System.MidpointRounding" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Decimal)">
      <summary>Возвращает значение, определяющее знак десятичного числа.</summary>
      <returns>Число, которое указывает знак значения <paramref name="value" />, как показано в следующей таблице.Возвращаемое значение Значение -1 Значение параметра <paramref name="value" /> меньше нуля. 0 Значение параметра <paramref name="value" /> равно нулю. 1 Значение параметра <paramref name="value" /> больше нуля. </returns>
      <param name="value">Десятичное число со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Double)">
      <summary>Возвращает значение, определяющее знак числа двойной точности с плавающей запятой.</summary>
      <returns>Число, которое указывает знак значения <paramref name="value" />, как показано в следующей таблице.Возвращаемое значение Значение -1 Значение параметра <paramref name="value" /> меньше нуля. 0 Значение параметра <paramref name="value" /> равно нулю. 1 Значение параметра <paramref name="value" /> больше нуля. </returns>
      <param name="value">Число со знаком. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> равно <see cref="F:System.Double.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int16)">
      <summary>Возвращает значение, определяющее знак 16-битового целого числа со знаком.</summary>
      <returns>Число, которое указывает знак значения <paramref name="value" />, как показано в следующей таблице.Возвращаемое значение Значение -1 Значение параметра <paramref name="value" /> меньше нуля. 0 Значение параметра <paramref name="value" /> равно нулю. 1 Значение параметра <paramref name="value" /> больше нуля. </returns>
      <param name="value">Число со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int32)">
      <summary>Возвращает значение, определяющее знак 32-битового целого числа со знаком.</summary>
      <returns>Число, которое указывает знак значения <paramref name="value" />, как показано в следующей таблице.Возвращаемое значение Значение -1 Значение параметра <paramref name="value" /> меньше нуля. 0 Значение параметра <paramref name="value" /> равно нулю. 1 Значение параметра <paramref name="value" /> больше нуля. </returns>
      <param name="value">Число со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int64)">
      <summary>Возвращает значение, определяющее знак 64-битового целого числа со знаком.</summary>
      <returns>Число, которое указывает знак значения <paramref name="value" />, как показано в следующей таблице.Возвращаемое значение Значение -1 Значение параметра <paramref name="value" /> меньше нуля. 0 Значение параметра <paramref name="value" /> равно нулю. 1 Значение параметра <paramref name="value" /> больше нуля. </returns>
      <param name="value">Число со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.SByte)">
      <summary>Возвращает значение, определяющее знак 8-битового целого числа со знаком.</summary>
      <returns>Число, которое указывает знак значения <paramref name="value" />, как показано в следующей таблице.Возвращаемое значение Значение -1 Значение параметра <paramref name="value" /> меньше нуля. 0 Значение параметра <paramref name="value" /> равно нулю. 1 Значение параметра <paramref name="value" /> больше нуля. </returns>
      <param name="value">Число со знаком. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Single)">
      <summary>Возвращает значение, определяющее знак числа одинарной точности с плавающей запятой.</summary>
      <returns>Число, которое указывает знак значения <paramref name="value" />, как показано в следующей таблице.Возвращаемое значение Значение -1 Значение параметра <paramref name="value" /> меньше нуля. 0 Значение параметра <paramref name="value" /> равно нулю. 1 Значение параметра <paramref name="value" /> больше нуля. </returns>
      <param name="value">Число со знаком. </param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> равно <see cref="F:System.Single.NaN" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sin(System.Double)">
      <summary>Возвращает синус указанного угла.</summary>
      <returns>Синус <paramref name="a" />.Если значение параметра <paramref name="a" /> равно <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> или <see cref="F:System.Double.PositiveInfinity" />, то данный метод возвращает <see cref="F:System.Double.NaN" />.</returns>
      <param name="a">Угол, измеряемый в радианах. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sinh(System.Double)">
      <summary>Возвращает гиперболический синус указанного угла.</summary>
      <returns>Гиперболический синус <paramref name="value" />.Если значение параметра <paramref name="value" /> равно <see cref="F:System.Double.NegativeInfinity" />, <see cref="F:System.Double.PositiveInfinity" /> или <see cref="F:System.Double.NaN" />, то этот метод возвращает значение <see cref="T:System.Double" />, равное <paramref name="value" />.</returns>
      <param name="value">Угол, измеряемый в радианах. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sqrt(System.Double)">
      <summary>Возвращает квадратный корень из указанного числа.</summary>
      <returns>Одно из значений, перечисленных в следующей таблице. Параметр <paramref name="d" />Возвращаемое значение Нуль или положительное число Положительный квадратный корень из <paramref name="d" />. Отрицательное число <see cref="F:System.Double.NaN" />Равняется <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />Равняется <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Число, квадратный корень которого требуется найти. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tan(System.Double)">
      <summary>Возвращает тангенс указанного угла.</summary>
      <returns>Тангенс <paramref name="a" />.Если значение параметра <paramref name="a" /> равно <see cref="F:System.Double.NaN" />, <see cref="F:System.Double.NegativeInfinity" /> или <see cref="F:System.Double.PositiveInfinity" />, то данный метод возвращает <see cref="F:System.Double.NaN" />.</returns>
      <param name="a">Угол, измеряемый в радианах. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tanh(System.Double)">
      <summary>Возвращает гиперболический тангенс указанного угла.</summary>
      <returns>Гиперболический тангенс <paramref name="value" />.Если значение параметра <paramref name="value" /> равно <see cref="F:System.Double.NegativeInfinity" />, этот метод возвращает -1.Если значение равно <see cref="F:System.Double.PositiveInfinity" />, этот метод возвращает 1.Если значение параметра <paramref name="value" /> равно <see cref="F:System.Double.NaN" />, этот метод возвращает <see cref="F:System.Double.NaN" />.</returns>
      <param name="value">Угол, измеряемый в радианах. </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Decimal)">
      <summary>Вычисляет целую часть заданного десятичного числа. </summary>
      <returns>Целая часть <paramref name="d" />, то есть число, остающееся после отбрасывания дробной части.</returns>
      <param name="d">Усекаемое число.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Double)">
      <summary>Вычисляет целую часть заданного числа двойной точности с плавающей запятой. </summary>
      <returns>Целая часть <paramref name="d" />, то есть число, которое остается после отбрасывания всех цифр дробной части, или одно из значений, перечисленных в следующей таблице. <paramref name="d" />Возвращаемое значение<see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">Усекаемое число.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.MidpointRounding">
      <summary>Задает способ обработки чисел, которые равноудалены от двух соседних чисел, в математических методах округления.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.MidpointRounding.AwayFromZero">
      <summary>Когда число находится посредине между двумя другими числами, оно округляется до ближайшего числа дальше от нуля.</summary>
    </member>
    <member name="F:System.MidpointRounding.ToEven">
      <summary>Когда число находится посредине между двумя другими числами, оно округляется до ближайшего четного числа.</summary>
    </member>
    <member name="T:System.Progress`1">
      <summary>Предоставляет <see cref="T:System.IProgress`1" />, вызывающий обратные вызовы для каждого заявленного значения хода выполнения.</summary>
      <typeparam name="T">Указывает тип значения отчета о ходе выполнения.</typeparam>
    </member>
    <member name="M:System.Progress`1.#ctor">
      <summary>Выполняет инициализацию объекта <see cref="T:System.Progress`1" />.</summary>
    </member>
    <member name="M:System.Progress`1.#ctor(System.Action{`0})">
      <summary>Инициализирует объект <see cref="T:System.Progress`1" /> с указанным обратным вызовом.</summary>
      <param name="handler">Обработчик, который необходимо вызывать для каждого сообщаемого значения хода выполнения.Этот обработчик вызывается в дополнение ко всем делегатам, зарегистрированным с событием <see cref="E:System.Progress`1.ProgressChanged" />.В зависимости от экземпляра <see cref="T:System.Threading.SynchronizationContext" />, перехваченного <see cref="T:System.Progress`1" /> при конструировании, возможно, что этот экземпляр обработчика может быть вызван синхронно с самим собой.</param>
    </member>
    <member name="M:System.Progress`1.OnReport(`0)">
      <summary>Сообщает об изменении хода выполнения.</summary>
      <param name="value">Значение обновленного хода выполнения.</param>
    </member>
    <member name="E:System.Progress`1.ProgressChanged">
      <summary>Вызывается для каждого зафиксированного значения хода выполнения.</summary>
    </member>
    <member name="M:System.Progress`1.System#IProgress{T}#Report(`0)">
      <summary>Сообщает об изменении хода выполнения.</summary>
      <param name="value">Значение обновленного хода выполнения.</param>
    </member>
    <member name="T:System.Random">
      <summary>Представляет генератор псевдослучайных чисел, то есть устройство, которое выдает последовательность чисел, отвечающую определенным статистическим критериям случайности.Просмотреть исходный код .NET Framework для этого типа можно на портале Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Random" /> с помощью зависимого от времени начального значения по умолчанию.</summary>
    </member>
    <member name="M:System.Random.#ctor(System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Random" /> с помощью указанного начального значения.</summary>
      <param name="Seed">Число, используемое для вычисления начального значения последовательности псевдослучайных чисел.Если задано отрицательное число, используется его абсолютное значение.</param>
    </member>
    <member name="M:System.Random.Next">
      <summary>Возвращает неотрицательное случайное целое число.</summary>
      <returns>32-разрядное целое число со знаком, которое больше или равно нулю и меньше, чем <see cref="F:System.Int32.MaxValue" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32)">
      <summary>Возвращает неотрицательное случайное целое число, которое меньше максимально допустимого значения.</summary>
      <returns>32-разрядное целое число со знаком, большее или равное 0 и меньшее, чем <paramref name="maxValue" />. То есть диапазон возвращаемых значений включает в себя 0, но не включает <paramref name="maxValue" />.Однако если значение параметра <paramref name="maxValue" /> равно нулю, возвращается значение <paramref name="maxValue" />.</returns>
      <param name="maxValue">Исключенный верхний предел создаваемого случайного числа.Значение <paramref name="maxValue" /> должно быть больше или равно 0.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxValue" /> is less than 0. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32,System.Int32)">
      <summary>Возвращает случайное целое число в указанном диапазоне.</summary>
      <returns>32-разрядное целое число со знаком, большее или равное <paramref name="minValue" /> и меньшее, чем <paramref name="maxValue" />. То есть диапазон возвращаемых значений включает в себя <paramref name="minValue" />, но не включает <paramref name="maxValue" />.Если значение параметра <paramref name="minValue" /> равно <paramref name="maxValue" />, возвращается значение <paramref name="minValue" />.</returns>
      <param name="minValue">Нижний предел возвращаемого случайного числа (включительно). </param>
      <param name="maxValue">Исключенный верхний предел возвращаемого случайного числа.Значение <paramref name="maxValue" /> должно быть больше или равно <paramref name="minValue" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minValue" /> is greater than <paramref name="maxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextBytes(System.Byte[])">
      <summary>Заполняет элементы указанного массива байтов случайными числами.</summary>
      <param name="buffer">Массив байтов, содержащий случайные числа. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextDouble">
      <summary>Возвращает случайное число с плавающей запятой, которое больше или равно 0,0 и меньше 1,0.</summary>
      <returns>Число двойной точности с плавающей запятой, которое больше или равно 0,0, и меньше 1,0.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Sample">
      <summary>Возвращает случайное число с плавающей запятой в диапазоне от 0,0 до 1,0.</summary>
      <returns>Число двойной точности с плавающей запятой, которое больше или равно 0,0, и меньше 1,0.</returns>
    </member>
    <member name="T:System.StringComparer">
      <summary>Представляет операции сравнения строк, в которых используются правила сравнения с учетом регистра, языка и региональных параметров или правил сравнения по порядковому номеру.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.StringComparer.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.StringComparer" />. </summary>
    </member>
    <member name="M:System.StringComparer.Compare(System.String,System.String)">
      <summary>При переопределении в производном классе сравнивает две строки и возвращает сведения об их относительном порядке сортировки.</summary>
      <returns>Знаковое целое число, которое определяет относительные значения параметров <paramref name="x" /> и <paramref name="y" />, как показано в следующей таблице.ЗначениеЗначениеМеньше нуля<paramref name="x" /> предшествует <paramref name="y" /> в порядке сортировки.-или-<paramref name="x" /> имеет значение null, а <paramref name="y" /> не имеет значения null.Нуль<paramref name="x" /> равно <paramref name="y" />.-или-Оба параметра <paramref name="x" /> и <paramref name="y" /> имеют значение null. Больше нуля<paramref name="x" /> следует за <paramref name="y" /> в порядке сортировки.-или-<paramref name="y" /> имеет значение null, а <paramref name="x" /> не имеет значения null. </returns>
      <param name="x">Строка, сравниваемая с параметром <paramref name="y" />.</param>
      <param name="y">Строка, сравниваемая с параметром <paramref name="x" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.CurrentCulture">
      <summary>Получает объект <see cref="T:System.StringComparer" />, выполняющий сравнение строк с учетом регистра, используя правила сравнения строк по словам для текущего языка и региональных параметров.</summary>
      <returns>Новый объект <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.StringComparer.CurrentCultureIgnoreCase">
      <summary>Получает объект <see cref="T:System.StringComparer" />, выполняющий сравнения строк без учета регистра, используя правила сравнения строк по словам для текущего языка и региональных параметров.</summary>
      <returns>Новый объект <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.StringComparer.Equals(System.String,System.String)">
      <summary>При переопределении в производном классе позволяет определить, равны ли две строки.</summary>
      <returns>Значение true, если параметры <paramref name="x" /> и <paramref name="y" /> указывают не один и тот же объект, если параметры <paramref name="x" /> и <paramref name="y" /> равны или если параметры <paramref name="x" /> и <paramref name="y" /> равны null; в противном случае — значение false.</returns>
      <param name="x">Строка, сравниваемая с параметром <paramref name="y" />.</param>
      <param name="y">Строка, сравниваемая с параметром <paramref name="x" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.GetHashCode(System.String)">
      <summary>При переопределении в производном классе возвращает хэш-код указанной строки.</summary>
      <returns>32-разрядный хэш-код, вычисленный на основе значения параметра <paramref name="obj" />.</returns>
      <param name="obj">Строка.</param>
      <exception cref="T:System.ArgumentException">Недостаточно памяти для выделения буфера, необходимого для вычисления хэш-кода.</exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="obj" /> имеет значение null. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.StringComparer.Ordinal">
      <summary>Получает объект <see cref="T:System.StringComparer" />, выполняющий сравнение строк по порядковому номеру с учетом регистра.</summary>
      <returns>Объект <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.OrdinalIgnoreCase">
      <summary>Получает объект <see cref="T:System.StringComparer" />, выполняющий сравнение строк по порядковому номеру без учета регистра.</summary>
      <returns>Объект <see cref="T:System.StringComparer" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Сравнивает два объекта и возвращает значение, которое указывает, равны ли эти объекты или один из них больше другого.</summary>
      <returns>Знаковое целое число, которое определяет относительные значения параметров <paramref name="x" /> и <paramref name="y" />, как показано в следующей таблице.ЗначениеЗначениеМеньше нуляЗначение <paramref name="x" /> меньше <paramref name="y" />.Нуль<paramref name="x" /> равняется <paramref name="y" />.Больше нуляЗначение <paramref name="x" /> больше значения <paramref name="y" />..</returns>
      <param name="x">Первый из сравниваемых объектов.</param>
      <param name="y">Второй из сравниваемых объектов.</param>
      <exception cref="T:System.ArgumentException">Ни параметр <paramref name="x" />, ни параметр <paramref name="y" /> не реализует интерфейс <see cref="T:System.IComparable" />.-или-Параметры <paramref name="x" /> и <paramref name="y" /> имеют разные типы и не могут сравниваться друг с другом.</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Определяет, равны ли два указанных объекта.</summary>
      <returns>true, если указанные объекты равны; в противном случае — false. </returns>
      <param name="x">Первый из сравниваемых объектов.</param>
      <param name="y">Второй из сравниваемых объектов.</param>
      <exception cref="T:System.ArgumentException">Параметры <paramref name="x" /> и <paramref name="y" /> относятся к разным типам и не могут сравниваться друг с другом. </exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Возвращает хэш-код указанного объекта.</summary>
      <returns>Хэш-код указанного объекта. </returns>
      <param name="obj">Объект, для которого необходимо возвратить хэш-код. </param>
      <exception cref="T:System.ArgumentNullException">Тип параметра <paramref name="obj" /> является ссылочным типом и значение параметра <paramref name="obj" /> — null. </exception>
    </member>
    <member name="T:System.UriBuilder">
      <summary>Предоставляет особый конструктор для универсальных идентификаторов ресурсов (URI), а также изменяет URI для класса <see cref="T:System.Uri" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.UriBuilder" />.</summary>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.UriBuilder" />, используя указанный URI.</summary>
      <param name="uri">Строка URI. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="uri" /> имеет значение null. </exception>
      <exception cref="T:System.UriFormatException">В .NET для приложений Магазина Windows или переносимой библиотеке классов вместо этого перехватите исключение базового класса <see cref="T:System.FormatException" />.В параметре <paramref name="uri" /> содержится строка нулевой длины или строка, состоящая только из пробелов.– или – Подпрограмма синтаксического анализа обнаружила схему в недопустимой форме.– или – Средство синтаксического анализа обнаружило более двух последовательно расположенных косых черт, для которых не используются схема File.– или – Недопустимый URI-адрес: <paramref name="uri" />. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.UriBuilder" /> в соответствии с заданной схемой и узлом.</summary>
      <param name="schemeName">Протокол доступа к Интернету. </param>
      <param name="hostName">DNS-имя домена или IP-адрес. </param>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.UriBuilder" /> в соответствии с заданной схемой, узлом и портом.</summary>
      <param name="scheme">Протокол доступа к Интернету. </param>
      <param name="host">DNS-имя домена или IP-адрес. </param>
      <param name="portNumber">Номер порта IP, используемый службой. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="portNumber" /> меньше -1 или больше 65535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.UriBuilder" /> в соответствии с заданной схемой, узлом, номером порта и путем.</summary>
      <param name="scheme">Протокол доступа к Интернету. </param>
      <param name="host">DNS-имя домена или IP-адрес. </param>
      <param name="port">Номер порта IP, используемый службой. </param>
      <param name="pathValue">Путь к Интернет- ресурсу. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="port" /> меньше -1 или больше 65535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.UriBuilder" /> в соответствии с заданной схемой, узлом, номером порта и строкой запроса или идентификатором фрагмента.</summary>
      <param name="scheme">Протокол доступа к Интернету. </param>
      <param name="host">DNS-имя домена или IP-адрес. </param>
      <param name="port">Номер порта IP, используемый службой. </param>
      <param name="path">Путь к Интернет- ресурсу. </param>
      <param name="extraValue">Строка запроса или идентификатор фрагмента. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="extraValue" /> не принимает ни значение null, ни значение <see cref="F:System.String.Empty" />, не является допустимым идентификатором фрагмента, начинающимся со знака решетки (#) и не является допустимой строкой запроса, начинающейся с вопросительного знака (?). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="port" /> меньше -1 или больше 65535. </exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.Uri)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.UriBuilder" /> в соответствии с указанным экземпляром класса <see cref="T:System.Uri" />.</summary>
      <param name="uri">Экземпляр класса <see cref="T:System.Uri" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="uri" /> имеет значение null. </exception>
    </member>
    <member name="M:System.UriBuilder.Equals(System.Object)">
      <summary>Сравнивает существующий экземпляр <see cref="T:System.Uri" /> с содержимым <see cref="T:System.UriBuilder" />, проверяя их равенство.</summary>
      <returns>Значение true, если параметр <paramref name="rparam" /> представляет тот же экземпляр класса <see cref="T:System.Uri" />, что и экземпляр <see cref="T:System.Uri" />, построенный с помощью данного экземпляра класса <see cref="T:System.UriBuilder" />; в противном случае — значение false.</returns>
      <param name="rparam">Объект для сравнения с текущим экземпляром. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Fragment">
      <summary>Возвращает или задает часть фрагмента URI.</summary>
      <returns>Часть фрагмента URI.Идентификатор фрагмента (#) добавляется в начале фрагмента.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.GetHashCode">
      <summary>Возвращает хэш-код для URI.</summary>
      <returns>Хэш-код, созданный для URI.</returns>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Host">
      <summary>Возвращает или задает DNS-имя узла или IP-адрес сервера.</summary>
      <returns>DNS-имя узла или IP-адрес сервера.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Password">
      <summary>Возвращает или задает пароль, связанный с пользователя, получающим доступ к URI.</summary>
      <returns>Пароль пользователя, получающего доступ к URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Path">
      <summary>Возвращает или задает путь к ресурсу, на который ссылается URI.</summary>
      <returns>Путь к ресурсу, на который ссылается URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Port">
      <summary>Возвращает или задает номер порта URI.</summary>
      <returns>Номер порта URI.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Номер порта не может быть меньше -1 или больше 65535. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Query">
      <summary>Возвращает или задает всевозможные сведения запроса, включенные в URI.</summary>
      <returns>Сведения запроса, включенные в URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Scheme">
      <summary>Возвращает или задает имя схемы URI.</summary>
      <returns>Схема URI.</returns>
      <exception cref="T:System.ArgumentException">В схеме невозможно задать недопустимое имя схемы. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.UriBuilder.ToString">
      <summary>Возвращает отображаемую строку для заданного экземпляра <see cref="T:System.UriBuilder" />.</summary>
      <returns>Строка, содержащая отображаемую строку <see cref="T:System.UriBuilder" />, не преобразованную в escape-последовательность.</returns>
      <exception cref="T:System.UriFormatException">В .NET для приложений Магазина Windows или переносимой библиотеке классов вместо этого перехватите исключение базового класса <see cref="T:System.FormatException" />.Экземпляр <see cref="T:System.UriBuilder" /> имеет ненадежный пароль. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Uri">
      <summary>Возвращает экземпляр <see cref="T:System.Uri" />, построенный при помощи заданного экземпляра <see cref="T:System.UriBuilder" />.</summary>
      <returns>Объект <see cref="T:System.Uri" />, содержащий URI, построенный с помощью объекта <see cref="T:System.UriBuilder" />.</returns>
      <exception cref="T:System.UriFormatException">В .NET для приложений Магазина Windows или переносимой библиотеке классов вместо этого перехватите исключение базового класса <see cref="T:System.FormatException" />.Универсальный код ресурса, построенный с помощью свойств класса <see cref="T:System.UriBuilder" />, является недопустимым. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.UserName">
      <summary>Имя, соответствующее пользователю, получающему доступ к URI.</summary>
      <returns>Имя пользователя, получающего доступ к URI.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Diagnostics.Stopwatch">
      <summary>Предоставляет набор методов и свойств, которые можно использовать для точного измерения затраченного времени.Просмотреть исходный код .NET Framework для этого типа можно на портале Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Diagnostics.Stopwatch" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.Elapsed">
      <summary>Получает общее затраченное время, измеренное текущим экземпляром.</summary>
      <returns>Доступный только для чтения экземпляр <see cref="T:System.TimeSpan" /> представляет общее затраченное время, измеренное текущим экземпляром.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedMilliseconds">
      <summary>Получает общее затраченное время в миллисекундах, измеренное текущим экземпляром.</summary>
      <returns>Доступное только для чтения длинное целое число, представляющее общее число миллисекунд, измеренное текущим экземпляром.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedTicks">
      <summary>Получает общее затраченное время в тактах таймера, измеренное текущим экземпляром.</summary>
      <returns>Доступное только для чтения длинное целое число, представляющее общее число тактов таймера, измеренное текущим экземпляром.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.Frequency">
      <summary>Получает частоту таймера в виде количества тактов в секунду.Это поле доступно только для чтения.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.GetTimestamp">
      <summary>Получает текущее число тактов временного механизма.</summary>
      <returns>Длинное целое число со знаком, представляющее значение счетчика тактов базового механизма таймера.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.IsHighResolution">
      <summary>Указывает, зависит ли таймер от счетчика производительности высокого разрешения.Это поле доступно только для чтения.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.IsRunning">
      <summary>Получает значение, указывающее, запущен ли таймер <see cref="T:System.Diagnostics.Stopwatch" />.</summary>
      <returns>Значение true, если экземпляр <see cref="T:System.Diagnostics.Stopwatch" /> в настоящее время выполняется и измеряет затраченное время интервала; в противном случае — значение false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Reset">
      <summary>Останавливает измерение интервала времени и обнуляет затраченное время.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Restart">
      <summary>Останавливает измерение интервала времени, обнуляет затраченное время и начинает измерение затраченного времени.</summary>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Start">
      <summary>Запускает или возобновляет измерение затраченного времени для интервала.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.StartNew">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Diagnostics.Stopwatch" />, задает свойство затраченного времени равным нулю и запускает измерение затраченного времени.</summary>
      <returns>Экземпляр <see cref="T:System.Diagnostics.Stopwatch" />, который только что начал измерять затраченное время.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Stop">
      <summary>Останавливает измерение затраченного времени для интервала.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.Path">
      <summary>Выполняет операции для экземпляров класса <see cref="T:System.String" />, содержащих сведения о пути к файлу или каталогу.Эти операции выполняются межплатформенным способом.Чтобы просмотреть исходный код .NET Framework для этого типа, см. Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.AltDirectorySeparatorChar">
      <summary>Предоставляет дополнительный символ, задаваемый платформой, для разделения уровней каталогов в строке пути, в которой отражена иерархическая организация файловой системы.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.ChangeExtension(System.String,System.String)">
      <summary>Изменяет расширение строки пути.</summary>
      <returns>Измененные сведения о пути.В настольных системах, работающих под управлением Windows, сведения о пути возвращаются без изменений, если значение параметра <paramref name="path" /> равно null или пустой строке ("").Если значение параметра <paramref name="extension" /> равно null, возвращаемая строка содержит указанный путь без расширения.Если <paramref name="path" /> не имеет расширения и значение параметра <paramref name="extension" /> не равно null, возвращаемая строка пути содержит <paramref name="extension" />, добавленное в конец <paramref name="path" />.</returns>
      <param name="path">Сведения о пути, которые нужно изменить.Путь не может содержать символы, определенные в <see cref="M:System.IO.Path.GetInvalidPathChars" />.</param>
      <param name="extension">Новое расширение (начинающееся с точки или без нее).Задает null для удаления существующего расширения из параметра <paramref name="path" />.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> содержит один или несколько недопустимых символов, определенных в <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String)">
      <summary>Объединяет две строки в путь.</summary>
      <returns>Объединенные пути.Если один из указанных путей является строкой нулевой длины, этот метод возвращает другой путь.Если в качестве значения параметра <paramref name="path2" /> задан абсолютный путь, этот метод возвращает <paramref name="path2" />.</returns>
      <param name="path1">Первый путь для объединения. </param>
      <param name="path2">Второй путь для объединения. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path1" /> или <paramref name="path2" /> содержит один или несколько недопустимых символов, определенных в <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="path1" /> или <paramref name="path2" /> имеет значение null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String,System.String)">
      <summary>Объединяет три строки в путь.</summary>
      <returns>Объединенные пути.</returns>
      <param name="path1">Первый путь для объединения. </param>
      <param name="path2">Второй путь для объединения. </param>
      <param name="path3">Третий путь для объединения.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path1" />, <paramref name="path2" /> или <paramref name="path3" /> содержит один или несколько недопустимых символов, определенных в <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="path1" />, <paramref name="path2" /> или <paramref name="path3" /> — null. </exception>
    </member>
    <member name="M:System.IO.Path.Combine(System.String[])">
      <summary>Объединяет массив строк в путь.</summary>
      <returns>Объединенные пути.</returns>
      <param name="paths">Массив частей пути.</param>
      <exception cref="T:System.ArgumentException">Одна из строк в массиве содержит один или несколько недопустимых символов, определенных в <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Одна из строк в массиве имеет значение null. </exception>
    </member>
    <member name="F:System.IO.Path.DirectorySeparatorChar">
      <summary>Предоставляет символ, задаваемый платформой, для разделения уровней папок в строке пути, в которой отражена иерархическая организация файловой системы.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetDirectoryName(System.String)">
      <summary>Возвращает для указанной строки пути сведения о каталоге.</summary>
      <returns>Сведения о каталоге для <paramref name="path" />, или значение null, если путь <paramref name="path" /> указывает на корневой каталог или равен NULL.Возвращает <see cref="F:System.String.Empty" />, если параметр <paramref name="path" /> не содержит сведения о каталоге.</returns>
      <param name="path">Путь к файлу или каталогу. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> содержит недопустимые символы, пустой или содержит только пробелы. </exception>
      <exception cref="T:System.IO.PathTooLongException">В .NET for Windows Store apps или переносимой библиотеки классов, перехватить исключение базового класса, <see cref="T:System.IO.IOException" />, вместо нее.Длина параметра <paramref name="path" /> больше максимальной длины, определенной системой.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetExtension(System.String)">
      <summary>Возвращает расширение указанной строки пути.</summary>
      <returns>Расширение указанного пути (включая точку ".") или значение null или <see cref="F:System.String.Empty" />.Если параметр <paramref name="path" /> имеет значение null, <see cref="M:System.IO.Path.GetExtension(System.String)" /> возвращает null.Если параметр <paramref name="path" /> не содержит сведений о расширении, <see cref="M:System.IO.Path.GetExtension(System.String)" /> возвращает <see cref="F:System.String.Empty" />.</returns>
      <param name="path">Строка пути, из которой нужно получить расширение. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> содержит один или несколько недопустимых символов, определенных в <see cref="M:System.IO.Path.GetInvalidPathChars" />.  </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileName(System.String)">
      <summary>Возвращает имя файла и расширение указанной строки пути.</summary>
      <returns>Символы, следующие за последним символом каталога в пути <paramref name="path" />.Если последним символом параметра <paramref name="path" /> является символ разделения тома или каталога, этот метод возвращает <see cref="F:System.String.Empty" />.Если значением параметра <paramref name="path" /> является null, метод возвращает null.</returns>
      <param name="path">Строка пути, из которой нужно получить имя файла и расширение. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> содержит один или несколько недопустимых символов, определенных в <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileNameWithoutExtension(System.String)">
      <summary>Возвращает имя файла указанной строки пути без расширения.</summary>
      <returns>Строка, возвращенная методом <see cref="M:System.IO.Path.GetFileName(System.String)" />, кроме последней точки (.) и всех следующих за ней символов.</returns>
      <param name="path">Путь к файлу. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> содержит один или несколько недопустимых символов, определенных в <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFullPath(System.String)">
      <summary>Возвращает для указанной строки пути абсолютный путь.</summary>
      <returns>Полное расположение <paramref name="path" />, например "C:\MyFile.txt".</returns>
      <param name="path">Файл или каталог, для которых нужно получить сведения об абсолютном пути. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано в <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или- Система не может извлечь абсолютный путь. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта нет необходимых разрешений. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> содержит двоеточие (:), которое не является частью идентификатора тома (например, "c:\"). </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetInvalidFileNameChars">
      <summary>Возвращает массив, содержащий символы, которые не разрешены в именах файлов.</summary>
      <returns>Массив, содержащий символы, которые не разрешены в именах файлов.</returns>
    </member>
    <member name="M:System.IO.Path.GetInvalidPathChars">
      <summary>Возвращает массив, содержащий символы, которые не разрешены в именах путей.</summary>
      <returns>Массив, содержащий символы, которые не разрешены в именах путей.</returns>
    </member>
    <member name="M:System.IO.Path.GetPathRoot(System.String)">
      <summary>Возвращает сведения о корневом каталоге для указанного пути.</summary>
      <returns>Корневой каталог для <paramref name="path" />, например "C:\", или null, если параметр <paramref name="path" /> имеет значение null, или пустая строка, если <paramref name="path" /> не содержит сведений о корневом каталоге.</returns>
      <param name="path">Путь, из которого нужно получить сведения о корневом каталоге. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> содержит один или несколько недопустимых символов, определенных в <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или- Компонент <see cref="F:System.String.Empty" /> был передан в <paramref name="path" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetRandomFileName">
      <summary>Возвращает произвольное имя каталога или файла.</summary>
      <returns>Произвольное имя каталога или файла.</returns>
    </member>
    <member name="M:System.IO.Path.GetTempFileName">
      <summary>Создает на диске временный пустой файл с уникальным именем и возвращает полный путь этого файла.</summary>
      <returns>Полный путь к временному файлу.</returns>
      <exception cref="T:System.IO.IOException">Возникает ошибка ввода-вывода, например, отсутствует уникальное имя временного файла.-или-Этот метод не смог создать временный файл.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetTempPath">
      <summary>Возвращает путь к временной папке текущего пользователя.</summary>
      <returns>Путь к временной папке, заканчивающийся обратной косой чертой.</returns>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта нет необходимых разрешений. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.HasExtension(System.String)">
      <summary>Определяет, включает ли путь расширение имени файла.</summary>
      <returns>Значение true, если знаки, следующие за последним разделителем каталога (\\ или /) или разделителем тома (:) в пути, включают точку (.), за которой следует один или несколько символов. В противном случае — значение false.</returns>
      <param name="path">Путь для поиска расширения. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> содержит один или несколько недопустимых символов, определенных в <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.IsPathRooted(System.String)">
      <summary>Возвращает значение, указывающее, содержит ли заданный путь корневую папку.</summary>
      <returns>Значение true, если параметр <paramref name="path" /> содержит корневую папку; в противном случае — значение false.</returns>
      <param name="path">Проверяемый путь. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> содержит один или несколько недопустимых символов, определенных в <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.PathSeparator">
      <summary>Разделитель, задаваемый платформой, который используется в переменных среды для разделения строк пути.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.VolumeSeparatorChar">
      <summary>Предоставляет разделитель томов, задаваемый платформой.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Net.WebUtility">
      <summary>Предоставляет методы кодирования и расшифровки URL-адресов во время обработки веб-запросов. </summary>
    </member>
    <member name="M:System.Net.WebUtility.HtmlDecode(System.String)">
      <summary>Преобразовывает строку в кодировке HTML для передачи по протоколу HTTP в расшифрованную строку.</summary>
      <returns>Расшифрованная строка.</returns>
      <param name="value">Строка для расшифровки.</param>
    </member>
    <member name="M:System.Net.WebUtility.HtmlEncode(System.String)">
      <summary>Преобразует строку в строку формата HTML.</summary>
      <returns>Кодируемая строка.</returns>
      <param name="value">Кодируемая строка.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecode(System.String)">
      <summary>Преобразовывает строку, зашифрованную для передачи по URL-адресу, в расшифрованную строку.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Расшифрованная строка.</returns>
      <param name="encodedValue">Декодируемая строка, закодированная как URL-адрес.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Преобразовывает кодированный массив байтов, кодированный для передачи в URL-адресе, в декодированный массив байтов.</summary>
      <returns>Возвращает <see cref="T:System.Byte" />.Расшифрованный массив <see cref="T:System.Byte" />.</returns>
      <param name="encodedValue">Декодируемый массив <see cref="T:System.Byte" />, закодированная как URL-адрес.</param>
      <param name="offset">Смещение измеряется в байтах, от начала массива <see cref="T:System.Byte" /> для декодирования.</param>
      <param name="count">Число, в байтах, для декодирования из массива <see cref="T:System.Byte" />.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncode(System.String)">
      <summary>Преобразует текстовую строку в строку, закодированную как URL-адрес.</summary>
      <returns>Возвращает <see cref="T:System.String" />.URL-кодируемая строка.</returns>
      <param name="value">Текст для преобразования в кодировку URL.</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Преобразовывает массив байтов в кодированный в URL массив байтов.</summary>
      <returns>Возвращает <see cref="T:System.Byte" />.Кодированный массив <see cref="T:System.Byte" />.</returns>
      <param name="value">Массив <see cref="T:System.Byte" /> для кодирования в URL.</param>
      <param name="offset">Смещение измеряется в байтах, от начала массива <see cref="T:System.Byte" /> для кодирования.</param>
      <param name="count">Число, в байтах, для кодирования из массива <see cref="T:System.Byte" />.</param>
    </member>
    <member name="T:System.Runtime.Versioning.FrameworkName">
      <summary>Представляет имя версии платформы .NET Framework.</summary>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.Versioning.FrameworkName" /> с помощью строки, содержащей информацию о версии платформы .NET Framework.</summary>
      <param name="frameworkName">Строка, содержащая информацию о версии платформы .NET Framework.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="frameworkName" /> имеет значение <see cref="F:System.String.Empty" />.-или-В параметре <paramref name="frameworkName" /> содержится менее двух или более трех компонентов.-или-<paramref name="frameworkName" /> не включает основной и дополнительный номера версии.-или-<paramref name="frameworkName " /> не включает допустимый номер версии.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="frameworkName" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.Versioning.FrameworkName" /> с помощью строки и объекта <see cref="T:System.Version" />, указывающих версию платформы .NET Framework.</summary>
      <param name="identifier">Строка, указывающая версию платформы .NET Framework. </param>
      <param name="version">Объект, содержащий информацию о версии платформы .NET Framework.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="identifier" /> имеет значение <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="identifier" /> имеет значение null.-или-Параметр <paramref name="version" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.Versioning.FrameworkName" /> с помощью строки, объекта <see cref="T:System.Version" />, указывающего версию платформы .NET Framework, и имени профиля.</summary>
      <param name="identifier">Строка, указывающая версию платформы .NET Framework.</param>
      <param name="version">Объект, содержащий информацию о версии платформы .NET Framework.</param>
      <param name="profile">Имя профиля.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="identifier" /> имеет значение <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="identifier" /> имеет значение null.-или-Параметр <paramref name="version" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Object)">
      <summary>Возвращает значение, указывающее, представляет ли данный экземпляр класса <see cref="T:System.Runtime.Versioning.FrameworkName" /> ту же версию платформы .NET Framework, что и указанный объект.</summary>
      <returns>Значение true, если все компоненты текущего объекта <see cref="T:System.Runtime.Versioning.FrameworkName" /> совпадают с соответствующими компонентами объекта <paramref name="obj" />; в противном случае — значение false.</returns>
      <param name="obj">Объект, сравниваемый с текущим экземпляром.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Runtime.Versioning.FrameworkName)">
      <summary>Возвращает значение, указывающее, представляет ли данный экземпляр класса <see cref="T:System.Runtime.Versioning.FrameworkName" /> ту же версию платформы .NET Framework, что и указанный экземпляр класса <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Значение true, если все компоненты текущего объекта <see cref="T:System.Runtime.Versioning.FrameworkName" /> совпадают с соответствующими компонентами объекта <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Объект, сравниваемый с текущим экземпляром.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.FullName">
      <summary>Получает полное имя данного объекта <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Полное имя данного объекта <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.GetHashCode">
      <summary>Возвращает хэш-код для объекта <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>32-разрядное целое число со знаком, представляющее хэш-код данного экземпляра.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Identifier">
      <summary>Получает идентификатор данного объекта <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Идентификатор данного объекта <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Equality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Возвращает значение, указывающее, представляют ли два объекта <see cref="T:System.Runtime.Versioning.FrameworkName" /> одну и ту же версию платформы .NET Framework.</summary>
      <returns>Значение true, если параметры <paramref name="left" /> и <paramref name="right" /> представляют одну и ту же версию платформы .NET Framework; в противном случае — значение false.</returns>
      <param name="left">Первый сравниваемый объект.</param>
      <param name="right">Второй сравниваемый объект.</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Inequality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>Возвращает значение, указывающее, представляют ли два объекта <see cref="T:System.Runtime.Versioning.FrameworkName" /> различные версии платформы .NET Framework.</summary>
      <returns>Значение true, если параметры <paramref name="left" /> и <paramref name="right" /> представляют различные версии платформы .NET Framework; в противном случае — значение false.</returns>
      <param name="left">Первый сравниваемый объект.</param>
      <param name="right">Второй сравниваемый объект.</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Profile">
      <summary>Получает имя профиля данного объекта <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Имя профиля данного объекта <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.ToString">
      <summary>Возвращает строковое представление данного объекта <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Строка, представляющая данный объект <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Version">
      <summary>Получает версию данного объекта <see cref="T:System.Runtime.Versioning.FrameworkName" />.</summary>
      <returns>Объект, содержащий информацию о версии данного объекта <see cref="T:System.Runtime.Versioning.FrameworkName" />.</returns>
    </member>
  </members>
</doc>