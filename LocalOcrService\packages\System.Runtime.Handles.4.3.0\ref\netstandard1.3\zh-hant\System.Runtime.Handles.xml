﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Handles</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeWaitHandle">
      <summary>代表等候控制代碼的包裝函式類別。</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeWaitHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>初始化 <see cref="T:Microsoft.Win32.SafeHandles.SafeWaitHandle" /> 類別的新執行個體。</summary>
      <param name="existingHandle">
        <see cref="T:System.IntPtr" /> 物件，表示要使用的既有控制代碼。</param>
      <param name="ownsHandle">true 表示在結束階段確實地釋放控制代碼，而 false 表示不要確實地釋放 (不建議)。</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeWaitHandle.IsInvalid"></member>
    <member name="T:System.IO.HandleInheritability">
      <summary>指定子處理序是否能繼承基礎控制代碼。</summary>
    </member>
    <member name="F:System.IO.HandleInheritability.Inheritable">
      <summary>指定子處理序可繼承控制代碼。</summary>
    </member>
    <member name="F:System.IO.HandleInheritability.None">
      <summary>指定子處理序不可繼承控制代碼。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CriticalHandle">
      <summary>表示控制代碼資源的包裝函式類別 (Wrapper Class)。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CriticalHandle.#ctor(System.IntPtr)">
      <summary>使用指定的無效控制代碼值，初始化 <see cref="T:System.Runtime.InteropServices.CriticalHandle" /> 類別的新執行個體。</summary>
      <param name="invalidHandleValue">無效控制代碼的值 (通常是 0 或 -1)。</param>
      <exception cref="T:System.TypeLoadException">衍生類別位於組件中，但是沒有 Unmanaged 程式碼存取權限。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.CriticalHandle.Dispose">
      <summary>釋放 <see cref="T:System.Runtime.InteropServices.CriticalHandle" /> 所使用的所有資源。</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.CriticalHandle.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Runtime.InteropServices.CriticalHandle" /> 類別所使用的 Unmanaged 資源，指定是否要執行一般處置 (Dispose) 作業。</summary>
      <param name="disposing">true 表示一般處置作業，而 false 則表示完成控制代碼。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CriticalHandle.Finalize">
      <summary>釋放與控制代碼相關的所有資源。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CriticalHandle.handle">
      <summary>指定要包裝的控制代碼。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.CriticalHandle.IsClosed">
      <summary>取得值，指出控制代碼是否已關閉。</summary>
      <returns>如果控制代碼已關閉，則為 true，否則為 false。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.Runtime.InteropServices.CriticalHandle.IsInvalid">
      <summary>在衍生類別中覆寫時，取得值以指出這個控制代碼值是否無效。</summary>
      <returns>如果控制代碼有效則為 true，否則為 false。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.CriticalHandle.ReleaseHandle">
      <summary>在衍生類別中覆寫時，執行釋放控制代碼所需的程式碼。</summary>
      <returns>如果成功釋放控制代碼，則為 true，但如果發生嚴重失敗的事件，則為 false。在這種情況下，它會產生 releaseHandleFailed MDA Managed 偵錯助理。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.CriticalHandle.SetHandle(System.IntPtr)">
      <summary>將控制代碼設定為指定的既有控制代碼。</summary>
      <param name="handle">要使用的既有控制代碼。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CriticalHandle.SetHandleAsInvalid">
      <summary>將控制代碼標記為無效。</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeHandle">
      <summary>表示作業系統控制代碼的包裝函式類別 (Wrapper Class)。這個類別必須被繼承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>使用指定的無效控制代碼值，初始化 <see cref="T:System.Runtime.InteropServices.SafeHandle" /> 類別的新執行個體。</summary>
      <param name="invalidHandleValue">無效控制代碼的值 (通常是 0 或 -1)。您的 <see cref="P:System.Runtime.InteropServices.SafeHandle.IsInvalid" /> 實作應該會針對這個值，傳回 true。</param>
      <param name="ownsHandle">true 表示確實讓 <see cref="T:System.Runtime.InteropServices.SafeHandle" /> 在結束階段釋放控制代碼，否則為 false (不建議)。</param>
      <exception cref="T:System.TypeLoadException">衍生類別位於組件中，但是沒有 Unmanaged 程式碼存取權限。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeHandle.DangerousAddRef(System.Boolean@)">
      <summary>手動遞增 <see cref="T:System.Runtime.InteropServices.SafeHandle" /> 執行個體上的參考計數器。</summary>
      <param name="success">如果成功遞增參考計數器，則為 true，否則為 false。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeHandle.DangerousGetHandle">
      <summary>傳回 <see cref="F:System.Runtime.InteropServices.SafeHandle.handle" /> 欄位的值。</summary>
      <returns>IntPtr，代表 <see cref="F:System.Runtime.InteropServices.SafeHandle.handle" /> 欄位的值。如果這個控制代碼已經用 <see cref="M:System.Runtime.InteropServices.SafeHandle.SetHandleAsInvalid" /> 標記為無效，則這個方法仍會傳回原始的控制代碼值，不過這個值可能為過時的值。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeHandle.DangerousRelease">
      <summary>手動遞減 <see cref="T:System.Runtime.InteropServices.SafeHandle" /> 執行個體上的參考計數器。</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeHandle.Dispose">
      <summary>釋放 <see cref="T:System.Runtime.InteropServices.SafeHandle" /> 類別所使用的所有資源。</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeHandle.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Runtime.InteropServices.SafeHandle" /> 類別所使用的 Unmanaged 資源，指定是否要執行一般處置作業。</summary>
      <param name="disposing">true 表示一般處置作業，false 則表示結束控制代碼。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeHandle.Finalize">
      <summary>釋放與控制代碼相關的所有資源。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.SafeHandle.handle">
      <summary>指定要包裝的控制代碼。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeHandle.IsClosed">
      <summary>取得值，指出控制代碼是否已關閉。</summary>
      <returns>如果控制代碼已關閉，則為 true，否則為 false。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeHandle.IsInvalid">
      <summary>在衍生類別中覆寫時，取得值以指出這個控制代碼值是否無效。</summary>
      <returns>如果控制代碼值無效，則為 true，否則為 false。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeHandle.ReleaseHandle">
      <summary>在衍生類別中覆寫時，執行釋放控制代碼所需的程式碼。</summary>
      <returns>如果成功釋放控制代碼，則為 true；如果發生嚴重失敗的事件，則為  false。在這種情況下，它會產生 releaseHandleFailed MDA Managed 偵錯助理。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeHandle.SetHandle(System.IntPtr)">
      <summary>將控制代碼設定為指定的既有控制代碼。</summary>
      <param name="handle">要使用的既有控制代碼。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeHandle.SetHandleAsInvalid">
      <summary>將控制代碼標記為不再使用。</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Threading.WaitHandleExtensions">
      <summary>提供方法來使用安全控制代碼等候處理。</summary>
    </member>
    <member name="M:System.Threading.WaitHandleExtensions.GetSafeWaitHandle(System.Threading.WaitHandle)">
      <summary>取得安全控制代碼的原生作業系統等候控制代碼。</summary>
      <returns>安全的等候控制代碼包裝原生作業系統等候控制代碼。</returns>
      <param name="waitHandle">原生作業系統控制代碼。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="waitHandle" /> 為 null。</exception>
    </member>
    <member name="M:System.Threading.WaitHandleExtensions.SetSafeWaitHandle(System.Threading.WaitHandle,Microsoft.Win32.SafeHandles.SafeWaitHandle)">
      <summary>設定原生作業系統等候控制代碼的安全控制代碼。</summary>
      <param name="waitHandle">等候控制代碼封裝等候共用資源的獨佔存取權的作業系統特定物件。</param>
      <param name="value">安全控制代碼，用來包裝作業系統控制代碼。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="waitHandle" /> 為 null。</exception>
    </member>
  </members>
</doc>