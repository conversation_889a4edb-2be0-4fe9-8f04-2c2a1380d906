﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices</name>
  </assembly>
  <members>
    <member name="T:System.DataMisalignedException">
      <summary>Исключение, которое выбрасывается, когда единица данных считывается или записывается по адресу, не кратному размеру данных.Этот класс не наследуется.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DataMisalignedException.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.DataMisalignedException" />. </summary>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.DataMisalignedException" />, используя указанное сообщение об ошибке.</summary>
      <param name="message">Объект <see cref="T:System.String" />, описывающий ошибку.Содержимое параметра <paramref name="message" /> должно быть понятным пользователю.Вызывающий оператор этого конструктора необходим, чтобы убедиться, локализована ли данная строка для текущего языка и региональных параметров системы.</param>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.DataMisalignedException" />, используя указанные сообщение об ошибке и исходное исключение.</summary>
      <param name="message">Объект <see cref="T:System.String" />, описывающий ошибку.Содержимое параметра <paramref name="message" /> должно быть понятным пользователю.Вызывающий оператор этого конструктора необходим, чтобы убедиться, локализована ли данная строка для текущего языка и региональных параметров системы.</param>
      <param name="innerException">Исключение, являющееся причиной текущего исключения <see cref="T:System.DataMisalignedException" />,.Если значение параметра <paramref name="innerException" /> не равно null, текущее исключение сгенерировано в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="T:System.DllNotFoundException">
      <summary>Исключение, которое выбрасывается в случае невозможности найти библиотеку DLL, указанную при импорте DLL.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DllNotFoundException.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.DllNotFoundException" /> значениями свойств по умолчанию.</summary>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.DllNotFoundException" /> с указанным сообщением об ошибке.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.DllNotFoundException" /> заданным сообщением об ошибке и ссылкой на внутреннее исключение, которое стало причиной данного исключения.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
      <param name="inner">Исключение, которое вызвало текущее исключение.Если значение параметра <paramref name="inner" /> не равно null, текущее исключение вызывается в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="T:System.Reflection.Missing">
      <summary>Представляет отсутствующий объект <see cref="T:System.Object" />.Этот класс не наследуется.</summary>
    </member>
    <member name="F:System.Reflection.Missing.Value">
      <summary>Представляет единственный экземпляр класса <see cref="T:System.Reflection.Missing" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ArrayWithOffset">
      <summary>Инкапсулирует массив и смещение в указанный массив.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.#ctor(System.Object,System.Int32)">
      <summary>Инициализирует новый экземпляр структуры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <param name="array">Управляемый массив. </param>
      <param name="offset">Смещение элемента в байтах, передаваемое с помощью вызова неуправляемого кода. </param>
      <exception cref="T:System.ArgumentException">Массив больше 2 гигабайт (ГБ).</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Object)">
      <summary>Показывает, соответствует ли указанный объект текущему объекту <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <returns>Значение true, если объект соответствует этому массиву <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />; в противном случае — значение false.</returns>
      <param name="obj">Объект, сравниваемый с этим экземпляром. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Показывает, соответствует ли указанный объект <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> текущему экземпляру.</summary>
      <returns>Значение true, если указанный объект <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> соответствует текущему экземпляру; в противном случае — значение false.</returns>
      <param name="obj">Объект <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />, сравниваемый с этим экземпляром.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetArray">
      <summary>Возвращает управляемый массив, на который ссылается этот массив <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <returns>Управляемый массив, на который ссылается этот экземпляр.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetHashCode">
      <summary>Возвращает хэш-код для этого типа значения.</summary>
      <returns>Хэш-код данного экземпляра.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetOffset">
      <summary>Возвращает смещение, предоставленное при создании этого массива <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <returns>Смещение для этого экземпляра.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Equality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Определяет, совпадают ли значения двух указанных объектов <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <returns>Значение true, если значение <paramref name="a" /> совпадает со значением <paramref name="b" />; в противном случае — значение false.</returns>
      <param name="a">Объект <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />, сравниваемый с параметром <paramref name="b" />. </param>
      <param name="b">Объект <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />, сравниваемый с параметром <paramref name="a" />.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Inequality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Определяет, равны ли значения двух указанных объектов <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <returns>Значение true, если значение <paramref name="a" /> не совпадает со значением <paramref name="b" />; в противном случае — значение false.</returns>
      <param name="a">Объект <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />, сравниваемый с параметром <paramref name="b" />. </param>
      <param name="b">Объект <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />, сравниваемый с параметром <paramref name="a" />.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.BestFitMappingAttribute">
      <summary>Проверяет, преобразованы ли знаки Юникода в наиболее подходящие знаки ANSI.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BestFitMappingAttribute.#ctor(System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.BestFitMappingAttribute" /> и присваивает ему значение свойства <see cref="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping" />.</summary>
      <param name="BestFitMapping">Значение true показывает, что режим наилучшего сопоставления включен; в противном случае используется значение false.Значение по умолчанию — true.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping">
      <summary>Возвращает текущие настройки наилучшего сопоставления при преобразовании знаков Юникода в знаки ANSI.</summary>
      <returns>Значение true, если наилучшее сопоставление включено; в противном случае — значение false.Значение по умолчанию — true.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.BestFitMappingAttribute.ThrowOnUnmappableChar">
      <summary>Включает и отключает возникновение исключений для неотображаемых символов Юникода, преобразующихся в знаки вопроса ("?") ANSI.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.BStrWrapper">
      <summary>Маршалирует данные типа VT_BSTR из управляемого кода в неуправляемый.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> с указанным объектом <see cref="T:System.Object" />.</summary>
      <param name="value">Объект, заключаемый в оболочку и маршалируемый как VT_BSTR.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> с указанным объектом <see cref="T:System.String" />.</summary>
      <param name="value">Объект, заключаемый в оболочку и маршалируемый как VT_BSTR.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BStrWrapper.WrappedObject">
      <summary>Возвращает инкапсулированный объект <see cref="T:System.String" /> для маршалинга в качестве типа VT_BSTR.</summary>
      <returns>Объект, заключенный в оболочку с помощью <see cref="T:System.Runtime.InteropServices.BStrWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CallingConvention">
      <summary>Определяет соглашение о вызове, используемое для вызова методов, реализованных в неуправляемом коде.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Cdecl">
      <summary>Вызывающий объект очищает стек.Это позволяет вызывать функции с varargs, чтобы использовать их для методов, работающих с переменным числом параметров, таких как Printf.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.StdCall">
      <summary>Вызываемый объект очищает стек.Это соглашение, используемое по умолчанию для вызова неуправляемых функций с вызовом неуправляемого кода.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.ThisCall">
      <summary>Первый параметр — это указатель this, хранящийся в регистре ECX.Другие параметры помещаются в стек.Это соглашение о вызове используется для вызова методов в классах, экспортируемых из неуправляемой динамической библиотеки DLL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Winapi">
      <summary>Этот член фактически не является соглашением о вызове, а вместо этого используется стандартное соглашение о вызове платформы.Например, в Windows по умолчанию используется <see cref="F:System.Runtime.InteropServices.CallingConvention.StdCall" />, а в Windows CE .NET — <see cref="F:System.Runtime.InteropServices.CallingConvention.Cdecl" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceAttribute">
      <summary>Показывает тип интерфейса класса, создаваемого для класса, представленного для COM, если интерфейс создается.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Int16)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> с заданным значением перечисления <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" />.</summary>
      <param name="classInterfaceType">Описывает тип интерфейса, созданного для класса. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Runtime.InteropServices.ClassInterfaceType)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> с заданным элементом перечисления <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" />.</summary>
      <param name="classInterfaceType">Одно из значений <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" />, описывающее тип интерфейса, созданного для класса. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ClassInterfaceAttribute.Value">
      <summary>Возвращает значение <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" />, описывающее тип интерфейса, который должен быть создать для класса.</summary>
      <returns>Значение <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" />, описывающее тип интерфейса, который требуется создать для класса.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceType">
      <summary>Определяет тип интерфейса класса, созданного для класса.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDispatch">
      <summary>Показывает, что класс поддерживает только позднюю привязку для клиентов COM.Интерфейс dispinterface класса автоматически предоставляется клиентам COM по запросу.Библиотека типов, созданная программой Tlbexp.exe (программа экспорта библиотек типов), не содержит информации о типе для интерфейса dispinterface, чтобы не допустить кэширование значений DISPID клиентами.Интерфейс dispinterface исключает возникновение проблем с версиями, рассмотренных в описании класса <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />, поскольку клиенты могут использовать только позднее связывание с интерфейсом.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDual">
      <summary>Показывает, что для класса автоматически создается сдвоенный интерфейс класса, который предоставляется COM.Сведения о типе создаются для класса интерфейса и публикуются в библиотеке типов.Использование AutoDual крайне нежелательно из-за ограничений, связанных с версиями и описанных для атрибута <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.None">
      <summary>Показывает, что для класса не создается интерфейс класса.Если интерфейсы не реализованы явно, класс может предоставить доступ только через позднее связывание при помощи интерфейса IDispatch.Это значение является рекомендованным для атрибута <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />.Использование ClassInterfaceType.None — это единственный способ предоставления функциональных возможностей при помощи интерфейсов, явно реализованных классом.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CoClassAttribute">
      <summary>Определяет идентификатор класса для совместного класса, импортированного из библиотеки типов.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CoClassAttribute.#ctor(System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.CoClassAttribute" />, используя идентификатор исходного совместного класса.</summary>
      <param name="coClass">
        <see cref="T:System.Type" />, содержащий идентификатор исходного совместного класса. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.CoClassAttribute.CoClass">
      <summary>Возвращает идентификатор исходного совместного класса.</summary>
      <returns>
        <see cref="T:System.Type" />, содержащий идентификатор исходного совместного класса.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComAwareEventInfo">
      <summary>Позволяет выполнять регистрацию обработчика событий с поздней привязкой.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.#ctor(System.Type,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ComAwareEventInfo" />, используя указанные тип и имя события в типе.</summary>
      <param name="type">Тип объекта. </param>
      <param name="eventName">Имя события в <paramref name="type" />.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.AddEventHandler(System.Object,System.Delegate)">
      <summary>Присоединяет обработчик событий к объекту модели COM.</summary>
      <param name="target">Целевой объект, к которому должен привязываться делегат события.</param>
      <param name="handler">Делегат события.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Attributes">
      <summary>Получает атрибуты этого события.</summary>
      <returns>Атрибуты этого события, доступные только для чтения.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.DeclaringType">
      <summary>Получает класс, объявляющий этот член.</summary>
      <returns>Объект <see cref="T:System.Type" /> для класса, объявляющего данный член.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Name">
      <summary>Возвращает имя текущего члена.</summary>
      <returns>Имя данного элемента.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.RemoveEventHandler(System.Object,System.Delegate)">
      <summary>Отсоединяет обработчик событий от объекта модели COM.</summary>
      <param name="target">Целевой объект, к которому привязан делегат события.</param>
      <param name="handler">Делегат события.</param>
      <exception cref="T:System.InvalidOperationException">Для этого события не предусмотрен открытый метод доступа remove.</exception>
      <exception cref="T:System.ArgumentException">Переданный обработчик нельзя использовать.</exception>
      <exception cref="T:System.Reflection.TargetException">В .NET для приложений Магазина Windows или переносимой библиотеке классов вместо этого перехватите <see cref="T:System.Exception" />.Параметр <paramref name="target" /> имеет значение null и событие не является статическим.– или – Объект <see cref="T:System.Reflection.EventInfo" /> не объявлен для целевого объекта.</exception>
      <exception cref="T:System.MethodAccessException">В .NET для приложений Магазина Windows или переносимой библиотеке классов вместо этого перехватите исключение базового класса <see cref="T:System.MemberAccessException" />.Вызывающий оператор не имеет разрешения на доступ к данному члену.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute">
      <summary>Определяет интерфейс по умолчанию, предоставляемый COM.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute" />, используя указанный объект <see cref="T:System.Type" /> в качестве интерфейса по умолчанию, предоставленного COM.</summary>
      <param name="defaultInterface">Значение <see cref="T:System.Type" />, показывающее интерфейс по умолчанию, предоставляемый для COM. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.Value">
      <summary>Получает объект <see cref="T:System.Type" />, определяющий интерфейс по умолчанию, предоставляемый для COM.</summary>
      <returns>Объект <see cref="T:System.Type" />, определяющий интерфейс по умолчанию, предоставляемый для COM.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventInterfaceAttribute">
      <summary>Определяет исходный интерфейс и класс, реализующий методы интерфейса события, созданного при импортировании совместного класса из библиотеки COM-типов.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventInterfaceAttribute.#ctor(System.Type,System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ComEventInterfaceAttribute" />, используя интерфейс источника и класс поставщика событий.</summary>
      <param name="SourceInterface">
        <see cref="T:System.Type" />, содержащий исходный интерфейс источника из библиотеки типов.Этот интерфейс используется в COM для обратного вызова управляемого класса.</param>
      <param name="EventProvider">
        <see cref="T:System.Type" />, содержащий класс, реализующий методы интерфейса события. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.EventProvider">
      <summary>Возвращает класс, реализующий методы интерфейса события.</summary>
      <returns>
        <see cref="T:System.Type" />, содержащий класс, реализующий методы интерфейса события.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.SourceInterface">
      <summary>Возвращает исходный интерфейс источника из библиотеки типов.</summary>
      <returns>
        <see cref="T:System.Type" />, содержащий исходный интерфейс.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventsHelper">
      <summary>Предоставляет методы, обеспечивающие делегаты .NET Framework, которые обрабатывают события, добавляемые в COM-объекты и удаляемые из них.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Combine(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>Добавляет делегат в список вызова источника событий, поступающих из COM-объекта.</summary>
      <param name="rcw">COM-объект, инициирующий события, на которые вызывающему объекту требуется реагировать.</param>
      <param name="iid">Идентификатор исходного интерфейса, с помощью которого COM-объект инициирует события. </param>
      <param name="dispid">Идентификатор диспетчеризации метода исходного интерфейса.</param>
      <param name="d">Делегат, вызываемый при срабатывании события COM.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Remove(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>Удаляет делегат из списка вызова событий, поступающих от COM-объекта.</summary>
      <returns>Делегат, удаленный из списка вызова.</returns>
      <param name="rcw">COM-объект, к которому прикреплен делегат.</param>
      <param name="iid">Идентификатор исходного интерфейса, с помощью которого COM-объект инициирует события. </param>
      <param name="dispid">Идентификатор диспетчеризации метода исходного интерфейса.</param>
      <param name="d">Делегат, удаляемый из списка вызова.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.COMException">
      <summary>Исключение, возникающее при возвращении неизвестного значения HRESULT после вызова метода COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.COMException" /> со значениями по умолчанию.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.COMException" /> с заданным сообщением.</summary>
      <param name="message">Сообщение, в котором указывается причина исключения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.COMException" /> с заданным сообщением об ошибке и ссылкой на внутреннее исключение, которое стало причиной данного исключения.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
      <param name="inner">Исключение, которое вызвало текущее исключение.Если значение параметра <paramref name="inner" /> не равно null, текущее исключение вызывается в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.COMException" />, используя заданное сообщение и код ошибки.</summary>
      <param name="message">Сообщение, указывающее причину возникновения исключения. </param>
      <param name="errorCode">Код ошибки (HRESULT) — это значение, связанное с этим исключением. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComImportAttribute">
      <summary>Указывает, что тип с атрибутом был ранее определен в COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComImportAttribute.#ctor">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Runtime.InteropServices.ComImportAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComInterfaceType">
      <summary>Определяет способ предоставления интерфейса для COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual">
      <summary>Указывает, что интерфейс предоставляется модели COM как сдвоенный интерфейс, позволяющий выполнять раннее и позднее связывание.<see cref="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual" /> является значением по умолчанию.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIDispatch">
      <summary>Показывает, что интерфейс предоставляется COM как диспетчерский интерфейс, позволяющий выполнять только позднее связывание.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIInspectable">
      <summary>Указывает, что интерфейс предоставляется модели COM как интерфейс Среда выполнения Windows. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIUnknown">
      <summary>Показывает, что интерфейс предоставляется модели COM как интерфейс, унаследованный от IUnknown и позволяющий выполнять только раннее связывание.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComMemberType">
      <summary>Описывает тип члена COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.Method">
      <summary>Этот член является обычным методом.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropGet">
      <summary>Член возвращает свойства.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropSet">
      <summary>Член задает свойства.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute">
      <summary>Определяет список интерфейсов, предоставляемых в виде источников событий COM для класса с атрибутом.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> с именем интерфейса-источника событий.</summary>
      <param name="sourceInterfaces">Список полных имен интерфейсов-источников событий, разделенных нулями. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> с типом, используемым в качестве интерфейса-источника.</summary>
      <param name="sourceInterface">Тип <see cref="T:System.Type" /> интерфейса-источника. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> с типами, используемыми в качестве интерфейсов-источников.</summary>
      <param name="sourceInterface1">Тип <see cref="T:System.Type" /> интерфейса-источника, используемого по умолчанию. </param>
      <param name="sourceInterface2">Тип <see cref="T:System.Type" /> интерфейса-источника. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type)">
      <summary>Инициализирует новый экземпляр класса ComSourceInterfacesAttribute с типами, используемыми в качестве интерфейсов-источников.</summary>
      <param name="sourceInterface1">Тип <see cref="T:System.Type" /> интерфейса-источника, используемого по умолчанию. </param>
      <param name="sourceInterface2">Тип <see cref="T:System.Type" /> интерфейса-источника. </param>
      <param name="sourceInterface3">Тип <see cref="T:System.Type" /> интерфейса-источника. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type,System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> с типами, используемыми в качестве интерфейсов-источников.</summary>
      <param name="sourceInterface1">Тип <see cref="T:System.Type" /> интерфейса-источника, используемого по умолчанию. </param>
      <param name="sourceInterface2">Тип <see cref="T:System.Type" /> интерфейса-источника. </param>
      <param name="sourceInterface3">Тип <see cref="T:System.Type" /> интерфейса-источника. </param>
      <param name="sourceInterface4">Тип <see cref="T:System.Type" /> интерфейса-источника. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComSourceInterfacesAttribute.Value">
      <summary>Возвращает проверенное имя интерфейса-источника событий.</summary>
      <returns>Возвращает полное имя интерфейса-источника событий.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CurrencyWrapper">
      <summary>Инкапсулирует объекты, которые необходимо маршалировать, как VT_CY.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Decimal)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" />, используя Decimal, для инкапсуляции, а также для маршалинга как типа VT_CY.</summary>
      <param name="obj">Decimal, для которого требуется выполнить инкапсуляцию, а также маршалинг как типа VT_CY. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" />, используя объект, содержащий Decimal, для инкапсуляции, а также для маршалинга как типа VT_CY.</summary>
      <param name="obj">Объект, содержащий Decimal, для инкапсуляции, а также для маршалинга как типа VT_CY. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="obj" /> не принадлежит к типу <see cref="T:System.Decimal" />.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.CurrencyWrapper.WrappedObject">
      <summary>Возвращает инкапсулированный объект, который должен быть маршалирован как тип VT_CY.</summary>
      <returns>Инкапсулированный объект, который должен быть маршалирован как тип VT_CY.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceMode">
      <summary>Указывает, может ли интерфейс <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> использоваться в вызовах IUnknown::QueryInterface метода <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Allow">
      <summary>Вызовы метода IUnknown::QueryInterface могут использовать интерфейс <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.Если используется это значение, перегруженный метод <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> действует аналогично перегруженному методу <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Ignore">
      <summary>Вызовы метода IUnknown::QueryInterface должны игнорировать интерфейс <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceResult">
      <summary>Предоставляет значения, возвращаемые методом <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Failed">
      <summary>Интерфейс, соответствующий определенному идентификатору интерфейса, недоступен.В этом случае возвращается интерфейс null.Объекту, вызвавшему метод IUnknown::QueryInterface, возвращается значение E_NOINTERFACE.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Handled">
      <summary>Указатель интерфейса, возвращаемый методом <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" />, можно использовать в качестве результата метода IUnknown::QueryInterface.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.NotHandled">
      <summary>Пользовательский интерфейс QueryInterface не используется.Вместо него следует использовать реализацию по умолчанию метода IUnknown::QueryInterface.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultCharSetAttribute">
      <summary>Определяет значение перечисления <see cref="T:System.Runtime.InteropServices.CharSet" />.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultCharSetAttribute.#ctor(System.Runtime.InteropServices.CharSet)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.DefaultCharSetAttribute" /> с заданным значением <see cref="T:System.Runtime.InteropServices.CharSet" />.</summary>
      <param name="charSet">Одно из значений <see cref="T:System.Runtime.InteropServices.CharSet" />.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultCharSetAttribute.CharSet">
      <summary>Возвращает значение по умолчанию <see cref="T:System.Runtime.InteropServices.CharSet" /> для любого вызова атрибута <see cref="T:System.Runtime.InteropServices.DllImportAttribute" />.</summary>
      <returns>Значение по умолчанию <see cref="T:System.Runtime.InteropServices.CharSet" /> для любого вызова атрибута <see cref="T:System.Runtime.InteropServices.DllImportAttribute" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute">
      <summary>Определяет пути, которые используются для поиска библиотек DLL, предоставляющих функции для вызовов неуправляемого кода. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.#ctor(System.Runtime.InteropServices.DllImportSearchPath)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> с указанием путей для поиска целевых объектов вызовов неуправляемого кода. </summary>
      <param name="paths">Битовая комбинация значений перечисления, указывающих пути, по которым ищет функция LoadLibraryEx во время вызовов платформы. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.Paths">
      <summary>Получает побитовое сочетание значений перечисления, указывающих пути, по которым ищет функция LoadLibraryEx во время вызовов неуправляемого кода. </summary>
      <returns>Битовая комбинация значений перечисления, определяющих пути поиска для вызовов платформы. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultParameterValueAttribute">
      <summary>Определяет значение по умолчанию при вызове из языка, поддерживающего параметры по умолчанию.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultParameterValueAttribute.#ctor(System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.DefaultParameterValueAttribute" /> значением параметра по умолчанию.</summary>
      <param name="value">Объект, представляющий значение параметра по умолчанию.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultParameterValueAttribute.Value">
      <summary>Возвращает значение, присваиваемое параметру по умолчанию.</summary>
      <returns>Объект, представляющий значение параметра по умолчанию.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispatchWrapper">
      <summary>Инкапсулирует объекты, которые необходимо маршалировать, как VT_DISPATCH.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispatchWrapper.#ctor(System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> с объектом, заключаемым в оболочку.</summary>
      <param name="obj">Объект, который необходимо заключить в оболочку и преобразовать в <see cref="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> не является классом или массивом.-или- <paramref name="obj" /> не поддерживает IDispatch. </exception>
      <exception cref="T:System.InvalidOperationException">Параметр <paramref name="obj" /> был помечен атрибутом <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" />, переданным как значение false.-или-Параметр <paramref name="obj" /> наследуется от типа, помеченного атрибутом <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" />, переданным как значение false.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.DispatchWrapper.WrappedObject">
      <summary>Возвращает объект, заключенный в оболочку с помощью <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />.</summary>
      <returns>Объект, заключенный в оболочку с помощью <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispIdAttribute">
      <summary>Задает идентификатор диспетчеризации COM (DISPID) для метода, поля или свойства.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispIdAttribute.#ctor(System.Int32)">
      <summary>Инициализирует новый экземпляр класса DispIdAttribute, используя указанный идентификатор DISPID.</summary>
      <param name="dispId">Идентификатор DISPID для этого члена. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DispIdAttribute.Value">
      <summary>Возвращает идентификатор DISPID для члена.</summary>
      <returns>Идентификатор DISPID для этого члена.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportAttribute">
      <summary>Показывает, что в качестве статической точки входа неуправляемая динамическая библиотека (DLL) предоставляет метод с атрибутами.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DllImportAttribute.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> с именем динамической библиотеки (DLL), содержащей импортируемый метод.</summary>
      <param name="dllName">Имя динамической библиотеки (DLL), содержащей неуправляемый метод.Если DLL включена в сборку, это имя может включать отображаемое имя сборки.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.BestFitMapping">
      <summary>Включает или отключает поведение наилучшего сопоставления при преобразовании знаков Юникода в знаки ANSI.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CallingConvention">
      <summary>Показывает соглашение о вызове для точки входа.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CharSet">
      <summary>Показывает способ маршалинга параметров строки для метода, а также управляет искажением имени.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.EntryPoint">
      <summary>Показывает имя или порядковый номер точки входа вызываемой динамической библиотеки (DLL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ExactSpelling">
      <summary>Контролирует запуск поиска имен точек входа помимо заданной точки в неуправляемой динамической библиотеке (DLL), выполняемого средой CLR, полем <see cref="F:System.Runtime.InteropServices.DllImportAttribute.CharSet" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.PreserveSig">
      <summary>Показывают, выполняется ли для методов с возвращаемыми значениями HRESULT или retval непосредственное преобразование, либо возвращаемые значения HRESULT или retval автоматически преобразуются в исключения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError">
      <summary>Показывает, вызывает ли вызываемый объект функцию SetLastError интерфейса Win32 API перед возвращением из метода, использующего атрибуты.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ThrowOnUnmappableChar">
      <summary>Включает и отключает возникновение исключений для неотображаемых символов Юникода, преобразующихся в знаки вопроса ("?") ANSI.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.DllImportAttribute.Value">
      <summary>Возвращает имя динамической библиотеки (DLL), содержащей точку входа.</summary>
      <returns>Имя файла динамической библиотеки (DLL), содержащей точку входа.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportSearchPath">
      <summary>Определяет пути, которые используются для поиска библиотек DLL, предоставляющих функции для вызовов неуправляемого кода. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.ApplicationDirectory">
      <summary>Включить каталог приложения в путь поиска DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.AssemblyDirectory">
      <summary>При поиске зависимостей сборки включите каталог, содержащий саму сборку, и сначала выполните поиск в этом каталоге.Это значение используется платформой .NET Framework, прежде чем пути передаются в функцию Win32 LoadLibraryEx.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.LegacyBehavior">
      <summary>Выполните поиск в каталоге приложения, а затем вызовите функцию Win32 LoadLibraryEx с флагом LOAD_WITH_ALTERED_SEARCH_PATH.Это значение пропускается, если указано любое другое значение.Операционные системы, которые не поддерживают атрибут <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" />, используют это значение и пропускают остальные значения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.SafeDirectories">
      <summary>Включить каталог приложения, каталог %WinDir%\System32 и каталоги пользователей в путь поиска DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.System32">
      <summary>Включить каталог %WinDir%\System32 в путь поиска DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UseDllDirectoryForDependencies">
      <summary>Выполнять поиск зависимостей библиотеки DLL в папке, где находится библиотека DLL, прежде чем в других папках. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UserDirectories">
      <summary>Включить любой путь, который был явно добавлен в путь поиска по всему процессу с помощью функции Win32 AddDllDirectory. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ErrorWrapper">
      <summary>Инкапсулирует объекты, которые необходимо маршалировать, как VT_ERROR.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> со значением HRESULT, которое относится к выданному исключению.</summary>
      <param name="e">Исключение для преобразования в код ошибки. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ErrorWrapper" />, используя значение HRESULT ошибки.</summary>
      <param name="errorCode">Значение HRESULT ошибки. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> с объектом, содержащим значение HRESULT ошибки.</summary>
      <param name="errorCode">Объект, содержащий значение HRESULT ошибки. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="errorCode" /> не принадлежит к типу <see cref="T:System.Int32" />.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.ErrorWrapper.ErrorCode">
      <summary>Возвращает код ошибки обертки.</summary>
      <returns>Значение HRESULT ошибки.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandle">
      <summary>Предоставляет способ доступа к управляемому объекту из неуправляемой памяти.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.AddrOfPinnedObject">
      <summary>Возвращает адрес объекта в дескрипторе <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />.</summary>
      <returns>Адрес закрепленного объекта как указатель <see cref="T:System.IntPtr" />. </returns>
      <exception cref="T:System.InvalidOperationException">The handle is any type other than <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object)">
      <summary>Выделяет дескриптор <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" /> для указанного объекта.</summary>
      <returns>Новый дескриптор <see cref="T:System.Runtime.InteropServices.GCHandle" />, защищающий объект от сборщика мусора.Этот дескриптор <see cref="T:System.Runtime.InteropServices.GCHandle" /> должен освобождаться с помощью метода <see cref="M:System.Runtime.InteropServices.GCHandle.Free" />, если в нем больше нет необходимости.</returns>
      <param name="value">Объект, использующий дескриптор <see cref="T:System.Runtime.InteropServices.GCHandle" />. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object,System.Runtime.InteropServices.GCHandleType)">
      <summary>Выделяет дескриптор указанного типа для указанного объекта.</summary>
      <returns>Новый дескриптор <see cref="T:System.Runtime.InteropServices.GCHandle" /> указанного типа.Этот дескриптор <see cref="T:System.Runtime.InteropServices.GCHandle" /> должен освобождаться с помощью метода <see cref="M:System.Runtime.InteropServices.GCHandle.Free" />, если в нем больше нет необходимости.</returns>
      <param name="value">Объект, использующий дескриптор <see cref="T:System.Runtime.InteropServices.GCHandle" />. </param>
      <param name="type">Одно из значений <see cref="T:System.Runtime.InteropServices.GCHandleType" />, показывающее тип создаваемого дескриптора <see cref="T:System.Runtime.InteropServices.GCHandle" />. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Runtime.InteropServices.GCHandle" /> текущему объекту <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <returns>Значение true, если заданный объект <see cref="T:System.Runtime.InteropServices.GCHandle" /> равен текущему объекту <see cref="T:System.Runtime.InteropServices.GCHandle" />; в противном случае — значение false.</returns>
      <param name="o">Объект <see cref="T:System.Runtime.InteropServices.GCHandle" /> для сравнения с текущим объектом <see cref="T:System.Runtime.InteropServices.GCHandle" />.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Free">
      <summary>Освобождает дескриптор <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <exception cref="T:System.InvalidOperationException">The handle was freed or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.FromIntPtr(System.IntPtr)">
      <summary>Возвращает новый объект <see cref="T:System.Runtime.InteropServices.GCHandle" />, созданный из дескриптора управляемого объекта.</summary>
      <returns>Новый объект<see cref="T:System.Runtime.InteropServices.GCHandle" />, соответствующий значению параметра.  </returns>
      <param name="value">Дескриптор <see cref="T:System.IntPtr" /> управляемого объекта для создания из него объекта <see cref="T:System.Runtime.InteropServices.GCHandle" />.</param>
      <exception cref="T:System.InvalidOperationException">The value of the <paramref name="value" /> parameter is <see cref="F:System.IntPtr.Zero" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.GetHashCode">
      <summary>Возвращает идентификатор для текущего объекта <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <returns>Идентификатор для текущего объекта <see cref="T:System.Runtime.InteropServices.GCHandle" />.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.IsAllocated">
      <summary>Возвращает значение, показывающее, выделен ли дескриптор.</summary>
      <returns>Значение true, если дескриптор выделен; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Equality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>Возвращает значение, показывающее, равны ли два объекта <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <returns>Значение true, если параметры <paramref name="a" /> и <paramref name="b" /> равны; в противном случае — значение false.</returns>
      <param name="a">Объект <see cref="T:System.Runtime.InteropServices.GCHandle" />, сравниваемый с параметром <paramref name="b" />. </param>
      <param name="b">Объект <see cref="T:System.Runtime.InteropServices.GCHandle" />, сравниваемый с параметром <paramref name="a" />.  </param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.IntPtr)~System.Runtime.InteropServices.GCHandle">
      <summary>Дескриптор <see cref="T:System.Runtime.InteropServices.GCHandle" /> хранится в виде целого числа как внутреннего представления.</summary>
      <returns>Сохраненный объект <see cref="T:System.Runtime.InteropServices.GCHandle" />, использующий внутреннее целочисленное представление.</returns>
      <param name="value">Указатель <see cref="T:System.IntPtr" /> на дескриптор, для которого необходимо выполнить преобразование. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.Runtime.InteropServices.GCHandle)~System.IntPtr">
      <summary>Дескриптор <see cref="T:System.Runtime.InteropServices.GCHandle" /> хранится в виде целого числа как внутреннего представления.</summary>
      <returns>Целочисленное значение.</returns>
      <param name="value">Дескриптор <see cref="T:System.Runtime.InteropServices.GCHandle" />, для которого требуется целое число. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Inequality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>Возвращает значение, показывающее, являются ли два объекта <see cref="T:System.Runtime.InteropServices.GCHandle" /> неравными.</summary>
      <returns>Значение true, если параметры <paramref name="a" /> и <paramref name="b" /> не равны; в противном случае — значение false.</returns>
      <param name="a">Объект <see cref="T:System.Runtime.InteropServices.GCHandle" />, сравниваемый с параметром <paramref name="b" />. </param>
      <param name="b">Объект <see cref="T:System.Runtime.InteropServices.GCHandle" />, сравниваемый с параметром <paramref name="a" />.  </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.Target">
      <summary>Возвращает или задает объект, предоставляемый дескриптором.</summary>
      <returns>Объект, представляемый дескриптором.</returns>
      <exception cref="T:System.InvalidOperationException">The handle was freed, or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.ToIntPtr(System.Runtime.InteropServices.GCHandle)">
      <summary>Возвращает внутреннее целочисленное представление объекта <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <returns>Объект <see cref="T:System.IntPtr" />, представляющий объект <see cref="T:System.Runtime.InteropServices.GCHandle" />. </returns>
      <param name="value">Объект <see cref="T:System.Runtime.InteropServices.GCHandle" /> для извлечения внутреннего целочисленного представления.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandleType">
      <summary>Предоставляет типы дескрипторов, которые могут быть выделены классом <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Normal">
      <summary>Этот тип дескриптора предоставляет непрозрачный дескриптор, то есть адрес закрепленного объекта, используемый дескриптором, нельзя разрешить с помощью дескриптора.Этот тип можно использовать для отслеживания объекта и предотвращения его уничтожения сборщиком мусора.Этот элемент перечисления используется, когда неуправляемый клиент содержит только одну ссылку на управляемый объект, которую не может обнаружить сборщик мусора.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Pinned">
      <summary>Этот дескриптор подобен <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" />, но он позволяет работать с адресом закрепленного объекта.Это не позволяет сборщику мусора переместить объект, в результате эффективность сборщика мусора снижается.Метод <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> используется для максимально быстрого освобождения выделенного дескриптора.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Weak">
      <summary>Этот тип дескриптора используется для отслеживания объекта, но позволяет выполнять его сбор.При сборе объекта содержимое объекта <see cref="T:System.Runtime.InteropServices.GCHandle" /> обнуляется.Перед запуском метода завершения ссылки Weak обнуляются, поэтому даже если метод завершения восстановит объект, ссылка Weak остается обнуленной.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.WeakTrackResurrection">
      <summary>Этот тип дескриптора подобен <see cref="F:System.Runtime.InteropServices.GCHandleType.Weak" />, но дескриптор не обнуляется при восстановлении объекта в процессе завершения.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.GuidAttribute">
      <summary>Предоставляет явный идентификатор <see cref="T:System.Guid" /> в случае, когда использование автоматического идентификатора GUID нежелательно.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GuidAttribute.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.GuidAttribute" /> с заданным идентификатором GUID.</summary>
      <param name="guid">Присваиваемый идентификатор <see cref="T:System.Guid" />. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GuidAttribute.Value">
      <summary>Возвращает идентификатор <see cref="T:System.Guid" /> класса.</summary>
      <returns>Идентификатор <see cref="T:System.Guid" /> класса.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.HandleCollector">
      <summary>Отслеживает незавершенные дескрипторы и инициирует сбор мусора при достижении заданного порога.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.HandleCollector" />, используя имя и порог, достижение которого вызывает сбор дескрипторов. </summary>
      <param name="name">Имя сборщика.Этот параметр позволяет называть сборщики, независимо отслеживающие типы дескрипторов.</param>
      <param name="initialThreshold">Значение, определяющее точку, с которой должен начаться сбор.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="initialThreshold" /> меньше 0.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.HandleCollector" />, используя имя, порог начала сбора дескрипторов и порок, при котором должен быть выполнен сбор дескрипторов. </summary>
      <param name="name">Имя сборщика.  Этот параметр позволяет называть сборщики, независимо отслеживающие типы дескрипторов.</param>
      <param name="initialThreshold">Значение, определяющее точку, с которой должен начаться сбор.</param>
      <param name="maximumThreshold">Значение, определяющее точку, в которой должен быть выполнен сбор.Оно должно быть равно максимальному числу доступных дескрипторов.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="initialThreshold" /> меньше 0.– или –Значение параметра <paramref name="maximumThreshold" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="maximumThreshold" /> меньше значения параметра <paramref name="initialThreshold" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Add">
      <summary>Увеличивает текущий счетчик дескрипторов.</summary>
      <exception cref="T:System.InvalidOperationException">Значение свойства <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> меньше 0.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Count">
      <summary>Возвращает количество собранных дескрипторов.</summary>
      <returns>Количество собранных дескрипторов.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.InitialThreshold">
      <summary>Возвращает значение, определяющее точку, с которой должен начаться сбор.</summary>
      <returns>Значение, определяющее точку, с которой должен начаться сбор.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.MaximumThreshold">
      <summary>Возвращает значение, определяющее точку, в которой должен быть выполнен сбор.</summary>
      <returns>Значение, определяющее точку, в которой должен быть выполнен сбор.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Name">
      <summary>Возвращает имя объекта <see cref="T:System.Runtime.InteropServices.HandleCollector" />.</summary>
      <returns>Это свойство <see cref="P:System.Runtime.InteropServices.HandleCollector.Name" /> позволяет называть сборщики, независимо отслеживающие типы дескрипторов.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Remove">
      <summary>Уменьшает текущий счетчик дескрипторов.</summary>
      <exception cref="T:System.InvalidOperationException">Значение свойства <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> меньше 0.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomAdapter">
      <summary>Предоставляет клиентам доступ к текущему объекту вместо передачи объекта адаптера настраиваемым модулем упаковки и передачи.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomAdapter.GetUnderlyingObject">
      <summary>Предоставляет доступ к базовому объекту, упакованному настраиваемым модулем упаковки и передачи.</summary>
      <returns>Объект, содержащийся в объекте адаптера.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomQueryInterface">
      <summary>Позволяет разработчикам предоставить собственную управляемую реализацию метода IUnknown::QueryInterface(REFIID riid, void **ppvObject) method.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)">
      <summary>Возвращает интерфейс, соответствующий указанному идентификатору интерфейса.</summary>
      <returns>Одно из значений перечисления, указывающих, используется ли пользовательская реализация интерфейса IUnknown::QueryInterface.</returns>
      <param name="iid">Идентификатор GUID запрашиваемого интерфейса.</param>
      <param name="ppv">Ссылка на запрашиваемый интерфейс (когда данный метод возвращает значение).</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InAttribute">
      <summary>Показывает, маршалинг каких данных необходимо выполнить при передаче от вызывающего объекта — вызываемому, но не обратно.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.InAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.InterfaceTypeAttribute">
      <summary>Показывает, является ли интерфейс при предоставлении COM сдвоенным, диспетчерским или только IUnknown.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Int16)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> с заданным элементом перечисления <see cref="T:System.Runtime.InteropServices.ComInterfaceType" />.</summary>
      <param name="interfaceType">Описывает способ предоставления интерфейса клиентам COM. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Runtime.InteropServices.ComInterfaceType)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> с заданным элементом перечисления <see cref="T:System.Runtime.InteropServices.ComInterfaceType" />.</summary>
      <param name="interfaceType">Одно из значений <see cref="T:System.Runtime.InteropServices.ComInterfaceType" />, описывающих способ предоставления интерфейса клиентам COM. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.InterfaceTypeAttribute.Value">
      <summary>Возвращает значение <see cref="T:System.Runtime.InteropServices.ComInterfaceType" />, описывающее способ предоставления интерфейса COM.</summary>
      <returns>Значение <see cref="T:System.Runtime.InteropServices.ComInterfaceType" />, описывающее способ предоставления интерфейса COM.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidComObjectException">
      <summary>Исключение, вызванное использованием недопустимого COM-объекта.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor">
      <summary>Инициализирует новый экземпляр класса InvalidComObjectException, используя свойства по умолчанию.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса InvalidComObjectException, используя сообщение.</summary>
      <param name="message">Сообщение, в котором указывается причина исключения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.InvalidComObjectException" /> с указанным сообщением об ошибке и ссылкой на внутреннее исключение, вызвавшее это исключение.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
      <param name="inner">Исключение, которое вызвало текущее исключение.Если значение параметра <paramref name="inner" /> не равно null, текущее исключение вызывается в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
      <summary>При обнаружении аргумента типа variant, маршалинг которого в управляемый код выполнить невозможно, модуль маршалинга вызывает исключение.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor">
      <summary>Инициализирует новый экземпляр класса InvalidOleVariantTypeException значениями по умолчанию.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса InvalidOleVariantTypeException с заданным сообщением.</summary>
      <param name="message">Сообщение, в котором указывается причина исключения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException" /> с указанным сообщением об ошибке и ссылкой на внутреннее исключение, вызвавшее это исключение.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
      <param name="inner">Исключение, которое вызвало текущее исключение.Если значение параметра <paramref name="inner" /> не равно null, текущее исключение вызывается в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.Marshal">
      <summary>Предоставляет коллекцию методов для выделения неуправляемой памяти, копирования блоков неуправляемой памяти и преобразования управляемых типов в неуправляемые, а также прочих разнообразных методов, используемых при взаимодействии с неуправляемым кодом.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AddRef(System.IntPtr)">
      <summary>Увеличивает счетчик ссылок для указанного интерфейса.</summary>
      <returns>Новое значение счетчика ссылок для параметра <paramref name="pUnk" />.</returns>
      <param name="pUnk">Увеличиваемый счетчик ссылок интерфейса.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)">
      <summary>Выделяет блок памяти указанного размера из механизма распределения памяти для задач COM.</summary>
      <returns>Целое число, представляющее адрес выделенного блока памяти.Освобождать эту память необходимо с помощью метода <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />.</returns>
      <param name="cb">Размер выделяемого блока памяти.</param>
      <exception cref="T:System.OutOfMemoryException">Недостаточно памяти для выполнения запроса.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.Int32)">
      <summary>Выделяет память из неуправляемой памяти процесса, используя заданное количество байтов.</summary>
      <returns>Указатель на только что выделенную память.Эта память должна освобождаться при помощи метода <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />.</returns>
      <param name="cb">Требуемое количество байтов памяти.</param>
      <exception cref="T:System.OutOfMemoryException">Недостаточно памяти для выполнения запроса.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)">
      <summary>Выделяет память из неуправляемой памяти процесса, используя указатель на заданное количество байтов.</summary>
      <returns>Указатель на только что выделенную память.Эта память должна освобождаться при помощи метода <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />.</returns>
      <param name="cb">Требуемое количество байтов памяти.</param>
      <exception cref="T:System.OutOfMemoryException">Недостаточно памяти для выполнения запроса.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AreComObjectsAvailableForCleanup">
      <summary>Указывает, доступны ли для очистки вызываемые оболочки времени выполнения (RCW) из какого-либо контекста.</summary>
      <returns>Значение true, если есть доступные для очистки вызываемые оболочки времени выполнения; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Byte[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Копирует данные из одномерного управляемого массива 8-битных целых чисел без знака в указатель неуправляемой памяти.</summary>
      <param name="source">Одномерный массив, из которого выполняется копирование.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в исходном массиве, с которого начинается копирование.</param>
      <param name="destination">Указатель памяти, в который выполняется копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметры <paramref name="startIndex" /> и <paramref name="length" /> являются недопустимыми.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> или <paramref name="length" /> равно null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Char[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Копирует данные из одномерного управляемого массива символов в неуправляемый указатель памяти.</summary>
      <param name="source">Одномерный массив, из которого выполняется копирование.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в исходном массиве, с которого начинается копирование.</param>
      <param name="destination">Указатель памяти, в который выполняется копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметры <paramref name="startIndex" /> и <paramref name="length" /> являются недопустимыми.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="startIndex" />, <paramref name="destination" /> или <paramref name="length" /> — null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Double[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Копирует данные из одномерного управляемого массива чисел с плавающей запятой двойной точности в указатель неуправляемой памяти.</summary>
      <param name="source">Одномерный массив, из которого выполняется копирование.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в исходном массиве, с которого начинается копирование.</param>
      <param name="destination">Указатель памяти, в который выполняется копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметры <paramref name="startIndex" /> и <paramref name="length" /> являются недопустимыми.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> или <paramref name="length" /> равно null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int16[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Копирует данные из одномерного управляемого массива 16-битных целых чисел со знаком в указатель неуправляемой памяти.</summary>
      <param name="source">Одномерный массив, из которого выполняется копирование.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в исходном массиве, с которого начинается копирование.</param>
      <param name="destination">Указатель памяти, в который выполняется копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметры <paramref name="startIndex" /> и <paramref name="length" /> являются недопустимыми.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> или <paramref name="length" /> равно null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int32[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Копирует данные из одномерного управляемого массива 32-битных целых чисел со знаком в указатель неуправляемой памяти.</summary>
      <param name="source">Одномерный массив, из которого выполняется копирование.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в исходном массиве, с которого начинается копирование.</param>
      <param name="destination">Указатель памяти, в который выполняется копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметры <paramref name="startIndex" /> и <paramref name="length" /> являются недопустимыми.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="startIndex" /> или <paramref name="length" /> — null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int64[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Копирует данные из одномерного управляемого массива 64-битных целых чисел со знаком в указатель неуправляемой памяти.</summary>
      <param name="source">Одномерный массив, из которого выполняется копирование.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в исходном массиве, с которого начинается копирование.</param>
      <param name="destination">Указатель памяти, в который выполняется копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметры <paramref name="startIndex" /> и <paramref name="length" /> являются недопустимыми.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> или <paramref name="length" /> равно null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Byte[],System.Int32,System.Int32)">
      <summary>Копирует данные из указателя неуправляемой памяти в одномерный управляемый массив 8-битных целых чисел без знака.</summary>
      <param name="source">Указатель памяти, из которого выполняется копирование.</param>
      <param name="destination">Массив для копирования данных.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в массиве назначения, с которого начинается копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> или <paramref name="length" /> равно null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Char[],System.Int32,System.Int32)">
      <summary>Копирует данные из указателя неуправляемой памяти в управляемый массив символов.</summary>
      <param name="source">Указатель памяти, из которого выполняется копирование.</param>
      <param name="destination">Массив для копирования данных.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в массиве назначения, с которого начинается копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> или <paramref name="length" /> равно null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Double[],System.Int32,System.Int32)">
      <summary>Копирует данные из неуправляемого указателя памяти в управляемый массив чисел с плавающей запятой двойной точности.</summary>
      <param name="source">Указатель памяти, из которого выполняется копирование.</param>
      <param name="destination">Массив для копирования данных.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в массиве назначения, с которого начинается копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> или <paramref name="length" /> равно null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int16[],System.Int32,System.Int32)">
      <summary>Копирует данные из указателя неуправляемой памяти в одномерный управляемый массив 16-битных целых чисел со знаком.</summary>
      <param name="source">Указатель памяти, из которого выполняется копирование.</param>
      <param name="destination">Массив для копирования данных.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в массиве назначения, с которого начинается копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> или <paramref name="length" /> равно null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int32[],System.Int32,System.Int32)">
      <summary>Копирует данные из указателя неуправляемой памяти в одномерный управляемый массив 32-битных целых чисел со знаком.</summary>
      <param name="source">Указатель памяти, из которого выполняется копирование.</param>
      <param name="destination">Массив для копирования данных.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в массиве назначения, с которого начинается копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> или <paramref name="length" /> равно null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int64[],System.Int32,System.Int32)">
      <summary>Копирует данные из указателя неуправляемой памяти в одномерный управляемый массив 64-битных целых чисел со знаком.</summary>
      <param name="source">Указатель памяти, из которого выполняется копирование.</param>
      <param name="destination">Массив для копирования данных.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в массиве назначения, с которого начинается копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> или <paramref name="length" /> равно null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
      <summary>Копирует данные из указателя неуправляемой памяти в управляемый массив <see cref="T:System.IntPtr" />.</summary>
      <param name="source">Указатель памяти, из которого выполняется копирование. </param>
      <param name="destination">Массив для копирования данных.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в массиве назначения, с которого начинается копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> или <paramref name="length" /> равно null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Single[],System.Int32,System.Int32)">
      <summary>Копирует данные из указателя неуправляемой памяти в управляемый массив чисел с плавающей запятой одиночной точности.</summary>
      <param name="source">Указатель памяти, из которого выполняется копирование. </param>
      <param name="destination">Массив для копирования данных. </param>
      <param name="startIndex">Отсчитываемый от нуля индекс в массиве назначения, с которого начинается копирование. </param>
      <param name="length">Число копируемых элементов массива. </param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> или <paramref name="length" /> равно null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Копирует данные из одномерного управляемого массива <see cref="T:System.IntPtr" /> в неуправляемый указатель памяти.</summary>
      <param name="source">Одномерный массив, из которого выполняется копирование.</param>
      <param name="startIndex">Отсчитываемый от нуля индекс в исходном массиве, с которого начинается копирование.</param>
      <param name="destination">Указатель памяти, в который выполняется копирование.</param>
      <param name="length">Число копируемых элементов массива.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> или <paramref name="length" /> равно null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Single[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Копирует данные из одномерного управляемого массива чисел с плавающей запятой одинарной точности в указатель неуправляемой памяти.</summary>
      <param name="source">Одномерный массив, из которого выполняется копирование. </param>
      <param name="startIndex">Отсчитываемый от нуля индекс в исходном массиве, с которого начинается копирование. </param>
      <param name="destination">Указатель памяти, в который выполняется копирование. </param>
      <param name="length">Число копируемых элементов массива. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметры <paramref name="startIndex" /> и <paramref name="length" /> являются недопустимыми. </exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> или <paramref name="length" /> равно null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject(System.IntPtr,System.Object)">
      <summary>Объединяет управляемый объект с заданным COM-объектом.</summary>
      <returns>Внутренний указатель IUnknown управляемого объекта.</returns>
      <param name="pOuter">Внешний указатель IUnknown.</param>
      <param name="o">Объект для объединения.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> является объектом Среда выполнения Windows.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject``1(System.IntPtr,``0)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Объединяет управляемый объект заданного типа с заданным COM-объектом. </summary>
      <returns>Внутренний указатель IUnknown управляемого объекта. </returns>
      <param name="pOuter">Внешний указатель IUnknown. </param>
      <param name="o">управляемый объект для агрегации. </param>
      <typeparam name="T">тип управляемого объекта для агрегации. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> является объектом Среда выполнения Windows. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType(System.Object,System.Type)">
      <summary>Инкапсулирует указанный COM-объект в объекте заданного типа.</summary>
      <returns>Новый инкапсулированный объект, являющийся экземпляром нужного типа.</returns>
      <param name="o">Инкапсулируемый объект. </param>
      <param name="t">Тип создаваемой оболочки. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="t" /> должен быть производным от __ComObject. -или-<paramref name="t" /> является типом Среда выполнения Windows.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="t" /> — null.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="o" /> не может быть преобразован в конечный тип, так как он не поддерживает все необходимые интерфейсы. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType``2(``0)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Инкапсулирует указанный COM-объект в объекте заданного типа.</summary>
      <returns>Только что Инкапсулированный объект. </returns>
      <param name="o">Инкапсулируемый объект. </param>
      <typeparam name="T">Тип пакуемого объекта. </typeparam>
      <typeparam name="TWrapper">Тип возвращаемого объекта. </typeparam>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="T" /> должен быть производным от __ComObject. -или-<paramref name="T" /> является типом Среда выполнения Windows.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="o" /> не может быть преобразован в <paramref name="TWrapper" />, так как он не поддерживает все необходимые интерфейсы. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Освобождает все вложенные структуры указанного типа, на которые указывает заданный блок неуправляемой памяти. </summary>
      <param name="ptr">Указатель на неуправляемый блок памяти. </param>
      <typeparam name="T">Тип отформатированной структуры.Предоставляет сведения распределения, необходимые для удаления буфера из параметра <paramref name="ptr" />.</typeparam>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="T" /> обеспечивает автоматическое распределение.Вместо него следует использовать последовательный или явный вариант.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)">
      <summary>Освобождает все вложенные структуры, на которые указывает заданный блок неуправляемой памяти.</summary>
      <param name="ptr">Указатель на неуправляемый блок памяти. </param>
      <param name="structuretype">Тип отформатированного класса.Предоставляет сведения распределения, необходимые для удаления буфера из параметра <paramref name="ptr" />.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="structureType" /> обеспечивает автоматическое распределение.Вместо него следует использовать последовательный или явный вариант.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FinalReleaseComObject(System.Object)">
      <summary>Высвобождает все ссылки на оболочку RCW (Вызываемая оболочка времени выполнения), задавая ее счетчику ссылок значение 0.</summary>
      <returns>Новое значение счетчика ссылок вызываемой оболочки времени выполнения, связанной с параметром <paramref name="o" />, равное нулю (0), если освобождение прошло успешно.</returns>
      <param name="o">Освобождаемая оболочка CLR.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="o" /> не является допустимым COM-объектом.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" />is null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeBSTR(System.IntPtr)">
      <summary>Освобождает строку BSTR с помощью функции COM SysFreeString.</summary>
      <param name="ptr">Адрес освобождаемой строки BSTR. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)">
      <summary>Освобождает блок памяти, выделенный неуправляемым механизмом распределения памяти для задач COM.</summary>
      <param name="ptr">Адрес освобождаемой памяти. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)">
      <summary>Освобождает память, выделенную ранее из неуправляемой памяти процесса.</summary>
      <param name="hglobal">Дескриптор, возвращенный исходным подходящим вызовом метода <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)">
      <summary>Возвращает указатель на интерфейс IUnknown, представляющий указанный интерфейс указанного объекта.Доступ к настраиваемому интерфейсу запросов включен по умолчанию.</summary>
      <returns>Указатель интерфейса, представляющий заданный интерфейс для объекта.</returns>
      <param name="o">Объект, предоставляющий интерфейс. </param>
      <param name="T">Тип запрашиваемого интерфейса. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="T" /> не является интерфейсом.-или- Тип недоступен для COM. -или-Параметр <paramref name="T" /> является универсальным типом.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="o" /> не поддерживает запрошенный интерфейс. </exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="o" /> — null.-или- Значение параметра <paramref name="T" /> — null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)">
      <summary>Возвращает указатель на интерфейс IUnknown, представляющий указанный интерфейс указанного объекта.Доступ к настраиваемому интерфейсу запросов контролируется указанным режимом настройки.</summary>
      <returns>Указатель интерфейса, представляющий интерфейс для объекта.</returns>
      <param name="o">Объект, предоставляющий интерфейс.</param>
      <param name="T">Тип запрашиваемого интерфейса.</param>
      <param name="mode">Одно из значений перечисления, определяющее, нужно ли применять пользовательскую настройку IUnknown::QueryInterface, предоставленную в интерфейсе <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="T" /> не является интерфейсом.-или- Тип недоступен для COM.-или-Параметр <paramref name="T" /> является универсальным типом.</exception>
      <exception cref="T:System.InvalidCastException">Объект <paramref name="o" /> не поддерживает запрошенный интерфейс.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="o" /> — null.-или- Значение параметра <paramref name="T" /> — null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject``2(``0)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Возвращает указатель на интерфейс IUnknown, представляющий указанный интерфейс объекта указанного типа.Доступ к настраиваемому интерфейсу запросов включен по умолчанию.</summary>
      <returns>Указатель интерфейса, представляющий интерфейс <paramref name="TInterface" />.</returns>
      <param name="o">Объект, предоставляющий интерфейс. </param>
      <typeparam name="T">Тип <paramref name="o" />. </typeparam>
      <typeparam name="TInterface">Тип возвращаемых интерфейсов. </typeparam>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="TInterface" /> не является интерфейсом.-или- Тип недоступен для COM. -или-Параметр <paramref name="T" />  является открытым универсальным типом.</exception>
      <exception cref="T:System.InvalidCastException">Параметр <paramref name="o" /> не поддерживает интерфейс <paramref name="TInterface" />. </exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="o" /> — null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer``1(System.IntPtr)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Преобразует указатель на неуправляемую функцию в делегат указанного типа. </summary>
      <returns>Экземпляр делегата указанного типа.</returns>
      <param name="ptr">Указатель на неуправляемую функцию, который требуется преобразовать. </param>
      <typeparam name="TDelegate">Тип возвращаемого делегата. </typeparam>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="TDelegate" /> не является делегатом или является открытым универсальным типом.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="ptr" /> — null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer(System.IntPtr,System.Type)">
      <summary>Преобразует указатель на неуправляемую функцию в делегат.</summary>
      <returns>Экземпляр делегата, который может быть приведен к соответствующему типу делегата.</returns>
      <param name="ptr">Указатель на неуправляемую функцию, который требуется преобразовать.</param>
      <param name="t">Тип возвращаемого делегата.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="t" /> не является делегатом или является универсальным.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="ptr" /> — null.-или-Значение параметра <paramref name="t" /> — null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionCode">
      <summary>Извлекает код, определяющий тип возникшего исключения.</summary>
      <returns>Тип исключения.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32)">
      <summary>Преобразует заданный код ошибки HRESULT в соответствующий объект <see cref="T:System.Exception" />.</summary>
      <returns>Объект, представляющий преобразованное значение HRESULT.</returns>
      <param name="errorCode">Преобразуемое значение HRESULT.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32,System.IntPtr)">
      <summary>Преобразует код ошибки HRESULT в соответствующий объект <see cref="T:System.Exception" />, с дополнительными сведениями об ошибки, передаваемыми в интерфейсе IErrorInfo для объекта исключения.</summary>
      <returns>Объект, представляющий преобразованное значение HRESULT и сведения, полученные из параметра <paramref name="errorInfo" />.</returns>
      <param name="errorCode">Преобразуемое значение HRESULT.</param>
      <param name="errorInfo">Указатель на интерфейс IErrorInfo, предоставляющий дополнительные сведения об ошибке.Можно задать IntPtr(0), чтобы использовать текущий интерфейс IErrorInfo, или IntPtr(-1), чтобы игнорировать текущий интерфейс IErrorInfo и создать исключение только из кода ошибки.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate(System.Delegate)">
      <summary>Преобразует делегат в указатель на функцию, вызываемый из неуправляемого кода.</summary>
      <returns>Значение, которое можно передать в неуправляемый код, который, в свою очередь, может использовать его для вызова базового управляемого делегата. </returns>
      <param name="d">Делегат, передаваемый в неуправляемый код.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="d" /> является универсальным типом.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="d" /> — null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate``1(``0)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Преобразует делегат указанного типа в указатель на функцию, вызываемый из неуправляемого кода. </summary>
      <returns>Значение, которое можно передать в неуправляемый код, который, в свою очередь, может использовать его для вызова базового управляемого делегата. </returns>
      <param name="d">Делегат, передаваемый в неуправляемый код. </param>
      <typeparam name="TDelegate">Тип преобразуемого делегата. </typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="d" /> — null. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForException(System.Exception)">
      <summary>Преобразует указанное исключение в значение HRESULT.</summary>
      <returns>Значение HRESULT, сопоставленное с заданным исключением.</returns>
      <param name="e">Исключение, преобразуемое в значение HRESULT.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForLastWin32Error">
      <summary>Возвращает значение HRESULT, соответствующее последней ошибке, вызванной кодом Win32, выполняемым с использованием класса <see cref="T:System.Runtime.InteropServices.Marshal" />.</summary>
      <returns>Значение HRESULT, соответствующее последнему коду ошибки Win32.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetIUnknownForObject(System.Object)">
      <summary>Возвращает интерфейс IUnknown из управляемого объекта.</summary>
      <returns>Указатель IUnknown для параметра <paramref name="o" />.</returns>
      <param name="o">Объект, для которого запрашивается интерфейс IUnknown.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error">
      <summary>Возвращает код ошибки, возвращенной последней неуправляемой функцией, вызванной при помощи вызова неуправляемого кода с установленным флагом <see cref="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError" />.</summary>
      <returns>Последний код ошибки, заданный вызовом функции SetLastError платформы Win32.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject(System.Object,System.IntPtr)">
      <summary>Преобразует объект в COM VARIANT.</summary>
      <param name="obj">Объект, для которого нужно получить COM VARIANT.</param>
      <param name="pDstNativeVariant">Указатель, получающий тип VARIANT, соответствующий параметру <paramref name="obj" />.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="obj" /> является универсальным типом.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject``1(``0,System.IntPtr)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Преобразует объект заданного типа в COM VARIANT. </summary>
      <param name="obj">Объект, для которого нужно получить COM VARIANT. </param>
      <param name="pDstNativeVariant">Указатель, получающий тип VARIANT, соответствующий параметру <paramref name="obj" />. </param>
      <typeparam name="T">Тип объекта для преобразования. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForIUnknown(System.IntPtr)">
      <summary>Возвращает экземпляр типа, представляющего объект COM с помощью указателя на его интерфейс IUnknown.</summary>
      <returns>Объект, представляющий указанный неуправляемый COM-объект.</returns>
      <param name="pUnk">Указатель на интерфейс IUnknown. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant(System.IntPtr)">
      <summary>Преобразует COM VARIANT в объект.</summary>
      <returns>Объект, соответствующий параметру <paramref name="pSrcNativeVariant" />.</returns>
      <param name="pSrcNativeVariant">Указатель на COM VARIANT.</param>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> не является допустимым типом VARIANT.</exception>
      <exception cref="T:System.NotSupportedException">Тип параметра <paramref name="pSrcNativeVariant" /> не поддерживается.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant``1(System.IntPtr)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Преобразует COM VARIANT в объект заданного типа. </summary>
      <returns>Объект указанного типа, соответствующий параметру <paramref name="pSrcNativeVariant" />. </returns>
      <param name="pSrcNativeVariant">Указатель на COM VARIANT. </param>
      <typeparam name="T">Тип, в который требуется преобразовать COM VARIANT. </typeparam>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> не является допустимым типом VARIANT. </exception>
      <exception cref="T:System.NotSupportedException">Тип параметра <paramref name="pSrcNativeVariant" /> не поддерживается. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants(System.IntPtr,System.Int32)">
      <summary>Преобразует массив элементов типа VARIANT модели COM в массив объектов. </summary>
      <returns>Массив объектов, соответствующий элементу <paramref name="aSrcNativeVariant" />.</returns>
      <param name="aSrcNativeVariant">Указатель на первый элемент массива типа COM VARIANT.</param>
      <param name="cVars">Число объектов типа COM VARIANT в <paramref name="aSrcNativeVariant" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="cVars" /> является отрицательным числом.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants``1(System.IntPtr,System.Int32)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Преобразует массив типа COM VARIANT в массив указанного типа. </summary>
      <returns>Массив объектов <paramref name="T" />, соответствующий элементу <paramref name="aSrcNativeVariant" />. </returns>
      <param name="aSrcNativeVariant">Указатель на первый элемент массива типа COM VARIANT. </param>
      <param name="cVars">Число объектов типа COM VARIANT в <paramref name="aSrcNativeVariant" />. </param>
      <typeparam name="T">Тип возвращаемого массива. </typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="cVars" /> является отрицательным числом. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetStartComSlot(System.Type)">
      <summary>Возвращает первую ячейку в таблице виртуальных функций (VTBL), которая содержит методы, определенные пользователем.</summary>
      <returns>Первая ячейка таблицы виртуальных функций (VTBL), содержащая методы, определенные пользователем.Первая ячейка содержит значение 3, если интерфейс основан на интерфейсе IUnknown, или значение 7, если он основан на интерфейсе IDispatch.</returns>
      <param name="t">Тип, представляющий интерфейс.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="t" /> недоступен из COM.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeFromCLSID(System.Guid)">
      <summary>Возвращает тип, связанный с заданным идентификатором класса (CLSID). </summary>
      <returns>System.__ComObject вне зависимости от того, допустим ли код CLSID. </returns>
      <param name="clsid">CLSID возвращаемого типа. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeInfoName(System.Runtime.InteropServices.ComTypes.ITypeInfo)">
      <summary>Извлекает имя типа, представленного объектом ITypeInfo.</summary>
      <returns>Имя типа, на который указывает параметр <paramref name="typeInfo" />.</returns>
      <param name="typeInfo">Объект, представляющий указатель ITypeInfo.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="typeInfo" /> — null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetUniqueObjectForIUnknown(System.IntPtr)">
      <summary>Создает уникальный объект оболочки RCW (Вызываемая оболочка времени выполнения) для заданного интерфейса IUnknown.</summary>
      <returns>Уникальная оболочка RCW для указанного интерфейса IUnknown.</returns>
      <param name="unknown">Управляемый указатель на интерфейс IUnknown.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.IsComObject(System.Object)">
      <summary>Показывает, представляет ли указанный объект COM-объект.</summary>
      <returns>Значение true, если параметр <paramref name="o" /> является COM-типом; в противном случае — значение false.</returns>
      <param name="o">Объект для проверки.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf``1(System.String)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Возвращает смещение поля для неуправляемой формы указанного управляемого класса.</summary>
      <returns>Смещение (в байтах) для параметра <paramref name="fieldName" /> в указанном классе, объявленном вызовом неуправляемого кода. </returns>
      <param name="fieldName">Имя поля в типе <paramref name="T" />. </param>
      <typeparam name="T">Управляемый тип значения или форматированный ссылочный тип.К классу должен применяться атрибут <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" />.</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf(System.Type,System.String)">
      <summary>Возвращает смещение поля для неуправляемой формы управляемого класса.</summary>
      <returns>Смещение (в байтах) для параметра <paramref name="fieldName" /> в указанном классе, объявленном вызовом неуправляемого кода.</returns>
      <param name="t">Тип значения или форматированный ссылочный тип, указывающий управляемый класс.К классу должен применяться <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" />.</param>
      <param name="fieldName">Поле внутри параметра <paramref name="t" />.</param>
      <exception cref="T:System.ArgumentException">Класс не может быть экспортирован как структура, или поле не является публичным.Начиная с версии 2.0 платформы .NET Framework поле может быть закрытым.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="t" /> — null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr)">
      <summary>Копирует все символы вплоть до первого пустого из неуправляемой строки ANSI в управляемый объект <see cref="T:System.String" /> и преобразует каждый символ ANSI в Юникод.</summary>
      <returns>Управляемая строка, содержащая копию неуправляемой строки ANSI.Если параметр <paramref name="ptr" /> равен null, метод возвращает пустую строку.</returns>
      <param name="ptr">Адрес первого символа в неуправляемой строке.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr,System.Int32)">
      <summary>Выделяет управляемый объект типа <see cref="T:System.String" />, копирует в него заданное число знаков из неуправляемой строки ANSI и преобразует каждый знак ANSI в Юникод.</summary>
      <returns>Управляемая строка, содержащая копию собственной строки ANSI, если значение параметра <paramref name="ptr" /> не равно null. В противном случае, этот метод возвращает значение null.</returns>
      <param name="ptr">Адрес первого символа в неуправляемой строке.</param>
      <param name="len">Копируемое количество байтов исходной строки.</param>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="len" /> меньше нуля.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringBSTR(System.IntPtr)">
      <summary>Выделяет управляемый объект <see cref="T:System.String" /> и копирует в него строку BSTR, хранящуюся в неуправляемой памяти.</summary>
      <returns>Управляемая строка, хранящая копию неуправляемой строки, если значение параметра <paramref name="ptr" /> не равно нулю null; в противном случае — значение null.</returns>
      <param name="ptr">Адрес первого символа в неуправляемой строке.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr)">
      <summary>Выделяет управляемый объект <see cref="T:System.String" /> и копирует в него все знаки до первого пустого знака из неуправляемой строки Юникода.</summary>
      <returns>Управляемая строка, хранящая копию неуправляемой строки, если значение параметра <paramref name="ptr" /> не равно нулю null; в противном случае — значение null.</returns>
      <param name="ptr">Адрес первого символа в неуправляемой строке.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr,System.Int32)">
      <summary>Выделяет управляемый объект <see cref="T:System.String" /> и копирует в него заданное число знаков неуправляемой строки Юникода.</summary>
      <returns>Управляемая строка, хранящая копию неуправляемой строки, если значение параметра <paramref name="ptr" /> не равно нулю null; в противном случае — значение null.</returns>
      <param name="ptr">Адрес первого символа в неуправляемой строке.</param>
      <param name="len">Число копируемых символов Юникода.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Маршалирует данные из неуправляемого блока памяти во вновь выделенный управляемый объект типа, указанного параметром универсального типа. </summary>
      <returns>Управляемый объект, содержащий данные, на которые указывает параметр <paramref name="ptr" />. </returns>
      <param name="ptr">Указатель на неуправляемый блок памяти. </param>
      <typeparam name="T">Тип объекта, в который копируются данные.Это должен быть форматированный класс или структура.</typeparam>
      <exception cref="T:System.ArgumentException">Макет <paramref name="T" /> не является ни последовательным, ни явным.</exception>
      <exception cref="T:System.MissingMethodException">Класс, Указанный <paramref name="T" /> не имеет доступного конструктора по умолчанию. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Object)">
      <summary>Маршалирует данные из неуправляемого блока памяти в управляемый объект.</summary>
      <param name="ptr">Указатель на неуправляемый блок памяти.</param>
      <param name="structure">Объект, в который копируются данные.Он должен представлять собой экземпляр форматированного класса.</param>
      <exception cref="T:System.ArgumentException">Распределение структуры не является ни последовательным, ни явным.-или- Структура принадлежит к упакованному типу значений.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Type)">
      <summary>Маршалирует данные из неуправляемого блока памяти во вновь выделенный управляемый объект указанного типа.</summary>
      <returns>Управляемый объект, содержащий данные, на которые указывает параметр <paramref name="ptr" />.</returns>
      <param name="ptr">Указатель на неуправляемый блок памяти.</param>
      <param name="structureType">Тип создаваемого объекта.Этот объект должен представлять форматированный класс или структуру.</param>
      <exception cref="T:System.ArgumentException">Распределения параметра <paramref name="structureType" />не является ни последовательным, ни явным.-или-Параметр <paramref name="structureType" /> является универсальным типом.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structureType" />is null.</exception>
      <exception cref="T:System.MissingMethodException">Класс, Указанный <paramref name="structureType" /> не имеет доступного конструктора по умолчанию. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr,``0)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Выполняет маршалирование данных из неуправляемого блока памяти в управляемый объекта указанного типа. </summary>
      <param name="ptr">Указатель на неуправляемый блок памяти. </param>
      <param name="structure">Объект, в который копируются данные. </param>
      <typeparam name="T">Тип <paramref name="structure" />.Этот должен быть форматированный класс.</typeparam>
      <exception cref="T:System.ArgumentException">Распределение структуры не является ни последовательным, ни явным. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.QueryInterface(System.IntPtr,System.Guid@,System.IntPtr@)">
      <summary>Запрашивает указатель на заданный интерфейс из COM-объекта.</summary>
      <returns>Значение HRESULT, показывающее, успешно ли выполнен вызов.</returns>
      <param name="pUnk">Запрашиваемый интерфейс.</param>
      <param name="iid">Идентификатор IID запрошенного интерфейса.</param>
      <param name="ppv">Когда этот метод возвращает результаты, в них содержится ссылка на возвращенный интерфейс.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr)">
      <summary>Считывает один байт из неуправляемой памяти.</summary>
      <returns>Байт, считанный из неуправляемой памяти.</returns>
      <param name="ptr">Адрес неуправляемой памяти, откуда производится чтение.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null. -или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr,System.Int32)">
      <summary>Считывает один байт с указанным смещением (или индексом) из неуправляемой памяти.</summary>
      <returns>Байт, считываемый из неуправляемой памяти с указанным смещением.</returns>
      <param name="ptr">Базовый адрес в неуправляемой памяти, относительно которого выполняется чтение.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед чтением.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.Object,System.Int32)">
      <summary>Считывает один байт с указанным смещением (или индексом) из неуправляемой памяти. </summary>
      <returns>Байт, считываемый из неуправляемой памяти с указанным смещением.</returns>
      <param name="ptr">Базовый адрес в неуправляемой памяти исходного объекта.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед чтением.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr)">
      <summary>Считывает из неуправляемой памяти 16-битное целое число со знаком.</summary>
      <returns>16-битное целое число со знаком, считанное из неуправляемой памяти.</returns>
      <param name="ptr">Адрес неуправляемой памяти, откуда производится чтение.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null.-или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr,System.Int32)">
      <summary>Считывает из неуправляемой памяти с указанным смещением 16-битное целое число со знаком.</summary>
      <returns>16-битное целое число со знаком, считанное из неуправляемой памяти с указанным смещением.</returns>
      <param name="ptr">Базовый адрес в неуправляемой памяти, относительно которого выполняется чтение.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед чтением.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.Object,System.Int32)">
      <summary>Считывает из неуправляемой памяти с указанным смещением 16-битное целое число со знаком.</summary>
      <returns>16-битное целое число со знаком, считанное из неуправляемой памяти с указанным смещением.</returns>
      <param name="ptr">Базовый адрес в неуправляемой памяти исходного объекта.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед чтением.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr)">
      <summary>Считывает из неуправляемой памяти 32-битное целое число со знаком.</summary>
      <returns>32-битное целое число со знаком, считанное из неуправляемой памяти.</returns>
      <param name="ptr">Адрес неуправляемой памяти, откуда производится чтение.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null.-или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr,System.Int32)">
      <summary>Считывает из неуправляемой памяти с указанным смещением 32-битное целое число со знаком.</summary>
      <returns>32-битное целое число со знаком, считанное из неуправляемой памяти.</returns>
      <param name="ptr">Базовый адрес в неуправляемой памяти, относительно которого выполняется чтение.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед чтением.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.Object,System.Int32)">
      <summary>Считывает из неуправляемой памяти с указанным смещением 32-битное целое число со знаком.</summary>
      <returns>32-битное целое число со знаком, считанное из неуправляемой памяти с указанным смещением.</returns>
      <param name="ptr">Базовый адрес в неуправляемой памяти исходного объекта.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед чтением.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr)">
      <summary>Считывает из неуправляемой памяти 64-битное целое число со знаком.</summary>
      <returns>64-битное целое число со знаком, считанное из неуправляемой памяти.</returns>
      <param name="ptr">Адрес неуправляемой памяти, откуда производится чтение.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null.-или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr,System.Int32)">
      <summary>Считывает из неуправляемой памяти с указанным смещением 64-битное целое число со знаком.</summary>
      <returns>64-битное целое число со знаком, считанное из неуправляемой памяти с указанным смещением.</returns>
      <param name="ptr">Базовый адрес в неуправляемой памяти, относительно которого выполняется чтение.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед чтением.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.Object,System.Int32)">
      <summary>Считывает из неуправляемой памяти с указанным смещением 64-битное целое число со знаком.</summary>
      <returns>64-битное целое число со знаком, считанное из неуправляемой памяти с указанным смещением.</returns>
      <param name="ptr">Базовый адрес в неуправляемой памяти исходного объекта.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед чтением.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr)">
      <summary>Считывает из неуправляемой памяти целое число, разрядность которого соответствует собственной разрядности процессора.</summary>
      <returns>Целое число, считанное из неуправляемой памяти.На 32-разрядных компьютерах возвращается 32-битное целое число, а на 64-разрядных компьютерах — 64-битное.</returns>
      <param name="ptr">Адрес неуправляемой памяти, откуда производится чтение.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null. -или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr,System.Int32)">
      <summary>Считывает из неуправляемой памяти с указанным смещением знаковое целое число, разрядность которого соответствует собственной разрядности процессора.</summary>
      <returns>Целое число, считываемое из неуправляемой памяти с указанным смещением.</returns>
      <param name="ptr">Базовый адрес в неуправляемой памяти, относительно которого выполняется чтение.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед чтением.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.Object,System.Int32)">
      <summary>Считывает из неуправляемой памяти целое число, разрядность которого соответствует собственной разрядности процессора.</summary>
      <returns>Целое число, считываемое из неуправляемой памяти с указанным смещением.</returns>
      <param name="ptr">Базовый адрес в неуправляемой памяти исходного объекта.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед чтением.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocCoTaskMem(System.IntPtr,System.Int32)">
      <summary>Изменяет размер блока памяти, предварительно выделенного с помощью <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" />.</summary>
      <returns>Целое число, представляющее адрес повторно выделенного блока памяти.Освобождать эту память необходимо с помощью метода <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />.</returns>
      <param name="pv">Указатель на память, выделенную с помощью метода <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" />.</param>
      <param name="cb">Новый размер выделенного блока.</param>
      <exception cref="T:System.OutOfMemoryException">Недостаточно памяти для выполнения запроса.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocHGlobal(System.IntPtr,System.IntPtr)">
      <summary>Изменяет размер блока памяти, предварительно выделенного с помощью <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />.</summary>
      <returns>Указатель на повторно выделенную память.Эта память должна быть освобождена при помощи метода <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />.</returns>
      <param name="pv">Указатель на память, выделенную с помощью метода <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />.</param>
      <param name="cb">Новый размер выделенного блока.Это не указатель; это запрашиваемое количество байтов, приведенное к типу <see cref="T:System.IntPtr" />.Если передается указатель, он рассматривается как размер.</param>
      <exception cref="T:System.OutOfMemoryException">Недостаточно памяти для выполнения запроса.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Release(System.IntPtr)">
      <summary>Уменьшает счетчик ссылок для указанного интерфейса.</summary>
      <returns>Новое значение счетчика ссылок для интерфейса, заданного параметром <paramref name="pUnk" />.</returns>
      <param name="pUnk">Освобождаемый интерфейс.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReleaseComObject(System.Object)">
      <summary>Уменьшает счетчик ссылок указанной оболочки RCW (Вызываемая оболочка времени выполнения), связанной с указанным COM-объектом.</summary>
      <returns>Новое значение счетчика ссылок оболочки среды RCW, связанной с параметром <paramref name="o" />.Это значение обычно равно нулю, поскольку оболочка RCW хранит только одну ссылку на COM-объект в оболочке вне зависимости от количества управляемых клиентов, которые ее вызывают.</returns>
      <param name="o">Освобождаемый COM-объект.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="o" /> не является допустимым COM-объектом.</exception>
      <exception cref="T:System.NullReferenceException">
        <paramref name="o" />is null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Возвращает размер неуправляемого типа в байтах. </summary>
      <returns>Размер, в байтах, типа, определяемого параметром универсального типа <paramref name="T" />. </returns>
      <typeparam name="T">Тип, размер которого возвращается. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Object)">
      <summary>Возвращает неуправляемый размер объекта в байтах.</summary>
      <returns>Размер указанного объекта в неуправляемом коде.</returns>
      <param name="structure">Объект, размер которого возвращается.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="structure" /> — null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Type)">
      <summary>Возвращает размер неуправляемого типа в байтах.</summary>
      <returns>Размер указанного типа в неуправляемом коде.</returns>
      <param name="t">Тип, размер которого возвращается.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="t" /> является универсальным типом.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="t" /> — null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1(``0)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Возвращает неуправляемый размер объекта указанного типа в байтах. </summary>
      <returns>Размер в байтах указанного объекта в неуправляемом коде. </returns>
      <param name="structure">Объект, размер которого возвращается. </param>
      <typeparam name="T">Тип параметра <paramref name="structure" />. </typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="structure" /> — null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToBSTR(System.String)">
      <summary>Выделяет строку BSTR и копирует в нее содержимое управляемого объекта <see cref="T:System.String" />.</summary>
      <returns>Неуправляемый указатель на строку BSTR или значение 0, если строка <paramref name="s" /> имеет значение null.</returns>
      <param name="s">Копируемая управляемая строка.</param>
      <exception cref="T:System.OutOfMemoryException">Недостаточно памяти.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Длина значения параметра <paramref name="s" /> вне диапазона.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemAnsi(System.String)">
      <summary>Копирует содержимое управляемого объекта типа <see cref="T:System.String" /> в блок памяти, выделенный из неуправляемого распределителя памяти для COM-задач.</summary>
      <returns>Целое число, представляющее указатель на блок памяти, выделенный для строки, или значение 0, если строка <paramref name="s" /> имеет значение null.</returns>
      <param name="s">Копируемая управляемая строка.</param>
      <exception cref="T:System.OutOfMemoryException">Недостаточно памяти.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="s" /> превышает максимальную длину, разрешенную операционной системой.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni(System.String)">
      <summary>Копирует содержимое управляемого объекта типа <see cref="T:System.String" /> в блок памяти, выделенный из неуправляемого распределителя памяти для COM-задач.</summary>
      <returns>Целое число, представляющее указатель на блок памяти, выделенный для строки, или значение 0, если строка s имеет значение null.</returns>
      <param name="s">Копируемая управляемая строка.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="s" /> превышает максимальную длину, разрешенную операционной системой.</exception>
      <exception cref="T:System.OutOfMemoryException">Недостаточно памяти.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalAnsi(System.String)">
      <summary>Копирует содержимое управляемого объекта <see cref="T:System.String" /> в неуправляемую память, преобразуя его по мере копирования в формат ANSI.</summary>
      <returns>Адрес в неуправляемой памяти, куда скопирована строка <paramref name="s" />, или значение 0, если строка <paramref name="s" /> имеет значение null.</returns>
      <param name="s">Копируемая управляемая строка.</param>
      <exception cref="T:System.OutOfMemoryException">Недостаточно памяти.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="s" /> превышает максимальную длину, разрешенную операционной системой.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalUni(System.String)">
      <summary>Копирует содержимое управляемого объекта <see cref="T:System.String" /> в неуправляемую память.</summary>
      <returns>Адрес в неуправляемой памяти, куда скопирована строка <paramref name="s" />, или значение 0, если строка <paramref name="s" /> имеет значение null.</returns>
      <param name="s">Копируемая управляемая строка.</param>
      <exception cref="T:System.OutOfMemoryException">Метод не может выделить в куче достаточное количество динамической памяти, присущей данному объекту.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="s" /> превышает максимальную длину, разрешенную операционной системой.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr(System.Object,System.IntPtr,System.Boolean)">
      <summary>Маршалирует данные из управляемого объекта в неуправляемый блок памяти.</summary>
      <param name="structure">Управляемый объект, содержащий данные для маршалинга.Этот объект должен представлять собой структуру или экземпляр форматированного класса.</param>
      <param name="ptr">Указатель на неуправляемый блок памяти, который должен быть выделен перед вызовом метода.</param>
      <param name="fDeleteOld">Значение true для вызова метода <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)" /> в параметре <paramref name="ptr" /> перед тем, как этот метод скопирует данные.Блок должен содержать допустимые данные.Обратите внимание, что передача false, когда блок памяти уже содержит данные, может привести к утечке памяти.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> — ссылочный тип, который не является форматированным классом. -или-<paramref name="structure" /> является универсальным типом. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr``1(``0,System.IntPtr,System.Boolean)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Выполняет маршалирование данных в неуправляемый блока памяти из управляемого объекта указанного типа. </summary>
      <param name="structure">Управляемый объект, содержащий данные для маршалинга.Объект должен представлять собой структуру или экземпляр форматированного класса.</param>
      <param name="ptr">Указатель на неуправляемый блок памяти, который должен быть выделен перед вызовом метода. </param>
      <param name="fDeleteOld">Значение true для вызова метода <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)" /> в параметре <paramref name="ptr" /> перед тем, как этот метод скопирует данные.Блок должен содержать допустимые данные.Обратите внимание, что передача false, когда блок памяти уже содержит данные, может привести к утечке памяти.</param>
      <typeparam name="T">Тип управляемого объекта. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> — ссылочный тип, который не является форматированным классом. </exception>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemDefaultCharSize">
      <summary>Представляет используемый по умолчанию размер символа в системе. По умолчанию для систем Юникода задается значение 2, а для систем ANSI значение 1.Это поле доступно только для чтения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemMaxDBCSCharSize">
      <summary>Представляет наибольший размер набора двухбайтовых символов (DBCS) в байтах для текущей операционной системы.Это поле доступно только для чтения.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32)">
      <summary>Создает исключение с определенным значением ошибки HRESULT.</summary>
      <param name="errorCode">Значение HRESULT, соответствующее нужному исключению.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)">
      <summary>Выдает исключение с определенным значением HRESULT, обозначающим сбой, в соответствии с указанным интерфейсом IErrorInfo.</summary>
      <param name="errorCode">Значение HRESULT, соответствующее нужному исключению.</param>
      <param name="errorInfo">Указатель на интерфейс IErrorInfo, предоставляющий дополнительные сведения об ошибке.Можно задать IntPtr(0), чтобы использовать текущий интерфейс IErrorInfo, или IntPtr(-1), чтобы игнорировать текущий интерфейс IErrorInfo и создать исключение только из кода ошибки.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement(System.Array,System.Int32)">
      <summary>Возвращает адрес элемента по указанному индексу внутри заданного массива.</summary>
      <returns>Адрес параметра <paramref name="index" /> в параметре <paramref name="arr" />.</returns>
      <param name="arr">Массив, содержащий требуемый элемент.</param>
      <param name="index">Индекс необходимого элемента в параметре <paramref name="arr" />.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement``1(``0[],System.Int32)">
      <summary>[Поддерживается только в .NET Framework 4.5.1 и более поздних версиях] Возвращает адрес элемента по указанному индексу внутри массива заданного типа. </summary>
      <returns>Адрес параметра <paramref name="index" /> в параметре <paramref name="arr" />. </returns>
      <param name="arr">Массив, содержащий требуемый элемент. </param>
      <param name="index">Индекс необходимого элемента в массиве <paramref name="arr" />. </param>
      <typeparam name="T">Тип данного массива. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Byte)">
      <summary>Записывает однобайтовое значение в неуправляемую память.</summary>
      <param name="ptr">Адрес в неуправляемой памяти, по которому производится запись.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null.-или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Int32,System.Byte)">
      <summary>Записывает однобайтовое значение в неуправляемую память с указанным смещением.</summary>
      <param name="ptr">Базовый адрес для записи в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.Object,System.Int32,System.Byte)">
      <summary>Записывает однобайтовое значение в неуправляемую память с указанным смещением.</summary>
      <param name="ptr">Базовый адрес конечного объекта в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Char)">
      <summary>Записывает в неуправляемую память символ в виде 16-битного целого числа.</summary>
      <param name="ptr">Адрес в неуправляемой памяти, по которому производится запись.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null.-или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int16)">
      <summary>Записывает в неуправляемую память 16-битное целое число.</summary>
      <param name="ptr">Адрес в неуправляемой памяти, по которому производится запись.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null.-или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Char)">
      <summary>Записывает 16-битное целое число со знаком в неуправляемую память с указанным смещением.</summary>
      <param name="ptr">Базовый адрес для записи в собственной куче.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Int16)">
      <summary>Записывает 16-битное целое число со знаком в неуправляемую память с указанным смещением.</summary>
      <param name="ptr">Базовый адрес для записи в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Char)">
      <summary>Записывает 16-битное целое число со знаком в неуправляемую память с указанным смещением.</summary>
      <param name="ptr">Базовый адрес конечного объекта в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Int16)">
      <summary>Записывает 16-битное целое число со знаком в неуправляемую память с указанным смещением.</summary>
      <param name="ptr">Базовый адрес конечного объекта в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью. </param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32)">
      <summary>Записывает в неуправляемую память 32-битное целое число со знаком.</summary>
      <param name="ptr">Адрес в неуправляемой памяти, по которому производится запись.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null. -или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32,System.Int32)">
      <summary>Записывает 32-битное целое число со знаком в неуправляемую память с указанным смещением.</summary>
      <param name="ptr">Базовый адрес для записи в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.Object,System.Int32,System.Int32)">
      <summary>Записывает 32-битное целое число со знаком в неуправляемую память с указанным смещением.</summary>
      <param name="ptr">Базовый адрес конечного объекта в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int32,System.Int64)">
      <summary>Записывает 64-битное целое число со знаком в неуправляемую память с указанным смещением.</summary>
      <param name="ptr">Базовый адрес для записи в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int64)">
      <summary>Записывает в неуправляемую память 64-битное целое число со знаком.</summary>
      <param name="ptr">Адрес в неуправляемой памяти, по которому производится запись.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null.-или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.Object,System.Int32,System.Int64)">
      <summary>Записывает 64-битное целое число со знаком в неуправляемую память с указанным смещением.</summary>
      <param name="ptr">Базовый адрес конечного объекта в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.Int32,System.IntPtr)">
      <summary>Записывает в неуправляемую память с указанным смещением целое число, разрядность которого соответствует собственной разрядности процессора.</summary>
      <param name="ptr">Базовый адрес для записи в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.IntPtr)">
      <summary>Записывает в неуправляемую память целое число, разрядность которого соответствует собственной разрядности процессора.</summary>
      <param name="ptr">Адрес в неуправляемой памяти, по которому производится запись.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> не является распознаваемым форматом.-или-<paramref name="ptr" />is null.-или-Значение параметра <paramref name="ptr" /> недопустимо.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.Object,System.Int32,System.IntPtr)">
      <summary>Записывает в неуправляемую память целое число, разрядность которого соответствует собственной разрядности процессора.</summary>
      <param name="ptr">Базовый адрес конечного объекта в неуправляемой памяти.</param>
      <param name="ofs">Дополнительное смещение байтов, добавляемое к параметру <paramref name="ptr" /> перед записью.</param>
      <param name="val">Значение для записи.</param>
      <exception cref="T:System.AccessViolationException">Сумма базового адреса (<paramref name="ptr" />) и байта смещения (<paramref name="ofs" />) дает значение null или недопустимый адрес.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="ptr" /> является объектом <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Этот метод не принимает параметры <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(System.IntPtr)">
      <summary>Освобождает указатель BSTR, выделенный с помощью метода <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToBSTR(System.Security.SecureString)" />.</summary>
      <param name="s">Адрес освобождаемой строки BSTR.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemAnsi(System.IntPtr)">
      <summary>Освобождает указатель на неуправляемую строку, выделенный с помощью метода <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemAnsi(System.Security.SecureString)" />.</summary>
      <param name="s">Адрес освобождаемой неуправляемой строки.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemUnicode(System.IntPtr)">
      <summary>Освобождает указатель на неуправляемую строку, выделенный с помощью метода <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemUnicode(System.Security.SecureString)" />.</summary>
      <param name="s">Адрес освобождаемой неуправляемой строки.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocAnsi(System.IntPtr)">
      <summary>Освобождает указатель на неуправляемую строку, выделенный с помощью метода <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocAnsi(System.Security.SecureString)" />.</summary>
      <param name="s">Адрес освобождаемой неуправляемой строки.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocUnicode(System.IntPtr)">
      <summary>Освобождает указатель на неуправляемую строку, выделенный с помощью метода <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocUnicode(System.Security.SecureString)" />.</summary>
      <param name="s">Адрес освобождаемой неуправляемой строки.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalAsAttribute">
      <summary>Показывает, как маршалировать данных между управляемым и неуправляемым кодом.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Int16)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> с заданным значением <see cref="T:System.Runtime.InteropServices.UnmanagedType" />.</summary>
      <param name="unmanagedType">Значение, в виде которого будут маршалированы данные. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Runtime.InteropServices.UnmanagedType)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> с заданным элементом перечисления <see cref="T:System.Runtime.InteropServices.UnmanagedType" />.</summary>
      <param name="unmanagedType">Значение, в виде которого будут маршалированы данные. </param>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType">
      <summary>Задает тип элемента неуправляемого массива <see cref="F:System.Runtime.InteropServices.UnmanagedType.LPArray" /> или <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValArray" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.IidParameterIndex">
      <summary>Задает индекс параметра неуправляемого атрибута iid_is, используемого в COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalCookie">
      <summary>Предоставляет дополнительные сведения для настраиваемого модуля маршалинга.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType">
      <summary>Задает полное имя настраиваемого модуля маршалинга.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalTypeRef">
      <summary>Реализует поле <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType" /> в виде типа.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType">
      <summary>Показывает тип элемента для поля <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArrayUserDefinedSubType">
      <summary>Указывает определенный пользователем тип элемента для поля <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst">
      <summary>Показывает количество элементов в массиве фиксированной длины или количество знаков (не байтов) в импортируемой строке.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex">
      <summary>Указывает параметр, содержащий отсчитываемое от нуля количество элементов массива, аналогичный параметру size_is в COM.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.MarshalAsAttribute.Value">
      <summary>Получает значение <see cref="T:System.Runtime.InteropServices.UnmanagedType" />, используемое для маршалинга данных.</summary>
      <returns>Значение типа <see cref="T:System.Runtime.InteropServices.UnmanagedType" />, используемого для маршалинга данных.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalDirectiveException">
      <summary>Исключение, которое создается модулем упаковки и передачи, когда он встречает неподдерживаемый атрибут <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor">
      <summary>Инициализирует новый экземпляр класса MarshalDirectiveException со свойствами по умолчанию.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String)">
      <summary>Выполняет инициализацию нового экземпляра класса MarshalDirectiveException с заданным сообщением об ошибке.</summary>
      <param name="message">Сообщение об ошибке с указанием причин исключения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.MarshalDirectiveException" /> с заданным сообщением об ошибке и ссылкой на внутреннее исключение, которое стало причиной данного исключения.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
      <param name="inner">Исключение, которое вызвало текущее исключение.Если значение параметра <paramref name="inner" /> не равно null, текущее исключение вызывается в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.OptionalAttribute">
      <summary>Указывает, что данный параметр необязателен.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.OptionalAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса OptionalAttribute со значениями по умолчанию.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.PreserveSigAttribute">
      <summary>Показывает, что преобразование значения HRESULT или сигнатуры retval, происходящее в процессе вызовов COM-взаимодействия, следует запретить.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.PreserveSigAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.PreserveSigAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayRankMismatchException">
      <summary>Это исключение возникает, если ранг входного массива SAFEARRAY не совпадает с рангом, указанным в управляемой подписи.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor">
      <summary>Инициализирует новый экземпляр класса SafeArrayTypeMismatchException значениями по умолчанию.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса SafeArrayRankMismatchException, используя заданное сообщение.</summary>
      <param name="message">Сообщение, в котором указывается причина исключения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.SafeArrayRankMismatchException" /> с указанным сообщением об ошибке и ссылкой на внутреннее исключение, вызвавшее это исключение.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
      <param name="inner">Исключение, которое вызвало текущее исключение.Если значение параметра <paramref name="inner" /> не равно null, текущее исключение вызывается в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException">
      <summary>Исключение создается, если тип входящего SAFEARRAY не совпадает с типом, указанным в управляемой подписи.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor">
      <summary>Инициализирует новый экземпляр класса SafeArrayTypeMismatchException значениями по умолчанию.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса SafeArrayTypeMismatchException, используя заданное сообщение.</summary>
      <param name="message">Сообщение, в котором указывается причина исключения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException" /> с указанным сообщением об ошибке и ссылкой на внутреннее исключение, вызвавшее это исключение.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
      <param name="inner">Исключение, которое вызвало текущее исключение.Если значение параметра <paramref name="inner" /> не равно null, текущее исключение вызывается в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeBuffer">
      <summary>Предоставляет управляемый буфер памяти с возможностью чтения и записи.Попытки доступа к памяти за пределами управляемого буфера (выше и ниже границ его диапазона) приводят к исключениям.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.#ctor(System.Boolean)">
      <summary>Создает новый экземпляр класса <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> и указывает, должен ли быть дескриптор буфера надежно освобожден. </summary>
      <param name="ownsHandle">Значение true, чтобы наверняка освободить дескриптор на стадии завершения; в противном случае — значение false (не рекомендуется).</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)">
      <summary>Получает из объекта <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> указатель для блока памяти.</summary>
      <param name="pointer">Указатель байта, передаваемый по ссылке, для получения указателя из объекта <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.Необходимо присвоить этому указателю значение null, прежде чем вызывать этот метод.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> не был вызван. </exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.ByteLength">
      <summary>Получает размер буфера (в байтах).</summary>
      <returns>Число байтов в буфере памяти.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> не был вызван.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize``1(System.UInt32)">
      <summary>Определяет размер выделяемой области памяти путем указания количества значений и их типа.Данный метод необходимо вызвать перед использованием экземпляра <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numElements">Количество элементов этого типа значения, под которое выделяется память.</param>
      <typeparam name="T">Тип значения, под который выделяется память.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="numElements" /> меньше нуля.– или –Произведение <paramref name="numElements" /> и размера каждого элемента превышает объем доступного адресного пространства.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt32,System.UInt32)">
      <summary>Задает размер выделяемого буфера памяти, используя указанное количество элементов и размер элемента.Данный метод необходимо вызвать перед использованием экземпляра <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numElements">Количество элементов в буфере.</param>
      <param name="sizeOfEachElement">Размер каждого элемента в буфере.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="numElements" /> меньше нуля. – или –Значение параметра <paramref name="sizeOfEachElement" /> меньше нуля.– или –Произведение <paramref name="numElements" /> и <paramref name="sizeOfEachElement" /> превышает объем доступного адресного пространства.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt64)">
      <summary>Определяет размер выделяемой области памяти (в байтах).Данный метод необходимо вызвать перед использованием экземпляра <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numBytes">Количество байтов в буфере.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="numBytes" /> меньше нуля.– или –<paramref name="numBytes" /> больше доступного адресного пространства.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.IsInvalid"></member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Read``1(System.UInt64)">
      <summary>Считывает из памяти тип значения с указанным смещением.</summary>
      <returns>Тип значения, считанный из памяти.</returns>
      <param name="byteOffset">Расположение, из которого считывается тип значения.Может потребоваться продумать проблемы выравнивания.</param>
      <typeparam name="T">Считываемый тип значения.</typeparam>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> не был вызван.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReadArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>Считывает из памяти указанное количество типов значений, начиная указанного смещения, и записывает их в массив, начиная с указанного индекса. </summary>
      <param name="byteOffset">Расположение, с которого начинается считывание.</param>
      <param name="array">Выходной массив для записи.</param>
      <param name="index">Расположение в выходном массиве, с которого начинается запись.</param>
      <param name="count">Количество типов значений, считываемых из входного массива и записываемых в выходной массив.</param>
      <typeparam name="T">Считываемый тип значения.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.– или –Значение параметра <paramref name="count" /> меньше нуля.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Длина массива за вычетом индекса меньше значения <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> не был вызван.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReleasePointer">
      <summary>Высвобождает указатель, полученный методом <see cref="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)" />.</summary>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> не был вызван.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Write``1(System.UInt64,``0)">
      <summary>Записывает тип значения в память в указанном расположении.</summary>
      <param name="byteOffset">Расположение, с которого начинается запись.Может потребоваться продумать проблемы выравнивания.</param>
      <param name="value">Записываемое значение.</param>
      <typeparam name="T">Записываемый тип значения.</typeparam>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> не был вызван.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.WriteArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>Записывает указанное количество типов значений в указанное расположение в памяти, считывая байты входного массива, начиная с указанного расположения.</summary>
      <param name="byteOffset">Расположение в памяти, в которое выполняется запись.</param>
      <param name="array">Входной массив.</param>
      <param name="index">Смещение в этом массиве, с которого начинается считывание.</param>
      <param name="count">Число записываемых типов значений.</param>
      <typeparam name="T">Записываемый тип значения.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> или <paramref name="count" /> меньше нуля.</exception>
      <exception cref="T:System.ArgumentException">Длина входного массива за вычетом <paramref name="index" /> меньше <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> не был вызван.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.SEHException">
      <summary>Представляет ошибки структурной обработки исключений (SEH). </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.SEHException" />. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.SEHException" /> с заданным сообщением.</summary>
      <param name="message">Сообщение, в котором указывается причина исключения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String,System.Exception)">
      <summary>Выполняет инициализацию нового экземпляра класса <see cref="T:System.Runtime.InteropServices.SEHException" /> с заданным сообщением об ошибке и ссылкой на внутреннее исключение, которое стало причиной данного исключения.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
      <param name="inner">Исключение, которое вызвало текущее исключение.Если значение параметра <paramref name="inner" /> не равно null, текущее исключение вызывается в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.CanResume">
      <summary>Показывает, возможно ли восстановление после исключения и возможно ли продолжить выполнение кода с точки возникновения этого исключения.</summary>
      <returns>Всегда имеет значение false, поскольку исключения с возможностью восстановления не реализованы.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.TypeIdentifierAttribute">
      <summary>Обеспечивает поддержку эквивалентности типов.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor">
      <summary>Создание нового экземпляра класса <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" />. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)">
      <summary>Создает новый экземпляр класса <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> с указанными областью и идентификатором. </summary>
      <param name="scope">Первая строка эквивалентности типов.</param>
      <param name="identifier">Вторая строка эквивалентности типов.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Identifier">
      <summary>Получает значение параметра <paramref name="identifier" />, переданного конструктору <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" />.</summary>
      <returns>Значение параметра <paramref name="identifier" /> конструктора.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Scope">
      <summary>Получает значение параметра <paramref name="scope" />, переданного конструктору <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" />.</summary>
      <returns>Значение параметра <paramref name="scope" /> конструктора.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnknownWrapper">
      <summary>Инкапсулирует объекты, которые необходимо маршалировать, как VT_UNKNOWN.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnknownWrapper.#ctor(System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.UnknownWrapper" /> инкапсулируемым в обертку объектом.</summary>
      <param name="obj">Инкапсулируемый в обертку объект. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.UnknownWrapper.WrappedObject">
      <summary>Возвращает объект, содержащийся в этой обертке.</summary>
      <returns>Инкапсулированный в обертку объект.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute">
      <summary>Управляет поведением при маршалинге сигнатуры делегата, передаваемой как неуправляемый указатель на функцию в неуправляемый код или из него.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute" /> с заданным соглашением о вызове. </summary>
      <param name="callingConvention">Заданное соглашение о вызове.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.BestFitMapping">
      <summary>Включает или отключает поведение наилучшего сопоставления при преобразовании знаков Юникода в знаки ANSI.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CallingConvention">
      <summary>Возвращает значение соглашения о вызове.</summary>
      <returns>Значение соглашения о вызове, указанное конструктором <see cref="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)" />.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CharSet">
      <summary>Указывает способ маршалинга параметров строки для метода, а также управляет искажением имени.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.SetLastError">
      <summary>Показывает, вызывает ли вызываемый объект функцию SetLastError интерфейса Win32 API перед возвращением из метода, использующего атрибуты.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.ThrowOnUnmappableChar">
      <summary>Включает и отключает возникновение исключений для неотображаемых символов Юникода, преобразующихся в знаки вопроса ("?") ANSI.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedType">
      <summary>Определяет способ маршалинга параметров или полей в неуправляемый код. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AnsiBStr">
      <summary>Строка однобайтовых знаков ANSI с префиксом, обозначающим ее длину.Этот элемент можно использовать для типа данных <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AsAny">
      <summary>Динамический тип, который определяет тип объекта во время выполнения и маршалирует объект как объекта данного типа.Этот член является допустимым только для методов вызова неуправляемого кода.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Bool">
      <summary>4-байтовое логическое значение (true != 0, false = 0).Это тип BOOL платформы Win32.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.BStr">
      <summary>Двухбайтовая строка символов Юникода с префиксом, обозначающим ее длину.Этот элемент, являющийся строкой, определенной в COM по умолчанию, можно использовать для типа данных <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValArray">
      <summary>Если свойство <see cref="P:System.Runtime.InteropServices.MarshalAsAttribute.Value" /> равно ByValArray, то для указания числа элементов массива следует задать поле <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" />.Если требуется различать типы строк, поле <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType" /> может содержать тип <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> элементов массива.<see cref="T:System.Runtime.InteropServices.UnmanagedType" /> можно использовать только для массива, элементы которого отображаются как поля в структуре.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr">
      <summary>Используется для встроенных массивов знаков фиксированной длины, появляющихся в структуре.Тип символов, используемый с <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr" />, определяется аргументом <see cref="T:System.Runtime.InteropServices.CharSet" /> атрибута <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" />, примененным к содержащей его структуре.Для задания размера массива всегда следует использовать поле <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Currency">
      <summary>Тип валюты.Используется в <see cref="T:System.Decimal" /> для маршалинга десятичного значения как типа денежной единицы COM, вместо Decimal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Error">
      <summary>Собственный тип, связанный с <see cref="F:System.Runtime.InteropServices.UnmanagedType.I4" /> или <see cref="F:System.Runtime.InteropServices.UnmanagedType.U4" />, который приводит к экспорту параметра как значения HRESULT в экспортированной библиотеке типов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.FunctionPtr">
      <summary>Целое число, которое может использоваться как указатель функции в стиле языка С.Этот член можно использовать для типа данных <see cref="T:System.Delegate" /> или типа, наследуемого от <see cref="T:System.Delegate" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.HString">
      <summary>Строка Среда выполнения Windows.Этот элемент можно использовать для типа данных <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I1">
      <summary>1-байтовое целое число со знаком.Этот элемент можно использовать для преобразования значения типа Boolean в 1-байтовое значение bool в стиле языка C (true = 1, false = 0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I2">
      <summary>2-байтовое целое число со знаком.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I4">
      <summary>4-байтовое целое число со знаком.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I8">
      <summary>8-байтовое целое число со знаком.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IDispatch">
      <summary>Указатель COM IDispatch (Object в Microsoft Visual Basic 6.0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IInspectable">
      <summary>Указатель интерфейса Среда выполнения Windows.Этот элемент можно использовать для типа данных <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Interface">
      <summary>Указатель интерфейса COM.Идентификатор <see cref="T:System.Guid" /> интерфейса получен из класса метаданных.Этот элемент можно использовать для указания точного типа интерфейса или типа интерфейса, используемого по умолчанию при применении к классу.Этот элемент определяет такое же поведение, что и <see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" />, при его применении к типу данных <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IUnknown">
      <summary>Указатель COM IUnknown.Этот элемент можно использовать для типа данных <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPArray">
      <summary>Указатель на первый элемент массива в стиле языка C.При маршалинге из управляемого кода в неуправляемый длина массива определяется исходя из длины управляемого массива.При маршалинге из неуправляемого кода в управляемый длина массива определяется исходя из значений полей <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> и <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex" />, за которыми, если требуется различать тип строк, следует тип элементов неуправляемого массива.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStr">
      <summary>Однобайтовая строка знаков ANSI, заканчивающаяся нулем.Этот член можно использовать для типов данных <see cref="T:System.String" /> и <see cref="T:System.Text.StringBuilder" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStruct">
      <summary>Указатель на структуру в стиле языка C, используемую для маршалинга управляемых форматируемых классов.Этот член является допустимым только для методов вызова неуправляемого кода.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPTStr">
      <summary>Строка знаков, зависящая от платформы: ANSI для Windows 98 и Юникод для Windows NT и Windows XP.Это значение поддерживается только для вызова неуправляемого кода и не используется для COM-взаимодействия, поскольку экспортирование строки типа LPTStr не поддерживается.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPWStr">
      <summary>2-байтовая строка символов Юникода, заканчивающаяся нулем.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R4">
      <summary>4-байтовое число с плавающей запятой.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R8">
      <summary>8-байтовое число с плавающей запятой.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SafeArray">
      <summary>SafeArray — это описывающий сам себя массив, передающий тип, ранг и границы соответствующего массива данных.Этот элемент можно использовать с полем <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType" />, чтобы переопределить тип элемента, заданный по умолчанию.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Struct">
      <summary>Значение типа VARIANT, используемое для маршалинга управляемых форматируемых классов и типов значений.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysInt">
      <summary>Платформозависимое знаковое целое число: 4 байта в 32-разрядной Windows, 8 байт в 64-разрядной Windows.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysUInt">
      <summary>Платформозависимое знаковое целое число без знака: 4 байта в 32-разрядной Windows, 8 байт в 64-разрядной Windows.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.TBStr">
      <summary>Зависящая от платформы строка char с префиксом: ANSI для Windows 98 и Юникод для Windows NT.Этот элемент, аналогичный BSTR, используется редко.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U1">
      <summary>1-байтовое целое число без знака.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U2">
      <summary>2-байтовое целое число без знака.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U4">
      <summary>4-байтовое целое число без знака.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U8">
      <summary>8-байтовое целое число без знака.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VariantBool">
      <summary>2-байтовое значение типа VARIANT_BOOL, определенное OLE (true = -1, false = 0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VBByRefStr">
      <summary>Значение, позволяющее Visual Basic изменять строку в неуправляемом коде и получать результаты, отраженные в управляемом коде.Это значение поддерживается только для вызова неуправляемого кода.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VarEnum">
      <summary>Показывает способ маршалинга элементов массива при маршалинге массива из управляемого кода в неуправляемый код как массива <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ARRAY">
      <summary>Показывает указатель SAFEARRAY.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB">
      <summary>Показывает длину префикса в байтах.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB_OBJECT">
      <summary>Показывает, что BLOB-объект содержит объект.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BOOL">
      <summary>Показывает логическое значение (Boolean).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BSTR">
      <summary>Показывает строку BSTR.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BYREF">
      <summary>Показывает, что значение является ссылкой.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CARRAY">
      <summary>Показывает массив в стиле языка C.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CF">
      <summary>Показывает формат буфера.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CLSID">
      <summary>Показывает идентификатор ID класса.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CY">
      <summary>Показывает денежное значение.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DATE">
      <summary>Показывает значение DATE.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DECIMAL">
      <summary>Показывает значение decimal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH">
      <summary>Показывает указатель IDispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_EMPTY">
      <summary>Показывает, что значение не задано.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ERROR">
      <summary>Показывает SCODE.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_FILETIME">
      <summary>Показывает значение FILETIME.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_HRESULT">
      <summary>Показывает значение HRESULT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I1">
      <summary>Показывает значение char.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I2">
      <summary>Показывает целое число short.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I4">
      <summary>Показывает целое число long.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I8">
      <summary>Показывает 64-разрядное целое число.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_INT">
      <summary>Показывает целочисленное значение.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPSTR">
      <summary>Показывает строку, заканчивающуюся нулем.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPWSTR">
      <summary>Показывает двухбайтную строку, завершенную null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_NULL">
      <summary>Показывает значение null, аналогичное значению null в SQL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_PTR">
      <summary>Показывает тип указателя.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R4">
      <summary>Показывает значение float.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R8">
      <summary>Показывает значение double.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_RECORD">
      <summary>Показывает тип, определенный пользователем.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_SAFEARRAY">
      <summary>Показывает SAFEARRAY.Не допустимо для значений типа VARIANT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORAGE">
      <summary>Показывает, что далее следует имя хранилища.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORED_OBJECT">
      <summary>Показывает, что хранилище содержит объект.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAM">
      <summary>Показывает, что далее следует имя потока.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAMED_OBJECT">
      <summary>Показывает, что поток содержит объект.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI1">
      <summary>Показывает byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI2">
      <summary>Показывает unsignedshort.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI4">
      <summary>Показывает unsignedlong.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI8">
      <summary>Показывает 64-разрядное целое число без знака.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UINT">
      <summary>Показывает целое число unsigned.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UNKNOWN">
      <summary>Показывает указатель IUnknown.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_USERDEFINED">
      <summary>Показывает тип, определенный пользователем.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VARIANT">
      <summary>Показывает указатель far типа VARIANT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VECTOR">
      <summary>Показывает одномерный сосчитанный массив.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VOID">
      <summary>Показывает void в стиле языка C.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VariantWrapper">
      <summary>Маршалирует данные типа VT_VARIANT | VT_BYREF из управляемого кода в неуправляемый.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.VariantWrapper.#ctor(System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> для заданного параметра <see cref="T:System.Object" />.</summary>
      <param name="obj">Объект для маршалинга. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.VariantWrapper.WrappedObject">
      <summary>Возвращает объект, инкапсулированный с помощью объекта <see cref="T:System.Runtime.InteropServices.VariantWrapper" />.</summary>
      <returns>Объект, инкапсулированный с помощью объекта <see cref="T:System.Runtime.InteropServices.VariantWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ADVF">
      <summary>Задает требуемое поведение при настройке приемника уведомления или подключения кэширования с объектом.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_DATAONSTOP">
      <summary>При использовании вспомогательных соединений с данными обеспечивает доступ к данным. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_NODATA">
      <summary>При использовании вспомогательных соединений с данными (<see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.DAdvise(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.ADVF,System.Runtime.InteropServices.ComTypes.IAdviseSink,System.Int32@)" /> или <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />) этот флаг требует, чтобы объект данных не отправлял данные, когда вызывается <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_ONLYONCE">
      <summary>Требует, чтобы объект создавал только одно уведомление об изменении или обновление кэша перед удалением соединения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_PRIMEFIRST">
      <summary>Требует, чтобы объект не ждал изменения данных или представления до выполнения начального вызова <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> (для вспомогательных соединений с данными или представлениями) или обновления кэша (для соединений с кэшем).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN">
      <summary>Это значение используется приложениями объектов DLL и дескрипторами объектов, которые выполняют рисунок своих объектов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_NOHANDLER">
      <summary>Синоним для <see cref="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN" />, который используется более часто.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_ONSAVE">
      <summary>Если используются соединения с кэшем, этот флаг обновляет кэшированное представление только при сохранении объекта, содержащего кэш.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BIND_OPTS">
      <summary>Сохраняет параметры, используемые в операции привязки монкера.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.cbStruct">
      <summary>Задает размер структуры BIND_OPTS в байтах.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.dwTickCountDeadline">
      <summary>Показывает время (значение в миллисекундах, возвращенное функцией GetTickCount), заданное вызывающим объектом для завершения операции привязки.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfFlags">
      <summary>Управляет элементами операций привязки моникера.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfMode">
      <summary>Представляет флаги, которые должны использоваться при открытии файла, содержащего объект, определенный моникером.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BINDPTR">
      <summary>Содержит указатель на связанную структуру <see cref="T:System.Runtime.InteropServices.FUNCDESC" />, структуру <see cref="T:System.Runtime.InteropServices.VARDESC" /> или интерфейс ITypeComp.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpfuncdesc">
      <summary>Представляет указатель на структуру <see cref="T:System.Runtime.InteropServices.FUNCDESC" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lptcomp">
      <summary>Представляет указатель на интерфейс <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeComp" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpvardesc">
      <summary>Представляет указатель на структуру <see cref="T:System.Runtime.InteropServices.VARDESC" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CALLCONV">
      <summary>Определяет соглашение о вызове, используемое методом, описанным в структуре METHODDATA.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_CDECL">
      <summary>Указывает на использование для данного метода соглашения о вызове CDECL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MACPASCAL">
      <summary>Указывает на использование для данного метода соглашения о вызове Macintosh Pascal (MACPASCAL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MAX">
      <summary>Указывает окончание перечисления <see cref="T:System.Runtime.InteropServices.ComTypes.CALLCONV" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWCDECL">
      <summary>Указывает на использование для данного метода соглашения о вызове Macintosh Programmers' Workbench (MPW) CDECL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWPASCAL">
      <summary>Указывает на использование для данного метода соглашения о вызове Macintosh Programmers' Workbench (MPW) PASCAL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MSCPASCAL">
      <summary>Указывает на использование для данного метода соглашения о вызове MSC Pascal (MSCPASCAL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_PASCAL">
      <summary>Указывает на использование для данного метода соглашения о вызове Pascal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_RESERVED">
      <summary>Это значение зарезервировано для использования в будущем.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_STDCALL">
      <summary>Указывает на использование для данного метода стандартного соглашения о вызове (STDCALL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_SYSCALL">
      <summary>Указывает на использование для данного метода стандартного соглашения о вызове SYSCALL.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CONNECTDATA">
      <summary>Описывает соединение к заданной точке подключения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.dwCookie">
      <summary>Представляет маркер соединения, возвращаемый из вызова <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.pUnk">
      <summary>Предоставляет указатель на интерфейс IUnknown подключенного вспомогательного приемника.Когда необходимость в структуре CONNECTDATA отпадет, Вызывающий объект должен вызвать IUnknown::Release для этого указателя.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DATADIR">
      <summary>Задает направление потока данных в параметре <paramref name="dwDirection" /> метода <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" />.Это определяет форматы, которые может перечислять результирующий перечислитель.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_GET">
      <summary>Требует, чтобы <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> предоставлял перечислитель для форматов, которые могут быть заданы в <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.GetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_SET">
      <summary>Требует, чтобы <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> предоставлял перечислитель для форматов, которые могут быть заданы в <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.SetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@,System.Boolean)" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DESCKIND">
      <summary>Определяет описание типа, к которому выполнена привязка.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_FUNCDESC">
      <summary>Показывает, что структура <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> была возвращена.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_IMPLICITAPPOBJ">
      <summary>Показывает возвращение IMPLICITAPPOBJ.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_MAX">
      <summary>Показывает метку окончания перечисления.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_NONE">
      <summary>Показывает отсутствие совпадений.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_TYPECOMP">
      <summary>Показывает возврат объекта TYPECOMP.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_VARDESC">
      <summary>Показывает возврат объекта VARDESC.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DISPPARAMS">
      <summary>Содержит аргументы, переданные IDispatch::Invoke методу или свойству.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cArgs">
      <summary>Предоставляет число аргументов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cNamedArgs">
      <summary>Представляет число именованных аргументов. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgdispidNamedArgs">
      <summary>Представляет диспетчерские идентификаторы именованных аргументов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgvarg">
      <summary>Представляет ссылку на массив аргументов.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DVASPECT">
      <summary>Задает желаемый аспект данных или представления для объекта при рисовании или получении данных.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_CONTENT">
      <summary>Представление объекта, позволяющее отображать объект в виде внедренного объекта внутри контейнера.Это значение обычно задается для объектов составных документов.Представление может использоваться для экрана или принтера.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_DOCPRINT">
      <summary>Представление объекта на экране выглядит так же, как при печати на принтере с помощью команды Печать из меню Файл.Описываемые данные могут представлять последовательность страниц.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_ICON">
      <summary>Символическое представление объекта.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_THUMBNAIL">
      <summary>Представление эскиза объекта, позволяющее отображать этот объект в средствах просмотра.Эскиз представляет собой аппаратно-независимый точечный рисунок, приблизительно 120 на 120 пикселей, 16 цветов (рекомендуется), возможно, помещенный в метафайл.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC">
      <summary>Содержит описание типа и сведения о процессе передачи для переменной, функции или параметра функции.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.desc">
      <summary>Содержит сведения об элементе.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.tdesc">
      <summary>Определяет тип элемента.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION">
      <summary>Содержит сведения об элементе. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.idldesc">
      <summary>Содержит сведения для удаленного взаимодействия с элементом.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.paramdesc">
      <summary>Содержит сведения о параметре.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.EXCEPINFO">
      <summary>Описывает исключения, происходящие в процессе IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrDescription">
      <summary>Описывает ошибку, предназначенную для заказчика.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrHelpFile">
      <summary>Содержит полный путь (диск, путь и имя файла) к файлу справки, содержащему дополнительные сведения об ошибке.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrSource">
      <summary>Показывает имя источника исключения.Обычно это имя приложения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.dwHelpContext">
      <summary>Показывает идентификатор контекста для раздела справки в файле справки.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pfnDeferredFillIn">
      <summary>Представляет указатель на функцию, использующую в качестве аргумента структуру <see cref="T:System.Runtime.InteropServices.EXCEPINFO" /> и возвращающую значение HRESULT.Чтобы избежать задержки при заполнении, этому полю присваивается значение null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pvReserved">
      <summary>Это поле зарезервировано, его значение должно быть равно null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.scode">
      <summary>Возвращаемое значение, описывающее ошибку.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wCode">
      <summary>Представляет код ошибки, определяющий ошибку.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wReserved">
      <summary>Это поле зарезервировано, его значение должно быть равно 0.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FILETIME">
      <summary>Представляет количество 100-наносекундных интервалов с 1 января 1601 г.Эта структура является 64-разрядным значением.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwHighDateTime">
      <summary>Задает старшие 32 бита структуры FILETIME.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwLowDateTime">
      <summary>Задает младшие 32 бита структуры FILETIME.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FORMATETC">
      <summary>Представляет обобщенный формат буфера обмена. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.cfFormat">
      <summary>Задает определенный формат буфера обмена.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.dwAspect">
      <summary>Задает одну из констант перечисления <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" />, указывающую, сколько сведений должно содержаться в отрисовке.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.lindex">
      <summary>Задает часть аспекта, когда данные должны быть разделены по разным страницам. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.ptd">
      <summary>Задает указатель на структуру DVTARGETDEVICE, содержащую сведения о целевом устройстве, для которого формируются данные. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.tymed">
      <summary>Задает одну из констант перечисления <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" />, которая указывает тип среды хранения, используемой для передачи данных объекта. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCDESC">
      <summary>Определяет описание функции.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.callconv">
      <summary>Определяет для функции соглашение о вызове.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParams">
      <summary>Подсчитывает полное число параметров.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParamsOpt">
      <summary>Подсчитывает число дополнительных параметров.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cScodes">
      <summary>Подсчитывает число разрешенных возвращенных значений.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.elemdescFunc">
      <summary>Содержит возвращаемый функцией тип.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.funckind">
      <summary>Показывает, является функция виртуальной, статической или диспетчерской.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.invkind">
      <summary>Задает тип функции свойства.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgelemdescParam">
      <summary>Показывает размер <see cref="F:System.Runtime.InteropServices.FUNCDESC.cParams" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgscode">
      <summary>Хранит число ошибок, которое функция может возвратить в 16-разрядной системе.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.memid">
      <summary>Определяет идентификатор члена функции.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.oVft">
      <summary>Определяет смещение в VTBL для <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_VIRTUAL" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.wFuncFlags">
      <summary>Показывает флаги <see cref="T:System.Runtime.InteropServices.FUNCFLAGS" /> функции.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCFLAGS">
      <summary>Определяет константы, определяющие свойства функции.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FBINDABLE">
      <summary>Функции, поддерживающие привязку данных.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTBIND">
      <summary>Функция, предоставляющая объект наилучшим образом.Только одна функция в типе может содержать этот атрибут.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTCOLLELEM">
      <summary>Допускает оптимизацию, в процессе которой компилятор ищет элемент с именем xyz типа abc.Если такой элемент обнаружен и помечен как функция доступа к элементу коллекции, заданной по умолчанию, то создается вызов этой функции-элемента.Разрешается для элементов диспетчерских и обычных интерфейсов, но запрещен для модулей.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDISPLAYBIND">
      <summary>Функция, отображаемая пользователю как связываемая.<see cref="F:System.Runtime.InteropServices.FUNCFLAGS.FUNCFLAG_FBINDABLE" /> также следует задать.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FHIDDEN">
      <summary>Эту функцию не следует показывать пользователю, хотя она существует и является связываемой.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FIMMEDIATEBIND">
      <summary>Отображаются как отдельные связываемые свойства.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FNONBROWSABLE">
      <summary>Это свойство отображается в обозревателе объектов и не отображается в обозревателе свойств.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREPLACEABLE">
      <summary>Помечает интерфейс как обладающий заданным по умолчанию поведением.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREQUESTEDIT">
      <summary>Если задано, любой вызов метод, задающего это свойство, сначала приводит к вызову IPropertyNotifySink::OnRequestEdit.Реализация OnRequestEdit определяет, разрешено ли вызову устанавливать данное свойство.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FRESTRICTED">
      <summary>Функция не должна быть доступной для макроязыков.Этот флаг предназначен для функций системного уровня или функций, которые не должны отображаться обозревателями типов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FSOURCE">
      <summary>Функция возвращает объект, являющийся источником события.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUIDEFAULT">
      <summary>Элемент сведений о типе является элементом по умолчанию для отображения в пользовательском интерфейсе.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUSESGETLASTERROR">
      <summary>Функция поддерживает GetLastError.Если в процессе выполнения функции возникает ошибка, вызывающий объект может вызвать GetLastError, чтобы извлечь код ошибки.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCKIND">
      <summary>Определяет способ доступа к функции.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_DISPATCH">
      <summary>Доступ к функции может быть получен только при помощи IDispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_NONVIRTUAL">
      <summary>Функция доступна по статическому (static) адресу и принимает неявный указатель this.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_PUREVIRTUAL">
      <summary>Функция доступна при помощи таблицы виртуальных функций (VTBL) и принимает неявный указатель this.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_STATIC">
      <summary>Функция доступна по статическому (static) адресу и не принимает неявный указатель this.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_VIRTUAL">
      <summary>Функция доступна таким же образом, как и <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_PUREVIRTUAL" />, за исключением того, что для этой функции существует реализация.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IAdviseSink">
      <summary>Предоставляет управляемое определение интерфейса IAdviseSink.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnClose">
      <summary>Уведомляет все зарегистрированные приемники уведомлений о том, что объект перешел из состояния выполнения в состояние загруженности.  Этот метод вызывается сервером.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)">
      <summary>Уведомляет все зарегистрированные в данный момент приемники уведомлений объектов данных, что объект изменился.</summary>
      <param name="format">
        <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />, переданный ссылкой, который описывает формат, целевое устройство, отрисовку и сведения о хранении для вызывающего объекта данных.</param>
      <param name="stgmedium">
        <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />, переданный ссылкой, который определяет среду хранения (глобальная память, файл на диске, объект хранилища, объект потока, объект интерфейса графических устройство (GDI) или неопределенная) и владение этой средой для вызывающего объекта данных.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnRename(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Уведомляет все зарегистрированные приемники уведомлений, что объект был переименован.Этот метод вызывается сервером.</summary>
      <param name="moniker">Указатель на интерфейс IMoniker на новом полном моникере объекта.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnSave">
      <summary>Уведомляет все зарегистрированные приемники уведомлений, что объект был сохранен.Этот метод вызывается сервером.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnViewChange(System.Int32,System.Int32)">
      <summary>Уведомляет зарегистрированные приемники уведомлений объекта, что его представление изменилось.Этот метод вызывается сервером.</summary>
      <param name="aspect">Аспект, или представление, объекта.Содержит значение, взятое из перечисления <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" />.</param>
      <param name="index">Часть представления, которое изменилось.В настоящий момент действительно только значение -1.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IBindCtx">
      <summary>Предоставляет управляемое определение интерфейса IBindCtx.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.EnumObjectParam(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>Перечисляет строки, являющиеся ключами таблицы внутреннего представления контекстных параметров объекта.</summary>
      <param name="ppenum">При возвращении данного метода содержит ссылку на перечислитель параметров объекта.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>Возвращает параметры текущей привязки, хранящиеся в контексте этой привязки.</summary>
      <param name="pbindopts">Указатель на структуру для получения параметров привязки. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetObjectParam(System.String,System.Object@)">
      <summary>Выполняет поиск заданного ключа таблицы внутреннего представления контекстных параметров объекта и возвращает соответствующий объект, если он существует.</summary>
      <param name="pszKey">Имя объекта, который нужно найти. </param>
      <param name="ppunk">При возвращении данного метода содержит указатель интерфейса объекта.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetRunningObjectTable(System.Runtime.InteropServices.ComTypes.IRunningObjectTable@)">
      <summary>Возвращает доступ к таблице текущих объектов ROT, относящейся к этому процессу привязки.</summary>
      <param name="pprot">При возвращении данного метода содержит ссылку на таблицу текущих объектов (ROT).Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)">
      <summary>Регистрирует переданный объект как один из объектов, привязка которых была выполнена во время операции моникера и которые необходимо освободить после завершения этой операции.</summary>
      <param name="punk">Объект для регистрации с целью освобождения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectParam(System.String,System.Object)">
      <summary>Регистрирует заданный указатель для объекта под указанным именем в таблице внутреннего представления указателей объектов.</summary>
      <param name="pszKey">Имя, используемое для регистрации <paramref name="punk" />. </param>
      <param name="punk">Объект для регистрации. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.ReleaseBoundObjects">
      <summary>Освобождает все объекты, зарегистрированные в текущий момент с контекстом привязки при помощи метода <see cref="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectBound(System.Object)">
      <summary>Удаляет все объекты из списка зарегистрированных объектов, нуждающихся в освобождении.</summary>
      <param name="punk">Объект для удаления регистрации с целью освобождения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectParam(System.String)">
      <summary>Отменяет регистрацию объекта, найденного в данный момент для заданного ключа во внутренней таблице контекстных параметров объекта, если такой ключ зарегистрирован.</summary>
      <returns>Значение S_OKHRESULT, если заданный ключ был успешно удален из таблицы; в противном случае — значение S_FALSEHRESULT.</returns>
      <param name="pszKey">Ключ, регистрацию которого необходимо удалить. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.SetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>Сохраняет блок параметров в контексте привязки.Эти параметры будут применены к последующим операциям UCOMIMoniker, которые используют этот контекст привязки.</summary>
      <param name="pbindopts">Структура, содержащая задаваемые параметры привязки. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPoint">
      <summary>Предоставляет управляемое определение интерфейса IConnectionPoint.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)">
      <summary>Устанавливает вспомогательное соединение между точкой подключения и объектом приемника вызывающего оператора.</summary>
      <param name="pUnkSink">Ссылка на приемник для получения вызовов выходящего интерфейса, управляемого с использованием этой точки подключения. </param>
      <param name="pdwCookie">При возвращении данного метода содержит файл cookie соединения.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.EnumConnections(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>Создает объект-перечислитель для итерации по соединениям, существующим для этой точки подключения.</summary>
      <param name="ppEnum">При возвращении данного метода содержит только что созданный перечислитель.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionInterface(System.Guid@)">
      <summary>Возвращает IID выходящего интерфейса, управляемого с использованием этой точки подключения.</summary>
      <param name="pIID">При возвращении этого параметра содержит IID выходящего интерфейса, управляемого с использованием данной точки подключения.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionPointContainer(System.Runtime.InteropServices.ComTypes.IConnectionPointContainer@)">
      <summary>Извлекает указатель интерфейса IConnectionPointContainer на доступный для соединения объект, которому принадлежит эта точка подключения.</summary>
      <param name="ppCPC">При возвращении этого параметра содержит интерфейс IConnectionPointContainer объекта, доступного для соединения.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Unadvise(System.Int32)">
      <summary>Завершает вспомогательное соединение, установленное ранее при помощи метода <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />.</summary>
      <param name="dwCookie">Файл cookie соединения, возвращенный ранее из метода <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer">
      <summary>Предоставляет управляемое определение интерфейса IConnectionPointContainer.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.EnumConnectionPoints(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>Создает перечислитель всех точек подключения, поддерживаемых в объекте, доступном для соединения, по одной точке подключения для каждого идентификатора IID.</summary>
      <param name="ppEnum">При возвращении данного метода содержит указатель интерфейса перечислителя.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.FindConnectionPoint(System.Guid@,System.Runtime.InteropServices.ComTypes.IConnectionPoint@)">
      <summary>Запрашивает у доступного для соединения объекта наличие точки подключения для определенного IID, и, если она имеется, возвращает указатель интерфейса IConnectionPoint на эту точку подключения.</summary>
      <param name="riid">Ссылка на идентификатор IID исходящего интерфейса, для которого запрашивается точка подключения. </param>
      <param name="ppCP">При возвращении данного метода содержит точку подключения, управляющую <paramref name="riid" /> выходящего интерфейса.Этот параметр передается без инициализации.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLDESC">
      <summary>Содержит сведения, необходимые для передачи значения, возвращенного функцией, параметром или элементом структуры между процессами.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.dwReserved">
      <summary>Зарезервировано, равно null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.wIDLFlags">
      <summary>Показывает значение <see cref="T:System.Runtime.InteropServices.IDLFLAG" />, описывающее тип.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLFLAG">
      <summary>Описывает способ передачи значения, возвращенного функцией, параметром или элементом структуры между процессами.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FIN">
      <summary>Параметр передает сведения из вызывающего объекта в вызываемый объект.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FLCID">
      <summary>Этот параметр является локальным идентификатором клиентского приложения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FOUT">
      <summary>Этот параметр возвращает сведения из вызываемого объекта в вызывающий объект.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FRETVAL">
      <summary>Этот параметр является значением, возвращаемым членом.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_NONE">
      <summary>Не задает, передает ли параметр сведения или получает их.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints">
      <summary>Управляет определением интерфейса IEnumConnectionPoints.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>Создает новый перечислитель с тем же состоянием перечисления, что и текущий.</summary>
      <param name="ppenum">При возвращении данного метода содержит ссылку на только что созданный перечислитель.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IConnectionPoint[],System.IntPtr)">
      <summary>Возвращает заданное число элементов последовательности перечисления.</summary>
      <returns>Значение S_OK, если параметр <paramref name="pceltFetched" /> равен параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число ссылок IConnectionPoint, возвращаемых в <paramref name="rgelt" />. </param>
      <param name="rgelt">При возвращении данного метода содержит ссылку на перечисленные соединения.Этот параметр передается без инициализации.</param>
      <param name="pceltFetched">При возвращении данного метода содержит ссылку на фактическое число соединений, перечисленных в <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Reset">
      <summary>Сбрасывает последовательность перечисления в начало.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Skip(System.Int32)">
      <summary>Пропускает заданное число элементов в последовательности перечисления.</summary>
      <returns>Значение S_OK, если число пропущенных элементов соответствует параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число элементов, пропускаемых при перечислении. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnections">
      <summary>Управляет определением интерфейса IEnumConnections.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>Создает новый перечислитель с тем же состоянием перечисления, что и текущий.</summary>
      <param name="ppenum">При возвращении данного метода содержит ссылку на только что созданный перечислитель.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Next(System.Int32,System.Runtime.InteropServices.ComTypes.CONNECTDATA[],System.IntPtr)">
      <summary>Возвращает заданное число элементов последовательности перечисления.</summary>
      <returns>Значение S_OK, если параметр <paramref name="pceltFetched" /> равен параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число структур <see cref="T:System.Runtime.InteropServices.CONNECTDATA" />, которые будут возвращены в <paramref name="rgelt" />. </param>
      <param name="rgelt">При возвращении данного метода содержит ссылку на перечисленные соединения.Этот параметр передается без инициализации.</param>
      <param name="pceltFetched">При возвращении данного метода содержит ссылку на фактическое число соединений, перечисленных в <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Reset">
      <summary>Сбрасывает последовательность перечисления в начало.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Skip(System.Int32)">
      <summary>Пропускает заданное число элементов в последовательности перечисления.</summary>
      <returns>Значение S_OK, если число пропущенных элементов соответствует параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число элементов, пропускаемых при перечислении. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC">
      <summary>Предоставляет управляемое определение интерфейса IEnumFORMATETC.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Clone(System.Runtime.InteropServices.ComTypes.IEnumFORMATETC@)">
      <summary>Создает новый перечислитель с тем же состоянием перечисления, что и текущий перечислитель.</summary>
      <param name="newEnum">При возвращении данного метода содержит ссылку на только что созданный перечислитель.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Next(System.Int32,System.Runtime.InteropServices.ComTypes.FORMATETC[],System.Int32[])">
      <summary>Возвращает заданное число элементов последовательности перечисления.</summary>
      <returns>Значение S_OK, если параметр <paramref name="pceltFetched" /> равен параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число ссылок <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />, возвращаемых в <paramref name="rgelt" />.</param>
      <param name="rgelt">При возвращении данного метода содержит ссылку на перечисленные ссылки <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />.Этот параметр передается без инициализации.</param>
      <param name="pceltFetched">При возвращении данного метода содержит ссылку на фактическое число ссылок, перечисленных в <paramref name="rgelt" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Reset">
      <summary>Сбрасывает последовательность перечисления в начало.</summary>
      <returns>HRESULT со значением S_OK.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Skip(System.Int32)">
      <summary>Пропускает заданное число элементов в последовательности перечисления.</summary>
      <returns>Значение S_OK, если число пропущенных элементов соответствует параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число элементов, пропускаемых при перечислении.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumMoniker">
      <summary>Управляет определением интерфейса IEnumMoniker.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Clone(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Создает новый перечислитель с тем же состоянием перечисления, что и текущий.</summary>
      <param name="ppenum">При возвращении данного метода содержит ссылку на только что созданный перечислитель.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker[],System.IntPtr)">
      <summary>Возвращает заданное число элементов последовательности перечисления.</summary>
      <returns>Значение S_OK, если параметр <paramref name="pceltFetched" /> равен параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число моникеров для возвращения в <paramref name="rgelt" />. </param>
      <param name="rgelt">При возвращении данного метода содержит ссылку на перечисленные моникеры.Этот параметр передается без инициализации.</param>
      <param name="pceltFetched">При возвращении данного метода содержит ссылку на фактическое число моникеров, перечисленных в <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Reset">
      <summary>Сбрасывает последовательность перечисления в начало.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Skip(System.Int32)">
      <summary>Пропускает заданное число элементов в последовательности перечисления.</summary>
      <returns>Значение S_OK, если число пропущенных элементов соответствует параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число элементов, пропускаемых при перечислении. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumString">
      <summary>Управляет определением интерфейса IEnumString.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Clone(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>Создает новый перечислитель с тем же состоянием перечисления, что и текущий.</summary>
      <param name="ppenum">При возвращении данного метода содержит ссылку на только что созданный перечислитель.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Next(System.Int32,System.String[],System.IntPtr)">
      <summary>Возвращает заданное число элементов последовательности перечисления.</summary>
      <returns>Значение S_OK, если параметр <paramref name="pceltFetched" /> равен параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число строк для возвращения в параметре <paramref name="rgelt" />. </param>
      <param name="rgelt">При возвращении данного метода содержит ссылку на перечисленные строки.Этот параметр передается без инициализации.</param>
      <param name="pceltFetched">При возвращении данного метода содержит ссылку на фактическое число строк, перечисленных в <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Reset">
      <summary>Сбрасывает последовательность перечисления в начало.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Skip(System.Int32)">
      <summary>Пропускает заданное число элементов в последовательности перечисления.</summary>
      <returns>Значение S_OK, если число пропущенных элементов соответствует параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число элементов, пропускаемых при перечислении. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT">
      <summary>Управляет определением интерфейса IEnumVARIANT.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Clone">
      <summary>Создает новый перечислитель с тем же состоянием перечисления, что и текущий.</summary>
      <returns>Ссылка <see cref="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT" /> на вновь созданный перечислитель.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Next(System.Int32,System.Object[],System.IntPtr)">
      <summary>Возвращает заданное число элементов последовательности перечисления.</summary>
      <returns>Значение S_OK, если параметр <paramref name="pceltFetched" /> равен параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число элементов для возвращения в <paramref name="rgelt" />. </param>
      <param name="rgVar">При возвращении данного метода содержит ссылку на перечисленные элементы.Этот параметр передается без инициализации.</param>
      <param name="pceltFetched">При возвращении данного метода содержит ссылку на фактическое число элементов, перечисленных в <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Reset">
      <summary>Сбрасывает последовательность перечисления в начало.</summary>
      <returns>HRESULT со значением S_OK.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Skip(System.Int32)">
      <summary>Пропускает заданное число элементов в последовательности перечисления.</summary>
      <returns>Значение S_OK, если число пропущенных элементов соответствует параметру <paramref name="celt" />; в противном случае — значение S_FALSE.</returns>
      <param name="celt">Число элементов, пропускаемых при перечислении. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMoniker">
      <summary>Предоставляет управляемое определение интерфейса IMoniker с функциональной возможностью COM из IPersist и IPersistStream.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToObject(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>Использует моникер для привязки к определяемому объекту.</summary>
      <param name="pbc">Ссылка на интерфейс IBindCtx объекта контекстной привязки, используемого в данной операции привязки. </param>
      <param name="pmkToLeft">Ссылка на моникер слева от данного моникера, если моникер является частью составного моникера. </param>
      <param name="riidResult">Идентификатор интерфейса (IID), выбранного клиентом для установки соединения с объектом, который определен моникером. </param>
      <param name="ppvResult">При возвращении данного метода содержит ссылку на интерфейс, запрошенный <paramref name="riidResult" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToStorage(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>Возвращает указатель интерфейса на хранилище, в котором содержится объект, определенный моникером.</summary>
      <param name="pbc">Ссылка на интерфейс IBindCtx объекта контекстной привязки, используемого в процессе операции привязки. </param>
      <param name="pmkToLeft">Ссылка на моникер слева от данного моникера, если моникер является частью составного моникера. </param>
      <param name="riid">Идентификатор IID запрошенного интерфейса хранилища. </param>
      <param name="ppvObj">При возвращении данного метода содержит ссылку на интерфейс, запрошенный <paramref name="riid" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.CommonPrefixWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Создает новый моникер на основании общего префикса, который данный моникер использует совместно с другим моникером.</summary>
      <param name="pmkOther">Ссылка на интерфейс IMoniker другого моникера, сравниваемого с данным моникером для выявления общего префикса. </param>
      <param name="ppmkPrefix">При возвращении данного метода содержит моникер, который является общим префиксом текущего моникера и <paramref name="pmkOther" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ComposeWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Boolean,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Объединяет текущий моникер с другим, создавая составной моникер.</summary>
      <param name="pmkRight">Ссылка на интерфейс IMoniker моникера для добавления в конец данного моникера. </param>
      <param name="fOnlyIfNotGeneric">Значение true показывает, что вызывающему объекту требуется неуниверсальное объединение.Операция продолжается только в том случае, если параметр <paramref name="pmkRight" /> является классом моникера, с которым текущий моникер может быть объединен каким-либо способом, кроме образования универсального объединения.Значение false показывает, что при необходимости метод может создать универсальное объединение.</param>
      <param name="ppmkComposite">При возвращении данного метода содержит ссылку на итоговый составной моникер.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Enum(System.Boolean,System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Предоставляет указатель на перечислитель, способный перечислить компоненты составного моникера.</summary>
      <param name="fForward">Значение true используется для перечисления моникеров слева направо.Значение false используется для перечисления справа налево.</param>
      <param name="ppenumMoniker">При возвращении данного метода содержит ссылку на объект перечислителя для моникера.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetClassID(System.Guid@)">
      <summary>Возвращает идентификатор класса CLSID для объекта.</summary>
      <param name="pClassID">При возвращении этого метода содержит CLSID.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String@)">
      <summary>Возвращает отображаемое имя, являющееся представлением текущего моникера, удобным для чтения пользователем.</summary>
      <param name="pbc">Ссылка на контекст привязки, используемый в данной операции. </param>
      <param name="pmkToLeft">Ссылка на моникер слева от данного моникера, если моникер является частью составного моникера. </param>
      <param name="ppszDisplayName">При возвращении данного метода содержит строку отображаемого имени.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetSizeMax(System.Int64@)">
      <summary>Возвращает размер потока, необходимого для сохранения объекта, в байтах.</summary>
      <param name="pcbSize">При возвращении данного метода содержит значение long, показывающее размер потока, необходимого для сохранения объекта, в байтах.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Предоставляет число, указывающее время последнего изменения объекта, определенного текущим моникером.</summary>
      <param name="pbc">Ссылка на контекст привязки, используемый при выполнении данной операции привязки. </param>
      <param name="pmkToLeft">Ссылка на моникер слева от данного моникера, если моникер является частью составного моникера. </param>
      <param name="pFileTime">При возвращении данного метода содержит время последнего изменения.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Hash(System.Int32@)">
      <summary>Вычисляет 32-разрядное целое число, используя внутреннее состояние моникера.</summary>
      <param name="pdwHash">При возвращении данного метода содержит хэш-значения для этого моникера.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Inverse(System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Предоставляет моникер, объединение которого с правой частью данного моникера или моникера с подобной структурой приводит к нулевому значению.</summary>
      <param name="ppmk">При возвращении данного метода содержит моникер, который является инверсией текущего моникера.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsDirty">
      <summary>Проверяет наличие изменений в объекте с момента его последнего сохранения.</summary>
      <returns>Значение S_OKHRESULT, если объект был изменен; в противном случае — значение S_FALSEHRESULT.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsEqual(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Сравнивает текущий моникер с заданным моникером и показывает, совпадают ли они.</summary>
      <returns>Значение S_OKHRESULT, если моникеры совпадают; в противном случае — значение S_FALSEHRESULT.  </returns>
      <param name="pmkOtherMoniker">Ссылка на моникер, используемый для сравнения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsRunning(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Определяет, загружен и запущен ли в данный момент объект, определенный текущим моникером.</summary>
      <returns>Значение S_OKHRESULT, если моникер выполняется, значение S_FALSEHRESULT, если моникер не выполняется, или значение E_UNEXPECTEDHRESULT.</returns>
      <param name="pbc">Ссылка на контекст привязки, используемый при выполнении данной операции привязки. </param>
      <param name="pmkToLeft">Ссылка на моникер слева от данного моникера, если текущий моникер является частью составного моникера. </param>
      <param name="pmkNewlyRunning">Ссылка на моникер, который был добавлен в таблицу текущих объектов (ROT) самым последним. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsSystemMoniker(System.Int32@)">
      <summary>Показывает, является ли данный моникер одним из классов моникеров, предоставляемых системой.</summary>
      <returns>Значение S_OKHRESULT, если моникер является моникером системы; в противном случае — значение S_FALSEHRESULT.</returns>
      <param name="pdwMksys">При возвращении данного метода содержит указатель на целое число, являющееся одним из значений из перечисления MKSYS и ссылающееся на один из классов моникеров COM.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Load(System.Runtime.InteropServices.ComTypes.IStream)">
      <summary>Инициализирует объект из потока, в котором он был предварительно сохранен.</summary>
      <param name="pStm">Поток, из которого загружается объект. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Считывает столько знаков указанного отображаемого имени, сколько может воспринять <see cref="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)" />, и создает моникер, соответствующий считанной части.</summary>
      <param name="pbc">Ссылка на контекст привязки, используемый при выполнении данной операции привязки. </param>
      <param name="pmkToLeft">Ссылка на моникер, созданный из отображаемого имени до данной точки. </param>
      <param name="pszDisplayName">Ссылка на строку, содержащую оставшуюся часть анализируемого отображаемого имени. </param>
      <param name="pchEaten">При возвращении данного метода содержит число знаков, использованных при анализе <paramref name="pszDisplayName" />.Этот параметр передается без инициализации.</param>
      <param name="ppmkOut">При возвращении данного метода содержит ссылку на моникер, построенный из <paramref name="pszDisplayName" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Reduce(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Возвращает уменьшенный моникер, представляющий собой другой моникер, ссылающийся на тот же объект, но который может быть привязан с такой же или большей эффективностью.</summary>
      <param name="pbc">Ссылка на интерфейс IBindCtx контекста привязки, используемый в данной операции привязки. </param>
      <param name="dwReduceHowFar">Значение, задающее, насколько следует уменьшить текущий моникер. </param>
      <param name="ppmkToLeft">Ссылка на моникер слева от текущего моникера. </param>
      <param name="ppmkReduced">При возвращении данного метода содержит ссылку на уменьшенную форму текущего моникера, который может иметь значение null, если происходит ошибка или если текущий моникер уменьшен до нуля.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.RelativePathTo(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Предоставляет моникер, добавление которого к текущему моникеру (или к одной из подобных структур) приводит к получению указанного моникера.</summary>
      <param name="pmkOther">Ссылка на моникер, для которого нужно получить относительный путь. </param>
      <param name="ppmkRelPath">При возвращении данного метода содержит ссылку на относительный моникер.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Save(System.Runtime.InteropServices.ComTypes.IStream,System.Boolean)">
      <summary>Сохраняет объект в указанном потоке.</summary>
      <param name="pStm">Поток, в котором будет сохранен объект. </param>
      <param name="fClearDirty">Значение true показывает, что необходима очистка измененного флага после сохранения; в противном случае — значение false.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS">
      <summary>Определяет атрибуты реализованного или унаследованного интерфейса типа.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULT">
      <summary>Интерфейс или диспетчерский интерфейс предоставляет источник или приемник, используемый по умолчанию.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULTVTABLE">
      <summary>Приемники получают события при помощи таблицы виртуальной функции (VTBL). </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FRESTRICTED">
      <summary>Не следует позволять пользователям отображать или программировать этот член.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FSOURCE">
      <summary>Этот член совместного класса вызывается, а не реализуется.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND">
      <summary>Задает способ вызова функции при помощи IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_FUNC">
      <summary>Член, вызываемый при помощи синтаксиса вызова обычной функции.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYGET">
      <summary>Функция, вызываемая при помощи синтаксиса обычного доступа к свойству.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUT">
      <summary>Функция, вызываемая при помощи синтаксиса присвоения значения свойству.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUTREF">
      <summary>Функция, вызываемая при помощи синтаксиса присвоения ссылки свойству.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IPersistFile">
      <summary>Предоставляет управляемое определение интерфейса IPersistFile с функциональными возможностями из IPersist.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetClassID(System.Guid@)">
      <summary>Возвращает идентификатор класса CLSID для объекта.</summary>
      <param name="pClassID">При возвращении данного метода содержит ссылку на идентификатор CLSID.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetCurFile(System.String@)">
      <summary>Извлекает либо абсолютный путь к текущему выполняемому файлу объекта, либо, если выполняемый файл отсутствует, выдаваемый по умолчанию запрос имени файла объекта.</summary>
      <param name="ppszFileName">При возвращении данного метода содержит адрес указателя на оканчивающуюся нулем строку, содержащую путь для текущего файла или используемый по умолчанию запрос имени файла (например, *.txt).Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.IsDirty">
      <summary>Проверяет наличие изменений в объекте с момента его последнего сохранения в текущем файле.</summary>
      <returns>Значение S_OK, если файл был изменен с момента последнего сохранения; значение S_FALSE — если файл не был изменен.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Load(System.String,System.Int32)">
      <summary>Открывает указанный файл и инициализирует объект из содержимого файла.</summary>
      <param name="pszFileName">Строка, оканчивающаяся нулем и содержащая абсолютный путь к файлу, который требуется открыть. </param>
      <param name="dwMode">Набор значений из перечисления STGM, показывающих режим доступа, используемый для открытия <paramref name="pszFileName" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Save(System.String,System.Boolean)">
      <summary>Сохраняет копию объекта в заданном файле.</summary>
      <param name="pszFileName">Строка, оканчивающаяся нулем и содержащая абсолютный путь к файлу, в котором требуется сохранить объект. </param>
      <param name="fRemember">Значение true, чтобы использовать параметр <paramref name="pszFileName" /> в качестве выполняемого файла; в противном случае — значение false. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.SaveCompleted(System.String)">
      <summary>Сообщает объекту, что он может выполнять запись в файл.</summary>
      <param name="pszFileName">Абсолютный путь к файлу, в котором ранее был сохранен объект. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IRunningObjectTable">
      <summary>Предоставляет управляемое определение интерфейса IRunningObjectTable.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.EnumRunning(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Перечисляет объекты, зарегистрированные в данный момент как запущенные.</summary>
      <param name="ppenumMoniker">При возвращении данного метода содержит новый перечислитель для таблицы текущих объектов (ROT).Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetObject(System.Runtime.InteropServices.ComTypes.IMoniker,System.Object@)">
      <summary>Возвращает зарегистрированный объект, если предоставленное имя объекта зарегистрировано как выполняемое.</summary>
      <returns>Значение HRESULT, показывающее успешное или неуспешное выполнение операции. </returns>
      <param name="pmkObjectName">Ссылка на моникер, используемый для поиска в таблице текущих объектов (ROT). </param>
      <param name="ppunkObject">При возвращении данного метода содержит запрошенный выполняемый объект.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Выполняет поиск этого моникера в таблице текущих объектов (ROT) и сообщает записанное время изменения, если моникер найден.</summary>
      <returns>Значение HRESULT, показывающее успешное или неуспешное выполнение операции.</returns>
      <param name="pmkObjectName">Ссылка на моникер, используемый для поиска в таблице текущих объектов (ROT). </param>
      <param name="pfiletime">При возвращении данного объекта содержит время последнего изменения объекта.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.IsRunning(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Определяет, зарегистрирован ли заданный моникер в таблице текущих объектов (ROT).</summary>
      <returns>Значение HRESULT, показывающее успешное или неуспешное выполнение операции.</returns>
      <param name="pmkObjectName">Ссылка на моникер, используемый для поиска в таблице текущих объектов (ROT). </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Записывает время изменения определенного объекта, чтобы IMoniker::GetTimeOfLastChange мог сообщить о времени внесения изменений.</summary>
      <param name="dwRegister">Запись измененного объекта в таблице текущих объектов (ROT). </param>
      <param name="pfiletime">Ссылка на время последнего изменения объекта. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Register(System.Int32,System.Object,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Регистрирует переход предоставленного объекта в состояние выполнения.</summary>
      <returns>Значение, которое можно использовать для определения записи в таблице ROT при последующих вызовах метода <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)" /> или <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)" />.</returns>
      <param name="grfFlags">Показывает, является ли ссылка таблицы текущих объектов ROT на <paramref name="punkObject" /> слабой или строгой, а также управляет доступом к объекту, используя соответствующую запись в таблице ROT. </param>
      <param name="punkObject">Ссылка на объект, зарегистрированный как выполняемый. </param>
      <param name="pmkObjectName">Ссылка на моникер, определяющий <paramref name="punkObject" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)">
      <summary>Удаляет регистрацию заданного объекта из таблицы текущих объектов (ROT).</summary>
      <param name="dwRegister">Удаляемая запись в таблице текущих объектов (ROT). </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IStream">
      <summary>Предоставляет управляемое определение интерфейса IStream с функциональными возможностями ISequentialStream.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
      <summary>Создает новый объект-поток с собственным указателем поиска, ссылающимся на те же байты, что и исходный поток.</summary>
      <param name="ppstm">При возвращении данного метода содержит новый объект потока.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)">
      <summary>Следует убедиться, что все изменения, внесенные в объект потока, открытый в режиме транзакций, отражены в родительском хранилище.</summary>
      <param name="grfCommitFlags">Значение, контролирующее выполнение изменений объекта потока. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
      <summary>Копирует указанное число байтов из текущего указателя поиска данного потока в текущий указатель поиска другого потока.</summary>
      <param name="pstm">Ссылка на поток назначения. </param>
      <param name="cb">Число байтов, копируемых из потока-источника. </param>
      <param name="pcbRead">При удачном возвращении содержит фактическое число байтов, считанных из источника. </param>
      <param name="pcbWritten">При удачном возвращении содержит фактическое число байтов, записанных в поток назначения. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>Ограничивает доступ к указанному диапазону байтов в потоке.</summary>
      <param name="libOffset">Смещение в байтах для начала диапазона. </param>
      <param name="cb">Длина диапазона, доступ к которому ограничен, в байтах. </param>
      <param name="dwLockType">Запрошенные ограничения для доступа к диапазону. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Read(System.Byte[],System.Int32,System.IntPtr)">
      <summary>Считывает заданное число байтов из объекта-потока в память, начиная с текущего указателя поиска.</summary>
      <param name="pv">При возвращении данного метода содержит данные, считанные из потока.Этот параметр передается без инициализации.</param>
      <param name="cb">Число байтов, которое требуется считать из объекта-потока. </param>
      <param name="pcbRead">Указатель на переменную ULONG, получающую фактическое число байтов, считанное из объекта потока. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Revert">
      <summary>Отменяет все изменения, выполненные в потоке, с которым шел обмен данными, с момента последнего вызова метода <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Seek(System.Int64,System.Int32,System.IntPtr)">
      <summary>Изменяет положение указателя поиска относительно начала потока, конца потока или текущего указателя поиска.</summary>
      <param name="dlibMove">Смещение, добавляемое к <paramref name="dwOrigin" />. </param>
      <param name="dwOrigin">Исходное положение поиска.Исходное положение может быть началом файла, текущим положением указателя поиска или концом файла.</param>
      <param name="plibNewPosition">При удачном возвращении содержит смещение указателя поиска от начала потока. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.SetSize(System.Int64)">
      <summary>Изменяет размер объекта-потока.</summary>
      <param name="libNewSize">Новый размер потока в байтах. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
      <summary>Извлекает структуру <see cref="T:System.Runtime.InteropServices.STATSTG" /> для этого потока.</summary>
      <param name="pstatstg">При возвращении данного метода содержит структуру STATSTG, описывающую этот объект потока.Этот параметр передается без инициализации.</param>
      <param name="grfStatFlag">Элементы структуры STATSTG, не возвращаемые этим методом, таким образом получается сэкономить на операциях по выделению памяти. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.UnlockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>Удаляет ограничения доступа к диапазону байтов, установленные ранее с помощью метода <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)" />.</summary>
      <param name="libOffset">Смещение в байтах для начала диапазона. </param>
      <param name="cb">Длина диапазона, доступ к которому ограничен, в байтах. </param>
      <param name="dwLockType">Ограничения доступа, заданные ранее для диапазона. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Write(System.Byte[],System.Int32,System.IntPtr)">
      <summary>Записывает заданное число байтов в объект-поток, начиная с текущего указателя поиска.</summary>
      <param name="pv">Буфер для записи потока. </param>
      <param name="cb">Число байтов для записи в поток. </param>
      <param name="pcbWritten">При удачном возвращении содержит фактическое число байтов, записанных в объект-поток.Если вызывающий объект задает для этого указателя значение <see cref="F:System.IntPtr.Zero" />, данный метод не предоставляет фактическое число записанных байтов.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeComp">
      <summary>Предоставляет управляемое определение интерфейса ITypeComp.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.Bind(System.String,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.DESCKIND@,System.Runtime.InteropServices.ComTypes.BINDPTR@)">
      <summary>Сопоставляет имя с типом элемента или связывает глобальные переменные и функции, содержащиеся в библиотеке типов.</summary>
      <param name="szName">Привязываемое имя. </param>
      <param name="lHashVal">Хэш-значение для <paramref name="szName" />, рассчитанное при помощи LHashValOfNameSys. </param>
      <param name="wFlags">Слово флагов, содержащее один или несколько используемых флагов, определенных в перечислении INVOKEKIND. </param>
      <param name="ppTInfo">При возвращении данного метода содержит ссылку на описание типа, содержащее элемент, с которым этот тип связан, если возвращено значение FUNCDESC или VARDESC.Этот параметр передается без инициализации.</param>
      <param name="pDescKind">При возвращении данного метода содержит ссылку на перечислитель DESCKIND, указывающий, является ли имя, к которому выполняется привязка, VARDESC, FUNCDESC или TYPECOMP.Этот параметр передается без инициализации.</param>
      <param name="pBindPtr">При возвращении данного метода содержит ссылку на интерфейс VARDESC, FUNCDESC или ITypeComp, к которому выполняется привязка.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.BindType(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Выполняет привязку к описаниям типов, содержащимся в библиотеке типов.</summary>
      <param name="szName">Привязываемое имя. </param>
      <param name="lHashVal">Хэш-значение для <paramref name="szName" />, определенное при помощи LHashValOfNameSys. </param>
      <param name="ppTInfo">При возвращении данного метода содержит ссылку на ITypeInfo типа, к которому был привязан параметр <paramref name="szName" />.Этот параметр передается без инициализации.</param>
      <param name="ppTComp">При возвращении данного метода содержит ссылку на переменную ITypeComp.Этот параметр передается без инициализации.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo">
      <summary>Предоставляет управляемое определение интерфейса автоматизации компонентов ITypeInfo.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>Извлекает адреса статических функций и переменных, определенных в библиотеке DLL.</summary>
      <param name="memid">Идентификатор элемента для извлекаемого адреса элемента типа static. </param>
      <param name="invKind">Одно из значений <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" />, указывающее, является ли элемент свойством, и, если да, то какого типа. </param>
      <param name="ppv">При возвращении данного метода содержит ссылку на элемент static.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>Создает новый экземпляр типа, описывающего класс компонента (совместный класс).</summary>
      <param name="pUnkOuter">Объект, действующий как управляющий IUnknown. </param>
      <param name="riid">Идентификатор IID интерфейса, используемый вызывающим объектом для связи с итоговым объектом. </param>
      <param name="ppvObj">При возвращении данного метода содержит ссылку на созданный объект.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>Извлекает библиотеку типов, содержащую описание этого типа и его индекс внутри этой библиотеки типов.</summary>
      <param name="ppTLB">При возвращении данного метода содержит ссылку на содержащую библиотеку типов.Этот параметр передается без инициализации.</param>
      <param name="pIndex">При возвращении данного метода содержит ссылку на индекс описания типа в содержащей библиотеке типов.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>Возвращает описание или спецификацию точки входа для функции в динамической библиотеке DLL.</summary>
      <param name="memid">Идентификатор функции элемента, для которого возвращается описание входа динамической библиотеки DLL. </param>
      <param name="invKind">Одно из значений <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" />, указывающее тип элемента, определенного с помощью <paramref name="memid" />. </param>
      <param name="pBstrDllName">Если значение не равно null, функция задает для параметра <paramref name="pBstrDllName" /> значение BSTR, содержащее имя динамической библиотеки DLL. </param>
      <param name="pBstrName">Если значение не равно null, функция задает для <paramref name="lpbstrName" /> значение BSTR, содержащее имя точки входа. </param>
      <param name="pwOrdinal">Если значение не равно null и функция определена порядковым номером, то параметр <paramref name="lpwOrdinal" /> должен указывать на порядковый номер. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Извлекает строку документации, полный файл справки и путь к нему, а также идентификатор контекста разделов справки для заданного описания типа.</summary>
      <param name="index">Идентификатор элемента, для которого возвращается документация. </param>
      <param name="strName">При возвращении данного метода содержит имя метода элемента.Этот параметр передается без инициализации.</param>
      <param name="strDocString">При возвращении данного метода содержит строку документации для заданного элемента.Этот параметр передается без инициализации.</param>
      <param name="dwHelpContext">При возвращении данного метода содержит ссылку на контекст справки, связанный с заданным элементом.Этот параметр передается без инициализации.</param>
      <param name="strHelpFile">При возвращении данного метода содержит полное имя файла справки.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>Возвращает структуру <see cref="T:System.Runtime.InteropServices.FUNCDESC" />, содержащую сведения о заданной функции.</summary>
      <param name="index">Индекс возвращаемого описания функции. </param>
      <param name="ppFuncDesc">При возвращении данного метода содержит ссылку на структуру FUNCDESC, описывающую заданную функцию.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>Сопоставляет друг с другом имена и идентификаторы элементов и параметров.</summary>
      <param name="rgszNames">Массив имен, которые следует сопоставить. </param>
      <param name="cNames">Число сопоставляемых имен. </param>
      <param name="pMemId">При возвращении данного метода содержит ссылку на массив, в который помещены сопоставления имен.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>Возвращает значение <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> для одного реализованного интерфейса или базового интерфейса в описании типа.</summary>
      <param name="index">Индекс реализованного интерфейса или базового интерфейса. </param>
      <param name="pImplTypeFlags">При возвращении данного метода содержит ссылку на перечисление IMPLTYPEFLAGS.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetMops(System.Int32,System.String@)">
      <summary>Извлекает сведения о маршалинге.</summary>
      <param name="memid">Идентификатор элемента, показывающий, какие необходимы сведения о маршалинге. </param>
      <param name="pBstrMops">При возвращении данного метода содержит ссылку на строку opcode, используемую при маршалинге полей структуры, описанной в описании ссылочного типа, либо возвращает null, если сведения отсутствуют.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>Извлекает переменную с указанным идентификатором (либо имя свойства или метода и его параметры), соответствующую заданному идентификатору функции.</summary>
      <param name="memid">Идентификатор элемента, для которого возвращается имя (или имена). </param>
      <param name="rgBstrNames">При возвращении данного метода содержит имя (или имена), связанное с элементом.Этот параметр передается без инициализации.</param>
      <param name="cMaxNames">Длина массива <paramref name="rgBstrNames" />. </param>
      <param name="pcNames">При возвращении данного метода содержит число имен в массиве <paramref name="rgBstrNames" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Извлекает описания ссылочных типов, если описание типа ссылается на другие описания типов.</summary>
      <param name="hRef">Возвращаемый дескриптор для описания ссылочного типа. </param>
      <param name="ppTI">При возвращении данного метода содержит описание ссылочного типа.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>Извлекает описание типа для реализованных типов интерфейсов, если в описании типа описывается класс COM.</summary>
      <param name="index">Индекс реализованного типа, дескриптор которого возвращается. </param>
      <param name="href">При возвращении данного метода содержит ссылку на дескриптор для реализованного интерфейса.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)">
      <summary>Возвращает структуру <see cref="T:System.Runtime.InteropServices.TYPEATTR" />, содержащую атрибуты описания типа.</summary>
      <param name="ppTypeAttr">При возвращении данного метода содержит ссылку на структуру, содержащую атрибуты этого описания типа.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Извлекает интерфейс ITypeComp описания типа, позволяющий компилятору клиента выполнить привязку к элементам описания типа.</summary>
      <param name="ppTComp">При возвращении данного метода содержит ссылку на интерфейс ITypeComp содержащей библиотеки типов.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>Извлекает структуру VARDESC, описывающую указанную переменную.</summary>
      <param name="index">Индекс возвращаемого описания переменной. </param>
      <param name="ppVarDesc">При возвращении данного метода содержит ссылку на структуру VARDESC, описывающую заданную переменную.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>Вызывает метод или обращается к свойству объекта, реализующему интерфейс, описанный в описании типа.</summary>
      <param name="pvInstance">Ссылка на интерфейс, описанный в данном описании типа. </param>
      <param name="memid">Значение, задающее элемент интерфейса. </param>
      <param name="wFlags">Флаги, описывающие контекст используемого вызова. </param>
      <param name="pDispParams">Ссылка на структуру, содержащую массив аргументов, массив идентификаторов DISPID именованных аргументов, а также количество элементов в каждом массиве. </param>
      <param name="pVarResult">Ссылка на место хранения результата.Если <paramref name="wFlags" /> задает DISPATCH_PROPERTYPUT или DISPATCH_PROPERTYPUTREF, то параметр <paramref name="pVarResult" /> не учитывается.Если результат не требуется, можно задать значение null.</param>
      <param name="pExcepInfo">Указатель на структуру сведений об исключении, заполняемую только при возвращении DISP_E_EXCEPTION. </param>
      <param name="puArgErr">Если Invoke возвращает DISP_E_TYPEMISMATCH, то параметр <paramref name="puArgErr" /> показывает индекс аргумента с неверным типом внутри <paramref name="rgvarg" />.Если ошибка возвращается для нескольких аргументов, <paramref name="puArgErr" /> показывает только первый из них.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseFuncDesc(System.IntPtr)">
      <summary>Освобождает структуру <see cref="T:System.Runtime.InteropServices.FUNCDESC" />, ранее возвращенную методом <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pFuncDesc">Ссылка на освобождаемую структуру FUNCDESC. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseTypeAttr(System.IntPtr)">
      <summary>Освобождает структуру <see cref="T:System.Runtime.InteropServices.TYPEATTR" />, ранее возвращенную методом <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />.</summary>
      <param name="pTypeAttr">Ссылка на освобождаемую структуру TYPEATTR. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseVarDesc(System.IntPtr)">
      <summary>Освобождает структуру VARDESC, ранее возвращенную методом <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pVarDesc">Ссылка на освобождаемую структуру VARDESC. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo2">
      <summary>Предоставляет управляемое определение интерфейса ITypeInfo2.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>Извлекает адреса статических функций и переменных, определенных в библиотеке DLL.</summary>
      <param name="memid">Идентификатор элемента для извлекаемого адреса элемента типа static. </param>
      <param name="invKind">Одно из значений <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" />, указывающее, является ли элемент свойством, и, если да, то какого типа. </param>
      <param name="ppv">При возвращении данного метода содержит ссылку на элемент static.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>Создает новый экземпляр типа, описывающего класс компонента (совместный класс).</summary>
      <param name="pUnkOuter">Объект, действующий как контролирующий IUnknown. </param>
      <param name="riid">Идентификатор IID интерфейса, используемый вызывающим объектом для связи с итоговым объектом. </param>
      <param name="ppvObj">При возвращении данного метода содержит ссылку на созданный объект.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllCustData(System.IntPtr)">
      <summary>Получает все пользовательские элементы данных для библиотеки.</summary>
      <param name="pCustData">Указатель на CUSTDATA, который содержит все элементы пользовательских данных. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllFuncCustData(System.Int32,System.IntPtr)">
      <summary>Получает все пользовательские данные из заданной функции.</summary>
      <param name="index">Индекс функции, для которой нужно получить пользовательские данные. </param>
      <param name="pCustData">Указатель на CUSTDATA, который содержит все элементы пользовательских данных. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllImplTypeCustData(System.Int32,System.IntPtr)">
      <summary>Получает все пользовательские данные для заданного типа реализации.</summary>
      <param name="index">Индекс типа реализации для пользовательских данных. </param>
      <param name="pCustData">Указатель на CUSTDATA, который содержит все элементы пользовательских данных. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllParamCustData(System.Int32,System.Int32,System.IntPtr)">
      <summary>Получает все пользовательские данные для заданного параметра функции.</summary>
      <param name="indexFunc">Индекс функции, для которой нужно получить пользовательские данные. </param>
      <param name="indexParam">Индекс параметра этой функции, для которой нужно получить пользовательские данные. </param>
      <param name="pCustData">Указатель на CUSTDATA, который содержит все элементы пользовательских данных. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllVarCustData(System.Int32,System.IntPtr)">
      <summary>Получает переменную для пользовательских данных.</summary>
      <param name="index">Индекс переменной, для которой нужно получить пользовательские данные. </param>
      <param name="pCustData">Указатель на CUSTDATA, который содержит все элементы пользовательских данных. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>Извлекает библиотеку типов, содержащую описание этого типа и его индекс внутри этой библиотеки типов.</summary>
      <param name="ppTLB">При возвращении данного метода содержит ссылку на содержащую библиотеку типов.Этот параметр передается без инициализации.</param>
      <param name="pIndex">При возвращении данного метода содержит ссылку на индекс описания типа в содержащей библиотеке типов.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetCustData(System.Guid@,System.Object@)">
      <summary>Получает пользовательские данные.</summary>
      <param name="guid">Идентификатор GUID, используемый для определения данных. </param>
      <param name="pVarVal">При возвращении данного метода содержит Object, указывающий, куда поместить извлеченные данные.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>Возвращает описание или спецификацию точки входа для функции в динамической библиотеке DLL.</summary>
      <param name="memid">Идентификатор функции элемента, для которого возвращается описание входа динамической библиотеки DLL. </param>
      <param name="invKind">Одно из значений <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" />, указывающее тип элемента, определенного с помощью <paramref name="memid" />. </param>
      <param name="pBstrDllName">Если значение не равно null, функция задает для параметра <paramref name="pBstrDllName" /> значение BSTR, содержащее имя динамической библиотеки DLL. </param>
      <param name="pBstrName">Если значение не равно null, функция задает для <paramref name="lpbstrName" /> значение BSTR, содержащее имя точки входа. </param>
      <param name="pwOrdinal">Если значение не равно null и функция определена порядковым номером, то параметр <paramref name="lpwOrdinal" /> должен указывать на порядковый номер. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Извлекает строку документации, полный файл справки и путь к нему, а также идентификатор контекста разделов справки для заданного описания типа.</summary>
      <param name="index">Идентификатор элемента, для которого возвращается документация. </param>
      <param name="strName">При возвращении данного метода содержит имя метода элемента.Этот параметр передается без инициализации.</param>
      <param name="strDocString">При возвращении данного метода содержит строку документации для заданного элемента.Этот параметр передается без инициализации.</param>
      <param name="dwHelpContext">При возвращении данного метода содержит ссылку на контекст справки, связанный с заданным элементом.Этот параметр передается без инициализации.</param>
      <param name="strHelpFile">При возвращении данного метода содержит полное имя файла справки.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>Возвращает строку документации, полное имя файла справки и путь к нему, используемый контекст локализации, а также идентификатор контекста для раздела справки библиотеки в файле справки.</summary>
      <param name="memid">Идентификатор элемента для описания типа. </param>
      <param name="pbstrHelpString">При возвращении данного метода содержит BSTR с именем заданного элемента.Если вызывающему объекту не требуется имя элемента, <paramref name="pbstrHelpString" /> может иметь значение null.Этот параметр передается без инициализации.</param>
      <param name="pdwHelpStringContext">При возвращении данного метода содержит контекст локализации справки.Если вызывающему объекту не требуется контекст справки, <paramref name="pdwHelpStringContext" /> может иметь значение null.Этот параметр передается без инициализации.</param>
      <param name="pbstrHelpStringDll">При возвращении данного метода содержит BSTR с полным именем файла, содержащего библиотеку DLL, используемую для файла справки.Если вызывающему объекту не требуется имя файла, <paramref name="pbstrHelpStringDll" /> может иметь значение null.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Получает пользовательские данные из заданной функции.</summary>
      <param name="index">Индекс функции, для которой нужно получить пользовательские данные. </param>
      <param name="guid">Идентификатор GUID, используемый для определения данных. </param>
      <param name="pVarVal">При возвращении данного метода содержит Object, который указывал, куда поместить данные.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>Возвращает структуру <see cref="T:System.Runtime.InteropServices.FUNCDESC" />, содержащую сведения о заданной функции.</summary>
      <param name="index">Индекс возвращаемого описания функции. </param>
      <param name="ppFuncDesc">При возвращении данного метода содержит ссылку на структуру FUNCDESC, описывающую заданную функцию.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncIndexOfMemId(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.Int32@)">
      <summary>Привязывает к определенному элементу, основанному на известном идентификаторе DISPID, где имя элемента неизвестно (например, при привязке к элементу, используемому по умолчанию).</summary>
      <param name="memid">Идентификатор элемента. </param>
      <param name="invKind">Одно из значений <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" />, указывающее тип элемента, определенного с помощью memid.</param>
      <param name="pFuncIndex">При возвращении данного метода содержит индекс на функцию.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>Сопоставляет друг с другом имена и идентификаторы элементов и параметров.</summary>
      <param name="rgszNames">Массив имен, которые следует сопоставить. </param>
      <param name="cNames">Число сопоставляемых имен. </param>
      <param name="pMemId">При возвращении данного метода содержит ссылку на массив, в который помещены сопоставления имен.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Получает тип реализации пользовательских данных.</summary>
      <param name="index">Индекс типа реализации для пользовательских данных. </param>
      <param name="guid">Идентификатор GUID, используемый для определения данных. </param>
      <param name="pVarVal">При возвращении данного метода содержит Object, указывающий, куда поместить извлеченные данные.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>Возвращает значение <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> для одного реализованного интерфейса или базового интерфейса в описании типа.</summary>
      <param name="index">Индекс реализованного интерфейса или базового интерфейса. </param>
      <param name="pImplTypeFlags">При возвращении данного метода содержит ссылку на перечисление IMPLTYPEFLAGS.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetMops(System.Int32,System.String@)">
      <summary>Извлекает сведения о маршалинге.</summary>
      <param name="memid">Идентификатор элемента, показывающий, какие необходимы сведения о маршалинге. </param>
      <param name="pBstrMops">При возвращении данного метода содержит ссылку на строку opcode, используемую при маршалинге полей структуры, описанной в описании ссылочного типа, либо возвращает null, если сведения отсутствуют.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>Извлекает переменную с указанным идентификатором (либо имя свойства или метода и его параметры), соответствующую заданному идентификатору функции.</summary>
      <param name="memid">Идентификатор элемента, для которого возвращается имя (или имена). </param>
      <param name="rgBstrNames">При возвращении данного метода содержит имя (или имена), связанное с элементом.Этот параметр передается без инициализации.</param>
      <param name="cMaxNames">Длина массива <paramref name="rgBstrNames" />. </param>
      <param name="pcNames">При возвращении данного метода содержит число имен в массиве <paramref name="rgBstrNames" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetParamCustData(System.Int32,System.Int32,System.Guid@,System.Object@)">
      <summary>Получает заданный параметр пользовательских данных.</summary>
      <param name="indexFunc">Индекс функции, для которой нужно получить пользовательские данные. </param>
      <param name="indexParam">Индекс параметра этой функции, для которой нужно получить пользовательские данные. </param>
      <param name="guid">Идентификатор GUID, используемый для определения данных. </param>
      <param name="pVarVal">При возвращении данного метода содержит Object, указывающий, куда поместить извлеченные данные.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Извлекает описания типов, на которые указывают ссылки, если описание типа ссылается на другие описания типов.</summary>
      <param name="hRef">Возвращаемый дескриптор для описания ссылочного типа. </param>
      <param name="ppTI">При возвращении данного метода содержит описание ссылочного типа.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>Извлекает описание типа для реализованных типов интерфейсов, если в описании типа описывается класс COM.</summary>
      <param name="index">Индекс реализованного типа, дескриптор которого возвращается. </param>
      <param name="href">При возвращении данного метода содержит ссылку на дескриптор для реализованного интерфейса.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeAttr(System.IntPtr@)">
      <summary>Возвращает структуру <see cref="T:System.Runtime.InteropServices.TYPEATTR" />, содержащую атрибуты описания типа.</summary>
      <param name="ppTypeAttr">При возвращении данного метода содержит ссылку на структуру, содержащую атрибуты этого описания типа.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Извлекает интерфейс ITypeComp описания типа, позволяющий компилятору клиента выполнить привязку к элементам описания типа.</summary>
      <param name="ppTComp">При возвращении данного метода содержит ссылку на ITypeComp содержащей библиотеки типов.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeFlags(System.Int32@)">
      <summary>Возвращает флаги типов без каких-либо распределений.Этот метод возвращает флаг типа DWORD, который расширяет флаги типов без увеличения TYPEATTR (атрибут type).</summary>
      <param name="pTypeFlags">При возвращении данного метода содержит ссылку DWORD на TYPEFLAG.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeKind(System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Возвращает перечисление TYPEKIND быстро, не выполняя распределения.</summary>
      <param name="pTypeKind">При возвращении данного метода содержит ссылку на перечисление TYPEKIND.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Получает переменную для пользовательских данных.</summary>
      <param name="index">Индекс переменной, для которой нужно получить пользовательские данные. </param>
      <param name="guid">Идентификатор GUID, используемый для определения данных. </param>
      <param name="pVarVal">При возвращении данного метода содержит Object, указывающий, куда поместить извлеченные данные.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>Извлекает структуру VARDESC, описывающую указанную переменную.</summary>
      <param name="index">Индекс возвращаемого описания переменной. </param>
      <param name="ppVarDesc">При возвращении данного метода содержит ссылку на структуру VARDESC, описывающую заданную переменную.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarIndexOfMemId(System.Int32,System.Int32@)">
      <summary>Привязывает к определенному элементу, основанному на известном идентификаторе DISPID, где имя элемента неизвестно (например, при привязке к элементу, используемому по умолчанию).</summary>
      <param name="memid">Идентификатор элемента. </param>
      <param name="pVarIndex">При возвращении данного метода содержит индекс для <paramref name="memid" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>Вызывает метод или обращается к свойству объекта, реализующему интерфейс, описанный в описании типа.</summary>
      <param name="pvInstance">Ссылка на интерфейс, описанный в данном описании типа. </param>
      <param name="memid">Идентификатор элемента интерфейса. </param>
      <param name="wFlags">Флаги, описывающие контекст используемого вызова. </param>
      <param name="pDispParams">Ссылка на структуру, содержащую массив аргументов, массив идентификаторов DISPID именованных аргументов, а также количество элементов в каждом массиве. </param>
      <param name="pVarResult">Ссылка на место хранения результата.Если <paramref name="wFlags" /> задает DISPATCH_PROPERTYPUT или DISPATCH_PROPERTYPUTREF, то параметр <paramref name="pVarResult" /> не учитывается.Если результат не требуется, можно задать значение null.</param>
      <param name="pExcepInfo">Указатель на структуру сведений об исключении, заполняемую только при возвращении DISP_E_EXCEPTION. </param>
      <param name="puArgErr">Если Invoke возвращает DISP_E_TYPEMISMATCH, то параметр <paramref name="puArgErr" /> показывает индекс аргумента с неверным типом.Если ошибка возвращается для нескольких аргументов, <paramref name="puArgErr" /> показывает только первый из них.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseFuncDesc(System.IntPtr)">
      <summary>Освобождает структуру <see cref="T:System.Runtime.InteropServices.FUNCDESC" />, ранее возвращенную методом <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pFuncDesc">Ссылка на освобождаемую структуру FUNCDESC. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseTypeAttr(System.IntPtr)">
      <summary>Освобождает структуру <see cref="T:System.Runtime.InteropServices.TYPEATTR" />, ранее возвращенную методом <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />.</summary>
      <param name="pTypeAttr">Ссылка на освобождаемую структуру TYPEATTR. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseVarDesc(System.IntPtr)">
      <summary>Освобождает структуру VARDESC, ранее возвращенную методом <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pVarDesc">Ссылка на освобождаемую структуру VARDESC. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib">
      <summary>Предоставляет управляемое определение интерфейса ITypeLib.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>Обнаруживает экземпляры описания типа в библиотеке типов.</summary>
      <param name="szNameBuf">Имя, которое требуется найти.Это параметр In или Out.</param>
      <param name="lHashVal">Хэш-значение для ускорения поиска, вычисленное при помощи функции LHashValOfNameSys.Если значение <paramref name="lHashVal" /> равно 0, то значение рассчитывается.</param>
      <param name="ppTInfo">При возвращении данного метода содержит массив указателей на описания типов, которые содержат имя, заданное в <paramref name="szNameBuf" />.Этот параметр передается без инициализации.</param>
      <param name="rgMemId">Массив значений MEMBERID найденных элементов. <paramref name="rgMemId" />[i] является значением MEMBERID, выполняющим индексацию в описании типа, заданном параметром <paramref name="ppTInfo" />[i].Не может иметь значение null.</param>
      <param name="pcFound">При входе показывает количество разыскиваемых экземпляров.Например, если <paramref name="pcFound" /> = 1 можно использовать для поиска первого экземпляра.Поиск прекращается после обнаружения первого экземпляра.При выходе показывает число обнаруженных экземпляров.Если значения in и out параметра <paramref name="pcFound" /> идентичны, возможно найдены не все описания типа, содержащие нужное имя.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Возвращает строку документации библиотеки, полный файл справки и путь к нему, а также идентификатор контекста для раздела справки библиотеки в файле справки.</summary>
      <param name="index">Индекс описания типа, для которого возвращается документация. </param>
      <param name="strName">При возвращении данного метода содержит строку, представляющую имя заданного элемента.Этот параметр передается без инициализации.</param>
      <param name="strDocString">При возвращении данного метода содержит строку, представляющую строку документации для заданного элемента.Этот параметр передается без инициализации.</param>
      <param name="dwHelpContext">При возвращении данного метода содержит идентификатор контекста справки, связанный с заданным элементом.Этот параметр передается без инициализации.</param>
      <param name="strHelpFile">При возвращении данного метода содержит строку, представляющую полное имя файла справки.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)">
      <summary>Возвращает структуру, содержащую атрибуты библиотеки.</summary>
      <param name="ppTLibAttr">При возвращении данного метода содержит структуру, содержащую атрибуты библиотеки.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Позволяет компилятору клиента выполнить привязку к библиотечным типам, переменным, константам и глобальным функциям.</summary>
      <param name="ppTComp">При возвращении данного метода содержит экземпляр ITypeComp для ITypeLib.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Возвращает описание заданного типа из библиотеки.</summary>
      <param name="index">Индекс возвращаемого интерфейса ITypeInfo. </param>
      <param name="ppTI">При возвращении данного метода содержит ITypeInfo с описанием типа, на который ссылается <paramref name="index" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoCount">
      <summary>Возвращает число описаний типов в библиотеке типов.</summary>
      <returns>Число описаний типов в библиотеке типов.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Загружает описание типа, соответствующее заданному идентификатору GUID.</summary>
      <param name="guid">IID интерфейса или CLSID класса, информация о типе которого запрашивается. </param>
      <param name="ppTInfo">При возвращении данного метода содержит требуемый интерфейс ITypeInfo.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Возвращает тип описания типа.</summary>
      <param name="index">Индекс описания типа внутри библиотеки типов. </param>
      <param name="pTKind">При возвращении данного метода содержит ссылку на перечисление TYPEKIND для описания типа.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.IsName(System.String,System.Int32)">
      <summary>Показывает, содержит ли переданная строка имя типа или члена, описанного в библиотеке.</summary>
      <returns>Значение true, если параметр <paramref name="szNameBuf" /> обнаружен в библиотеке типов; в противном случае — значение false.</returns>
      <param name="szNameBuf">Строка для проверки.Это параметр In или Out.</param>
      <param name="lHashVal">Хэш-значение для <paramref name="szNameBuf" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.ReleaseTLibAttr(System.IntPtr)">
      <summary>Освобождает структуру <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" />, первоначально полученную из метода <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" />.</summary>
      <param name="pTLibAttr">Структура TLIBATTR, которую следует освободить. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib2">
      <summary>Предоставляет управляемое определение интерфейса ITypeLib2.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>Обнаруживает экземпляры описания типа в библиотеке типов.</summary>
      <param name="szNameBuf">Имя, которое требуется найти. </param>
      <param name="lHashVal">Хэш-значение для ускорения поиска, вычисленное при помощи функции LHashValOfNameSys.Если значение <paramref name="lHashVal" /> равно 0, то значение рассчитывается.</param>
      <param name="ppTInfo">При возвращении данного метода содержит массив указателей на описания типов, которые содержат имя, заданное в <paramref name="szNameBuf" />.Этот параметр передается без инициализации.</param>
      <param name="rgMemId">При возвращении данного метода содержит массив идентификаторов MEMBERID найденных элементов; <paramref name="rgMemId" /> [i] является значением MEMBERID, выполняющим индексацию в описании типа, заданном параметром <paramref name="ppTInfo" /> [i].Этот параметр не может иметь значение null.Этот параметр передается без инициализации.</param>
      <param name="pcFound">На входе значение, переданное ссылкой, указывающее, сколько экземпляров следует искать.Например, если <paramref name="pcFound" /> = 1 можно использовать для поиска первого экземпляра.Поиск прекращается после обнаружения первого экземпляра.При выходе показывает число обнаруженных экземпляров.Если значения in и out параметра <paramref name="pcFound" /> идентичны, возможно найдены не все описания типа, содержащие нужное имя.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetAllCustData(System.IntPtr)">
      <summary>Получает все пользовательские элементы данных для библиотеки.</summary>
      <param name="pCustData">Указатель на CUSTDATA, который содержит все элементы пользовательских данных. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetCustData(System.Guid@,System.Object@)">
      <summary>Получает пользовательские данные.</summary>
      <param name="guid">Идентификатор <see cref="T:System.Guid" />, переданный ссылкой, который используется для определения данных. </param>
      <param name="pVarVal">При возвращении данного метода содержит объект, указывающий, куда поместить извлеченные данные.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Возвращает строку документации библиотеки, полный файл справки и путь к нему, а также идентификатор контекста для раздела справки библиотеки в файле справки.</summary>
      <param name="index">Индекс описания типа, для которого возвращается документация. </param>
      <param name="strName">При возвращении данного метода содержит строку, задающую имя заданного элемента.Этот параметр передается без инициализации.</param>
      <param name="strDocString">При возвращении данного метода содержит строку документации для заданного элемента.Этот параметр передается без инициализации.</param>
      <param name="dwHelpContext">При возвращении данного метода содержит идентификатор контекста справки, связанный с заданным элементом.Этот параметр передается без инициализации.</param>
      <param name="strHelpFile">При возвращении данного метода содержит строку, задающую полное имя файла справки.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>Извлекает строку документации библиотеки, полное имя файла справки и путь к нему, используемый контекст локализации и идентификатор контекста для раздела справки библиотеки в файле справки.</summary>
      <param name="index">Индекс описания типа, документация которого должна быть возвращена; если параметр <paramref name="index" /> равен -1, возвращается документация для библиотеки. </param>
      <param name="pbstrHelpString">При возвращении данного метода содержит строку BSTR, задающую имя заданного элемента.Если вызывающему объекту не требуется имя элемента, <paramref name="pbstrHelpString" /> может иметь значение null.Этот параметр передается без инициализации.</param>
      <param name="pdwHelpStringContext">При возвращении данного метода содержит контекст локализации справки.Если вызывающему объекту не требуется контекст справки, <paramref name="pdwHelpStringContext" /> может иметь значение null.Этот параметр передается без инициализации.</param>
      <param name="pbstrHelpStringDll">При возвращении данного метода содержит строку BSTR, задающую полное имя файла, содержащего библиотеку DLL, используемую для файла справки.Если вызывающему объекту не требуется имя файла, <paramref name="pbstrHelpStringDll" /> может иметь значение null.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibAttr(System.IntPtr@)">
      <summary>Возвращает структуру, содержащую атрибуты библиотеки.</summary>
      <param name="ppTLibAttr">При возвращении данного метода содержит структуру, содержащую атрибуты библиотеки.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibStatistics(System.IntPtr,System.Int32@)">
      <summary>Возвращает статистику о библиотеке типов, необходимую для эффективного определения размеров хэш-таблиц.</summary>
      <param name="pcUniqueNames">Указатель на число уникальных имен.Если вызывающему объекту не требуются эти сведения, задайте значение null.</param>
      <param name="pcchUniqueNames">При возвращении данного метода содержит указатель на изменение числа уникальных имен.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Позволяет компилятору клиента выполнить привязку к библиотечным типам, переменным, константам и глобальным функциям.</summary>
      <param name="ppTComp">При возвращении данного метода содержит экземпляр ITypeComp для ITypeLib.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Возвращает описание заданного типа из библиотеки.</summary>
      <param name="index">Индекс возвращаемого интерфейса ITypeInfo. </param>
      <param name="ppTI">При возвращении данного метода содержит ITypeInfo с описанием типа, на который ссылается <paramref name="index" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoCount">
      <summary>Возвращает число описаний типов в библиотеке типов.</summary>
      <returns>Число описаний типов в библиотеке типов.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Загружает описание типа, соответствующее заданному идентификатору GUID.</summary>
      <param name="guid">Идентификатор <see cref="T:System.Guid" />, переданный ссылкой, представляющий IID интерфейса CLSID класса, для которого требуются сведения о типе. </param>
      <param name="ppTInfo">При возвращении данного метода содержит требуемый интерфейс ITypeInfo.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Возвращает тип описания типа.</summary>
      <param name="index">Индекс описания типа внутри библиотеки типов. </param>
      <param name="pTKind">При возвращении данного метода содержит ссылку на перечисление TYPEKIND для описания типа.Этот параметр передается без инициализации.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.IsName(System.String,System.Int32)">
      <summary>Показывает, содержит ли переданная строка имя типа или члена, описанного в библиотеке.</summary>
      <returns>Значение true, если параметр <paramref name="szNameBuf" /> обнаружен в библиотеке типов; в противном случае — значение false.</returns>
      <param name="szNameBuf">Строка для проверки. </param>
      <param name="lHashVal">Хэш-значение для <paramref name="szNameBuf" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.ReleaseTLibAttr(System.IntPtr)">
      <summary>Освобождает структуру <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" />, первоначально полученную из метода <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" />.</summary>
      <param name="pTLibAttr">Структура TLIBATTR, которую следует освободить. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.LIBFLAGS">
      <summary>Определяет флаги, применяемые к библиотекам типов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FCONTROL">
      <summary>Библиотеки типов описывают элементы управления и не отображаются в обозревателях типов, предназначенных для невизуальных объектов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHASDISKIMAGE">
      <summary>Библиотека типов хранится на диске.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHIDDEN">
      <summary>Библиотеку типов не следует отображать для пользователей, хотя ее использование не ограничено.Библиотеку типов не следует использовать с помощью элементов управления.узлы должны создавать новую библиотеку типов, инкапсулирующую элемент управления с расширенными свойствами.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FRESTRICTED">
      <summary>Библиотека типов ограничена и не должна отображаться для пользователей.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMDESC">
      <summary>Содержит сведения о способе передачи значения, возвращаемого функцией, параметром или элементом структуры между процессами.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.lpVarValue">
      <summary>Представляет указатель на значение, передаваемое между процессами.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.wParamFlags">
      <summary>Представляет значения битовой маски, описывающие элемент структуры, параметр или возвращаемое значение.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMFLAG">
      <summary>Описывает способ передачи значения, возвращенного функцией, параметром или элементом структуры между процессами.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASCUSTDATA">
      <summary>Для параметра заданы пользовательские данные.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASDEFAULT">
      <summary>Для параметра определено поведение по умолчанию.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FIN">
      <summary>Параметр передает сведения из вызывающего объекта в вызываемый объект.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FLCID">
      <summary>Этот параметр является локальным идентификатором клиентского приложения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOPT">
      <summary>Это необязательный параметр.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOUT">
      <summary>Этот параметр возвращает сведения из вызываемого объекта в вызывающий объект.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FRETVAL">
      <summary>Этот параметр является значением, возвращаемым членом.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_NONE">
      <summary>Не задает, передает ли параметр сведения или получает их.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATDATA">
      <summary>Предоставляет управляемое определение структуры STATDATA.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advf">
      <summary>Представляет значение перечисления <see cref="T:System.Runtime.InteropServices.ComTypes.ADVF" />, определяющее, когда приемник будет уведомлен об изменениях в данных.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advSink">
      <summary>Представляет интерфейс <see cref="T:System.Runtime.InteropServices.ComTypes.IAdviseSink" />, который будет получать уведомления об изменениях.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.connection">
      <summary>Представляет маркер, уникально определяющий вспомогательное соединение.Этот маркер возвращается методом, настраивающим вспомогательное соединение.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.formatetc">
      <summary>Представляет структуру <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> для данных, интересующих приемник уведомлений.Приемник получает уведомление об изменениях в данных, заданных этой структурой <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATSTG">
      <summary>Содержит статистические данные об открытом хранилище, потоке или объекте массива байтов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.atime">
      <summary>Задает время последнего обращения к хранилищу, потоку или массиву байтов. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.cbSize">
      <summary>Задает размер в байтах для потока или массива байтов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.clsid">
      <summary>Указывает идентификатор класса для объекта хранилища.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.ctime">
      <summary>Показывает время создания хранилища, потока или массива байтов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfLocksSupported">
      <summary>Показывает типы региональных блокировок, поддерживаемые потоком или массивом байтов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfMode">
      <summary>Показывает режим доступа, заданный при открытии объекта.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfStateBits">
      <summary>Показывает текущие биты состояния объекта хранилища (значение чаще всего устанавливается методом IStorage::SetStateBits).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.mtime">
      <summary>Показывает время последнего изменения хранилища, потока или массива байтов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.pwcsName">
      <summary>Представляет указатель на строку с завершающим нулем, содержащую имя объекта, описанного этой структурой.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.reserved">
      <summary>Зарезервировано для использования в будущем.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.type">
      <summary>Показывает тип объекта хранилища, являющегося одним из значений перечисления STGTY.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM">
      <summary>Предоставляет управляемое определение структуры STGMEDIUM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease">
      <summary>Представляет указатель на экземпляр интерфейса, который позволяет отправляющему процессу контролировать способ освобождения хранилища при вызове функции ReleaseStgMedium получающим процессом.Если <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> равняется null, ReleaseStgMedium использует процедуры по умолчанию для освобождения хранилища; в противном случае ReleaseStgMedium использует заданный интерфейс IUnknown.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.tymed">
      <summary>Задает тип среды хранения.Процедура маршалинга и процедура распаковки используют это значение, чтобы определить, какой элемент объединения использовался.Это значение должно быть одним из элементов перечисления <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.unionmember">
      <summary>Представляет дескриптор, строку или указатель интерфейса, которые получающий процесс может использовать для доступа к передаваемым данным.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.SYSKIND">
      <summary>Указывает целевую платформу операционной системы.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_MAC">
      <summary>Целевая операционная система для библиотеки типов — Apple Macintosh.По умолчанию все поля данных выравниваются по границам четных байтов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN16">
      <summary>Целевая операционная система для библиотеки типов — 16-разрядные системы Windows.Поля данных по умолчанию упакованы.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN32">
      <summary>Целевая операционная система для библиотеки типов — 32-разрядные системы Windows.Поля данных по умолчанию выровнены естественным образом (например, 2-байтовые целые числа выровнены по границам четных байтов, 4-байтовые целые числа — по границам четырехбайтовых слов и т. д.).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN64">
      <summary>Целевая операционная система для библиотеки типов — 64-разрядные системы Windows.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYMED">
      <summary>Предоставляет управляемое определение структуры TYMED.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ENHMF">
      <summary>Среда хранения представляет собой расширенный метафайл.Если элемент <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> имеет значение null, процесс назначения должен использовать DeleteEnhMetaFile для удаления растрового изображения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_FILE">
      <summary>Среда хранения представляет собой файл на диске, заданный с помощью пути.Если элемент STGMEDIUM<see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> имеет значение null, процесс назначения должен использовать OpenFile для удаления файла.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_GDI">
      <summary>Среда хранения представляет собой компонент "Интерфейс графических устройств" (GDI) (HBITMAP).Если элемент <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> имеет значение null, процесс назначения должен использовать DeleteObject для удаления растрового изображения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_HGLOBAL">
      <summary>Среда хранения представляет собой глобальный дескриптор памяти (HGLOBAL).Глобальный дескриптор выделяется с помощью флага GMEM_SHARE.Если элемент <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> имеет значение null, процесс назначения должен использовать GlobalFree для освобождения памяти.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTORAGE">
      <summary>Среда хранения представляет собой компонент хранения, заданный указателем IStorage.Данные находятся в потоках и хранилищах, содержащихся в этом экземпляре IStorage.Если элемент <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> не имеет значение null, процесс назначения должен использовать IStorage::Release для освобождения компонента хранения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTREAM">
      <summary>Среда хранения представляет собой объект потока, заданный указателем IStream.ISequentialStream::Read служит для чтения данных.Если элемент <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> не имеет значение null, процесс назначения должен использовать IStream::Release для освобождения компонента потока.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_MFPICT">
      <summary>Среда хранения представляет собой метафайл (HMETAFILE).Для доступа к данным метафайла используются функции Windows или WIN32.Если элемент <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> имеет значение null, процесс назначения должен использовать DeleteMetaFile для удаления растрового изображения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_NULL">
      <summary>Данные не передаются.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEATTR">
      <summary>Содержит атрибуты UCOMITypeInfo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbAlignment">
      <summary>Задает выравнивание по границе байта для экземпляра этого типа.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeInstance">
      <summary>Размер экземпляра этого типа.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeVft">
      <summary>Размер таблицы виртуальных методов этого типа (VTBL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cFuncs">
      <summary>Указывает количество функций в интерфейсе, описанном структурой.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cImplTypes">
      <summary>Указывает количество реализованных интерфейсов в интерфейсе, описанном структурой.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cVars">
      <summary>Указывает число переменных и полей данных в интерфейсе, описанном структурой.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.dwReserved">
      <summary>Зарезервировано для использования в будущем.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.guid">
      <summary>Идентификатор GUID информации о типе.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.idldescType">
      <summary>Атрибуты IDL описанного типа.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lcid">
      <summary>Языковой стандарт имен элементов и строк документации.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lpstrSchema">
      <summary>Зарезервировано для использования в будущем.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.MEMBER_ID_NIL">
      <summary>Константа, используемая с полями <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidConstructor" /> и <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidDestructor" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidConstructor">
      <summary>Идентификатор конструктора или <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" />, если конструктор отсутствует.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidDestructor">
      <summary>Идентификатор деструктора или <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" />, если деструктор отсутствует.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.tdescAlias">
      <summary>Если <see cref="F:System.Runtime.InteropServices.TYPEATTR.typekind" /> == <see cref="F:System.Runtime.InteropServices.TYPEKIND.TKIND_ALIAS" />, определяет тип, для которого данный тип является псевдонимом.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.typekind">
      <summary>Значение <see cref="T:System.Runtime.InteropServices.TYPEKIND" />, описывающее тип, описанный этими данными.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMajorVerNum">
      <summary>Основной номер версии.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMinorVerNum">
      <summary>Дополнительный номер версии.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wTypeFlags">
      <summary>Значение <see cref="T:System.Runtime.InteropServices.TYPEFLAGS" />, описывающее эти данные.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEDESC">
      <summary>Описывает тип переменной, возвращаемый тип функции или тип параметра функции.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.lpValue">
      <summary>Если переменная равна VT_SAFEARRAY или VT_PTR, то поле lpValue содержит указатель на TYPEDESC, показывающий тип элемента.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.vt">
      <summary>Показывает тип variant элемента, описанного при помощи TYPEDESC.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEFLAGS">
      <summary>Определяет свойства и атрибуты описания типа.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAGGREGATABLE">
      <summary>Класс поддерживает агрегирование.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAPPOBJECT">
      <summary>Описание типа, описывающее объект Application.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCANCREATE">
      <summary>Экземпляры этого типа могут быть созданы при помощи ITypeInfo::CreateInstance.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCONTROL">
      <summary>Этот тип является элементом управления, из которого производятся другие типы. Он не должен отображаться для пользователей.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDISPATCHABLE">
      <summary>Показывает, что интерфейс является производным от IDispatch, прямо или косвенно.Этот флаг вычисляется: для него не существует языка описания объекта.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDUAL">
      <summary>Интерфейс поддерживает и IDispatch, и привязку VTBL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FHIDDEN">
      <summary>Этот тип не должен отображаться для браузеров.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FLICENSED">
      <summary>Этот тип лицензирован.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FNONEXTENSIBLE">
      <summary>Интерфейс не может добавлять члены в процессе выполнения.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FOLEAUTOMATION">
      <summary>Типы, используемые в данном интерфейсе, полностью совместимы с автоматизацией, включая поддержку привязки VTBL.Установка двойного интерфейса задает и этот флаг, и <see cref="F:System.Runtime.InteropServices.TYPEFLAGS.TYPEFLAG_FDUAL" />.Этот флаг не разрешен на диспетчерских интерфейсах.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPREDECLID">
      <summary>Этот тип определен заранее.Клиентское приложение должно автоматически создать единственный экземпляр объекта с данным атрибутом.Имя переменной, указывающей на данный объект, совпадает с именем класса объекта.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPROXY">
      <summary>Показывает, что интерфейс будет использовать динамическую библиотеку заглушки/прокси-сервера.Этот флаг показывает, что, если регистрация библиотеки типов удалена, не следует удалять регистрацию для прокси-сервера библиотеки типов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREPLACEABLE">
      <summary>Объект поддерживает IConnectionPointWithDefault, и для него задано поведение по умолчанию.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FRESTRICTED">
      <summary>Следует исключить доступность из макроязыков.Этот флаг предназначен для типов уровня системы или типов, которые не должны отображаться обозревателями типов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREVERSEBIND">
      <summary>Показывает, что расширения имен основных интерфейсов должны проверяться перед проверкой дочерних элементов, в отличие от поведения по умолчанию.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEKIND">
      <summary>Задает различные типы данных и функций.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ALIAS">
      <summary>Тип, являющийся псевдонимом для другого типа.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_COCLASS">
      <summary>Набор реализованных интерфейсов компонентов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_DISPATCH">
      <summary>Набор методов и свойств, доступных с помощью IDispatch::Invoke.По умолчанию двойные интерфейсы возвращают значение TKIND_DISPATCH.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ENUM">
      <summary>Набор перечислителей.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_INTERFACE">
      <summary>Тип, содержащий виртуальные функции, каждая из которых — чистая.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MAX">
      <summary>Метка окончания перечисления.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MODULE">
      <summary>Модуль, который может содержать только статические функции и данные (например, динамическая библиотека DLL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_RECORD">
      <summary>Структура без методов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_UNION">
      <summary>Объединение всех методов, смещение которых равно нулю.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPELIBATTR">
      <summary>Определяет конкретную библиотеку типов и предоставляет поддержку локализации имен элементов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.guid">
      <summary>Предоставляет уникальную глобальную библиотеку идентификаторов для библиотеки типов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.lcid">
      <summary>Представляет код языка библиотеки типов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.syskind">
      <summary>Представляет целевую аппаратную платформу библиотеки типов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wLibFlags">
      <summary>Представляет флаги библиотеки.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMajorVerNum">
      <summary>Представляет номер основной версии библиотеки типов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMinorVerNum">
      <summary>Представляет дополнительный номер версии библиотеки типов.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC">
      <summary>Описывает переменную, константу или элемент данных.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.desc">
      <summary>Содержит сведения о переменной.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.elemdescVar">
      <summary>Содержит тип переменной.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.lpstrSchema">
      <summary>Это поле зарезервировано для использования в будущем.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.memid">
      <summary>Показывает идентификатор ID элемента переменной.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.varkind">
      <summary>Определяет, как маршалировать переменную.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.wVarFlags">
      <summary>Определяет свойства переменной.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION">
      <summary>Содержит сведения о переменной.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.lpvarValue">
      <summary>Описывает символьную константу.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.oInst">
      <summary>Показывает смещение переменной внутри экземпляра.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARFLAGS">
      <summary>Определяет константы, определяющие свойства переменной.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FBINDABLE">
      <summary>Переменная поддерживает привязку данных.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTBIND">
      <summary>Переменная является единственным свойством, представляющим объект наилучшим образом.Только одна переменная в сведениях о типе может содержать этот атрибут.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTCOLLELEM">
      <summary>Допускает оптимизацию, в процессе которой компилятор ищет элемент с именем xyz типа abc.Если такой элемент обнаружен и помечен как функция доступа к элементу коллекции, заданной по умолчанию, то создается вызов этой функции-элемента.Разрешается для элементов диспетчерских и обычных интерфейсов, но запрещен для модулей.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDISPLAYBIND">
      <summary>Переменная, отображаемая пользователю как связываемая.<see cref="F:System.Runtime.InteropServices.VARFLAGS.VARFLAG_FBINDABLE" /> также следует задать.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FHIDDEN">
      <summary>Эту переменную не следует показывать пользователю в браузере, хотя она существует и является связываемой.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FIMMEDIATEBIND">
      <summary>Переменная сопоставляется так же, как собственные связываемые свойства.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FNONBROWSABLE">
      <summary>Переменная появляется в обозревателе объектов, но не отображается в обозревателе свойств.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREADONLY">
      <summary>Не следует разрешать присвоение значений этой переменной.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREPLACEABLE">
      <summary>Помечает интерфейс как обладающий заданным по умолчанию поведением.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREQUESTEDIT">
      <summary>При установке все попытки прямого изменения свойства приводят к вызову IPropertyNotifySink::OnRequestEdit.Реализация OnRequestEdit определяет, принято ли изменение.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FRESTRICTED">
      <summary>Переменную следует сделать недоступной из макроязыков.Этот флаг предназначен для переменных уровня системы или для переменных, которые не нужно отображать в обозревателях типов.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FSOURCE">
      <summary>Переменная возвращает объект, являющийся источником событий.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FUIDEFAULT">
      <summary>Переменная отображается по умолчанию в пользовательском интерфейсе.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARKIND">
      <summary>Определяет тип переменной.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_CONST">
      <summary>Структура VARDESC описывает символьную константу.Связанная с ней память отсутствует.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_DISPATCH">
      <summary>Доступ к переменной может быть получен только при помощи IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_PERINSTANCE">
      <summary>Переменная является полем или элементом типа.Она существует в фиксированном смещении в каждом экземпляре типа.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_STATIC">
      <summary>Имеется только один экземпляр переменной.</summary>
    </member>
  </members>
</doc>