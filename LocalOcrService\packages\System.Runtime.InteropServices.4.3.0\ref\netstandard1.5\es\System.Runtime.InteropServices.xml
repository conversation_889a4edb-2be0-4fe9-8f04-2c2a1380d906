﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices</name>
  </assembly>
  <members>
    <member name="T:System.DataMisalignedException">
      <summary>La excepción que se produce cuando una unidad de datos se lee de una dirección, o se escribe en ella, que no es múltiplo del tamaño de los datos.Esta clase no puede heredarse.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DataMisalignedException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.DataMisalignedException" />. </summary>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.DataMisalignedException" /> mediante el mensaje de error especificado.</summary>
      <param name="message">
        <see cref="T:System.String" /> que describe el error.Se pretende que el contenido de <paramref name="message" /> sea inteligible.El llamador de este constructor debe asegurarse de que esta cadena se ha traducido para la actual referencia cultural del sistema.</param>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.DataMisalignedException" /> mediante el mensaje de error y la excepción subyacente especificados.</summary>
      <param name="message">
        <see cref="T:System.String" /> que describe el error.Se pretende que el contenido de <paramref name="message" /> sea inteligible.El llamador de este constructor debe asegurarse de que esta cadena se ha traducido para la actual referencia cultural del sistema.</param>
      <param name="innerException">La excepción que es la causa de la <see cref="T:System.DataMisalignedException" /> actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.DllNotFoundException">
      <summary>Excepción que se produce cuando no se encuentra el archivo DLL especificado en una importación de DLL.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DllNotFoundException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.DllNotFoundException" /> con propiedades predeterminadas.</summary>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.DllNotFoundException" /> con el mensaje de error especificado.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.DllNotFoundException" /> con el mensaje de error especificado y una referencia a la excepción interna que causó esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="inner" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.Reflection.Missing">
      <summary>Representa un tipo <see cref="T:System.Object" /> que falta.Esta clase no puede heredarse.</summary>
    </member>
    <member name="F:System.Reflection.Missing.Value">
      <summary>Representa la única instancia de la clase <see cref="T:System.Reflection.Missing" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ArrayWithOffset">
      <summary>Encapsula una matriz y un desplazamiento dentro de la matriz especificada.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.#ctor(System.Object,System.Int32)">
      <summary>Inicializa una nueva instancia de la estructura <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <param name="array">Matriz administrada. </param>
      <param name="offset">Desplazamiento en bytes del elemento que se va a pasar a través de una invocación de plataforma. </param>
      <exception cref="T:System.ArgumentException">La matriz es mayor de 2 gigabytes (GB).</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Object)">
      <summary>Indica si el objeto especificado coincide con el objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> actual.</summary>
      <returns>Es true si el objeto coincide con <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con esta instancia. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Indica si el objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> especificado coincide con la instancia actual.</summary>
      <returns>Es true si el objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> especificado coincide con la instancia actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> que se va a comparar con esta instancia.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetArray">
      <summary>Devuelve la matriz administrada a la que hace referencia este <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <returns>Matriz administrada a la que hace referencia esta instancia.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetHashCode">
      <summary>Devuelve un código hash para este tipo de valor.</summary>
      <returns>Código hash para esta instancia.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetOffset">
      <summary>Devuelve el desplazamiento proporcionado cuando se construyó este <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <returns>Desplazamiento para esta instancia.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Equality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Determina si dos objetos <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> especificados tienen el mismo valor.</summary>
      <returns>Es true si el valor de <paramref name="a" /> es igual al valor de <paramref name="b" />; de lo contrario, es false.</returns>
      <param name="a">Objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> que se va a comparar con el parámetro <paramref name="b" />. </param>
      <param name="b">Objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> que se va a comparar con el parámetro <paramref name="a" />.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Inequality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Determina si dos objetos <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> especificados tienen valores distintos.</summary>
      <returns>Es true si el valor de <paramref name="a" /> no es igual al valor de <paramref name="b" />; en caso contrario, es false.</returns>
      <param name="a">Objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> que se va a comparar con el parámetro <paramref name="b" />. </param>
      <param name="b">Objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> que se va a comparar con el parámetro <paramref name="a" />.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.BestFitMappingAttribute">
      <summary>Controla si los caracteres Unicode se convierten en los caracteres ANSI coincidentes más cercanos.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BestFitMappingAttribute.#ctor(System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.BestFitMappingAttribute" /> con el valor de la propiedad <see cref="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping" />.</summary>
      <param name="BestFitMapping">Es true para indicar que la asignación de ajuste perfecto está habilitada; en caso contrario, es false.El valor predeterminado es true.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping">
      <summary>Obtiene el comportamiento de la asignación de ajuste perfecto a la hora de convertir caracteres Unicode en caracteres ANSI.</summary>
      <returns>Es true si la asignación de ajuste perfecto está habilitada; en caso contrario, es false.El valor predeterminado es true.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.BestFitMappingAttribute.ThrowOnUnmappableChar">
      <summary>Habilita o deshabilita el inicio de una excepción cuando un carácter Unicode que no se puede asignar se convierte en un carácter ANSI "?".</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.BStrWrapper">
      <summary>Calcula las referencias de los datos de tipo VT_BSTR de código administrado a no administrado.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.Object)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> con el objeto <see cref="T:System.Object" /> especificado.</summary>
      <param name="value">Objeto que se va a ajustar y para el que se van a calcular las referencias como VT_BSTR.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> con el objeto <see cref="T:System.String" /> especificado.</summary>
      <param name="value">Objeto que se va a ajustar y para el que se van a calcular las referencias como VT_BSTR.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BStrWrapper.WrappedObject">
      <summary>Obtiene el objeto <see cref="T:System.String" /> encapsulado cuyas referencias se van a calcular como de tipo VT_BSTR.</summary>
      <returns>Objeto ajustado por <see cref="T:System.Runtime.InteropServices.BStrWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CallingConvention">
      <summary>Especifica la convención de llamada necesaria para llamar a métodos implementados en código no administrado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Cdecl">
      <summary>El llamador limpia la pila.Esto permite llamar a funciones con varargs, que resulta apropiado para métodos que aceptan un número variable de parámetros, como Printf.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.StdCall">
      <summary>El destinatario de la llamada limpia la pila.Esta es la convención predeterminada para la llamada a funciones no administradas con invocación de plataforma.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.ThisCall">
      <summary>El primer parámetro es el puntero this y se almacena en ECX de registro.Los demás parámetros se insertan en la pila.Esta convención de llamada se utiliza para llamar a métodos en clases exportadas desde un archivo DLL no administrado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Winapi">
      <summary>Este miembro no es una convención de llamada en realidad, sino que utiliza la convención de llamada de plataforma predeterminada.Por ejemplo, en Windows la convención de llamada predeterminada es <see cref="F:System.Runtime.InteropServices.CallingConvention.StdCall" /> y en Windows CE.NET es <see cref="F:System.Runtime.InteropServices.CallingConvention.Cdecl" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceAttribute">
      <summary>Indica el tipo de interfaz de clase que debe generarse para una clase expuesta a COM, en caso de que se genere una interfaz.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Int16)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> con el valor de enumeración <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> especificado.</summary>
      <param name="classInterfaceType">Describe el tipo de interfaz que se genera para una clase. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Runtime.InteropServices.ClassInterfaceType)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> con el miembro de enumeración <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> especificado.</summary>
      <param name="classInterfaceType">Uno de los valores de <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> que describe el tipo de interfaz que se genera para una clase. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ClassInterfaceAttribute.Value">
      <summary>Obtiene el valor de <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> que describe qué tipo de interfaz debe generarse para la clase.</summary>
      <returns>Valor de <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> que describe qué tipo de interfaz debe generarse para la clase.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceType">
      <summary>Identifica el tipo de interfaz de clase que se genera para una clase.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDispatch">
      <summary>Indica que la clase sólo admite enlaces en tiempo de ejecución para los clientes COM.Si se solicita, se expone automáticamente a los clientes COM una interfaz dispinterface para la clase.La biblioteca de tipos creada por Tlbexp.exe (Exportador de la biblioteca de tipos) no contiene información de tipos para la interfaz dispinterface con el fin de impedir que los clientes almacenen en memoria caché los DISPID de la interfaz.La interfaz dispinterface no presenta los problemas de control de versiones descritos en <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />, ya que los clientes solo se pueden enlazar a la interfaz en tiempo de ejecución.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDual">
      <summary>Indica que se genera una interfaz de clase dual para la clase y se expone a COM automáticamente.Se genera la información de tipos para la interfaz de clase y se publica en la biblioteca de tipos.Se desaconseja el uso de AutoDual debido a las limitaciones de las versiones descritas en <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.None">
      <summary>Indica que no se genera ninguna interfaz de clase para la clase.Si no se implementan interfaces explícitamente, la clase solo proporcionará acceso de enlace en tiempo de ejecución a través de la interfaz IDispatch.Éste es el valor recomendado para <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />.La utilización de ClassInterfaceType.None es la única manera de exponer funcionalidad a través de interfaces implementadas explícitamente por la clase.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CoClassAttribute">
      <summary>Especifica el identificador de clase de una coclase importada de una biblioteca de tipos.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CoClassAttribute.#ctor(System.Type)">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Runtime.InteropServices.CoClassAttribute" /> con el identificador de clase de la coclase original.</summary>
      <param name="coClass">Un objeto <see cref="T:System.Type" /> que contiene el identificador de clase de la coclase original. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.CoClassAttribute.CoClass">
      <summary>Obtiene el identificador de clase de la coclase original.</summary>
      <returns>Un objeto <see cref="T:System.Type" /> que contiene el identificador de clase de la coclase original.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComAwareEventInfo">
      <summary>Permite el registro enlazado en tiempo de ejecución de un controlador de eventos.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.#ctor(System.Type,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ComAwareEventInfo" /> utilizando el tipo especificado y un nombre del evento en el tipo.</summary>
      <param name="type">Tipo del objeto. </param>
      <param name="eventName">El nombre de un evento en <paramref name="type" />.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.AddEventHandler(System.Object,System.Delegate)">
      <summary>Asocia un controlador de eventos a un objeto COM.</summary>
      <param name="target">El objeto de destino con que el delegado de eventos debería enlazar.</param>
      <param name="handler">El delegado de eventos.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Attributes">
      <summary>Obtiene los atributos de este evento.</summary>
      <returns>Atributos de sólo lectura de este evento.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.DeclaringType">
      <summary>Obtiene la clase que declara este miembro.</summary>
      <returns>Objeto <see cref="T:System.Type" /> de la clase que declara este miembro.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Name">
      <summary>Obtiene el nombre del miembro actual.</summary>
      <returns>Nombre de este miembro.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.RemoveEventHandler(System.Object,System.Delegate)">
      <summary>Desasocia un controlador de eventos de un objeto COM.</summary>
      <param name="target">El objeto de destino al que el delegado de eventos está enlazado.</param>
      <param name="handler">El delegado de eventos.</param>
      <exception cref="T:System.InvalidOperationException">El evento no posee un descriptor de acceso remove público.</exception>
      <exception cref="T:System.ArgumentException">No se puede utilizar el controlador que se pasó.</exception>
      <exception cref="T:System.Reflection.TargetException">En las API de .NET para aplicaciones de la Tienda Windows o en la Biblioteca de clases portable, encuentre la excepción de la clase base, <see cref="T:System.Exception" />, en su lugar.El parámetro <paramref name="target" /> es null y el evento no es estático.O bien <see cref="T:System.Reflection.EventInfo" /> no está declarado en el destino.</exception>
      <exception cref="T:System.MethodAccessException">En las API de .NET para aplicaciones de la Tienda Windows o en la Biblioteca de clases portable, encuentre la excepción de la clase base, <see cref="T:System.MemberAccessException" />, en su lugar.El llamador no tiene permiso de acceso a este miembro.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute">
      <summary>Especifica una interfaz predeterminada que se va a exponer a COM.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute" /> con el objeto <see cref="T:System.Type" /> especificado como la interfaz predeterminada expuesta a COM.</summary>
      <param name="defaultInterface">Valor <see cref="T:System.Type" /> que indica la interfaz predeterminada que se va a exponer a COM. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.Value">
      <summary>Obtiene el objeto <see cref="T:System.Type" /> que especifica la interfaz predeterminada que se va a exponer a COM.</summary>
      <returns>Objeto <see cref="T:System.Type" /> que especifica la interfaz predeterminada que se va a exponer a COM.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventInterfaceAttribute">
      <summary>Identifica la interfaz de origen y la clase que implementa los métodos de la interfaz de evento que se genera cuando se importa una coclase de una biblioteca de tipos COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventInterfaceAttribute.#ctor(System.Type,System.Type)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ComEventInterfaceAttribute" /> con la interfaz de origen y la clase proveedora de eventos.</summary>
      <param name="SourceInterface">
        <see cref="T:System.Type" /> que contiene la interfaz de origen inicial de la biblioteca de tipos.COM utiliza esta interfaz para volver a llamar a la clase administrada.</param>
      <param name="EventProvider">
        <see cref="T:System.Type" /> que contiene la clase que implementa los métodos de la interfaz de eventos. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.EventProvider">
      <summary>Obtiene la clase que implementa los métodos de la interfaz de eventos.</summary>
      <returns>
        <see cref="T:System.Type" /> que contiene la clase que implementa los métodos de la interfaz de eventos.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.SourceInterface">
      <summary>Obtiene la interfaz de origen inicial de la biblioteca de tipos.</summary>
      <returns>
        <see cref="T:System.Type" /> que contiene la interfaz de origen.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventsHelper">
      <summary>Proporciona métodos que permiten agregar y quitar de objetos COM delegados de .NET Framework que administran eventos.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Combine(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>Agrega un delegado a la lista de invocación de eventos que se originan en un objeto COM.</summary>
      <param name="rcw">Objeto COM que desencadena los eventos al que al llamador le gustaría responder.</param>
      <param name="iid">Identificador de la interfaz de origen utilizado por el objeto COM para desencadenar eventos. </param>
      <param name="dispid">Identificador de envío del método en la interfaz de origen.</param>
      <param name="d">Delegado al que se va a invocar cuando se active el evento COM.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Remove(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>Quita un delegado de la lista de invocación de eventos que se originan en un objeto COM.</summary>
      <returns>Delegado que se quitó de la lista de invocación.</returns>
      <param name="rcw">Objeto COM al que está adjunto el delegado.</param>
      <param name="iid">Identificador de la interfaz de origen utilizado por el objeto COM para desencadenar eventos. </param>
      <param name="dispid">Identificador de envío del método en la interfaz de origen.</param>
      <param name="d">Delegado que se va a quitar de la lista de invocación.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.COMException">
      <summary>Excepción que se produce cuando se devuelve un valor HRESULT no reconocido desde una llamada de método COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.COMException" /> con valores predeterminados.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.COMException" /> con el mensaje especificado.</summary>
      <param name="message">Mensaje que indica el motivo de la excepción. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.COMException" /> con un mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="inner" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.COMException" /> con un mensaje y un código de error especificados.</summary>
      <param name="message">El mensaje que indica la causa de que se haya producido la excepción. </param>
      <param name="errorCode">Valor del código de error (HRESULT) asociado a esta excepción. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComImportAttribute">
      <summary>Indica que el tipo con atributos estaba previamente definido en COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComImportAttribute.#ctor">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Runtime.InteropServices.ComImportAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComInterfaceType">
      <summary>Indica cómo exponer una interfaz a COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual">
      <summary>Indica que la interfaz se expone a COM como una interfaz dual, lo que permite los enlaces en tiempo de compilación y en tiempo de ejecución.El valor predeterminado es <see cref="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIDispatch">
      <summary>Indica que una interfaz se expone a COM como una interfaz dispinterface, que sólo permite los enlaces en tiempo de ejecución.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIInspectable">
      <summary>Indica que una interfaz está expuesta a COM como interfaz de Windows en tiempo de ejecución . </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIUnknown">
      <summary>Indica que una interfaz se expone a COM como una interfaz derivada de IUnknown, que solo permite enlaces en tiempo de compilación.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComMemberType">
      <summary>Describe el tipo de un miembro COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.Method">
      <summary>El miembro es un método normal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropGet">
      <summary>El miembro obtiene propiedades.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropSet">
      <summary>El miembro establece propiedades.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute">
      <summary>Muestra una lista de las interfaces expuestas como origen de los eventos COM de la clase atribuida.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> con el nombre de la interfaz de origen de los eventos.</summary>
      <param name="sourceInterfaces">Lista delimitada por valores null de nombres completos de interfaces de origen de eventos. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> con el tipo que se va a utilizar como interfaz de origen.</summary>
      <param name="sourceInterface">
        <see cref="T:System.Type" /> de la interfaz de origen. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> con los tipos que se van a utilizar como interfaces de origen.</summary>
      <param name="sourceInterface1">
        <see cref="T:System.Type" /> de la interfaz de origen predeterminada. </param>
      <param name="sourceInterface2">
        <see cref="T:System.Type" /> de una interfaz de origen. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type)">
      <summary>Inicializa una nueva instancia de la clase ComSourceInterfacesAttribute con los tipos que se van a utilizar como interfaces de origen.</summary>
      <param name="sourceInterface1">
        <see cref="T:System.Type" /> de la interfaz de origen predeterminada. </param>
      <param name="sourceInterface2">
        <see cref="T:System.Type" /> de una interfaz de origen. </param>
      <param name="sourceInterface3">
        <see cref="T:System.Type" /> de una interfaz de origen. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type,System.Type)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> con los tipos que se van a utilizar como interfaces de origen.</summary>
      <param name="sourceInterface1">
        <see cref="T:System.Type" /> de la interfaz de origen predeterminada. </param>
      <param name="sourceInterface2">
        <see cref="T:System.Type" /> de una interfaz de origen. </param>
      <param name="sourceInterface3">
        <see cref="T:System.Type" /> de una interfaz de origen. </param>
      <param name="sourceInterface4">
        <see cref="T:System.Type" /> de una interfaz de origen. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComSourceInterfacesAttribute.Value">
      <summary>Obtiene el nombre completo de la interfaz de origen de los eventos.</summary>
      <returns>Nombre completo de la interfaz de origen de los eventos.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CurrencyWrapper">
      <summary>Encapsula objetos cuyas referencias el contador de referencias debería calcular como VT_CY.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Decimal)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> con el Decimal que se va a encapsular y cuyas referencias se van a calcular como de tipo VT_CY.</summary>
      <param name="obj">Decimal que se va a encapsular y cuyas referencias se van a calcular como VT_CY. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Object)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> con el objeto que contiene el Decimal que se va a encapsular y cuyas referencias se van a calcular como de tipo VT_CY.</summary>
      <param name="obj">Objeto que contiene el Decimal que se va a encapsular y cuyas referencias se van a calcular como de tipo VT_CY. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="obj" /> no es un tipo <see cref="T:System.Decimal" />.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.CurrencyWrapper.WrappedObject">
      <summary>Obtiene el objeto encapsulado cuyas referencias se van a calcular como de tipo VT_CY.</summary>
      <returns>Objeto encapsulado cuyas referencias se van a calcular como de tipo VT_CY.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceMode">
      <summary>Indica si las llamadas IUnknown::QueryInterface del método <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> pueden utilizar la interfaz <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Allow">
      <summary>Las llamadas al método IUnknown::QueryInterface pueden usar la interfaz <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.Cuando usa este valor, la sobrecarga del método <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> funciona como la sobrecarga de <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Ignore">
      <summary>Las llamadas al método IUnknown::QueryInterface deberían ignorar la interfaz <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceResult">
      <summary>Proporciona valores devueltos para el método <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Failed">
      <summary>La interfaz para un id. de interfaz concreto no está disponible.En este caso, la interfaz devuelta es null.Se devuelve E_NOINTERFACE al llamador de IUnknown::QueryInterface.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Handled">
      <summary>Puntero a interfaz que devuelve el método <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> y que puede usarse como el resultado de IUnknown::QueryInterface.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.NotHandled">
      <summary>No se utilizó el QueryInterface personalizado.En su lugar, se debe usar la implementación predeterminada de IUnknown::QueryInterface.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultCharSetAttribute">
      <summary>Especifica el valor de la enumeración <see cref="T:System.Runtime.InteropServices.CharSet" />.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultCharSetAttribute.#ctor(System.Runtime.InteropServices.CharSet)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.DefaultCharSetAttribute" /> con el valor de <see cref="T:System.Runtime.InteropServices.CharSet" /> especificado.</summary>
      <param name="charSet">Uno de los valores de <see cref="T:System.Runtime.InteropServices.CharSet" />.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultCharSetAttribute.CharSet">
      <summary>Obtiene el valor predeterminado de <see cref="T:System.Runtime.InteropServices.CharSet" /> para cualquier llamada a <see cref="T:System.Runtime.InteropServices.DllImportAttribute" />.</summary>
      <returns>El valor predeterminado de <see cref="T:System.Runtime.InteropServices.CharSet" /> para cualquier llamada a <see cref="T:System.Runtime.InteropServices.DllImportAttribute" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute">
      <summary>Especifica las rutas de acceso que se usan para buscar archivos DLL que proporcionan funciones para las invocaciones de plataforma. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.#ctor(System.Runtime.InteropServices.DllImportSearchPath)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> y especifica las rutas que se van a usar cuando se buscan los destinos de las invocaciones de plataforma. </summary>
      <param name="paths">Combinación bit a bit de valores de enumeración que especifican las rutas que la función LoadLibraryEx busca durante las invocaciones de plataforma. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.Paths">
      <summary>Obtiene una combinación bit a bit de valores de enumeración que especifican las rutas que la función LoadLibraryEx busca durante las invocaciones de plataforma. </summary>
      <returns>Una combinación bit a bit de valores de enumeración que especifican las rutas de acceso de búsqueda para llamadas a la plataforma. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultParameterValueAttribute">
      <summary>Establece el valor predeterminado de un parámetro cuando se efectúa la llamada en un lenguaje que admite los parámetros predeterminados.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultParameterValueAttribute.#ctor(System.Object)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.DefaultParameterValueAttribute" /> con el valor predeterminado de un parámetro.</summary>
      <param name="value">Objeto que representa el valor predeterminado de un parámetro.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultParameterValueAttribute.Value">
      <summary>Obtiene el valor predeterminado del parámetro.</summary>
      <returns>Objeto que representa el valor predeterminado de un parámetro.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispatchWrapper">
      <summary>Encapsula los objetos cuyas referencias se van a calcular como VT_DISPATCH.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispatchWrapper.#ctor(System.Object)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> con el objeto que se va a ajustar.</summary>
      <param name="obj">Objeto que se va a ajustar y convertir a <see cref="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> no es una clase ni una matriz.O bien <paramref name="obj" /> no es compatible con IDispatch. </exception>
      <exception cref="T:System.InvalidOperationException">El parámetro <paramref name="obj" /> se ha marcado con un atributo <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> que se pasó con el valor de false.O bienEl parámetro <paramref name="obj" /> hereda de un tipo marcado con un atributo <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> que se pasó con el valor de false.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.DispatchWrapper.WrappedObject">
      <summary>Obtiene el objeto ajustado por <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />.</summary>
      <returns>Objeto ajustado por <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispIdAttribute">
      <summary>Especifica el identificador de envío (DISPID) COM de un método, campo o propiedad.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispIdAttribute.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase DispIdAttribute con el identificador de envío (DISPID) especificado.</summary>
      <param name="dispId">Identificador de envío (DISPID) del miembro. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DispIdAttribute.Value">
      <summary>Obtiene el identificador de envío (DISPID) del miembro.</summary>
      <returns>Identificador de envío (DISPID) del miembro.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportAttribute">
      <summary>Indica que una biblioteca de vínculos dinámicos (DLL) no administrada expone el método con atributos como punto de entrada estático.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DllImportAttribute.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> con el nombre del archivo DLL que contiene el método que se va a importar.</summary>
      <param name="dllName">Nombre del archivo DLL que contiene el método no administrado.Puede incluir un nombre para mostrar del ensamblado, si el archivo DLL está incluido en un ensamblado.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.BestFitMapping">
      <summary>Habilita o deshabilita el comportamiento de asignación de ajuste perfecto al convertir caracteres Unicode en caracteres ANSI.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CallingConvention">
      <summary>Indica la convención de llamada de un punto de entrada.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CharSet">
      <summary>Indica la forma de calcular las referencias de los parámetros de cadena al método, y controla los daños en los nombres.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.EntryPoint">
      <summary>Indica el nombre u ordinal del punto de entrada de la DLL al que se va a llamar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ExactSpelling">
      <summary>Controla si el campo <see cref="F:System.Runtime.InteropServices.DllImportAttribute.CharSet" /> hace que Common Language Runtime busque en un archivo DLL no administrado nombres de puntos de entrada distintos del especificado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.PreserveSig">
      <summary>Indica si los métodos no administrados que tienen el valor devuelto HRESULT o retval se traducen directamente o si los valores devueltos HRESULT o retval se convierten automáticamente en excepciones.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError">
      <summary>Indica que el destinatario de la llamada llamará a la función SetLastError de la API Win32 antes de volver del método con atributos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ThrowOnUnmappableChar">
      <summary>Habilita o deshabilita el inicio de una excepción cuando un carácter Unicode que no se puede asignar se convierte en un carácter ANSI "?".</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.DllImportAttribute.Value">
      <summary>Obtiene el nombre del archivo DLL que contiene el punto de entrada.</summary>
      <returns>Nombre del archivo DLL que contiene el punto de entrada.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportSearchPath">
      <summary>Especifica las rutas de acceso que se usan para buscar archivos DLL que proporcionan funciones para las invocaciones de plataforma. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.ApplicationDirectory">
      <summary>Incluya el directorio de la aplicación en la ruta de búsqueda de DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.AssemblyDirectory">
      <summary>Al buscar dependencias del ensamblado, incluya el directorio que contiene el propio ensamblado y busque en ese directorio primero..NET Framework usa este valor antes de que las rutas de acceso se pasen a la función de Win32 LoadLibraryEx.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.LegacyBehavior">
      <summary>Busque en el directorio de la aplicación y, a continuación, llame a la función LoadLibraryEx de Win32 con la marca LOAD_WITH_ALTERED_SEARCH_PATH.Se omite este valor si se especifica cualquier otro valor.Los sistemas operativos que no admiten el atributo <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> usan este valor y omiten otros valores.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.SafeDirectories">
      <summary>Incluya el directorio de la aplicación, el directorio %WinDir%\System32 y los directorios de usuario en la ruta de búsqueda de DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.System32">
      <summary>Incluya el directorio %WinDir%\System32 en la ruta de búsqueda de DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UseDllDirectoryForDependencies">
      <summary>Buscar las dependencias de una DLL en la carpeta donde se encuentra dicha DLL antes de buscar en otras carpetas. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UserDirectories">
      <summary>Incluya cualquier ruta que se haya agregado explícitamente a la ruta de búsqueda en todo el proceso mediante la función de Win32 AddDllDirectory . </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ErrorWrapper">
      <summary>Encapsula los objetos cuyas referencias se van a calcular como VT_ERROR.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> con el HRESULT que corresponda a la excepción suministrada.</summary>
      <param name="e">Excepción que se convertirá en código de error. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> con el HRESULT del error.</summary>
      <param name="errorCode">HRESULT del error. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Object)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> con un objeto que contiene el HRESULT del error.</summary>
      <param name="errorCode">Objeto que contiene el HRESULT del error. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="errorCode" /> no es un tipo <see cref="T:System.Int32" />.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.ErrorWrapper.ErrorCode">
      <summary>Obtiene el código de error del contenedor.</summary>
      <returns>HRESULT del error.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandle">
      <summary>Proporciona una forma de obtener acceso a un objeto administrado desde la memoria no administrada.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.AddrOfPinnedObject">
      <summary>Recupera la dirección de un objeto en un identificador <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />.</summary>
      <returns>Dirección del objeto anclado como <see cref="T:System.IntPtr" />. </returns>
      <exception cref="T:System.InvalidOperationException">The handle is any type other than <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object)">
      <summary>Asigna un identificador <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" /> para el objeto especificado.</summary>
      <returns>Nuevo <see cref="T:System.Runtime.InteropServices.GCHandle" /> que protege al objeto de la recolección de elementos no utilizados.<see cref="T:System.Runtime.InteropServices.GCHandle" /> debe liberarse con <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> cuando ya no sea necesario.</returns>
      <param name="value">Objeto que utiliza <see cref="T:System.Runtime.InteropServices.GCHandle" />. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object,System.Runtime.InteropServices.GCHandleType)">
      <summary>Asigna un identificador del tipo especificado para el objeto especificado.</summary>
      <returns>Nuevo <see cref="T:System.Runtime.InteropServices.GCHandle" /> del tipo especificado.<see cref="T:System.Runtime.InteropServices.GCHandle" /> debe liberarse con <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> cuando ya no sea necesario.</returns>
      <param name="value">Objeto que utiliza <see cref="T:System.Runtime.InteropServices.GCHandle" />. </param>
      <param name="type">Uno de los valores de <see cref="T:System.Runtime.InteropServices.GCHandleType" />, que indica el tipo de <see cref="T:System.Runtime.InteropServices.GCHandle" /> que se va a crear. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Equals(System.Object)">
      <summary>Determina si el objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> especificado es igual al objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> actual.</summary>
      <returns>Es true si el objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> especificado es igual al objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> actual; de lo contrario, es false.</returns>
      <param name="o">Objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> que se va a comparar con el objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> actual.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Free">
      <summary>Libera un <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <exception cref="T:System.InvalidOperationException">The handle was freed or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.FromIntPtr(System.IntPtr)">
      <summary>Devuelve un nuevo objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> creado a partir de un identificador a un objeto administrado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> que corresponde al parámetro de valor.  </returns>
      <param name="value">Identificador <see cref="T:System.IntPtr" /> a un objeto administrado del que se va a crear un objeto <see cref="T:System.Runtime.InteropServices.GCHandle" />.</param>
      <exception cref="T:System.InvalidOperationException">The value of the <paramref name="value" /> parameter is <see cref="F:System.IntPtr.Zero" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.GetHashCode">
      <summary>Devuelve un identificador para el objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> actual.</summary>
      <returns>Identificador para el objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> actual.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.IsAllocated">
      <summary>Obtiene un valor que indica si el identificador está asignado.</summary>
      <returns>Es true si el identificador está asignado; en caso contrario, es false.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Equality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>Devuelve un valor que indica si dos objetos <see cref="T:System.Runtime.InteropServices.GCHandle" /> son iguales.</summary>
      <returns>Es true si los parámetros <paramref name="a" /> y <paramref name="b" /> son iguales; en caso contrario, es false.</returns>
      <param name="a">Objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> que se va a comparar con el parámetro <paramref name="b" />. </param>
      <param name="b">Objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> que se va a comparar con el parámetro <paramref name="a" />.  </param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.IntPtr)~System.Runtime.InteropServices.GCHandle">
      <summary>Un <see cref="T:System.Runtime.InteropServices.GCHandle" /> se almacena mediante una representación de entero interna.</summary>
      <returns>Objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> almacenado mediante una representación de entero interna.</returns>
      <param name="value">
        <see cref="T:System.IntPtr" /> que indica el identificador para el que se requiere la conversión. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.Runtime.InteropServices.GCHandle)~System.IntPtr">
      <summary>Un <see cref="T:System.Runtime.InteropServices.GCHandle" /> se almacena mediante una representación de entero interna.</summary>
      <returns>El valor del entero.</returns>
      <param name="value">
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> para el que se requiere el entero. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Inequality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>Devuelve un valor que indica si dos objetos <see cref="T:System.Runtime.InteropServices.GCHandle" /> no son iguales.</summary>
      <returns>Es true si los parámetros <paramref name="a" /> y <paramref name="b" /> no son iguales; en caso contrario, es false.</returns>
      <param name="a">Objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> que se va a comparar con el parámetro <paramref name="b" />. </param>
      <param name="b">Objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> que se va a comparar con el parámetro <paramref name="a" />.  </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.Target">
      <summary>Obtiene o establece el objeto al que representa este identificador.</summary>
      <returns>Objeto que representa este identificador.</returns>
      <exception cref="T:System.InvalidOperationException">The handle was freed, or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.ToIntPtr(System.Runtime.InteropServices.GCHandle)">
      <summary>Devuelve la representación entera interna de un objeto <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <returns>Objeto <see cref="T:System.IntPtr" /> que representa un objeto <see cref="T:System.Runtime.InteropServices.GCHandle" />. </returns>
      <param name="value">Objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> del que se va a recuperar una representación de entero interno.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandleType">
      <summary>Representa los tipos de identificadores que puede asignar la clase <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Normal">
      <summary>Este tipo de identificador representa un identificador opaco, es decir, la dirección del objeto anclado no se puede resolver a través del identificador.Se puede utilizar este tipo para realizar el seguimiento de un objeto e impedir que se envíe al recolector de elementos no utilizados.Este miembro de la enumeración resulta útil cuando un cliente no administrado contiene la única referencia que no se puede detectar desde el recolector de elementos no utilizados, a un objeto administrado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Pinned">
      <summary>Este tipo de identificador es similar a <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" />, con la diferencia de que permite tomar la dirección del objeto anclado.De este modo, se impide que el recolector de elementos no utilizados traslade el objeto con la consiguiente reducción de eficacia.Utilice el método <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> para liberar el identificador asignado lo antes posible.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Weak">
      <summary>Este tipo de identificador se utiliza para realizar el seguimiento de un objeto, pero permite su recolección.Cuando se recopila un objeto, el contenido del objeto <see cref="T:System.Runtime.InteropServices.GCHandle" /> está lleno de ceros.Las referencias Weak se llenan de ceros antes de que el finalizador se ejecute; por tanto, aunque el finalizador restablezca el objeto, la referencia Weak sigue llena de ceros.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.WeakTrackResurrection">
      <summary>Este tipo de identificador es similar a <see cref="F:System.Runtime.InteropServices.GCHandleType.Weak" />, con la diferencia de que el identificador no se llena con ceros si el objeto se restablece durante la finalización.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.GuidAttribute">
      <summary>Suministra un objeto <see cref="T:System.Guid" /> explícitamente cuando no se desea que se genere un GUID automáticamente.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GuidAttribute.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.GuidAttribute" /> con el GUID especificado.</summary>
      <param name="guid">La estructura <see cref="T:System.Guid" /> que se va a asignar. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GuidAttribute.Value">
      <summary>Obtiene la estructura <see cref="T:System.Guid" /> de la clase.</summary>
      <returns>La estructura <see cref="T:System.Guid" /> de la clase.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.HandleCollector">
      <summary>Realiza el seguimiento de los identificadores pendientes y fuerza la recolección de elementos no utilizados cuando se alcanza el umbral especificado.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.HandleCollector" /> utilizando un nombre y un umbral en el que va a comenzar la colección de identificadores. </summary>
      <param name="name">Nombre del recolector.Este parámetro le permite denominar recolectores que realizan el seguimiento de tipos de identificador por separado.</param>
      <param name="initialThreshold">Valor que especifica el punto en el que deben comenzar las colecciones.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">El parámetro <paramref name="initialThreshold" /> es menor que 0.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.HandleCollector" /> utilizando un nombre, un umbral en el que va a comenzar la colección de identificadores y otro umbral en el que debe producirse la colección de identificadores. </summary>
      <param name="name">Nombre del recolector.  Este parámetro le permite denominar recolectores que realizan el seguimiento de tipos de identificador por separado.</param>
      <param name="initialThreshold">Valor que especifica el punto en el que deben comenzar las colecciones.</param>
      <param name="maximumThreshold">Valor que especifica el punto en el que deben producirse las colecciones.Este debe establecerse en el número máximo de identificadores disponibles.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">El parámetro <paramref name="initialThreshold" /> es menor que 0.O bienEl parámetro <paramref name="maximumThreshold" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="maximumThreshold" /> es menor que el parámetro <paramref name="initialThreshold" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Add">
      <summary>Incrementa el contador del identificador actual.</summary>
      <exception cref="T:System.InvalidOperationException">El valor de la propiedad <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> es menor que 0.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Count">
      <summary>Obtiene el número de identificadores recolectados.</summary>
      <returns>Número de identificadores recolectados.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.InitialThreshold">
      <summary>Obtiene un valor que especifica el punto en el que deben comenzar las colecciones.</summary>
      <returns>Valor que especifica el punto en el que deben comenzar las colecciones.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.MaximumThreshold">
      <summary>Obtiene un valor que especifica el punto en el que deben producirse las colecciones.</summary>
      <returns>Valor que especifica el punto en el que deben producirse las colecciones.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Name">
      <summary>Obtiene el nombre de un objeto <see cref="T:System.Runtime.InteropServices.HandleCollector" />.</summary>
      <returns>Esta propiedad <see cref="P:System.Runtime.InteropServices.HandleCollector.Name" /> le permite denominar recolectores que realizan el seguimiento de tipos de identificador por separado.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Remove">
      <summary>Decrementa el contador del identificador actual.</summary>
      <exception cref="T:System.InvalidOperationException">El valor de la propiedad <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> es menor que 0.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomAdapter">
      <summary>Proporciona la forma en que los clientes pueden obtener acceso a un objeto real, en lugar del objeto adaptador proporcionado por un contador de referencias personalizado.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomAdapter.GetUnderlyingObject">
      <summary>Proporciona acceso al objeto subyacente ajustado por un contador de referencias personalizado.</summary>
      <returns>Objeto incluido en el objeto adaptador.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomQueryInterface">
      <summary>Permite a los programadores proporcionar una implementación personalizada, administrada, del método IUnknown::QueryInterface(REFIID riid, void **ppvObject).</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)">
      <summary>Devuelve una interfaz correspondiente a un id. de interfaz especificado.</summary>
      <returns>Uno de los valores de enumeración que indica si se utilizó una implementación personalizada de IUnknown::QueryInterface.</returns>
      <param name="iid">GUID de la interfaz solicitada.</param>
      <param name="ppv">Referencia a la interfaz solicitada, cuando este método vuelve.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InAttribute">
      <summary>Indica que el cálculo de las referencias de los datos debe realizarse del llamador al destinatario de la llamada, pero no de vuelta al llamador.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InAttribute.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.InAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.InterfaceTypeAttribute">
      <summary>Indica si una interfaz administrada es dual, de sólo distribución o sólo IUnknown cuando se expone a COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Int16)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> con el miembro de enumeración <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> especificado.</summary>
      <param name="interfaceType">Describe la forma en que la interfaz debe exponerse a clientes COM. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Runtime.InteropServices.ComInterfaceType)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> con el miembro de enumeración <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> especificado.</summary>
      <param name="interfaceType">Uno de los valores de <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> que describe la forma en que la interfaz debe exponerse a clientes COM. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.InterfaceTypeAttribute.Value">
      <summary>Obtiene el valor de <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> que describe la forma en que la interfaz debe exponerse a COM.</summary>
      <returns>Valor de <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> que describe la forma en que la interfaz debe exponerse a COM.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidComObjectException">
      <summary>Excepción que se produce al utilizar un objeto COM no válido.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor">
      <summary>Inicializa una instancia de InvalidComObjectException con propiedades predeterminadas.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String)">
      <summary>Inicializa una instancia de InvalidComObjectException con un mensaje.</summary>
      <param name="message">Mensaje que indica el motivo de la excepción. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.InvalidComObjectException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="inner" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
      <summary>Excepción producida por el contador de referencias cuando detecta un argumento de tipo Variant cuyas referencias al código administrado no se pueden calcular.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor">
      <summary>Inicializa una nueva instancia de la clase InvalidOleVariantTypeException con valores predeterminados.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase InvalidOleVariantTypeException con el mensaje especificado.</summary>
      <param name="message">Mensaje que indica el motivo de la excepción. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="inner" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.Marshal">
      <summary>Proporciona una colección de métodos para asignar memoria no administrada, copiar bloques de memoria no administrados y convertir los tipos administrados en no administrados, así como otros métodos diversos que se utilizan al interactuar con código no administrado.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AddRef(System.IntPtr)">
      <summary>Incrementa el contador de referencia en la interfaz especificada.</summary>
      <returns>Nuevo valor del contador de referencias en el parámetro <paramref name="pUnk" />.</returns>
      <param name="pUnk">Contador de referencia de la interfaz a incrementar.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)">
      <summary>Asigna un bloque de memoria de un tamaño especificado del asignador de memoria de tareas COM.</summary>
      <returns>Entero que representa la dirección del bloque de memoria asignado.Debe liberar esta memoria con <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />.</returns>
      <param name="cb">Tamaño del bloque de memoria a asignar.</param>
      <exception cref="T:System.OutOfMemoryException">No hay memoria suficiente para tratar la petición.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.Int32)">
      <summary>Asigna memoria de la memoria no administrada del proceso utilizando el número especificado de bytes.</summary>
      <returns>Puntero a la memoria recién asignada.Debe liberar esta memoria utilizando la <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> método.</returns>
      <param name="cb">Número necesario de bytes en memoria.</param>
      <exception cref="T:System.OutOfMemoryException">No hay memoria suficiente para tratar la petición.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)">
      <summary>Asigna memoria de la memoria no administrada del proceso utilizando el puntero al número especificado de bytes.</summary>
      <returns>Puntero a la memoria recién asignada.Debe liberar esta memoria utilizando la <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> método.</returns>
      <param name="cb">Número necesario de bytes en memoria.</param>
      <exception cref="T:System.OutOfMemoryException">No hay memoria suficiente para tratar la petición.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AreComObjectsAvailableForCleanup">
      <summary>Indica si hay contenedores RCW de cualquier contexto disponibles para la limpieza.</summary>
      <returns>Es true si hay contenedores RCW disponibles para la limpieza; en caso contrario, es false.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Byte[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia datos de una matriz unidimensional y administrada de enteros de 8 bits sin signo a un puntero de memoria no administrada.</summary>
      <param name="source">Matriz unidimensional de la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de origen donde debe comenzar la copia.</param>
      <param name="destination">Puntero de memoria en el que se va a copiar.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> y <paramref name="length" /> no son válidos.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" />es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Char[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia datos de una matriz de caracteres unidimensional y administrada a un puntero de memoria no administrada.</summary>
      <param name="source">Matriz unidimensional de la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de origen donde debe comenzar la copia.</param>
      <param name="destination">Puntero de memoria en el que se va a copiar.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> y <paramref name="length" /> no son válidos.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" /> es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Double[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia datos de una matriz unidimensional y administrada de números de punto flotante de precisión doble a un puntero de memoria no administrada.</summary>
      <param name="source">Matriz unidimensional de la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de origen donde debe comenzar la copia.</param>
      <param name="destination">Puntero de memoria en el que se va a copiar.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> y <paramref name="length" /> no son válidos.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" />es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int16[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia datos de una matriz unidimensional administrada de enteros de 16 bits con signo a un puntero de memoria no administrada.</summary>
      <param name="source">Matriz unidimensional de la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de origen donde debe comenzar la copia.</param>
      <param name="destination">Puntero de memoria en el que se va a copiar.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> y <paramref name="length" /> no son válidos.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" />es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int32[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia datos de una matriz unidimensional administrada de enteros de 32 bits con signo a un puntero de memoria no administrada.</summary>
      <param name="source">Matriz unidimensional de la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de origen donde debe comenzar la copia.</param>
      <param name="destination">Puntero de memoria en el que se va a copiar.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> y <paramref name="length" /> no son válidos.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" /> o <paramref name="length" /> es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int64[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia datos de una matriz unidimensional administrada de enteros de 64 bits con signo a un puntero de memoria no administrada.</summary>
      <param name="source">Matriz unidimensional de la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de origen donde debe comenzar la copia.</param>
      <param name="destination">Puntero de memoria en el que se va a copiar.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> y <paramref name="length" /> no son válidos.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" />es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Byte[],System.Int32,System.Int32)">
      <summary>Copia datos de un puntero de memoria no administrada a una matriz administrada de enteros de 8 bits sin signo.</summary>
      <param name="source">Puntero de memoria del que se va a copiar.</param>
      <param name="destination">Matriz en la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de destino donde debe comenzar la copia.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" />es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Char[],System.Int32,System.Int32)">
      <summary>Copia datos de un puntero de memoria no administrada a una matriz de caracteres administrada.</summary>
      <param name="source">Puntero de memoria del que se va a copiar.</param>
      <param name="destination">Matriz en la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de destino donde debe comenzar la copia.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" />es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Double[],System.Int32,System.Int32)">
      <summary>Copia datos de un puntero de memoria no administrada a una matriz administrada de números de punto flotante de precisión doble.</summary>
      <param name="source">Puntero de memoria del que se va a copiar.</param>
      <param name="destination">Matriz en la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de destino donde debe comenzar la copia.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" />es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int16[],System.Int32,System.Int32)">
      <summary>Copia datos de un puntero de memoria no administrada a una matriz administrada de enteros de 16 bits con signo.</summary>
      <param name="source">Puntero de memoria del que se va a copiar.</param>
      <param name="destination">Matriz en la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de destino donde debe comenzar la copia.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" />es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int32[],System.Int32,System.Int32)">
      <summary>Copia datos de un puntero de memoria no administrada a una matriz administrada de enteros de 32 bits con signo.</summary>
      <param name="source">Puntero de memoria del que se va a copiar.</param>
      <param name="destination">Matriz en la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de destino donde debe comenzar la copia.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" />es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int64[],System.Int32,System.Int32)">
      <summary>Copia datos de un puntero de memoria no administrada a una matriz administrada de enteros de 64 bits con signo.</summary>
      <param name="source">Puntero de memoria del que se va a copiar.</param>
      <param name="destination">Matriz en la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de destino donde debe comenzar la copia.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" />es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
      <summary>Copia datos de un puntero de memoria no administrada a una matriz <see cref="T:System.IntPtr" /> administrada.</summary>
      <param name="source">Puntero de memoria del que se va a copiar. </param>
      <param name="destination">Matriz en la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de destino donde debe comenzar la copia.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" />es null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Single[],System.Int32,System.Int32)">
      <summary>Copia datos de un puntero de memoria no administrada a una matriz administrada de números de punto flotante de precisión sencilla.</summary>
      <param name="source">Puntero de memoria del que se va a copiar. </param>
      <param name="destination">Matriz en la que se va a copiar. </param>
      <param name="startIndex">Índice de base cero de la matriz de destino donde debe comenzar la copia. </param>
      <param name="length">Número de elementos de la matriz que se van a copiar. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" />es null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia datos de una matriz <see cref="T:System.IntPtr" /> unidimensional y administrada a un puntero de memoria no administrada.</summary>
      <param name="source">Matriz unidimensional de la que se va a copiar.</param>
      <param name="startIndex">Índice de base cero de la matriz de origen donde debe comenzar la copia.</param>
      <param name="destination">Puntero de memoria en el que se va a copiar.</param>
      <param name="length">Número de elementos de la matriz que se van a copiar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" />es null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Single[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia datos de una matriz unidimensional y administrada de números de punto flotante de precisión sencilla a un puntero de memoria no administrada.</summary>
      <param name="source">Matriz unidimensional de la que se va a copiar. </param>
      <param name="startIndex">Índice de base cero de la matriz de origen donde debe comenzar la copia. </param>
      <param name="destination">Puntero de memoria en el que se va a copiar. </param>
      <param name="length">Número de elementos de la matriz que se van a copiar. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> y <paramref name="length" /> no son válidos. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" />es null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject(System.IntPtr,System.Object)">
      <summary>Agrega un objeto administrado al objeto COM especificado.</summary>
      <returns>Puntero IUnknown interno del objeto administrado.</returns>
      <param name="pOuter">Puntero IUnknown externo.</param>
      <param name="o">Objeto que se va a agregar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> es un objeto Windows en tiempo de ejecución.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject``1(System.IntPtr,``0)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Agrega un objeto administrado del tipo especificado con el objeto COM especificado. </summary>
      <returns>Puntero interno IUnknown del objeto administrado. </returns>
      <param name="pOuter">Puntero externo IUnknown. </param>
      <param name="o">Objeto administrado que se va a agregar. </param>
      <typeparam name="T">Tipo del objeto administrado que se va a agregar. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> es un objeto Windows en tiempo de ejecución. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType(System.Object,System.Type)">
      <summary>Ajusta el objeto COM especificado en un objeto del tipo especificado.</summary>
      <returns>Objeto recién ajustado que es una instancia del tipo deseado.</returns>
      <param name="o">Objeto que se va a almacenar en un contenedor. </param>
      <param name="t">Tipo de contenedor que se va a crear. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> debe derivar de __ComObject. o bien<paramref name="t" /> es un tipo Windows en tiempo de ejecución.</exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="t" /> es null.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> no se puede convertir en el tipo de destino porque no admite todas las interfaces necesarias. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType``2(``0)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Ajusta el objeto COM especificado en un objeto del tipo especificado.</summary>
      <returns>Objeto recién ajustado. </returns>
      <param name="o">Objeto que se va a almacenar en un contenedor. </param>
      <typeparam name="T">Tipo de objeto que se va a incluir. </typeparam>
      <typeparam name="TWrapper">Tipo de objeto que se va a devolver. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> debe derivar de __ComObject. o bien<paramref name="T" /> es un tipo Windows en tiempo de ejecución.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> no se puede convertir en <paramref name="TWrapper" /> porque no admite todas las interfaces necesarias. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Libera todas las subestructuras de un tipo especificado a las que apunta el bloque de memoria no administrada especificado. </summary>
      <param name="ptr">Puntero a un bloque de memoria no administrado. </param>
      <typeparam name="T">El tipo de estructura con formato.Proporciona la información de diseño necesaria para eliminar el búfer del parámetro <paramref name="ptr" />.</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> tiene un diseño automático.Utilice, en su lugar, un diseño secuencial o explícito.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)">
      <summary>Libera todas las subestructuras a las que apunta el bloque de memoria no administrada especificado.</summary>
      <param name="ptr">Puntero a un bloque de memoria no administrado. </param>
      <param name="structuretype">Tipo de una clase con formato.Proporciona la información de diseño necesaria para eliminar el búfer del parámetro <paramref name="ptr" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> tiene un diseño automático.Utilice, en su lugar, un diseño secuencial o explícito.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FinalReleaseComObject(System.Object)">
      <summary>Libera todas las referencias a un contenedor RCW Contenedor al que se puede llamar en tiempo de ejecución estableciendo su recuento de referencias en 0.</summary>
      <returns>Nuevo valor del recuento de referencias del RCW asociado al parámetro <paramref name="o" />, que es 0 (cero) si la liberación se realiza correctamente.</returns>
      <param name="o">RCW que se va a liberar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> no es un objeto COM válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" />is null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeBSTR(System.IntPtr)">
      <summary>Libera un BSTR utilizando la función SysFreeString de COM.</summary>
      <param name="ptr">Dirección del BSTR que se va a liberar. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)">
      <summary>Libera un bloque de memoria asignado por el asignador de memoria de tareas COM no administrada.</summary>
      <param name="ptr">Dirección de la memoria que se va a liberar. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)">
      <summary>Libera memoria previamente asignada de la memoria no administrada del proceso.</summary>
      <param name="hglobal">Controlador devuelto por la llamada coincidente original a <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)">
      <summary>Devuelve un puntero a una interfaz IUnknown que representa la interfaz especificada en el objeto indicado.El acceso a la interfaz de consulta personalizada está habilitado de forma predeterminada.</summary>
      <returns>Puntero de interfaz que representa la interfaz especificada para el objeto.</returns>
      <param name="o">Objeto que proporciona la interfaz. </param>
      <param name="T">Tipo de interfaz que se solicita. </param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="T" /> no es una interfaz.o bien El tipo no es visible para COM. o bienEl parámetro <paramref name="T" /> es un tipo genérico.</exception>
      <exception cref="T:System.InvalidCastException">El parámetro <paramref name="o" /> no admite la interfaz solicitada. </exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="o" /> es null.o bien El valor del parámetro <paramref name="T" /> es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)">
      <summary>Devuelve un puntero a una interfaz IUnknown que representa la interfaz especificada en el objeto indicado.El modo de personalización especificado controla el acceso a la interfaz de consulta personalizada.</summary>
      <returns>Puntero de interfaz que representa la interfaz para el objeto.</returns>
      <param name="o">Objeto que proporciona la interfaz.</param>
      <param name="T">Tipo de interfaz que se solicita.</param>
      <param name="mode">Uno de los valores de enumeración que indica si se aplica una personalización IUnknown::QueryInterface proporcionada por <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="T" /> no es una interfaz.o bien El tipo no es visible para COM.o bienEl parámetro <paramref name="T" /> es un tipo genérico.</exception>
      <exception cref="T:System.InvalidCastException">El objeto <paramref name="o" /> no admite la interfaz solicitada.</exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="o" /> es null.o bien El valor del parámetro <paramref name="T" /> es null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject``2(``0)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Devuelve un puntero a una interfaz IUnknown que representa la interfaz en un objeto del tipo especificado.El acceso a la interfaz de consulta personalizada está habilitado de forma predeterminada.</summary>
      <returns>Puntero de interfaz que representa la interfaz <paramref name="TInterface" />.</returns>
      <param name="o">Objeto que proporciona la interfaz. </param>
      <typeparam name="T">Tipo de <paramref name="o" />. </typeparam>
      <typeparam name="TInterface">Tipo de interfaz que se va a devolver. </typeparam>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="TInterface" /> no es una interfaz.o bien El tipo no es visible para COM. o bienEl parámetro <paramref name="T" /> es un tipo genérico abierto.</exception>
      <exception cref="T:System.InvalidCastException">El parámetro <paramref name="o" /> no admite la interfaz <paramref name="TInterface" />. </exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="o" /> es null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer``1(System.IntPtr)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Convierte un puntero a función no administrada en un delegado de un tipo especificado. </summary>
      <returns>Una instancia del tipo de delegado especificado.</returns>
      <param name="ptr">Puntero a función no administrada que se va a convertir. </param>
      <typeparam name="TDelegate">Tipo del delegado que se va a devolver. </typeparam>
      <exception cref="T:System.ArgumentException">El parámetro genérico <paramref name="TDelegate" /> no es un delegado o es un tipo genérico abierto.</exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="ptr" /> es null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer(System.IntPtr,System.Type)">
      <summary>Convierte un puntero de función no administrada en un delegado.</summary>
      <returns>Instancia de delegado que se puede convertir al tipo de delegado adecuado.</returns>
      <param name="ptr">Puntero a función no administrada que se va a convertir.</param>
      <param name="t">Tipo del delegado que se va a devolver.</param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="t" /> no es un delegado o es genérico.</exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="ptr" /> es null.o bienEl valor del parámetro <paramref name="t" /> es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionCode">
      <summary>Devuelve el código que identifica el tipo de excepción que ocurrió.</summary>
      <returns>El tipo de la excepción.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32)">
      <summary>Convierte el código de error HRESULT especificado en un objeto <see cref="T:System.Exception" /> correspondiente.</summary>
      <returns>Objeto que representa el HRESULT convertido.</returns>
      <param name="errorCode">HRESULT que se va a convertir.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32,System.IntPtr)">
      <summary>Convierte el código de error HRESULT especificado en un objeto <see cref="T:System.Exception" /> correspondiente, con información de error adicional pasada en una interfaz IErrorInfo para el objeto de excepción.</summary>
      <returns>Objeto que representa el HRESULT convertido e información obtenida de <paramref name="errorInfo" />.</returns>
      <param name="errorCode">HRESULT que se va a convertir.</param>
      <param name="errorInfo">Puntero a la interfaz IErrorInfo que proporciona más información sobre el error.Puede especificar IntPtr(0) para utilizar la interfaz IErrorInfo actual o IntPtr(-1) para omitir la interfaz IErrorInfo actual y construir la excepción del código de error únicamente.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate(System.Delegate)">
      <summary>Convierte un delegado en un puntero a función invocable desde código no administrado.</summary>
      <returns>Valor que se puede pasar a código no administrado que, a su vez, puede utilizarlo para llamar al delegado administrado subyacente. </returns>
      <param name="d">Delegado que se va a pasar a código no administrado.</param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="d" /> es un tipo genérico.</exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="d" /> es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate``1(``0)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Convierte un delegado de un tipo especificado en un puntero a función invocable desde código no administrado. </summary>
      <returns>Valor que se puede pasar a código no administrado que, a su vez, puede utilizarlo para llamar al delegado administrado subyacente. </returns>
      <param name="d">Delegado que se va a pasar a código no administrado. </param>
      <typeparam name="TDelegate">Tipo de delegado que se va a convertir. </typeparam>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="d" /> es null. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForException(System.Exception)">
      <summary>Convierte la excepción especificada en HRESULT.</summary>
      <returns>HRESULT asignado a la excepción proporcionada.</returns>
      <param name="e">Excepción que se va a convertir en HRESULT.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForLastWin32Error">
      <summary>Devuelve el HRESULT correspondiente al último error producido en código Win32 ejecutado mediante <see cref="T:System.Runtime.InteropServices.Marshal" />.</summary>
      <returns>HRESULT correspondiente al último código de error Win32.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetIUnknownForObject(System.Object)">
      <summary>Devuelve una interfaz IUnknown de un objeto administrado.</summary>
      <returns>Puntero IUnknown para el parámetro <paramref name="o" />.</returns>
      <param name="o">Objeto cuya interfaz IUnknown se solicita.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error">
      <summary>Devuelve el código de error devuelto por la última función no administrada a la que se llamó mediante la invocación de plataforma que tiene la marca <see cref="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError" /> activada.</summary>
      <returns>Último código de error establecido por una llamada a la función SetLastError Win32.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject(System.Object,System.IntPtr)">
      <summary>Convierte un objeto en un COM VARIANT.</summary>
      <param name="obj">Objeto para el que se va a obtener un tipo COM VARIANT.</param>
      <param name="pDstNativeVariant">Puntero para recibir el VARIANT correspondiente al parámetro <paramref name="obj" />.</param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="obj" /> es un tipo genérico.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject``1(``0,System.IntPtr)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Convierte un objeto de un tipo especificado en un objeto COM VARIANT. </summary>
      <param name="obj">Objeto para el que se va a obtener un tipo COM VARIANT. </param>
      <param name="pDstNativeVariant">Puntero para recibir el VARIANT correspondiente al parámetro <paramref name="obj" />. </param>
      <typeparam name="T">Tipo del objeto que se va a convertir. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForIUnknown(System.IntPtr)">
      <summary>Devuelve una instancia de un tipo que representa un objeto COM por un puntero a su interfaz IUnknown.</summary>
      <returns>Objeto que representa el objeto COM no administrado especificado.</returns>
      <param name="pUnk">Puntero a la interfaz IUnknown. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant(System.IntPtr)">
      <summary>Convierte un COM VARIANT en un objeto.</summary>
      <returns>Objeto que corresponde al parámetro <paramref name="pSrcNativeVariant" />.</returns>
      <param name="pSrcNativeVariant">Puntero a un tipo de datos VARIANT COM.</param>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> no es un tipo VARIANT válido.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> tiene un tipo incompatible.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant``1(System.IntPtr)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Convierte un objeto COM VARIANT en un objeto de un tipo especificado. </summary>
      <returns>Objeto del tipo especificado que corresponde al parámetro <paramref name="pSrcNativeVariant" />. </returns>
      <param name="pSrcNativeVariant">Puntero a un tipo de datos VARIANT COM. </param>
      <typeparam name="T">Tipo al que se va a convertir el COM VARIANT. </typeparam>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> no es un tipo VARIANT válido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> tiene un tipo incompatible. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants(System.IntPtr,System.Int32)">
      <summary>Convierte una matriz de tipos VARIANT COM en una matriz de objetos. </summary>
      <returns>Matriz de objetos correspondiente a <paramref name="aSrcNativeVariant" />.</returns>
      <param name="aSrcNativeVariant">Puntero al primer elemento de una matriz de elementos VARIANT COM.</param>
      <param name="cVars">Número de elementos VARIANT COM en <paramref name="aSrcNativeVariant" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> es un número negativo.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants``1(System.IntPtr,System.Int32)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Convierte una matriz de elementos COM VARIANT en una matriz de un tipo especificado. </summary>
      <returns>Matriz de objetos <paramref name="T" /> correspondiente a <paramref name="aSrcNativeVariant" />. </returns>
      <param name="aSrcNativeVariant">Puntero al primer elemento de una matriz de elementos VARIANT COM. </param>
      <param name="cVars">Número de elementos VARIANT COM en <paramref name="aSrcNativeVariant" />. </param>
      <typeparam name="T">Tipo del matriz que se va a devolver. </typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> es un número negativo. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetStartComSlot(System.Type)">
      <summary>Obtiene la primera ranura de la tabla de funciones virtuales (v-table o VTBL) que contiene métodos definidos por el usuario.</summary>
      <returns>Primera ranura VTBL que contiene métodos definidos por el usuario.La primera ranura es 3 si la interfaz se basa en IUnknown, y 7 si la interfaz se basa en IDispatch.</returns>
      <param name="t">Tipo que representa una interfaz.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> no es visible desde COM.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeFromCLSID(System.Guid)">
      <summary>Devuelve el tipo asociado al identificador de clase especificado (CLSID). </summary>
      <returns>System.__ComObject independientemente de que CLSID sea válido. </returns>
      <param name="clsid">CLSID del tipo que se va a devolver. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeInfoName(System.Runtime.InteropServices.ComTypes.ITypeInfo)">
      <summary>Recupera el nombre del tipo representado por un objeto ITypeInfo.</summary>
      <returns>Nombre del tipo al que apunta el parámetro <paramref name="typeInfo" />.</returns>
      <param name="typeInfo">Objeto que representa un puntero ITypeInfo.</param>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="typeInfo" /> es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetUniqueObjectForIUnknown(System.IntPtr)">
      <summary>Crea un objeto contenedor RCW (Contenedor al que se puede llamar en tiempo de ejecución) único para una interfaz IUnknown dada.</summary>
      <returns>RCW único para la interfaz IUnknown especificada.</returns>
      <param name="unknown">Puntero administrado a una interfaz IUnknown.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.IsComObject(System.Object)">
      <summary>Indica si un objeto especificado representa un objeto COM.</summary>
      <returns>Es true si el parámetro <paramref name="o" /> es un tipo COM; en caso contrario, es false.</returns>
      <param name="o">Objeto que se va a comprobar.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf``1(System.String)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Devuelve el desplazamiento de campo del formato no administrado de una clase administrada específica.</summary>
      <returns>Desplazamiento, en bytes, para el parámetro <paramref name="fieldName" /> dentro de la clase especificada que se declara mediante invocación de plataforma. </returns>
      <param name="fieldName">Nombre del campo en el tipo <paramref name="T" />. </param>
      <typeparam name="T">Tipo de valor administrado o tipo de referencia con formato.Debe aplicar el atributo <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> a la clase.</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf(System.Type,System.String)">
      <summary>Devuelve el desplazamiento de campo del formato no administrado de la clase administrada.</summary>
      <returns>Desplazamiento, en bytes, para el parámetro <paramref name="fieldName" /> dentro de la clase especificada que se declara mediante invocación de plataforma.</returns>
      <param name="t">Tipo de valor o tipo de referencia con formato que especifica la clase administrada.Debe aplicar <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> a la clase.</param>
      <param name="fieldName">Campo dentro del parámetro <paramref name="t" />.</param>
      <exception cref="T:System.ArgumentException">La clase no se puede exportar como estructura o el campo no es público.A partir de la versión 2.0 de .NET Framework, el campo puede ser privado.</exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="t" /> es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr)">
      <summary>Copia todos los caracteres hasta el primer carácter nulo de una cadena ANSI no administrada a un <see cref="T:System.String" />, administrado y ensancha cada carácter ANSI a Unicode.</summary>
      <returns>Cadena administrada que contiene una copia de la cadena ANSI no administrada.Si <paramref name="ptr" /> es null, el método devuelve una cadena nula.</returns>
      <param name="ptr">Dirección del primer carácter de la cadena no administrada.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr,System.Int32)">
      <summary>Asigna un <see cref="T:System.String" />, administrado, copia en él un número especificado de caracteres de una cadena ANSI no administrada y ensancha cada carácter ANSI a Unicode.</summary>
      <returns>Cadena administrada que contiene una copia de la cadena ANSI nativa si el valor del parámetro <paramref name="ptr" /> no es null; de lo contrario, este método devuelve null.</returns>
      <param name="ptr">Dirección del primer carácter de la cadena no administrada.</param>
      <param name="len">Número de bytes de la cadena de entrada que se va a copiar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="len" /> es menor que cero.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringBSTR(System.IntPtr)">
      <summary>Asigna un administrado <see cref="T:System.String" /> y copia un BSTR cadena almacenada en memoria no administrada en él.</summary>
      <returns>Cadena administrada que contiene una copia de la cadena no administrada si el valor del parámetro <paramref name="ptr" /> no es null; en caso contrario, este método devuelve null.</returns>
      <param name="ptr">Dirección del primer carácter de la cadena no administrada.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr)">
      <summary>Asigna un <see cref="T:System.String" /> administrado y copia en él todos los caracteres hasta el primer carácter nulo de una cadena Unicode no administrada.</summary>
      <returns>Cadena administrada que contiene una copia de la cadena no administrada si el valor del parámetro <paramref name="ptr" /> no es null; en caso contrario, este método devuelve null.</returns>
      <param name="ptr">Dirección del primer carácter de la cadena no administrada.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr,System.Int32)">
      <summary>Asigna un <see cref="T:System.String" /> administrado y copia en él un número de caracteres especificado de una cadena Unicode no administrada.</summary>
      <returns>Cadena administrada que contiene una copia de la cadena no administrada si el valor del parámetro <paramref name="ptr" /> no es null; en caso contrario, este método devuelve null.</returns>
      <param name="ptr">Dirección del primer carácter de la cadena no administrada.</param>
      <param name="len">Número de caracteres Unicode que se van a copiar.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Calcula las referencias a los datos desde un bloque de memoria no administrado a un objeto administrado y recién asignado del tipo especificado por un parámetro de tipo genérico. </summary>
      <returns>Objeto administrado que contiene los datos a los que apunta el parámetro <paramref name="ptr" />. </returns>
      <param name="ptr">Puntero a un bloque de memoria no administrado. </param>
      <typeparam name="T">Tipo de objeto en el que se copiarán los datos.Este objeto debe ser una clase con formato o una estructura.</typeparam>
      <exception cref="T:System.ArgumentException">El diseño de <paramref name="T" /> no es secuencial ni explícito.</exception>
      <exception cref="T:System.MissingMethodException">La clase especificada por <paramref name="T" /> no tiene un constructor predeterminado accesible. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Object)">
      <summary>Calcula las referencias a los datos desde un bloque de memoria no administrada a un objeto administrado.</summary>
      <param name="ptr">Puntero a un bloque de memoria no administrado.</param>
      <param name="structure">Objeto en el que se copiarán los datos.Este objeto debe ser una instancia de una clase con formato.</param>
      <exception cref="T:System.ArgumentException">El diseño de la estructura no es secuencial ni explícito.o bien La estructura es un tipo de valor al que se ha aplicado la conversión boxing.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Type)">
      <summary>Calcula las referencias a los datos desde un bloque de memoria no administrado a un objeto administrado y recién asignado del tipo especificado.</summary>
      <returns>Objeto administrado que contiene los datos a los que apunta el parámetro <paramref name="ptr" />.</returns>
      <param name="ptr">Puntero a un bloque de memoria no administrado.</param>
      <param name="structureType">Tipo de objeto que se va a crear.Este objeto debe representar una clase con formato o una estructura.</param>
      <exception cref="T:System.ArgumentException">El diseño del parámetro <paramref name="structureType" /> no es secuencial ni explícito.o bienEl parámetro <paramref name="structureType" /> es un tipo genérico.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structureType" />is null.</exception>
      <exception cref="T:System.MissingMethodException">La clase especificada por <paramref name="structureType" /> no tiene un constructor predeterminado accesible. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr,``0)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Calcula las referencias desde un bloque de memoria no administrado a un objeto administrado de un tipo especificado. </summary>
      <param name="ptr">Puntero a un bloque de memoria no administrado. </param>
      <param name="structure">Objeto en el que se copiarán los datos. </param>
      <typeparam name="T">Tipo de <paramref name="structure" />.Debe ser una clase con formato.</typeparam>
      <exception cref="T:System.ArgumentException">El diseño de la estructura no es secuencial ni explícito. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.QueryInterface(System.IntPtr,System.Guid@,System.IntPtr@)">
      <summary>Solicita un puntero a una interfaz especificada de un objeto COM.</summary>
      <returns>HRESULT que indica si la llamada se realizó correctamente o no.</returns>
      <param name="pUnk">Interfaz que se va a consultar.</param>
      <param name="iid">Identificador de interfaz (IID) de la interfaz solicitada.</param>
      <param name="ppv">Cuando este método finaliza, contiene una referencia a la interfaz devuelta.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr)">
      <summary>Lee un único byte desde la memoria no administrada.</summary>
      <returns>Byte leído de la memoria no administrada.</returns>
      <param name="ptr">Dirección en la memoria no administrada desde la que se va a leer.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null. o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr,System.Int32)">
      <summary>Lee un único byte en un desplazamiento (o índice) dado desde la memoria no administrada.</summary>
      <returns>Byte leído de la memoria no administrada en el desplazamiento dado.</returns>
      <param name="ptr">Dirección base en la memoria no administrada desde la que se va a leer.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la lectura.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.Object,System.Int32)">
      <summary>Lee un único byte en un desplazamiento (o índice) dado desde la memoria no administrada. </summary>
      <returns>Byte leído de la memoria no administrada en el desplazamiento dado.</returns>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de origen.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la lectura.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr)">
      <summary>Lee un entero de 16 bits con signo de la memoria no administrada.</summary>
      <returns>Entero de 16 bits con signo leído de la memoria no administrada.</returns>
      <param name="ptr">Dirección en la memoria no administrada desde la que se va a leer.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null.o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr,System.Int32)">
      <summary>Lee un entero de 16 bits con signo en un desplazamiento dado de la memoria no administrada.</summary>
      <returns>Entero de 16 bits con signo leído de la memoria no administrada en el desplazamiento dado.</returns>
      <param name="ptr">Dirección base en la memoria no administrada desde la que se va a leer.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la lectura.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.Object,System.Int32)">
      <summary>Lee un entero de 16 bits con signo en un desplazamiento dado de la memoria no administrada.</summary>
      <returns>Entero de 16 bits con signo leído de la memoria no administrada en el desplazamiento dado.</returns>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de origen.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la lectura.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr)">
      <summary>Lee un entero de 32 bits con signo de la memoria no administrada.</summary>
      <returns>Entero de 32 bits con signo leído de la memoria no administrada.</returns>
      <param name="ptr">Dirección en la memoria no administrada desde la que se va a leer.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null.o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr,System.Int32)">
      <summary>Lee un entero de 32 bits con signo en un desplazamiento dado de la memoria no administrada.</summary>
      <returns>Entero de 32 bits con signo leído de la memoria no administrada.</returns>
      <param name="ptr">Dirección base en la memoria no administrada desde la que se va a leer.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la lectura.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.Object,System.Int32)">
      <summary>Lee un entero de 32 bits con signo en un desplazamiento dado de la memoria no administrada.</summary>
      <returns>Entero de 32 bits con signo leído de la memoria no administrada en el desplazamiento dado.</returns>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de origen.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la lectura.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr)">
      <summary>Lee un entero de 64 bits con signo de la memoria no administrada.</summary>
      <returns>Entero de 64 bits con signo leído de la memoria no administrada.</returns>
      <param name="ptr">Dirección en la memoria no administrada desde la que se va a leer.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null.o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr,System.Int32)">
      <summary>Lee un entero de 64 bits con signo en un desplazamiento dado de la memoria no administrada.</summary>
      <returns>Entero de 64 bits con signo leído de la memoria no administrada en el desplazamiento dado.</returns>
      <param name="ptr">Dirección base en la memoria no administrada desde la que se va a leer.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la lectura.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.Object,System.Int32)">
      <summary>Lee un entero de 64 bits con signo en un desplazamiento dado de la memoria no administrada.</summary>
      <returns>Entero de 64 bits con signo leído de la memoria no administrada en el desplazamiento dado.</returns>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de origen.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la lectura.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr)">
      <summary>Lee de la memoria no administrada un entero cuyo tamaño es propio del procesador nativo.</summary>
      <returns>Entero leído de la memoria no administrada.Se devuelve un entero de 32 bits en los equipos de 32 bits y un entero de 64 bits en los equipos de 64 bits.</returns>
      <param name="ptr">Dirección en la memoria no administrada desde la que se va a leer.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null. o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr,System.Int32)">
      <summary>Lee de la memoria no administrada un valor de tipo entero cuyo tamaño en bytes es propio del procesador nativo en un desplazamiento dado.</summary>
      <returns>Entero leído de la memoria no administrada en el desplazamiento dado.</returns>
      <param name="ptr">Dirección base en la memoria no administrada desde la que se va a leer.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la lectura.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.Object,System.Int32)">
      <summary>Lee de la memoria no administrada un valor de tipo entero cuyo tamaño en bytes es propio del procesador nativo.</summary>
      <returns>Entero leído de la memoria no administrada en el desplazamiento dado.</returns>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de origen.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la lectura.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocCoTaskMem(System.IntPtr,System.Int32)">
      <summary>Cambia el tamaño de un bloque de memoria asignado previamente con <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" />.</summary>
      <returns>Entero que representa la dirección del bloque de memoria reasignado.Debe liberar esta memoria con <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />.</returns>
      <param name="pv">Puntero a memoria asignada con <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" />.</param>
      <param name="cb">Nuevo tamaño del bloque asignado.</param>
      <exception cref="T:System.OutOfMemoryException">No hay memoria suficiente para tratar la petición.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocHGlobal(System.IntPtr,System.IntPtr)">
      <summary>Cambia el tamaño de un bloque de memoria asignado previamente con <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />.</summary>
      <returns>Puntero a la memoria reasignada.Esta memoria debe liberarse mediante <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />.</returns>
      <param name="pv">Puntero a memoria asignada con <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />.</param>
      <param name="cb">Nuevo tamaño del bloque asignado.Esto no es un puntero; es el recuento de bytes que está solicitando, convertido al tipo <see cref="T:System.IntPtr" />.Si pasa un puntero, se trata como un tamaño.</param>
      <exception cref="T:System.OutOfMemoryException">No hay memoria suficiente para tratar la petición.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Release(System.IntPtr)">
      <summary>Disminuye el contador de referencia de la interfaz especificada.</summary>
      <returns>Nuevo valor del contador de referencias en la interfaz especificada por el parámetro <paramref name="pUnk" />.</returns>
      <param name="pUnk">Interfaz que se va a liberar.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReleaseComObject(System.Object)">
      <summary>Decrements the reference count of the specified Contenedor al que se puede llamar en tiempo de ejecución (RCW) associated with the specified COM object.</summary>
      <returns>Nuevo valor del recuento de referencias del contenedor RCW asociado a <paramref name="o" />.Este valor suele ser cero ya que el RCW conserva una sola referencia al objeto COM incluido en el contenedor, independientemente del número de clientes administrados que lo llamen.</returns>
      <param name="o">Objeto COM que se va a liberar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> no es un objeto COM válido.</exception>
      <exception cref="T:System.NullReferenceException">
        <paramref name="o" />is null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Devuelve el tamaño, expresado en bytes, de un tipo no administrado. </summary>
      <returns>El tamaño, en bytes, del tipo especificado por el parámetro de tipo genérico <paramref name="T" />. </returns>
      <typeparam name="T">Tipo cuyo tamaño se va a devolver. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Object)">
      <summary>Devuelve el tamaño no administrado de un objeto en bytes.</summary>
      <returns>Tamaño del objeto especificado en código no administrado.</returns>
      <param name="structure">Objeto cuyo tamaño se devolverá.</param>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="structure" /> es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Type)">
      <summary>Devuelve el tamaño, expresado en bytes, de un tipo no administrado.</summary>
      <returns>Tamaño del tipo especificado en código no administrado.</returns>
      <param name="t">Tipo cuyo tamaño se va a devolver.</param>
      <exception cref="T:System.ArgumentException">El parámetro <paramref name="t" /> es un tipo genérico.</exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="t" /> es null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1(``0)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Devuelve el tamaño no administrado de un objeto de un tipo especificado en bytes. </summary>
      <returns>Tamaño del objeto especificado, en bytes, en código no administrado. </returns>
      <param name="structure">Objeto cuyo tamaño se devolverá. </param>
      <typeparam name="T">Tipo del parámetro <paramref name="structure" />. </typeparam>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="structure" /> es null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToBSTR(System.String)">
      <summary>Asigna un BSTR y copia en él el contenido de un objeto <see cref="T:System.String" /> administrado.</summary>
      <returns>Puntero no administrado a BSTR o 0 si <paramref name="s" /> es null.</returns>
      <param name="s">Cadena administrada que se va a copiar.</param>
      <exception cref="T:System.OutOfMemoryException">No hay memoria suficiente.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La longitud de <paramref name="s" /> está fuera del intervalo.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemAnsi(System.String)">
      <summary>Copia el contenido de una clase administrada <see cref="T:System.String" /> a un bloque de memoria asignado por el asignador de tareas COM no administrada.</summary>
      <returns>Entero que representa un puntero al bloque de memoria asignado a la cadena, o 0 si <paramref name="s" /> es null.</returns>
      <param name="s">Cadena administrada que se va a copiar.</param>
      <exception cref="T:System.OutOfMemoryException">No hay memoria suficiente.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El parámetro <paramref name="s" /> supera la longitud máxima permitida por el sistema operativo.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni(System.String)">
      <summary>Copia el contenido de una clase administrada <see cref="T:System.String" /> a un bloque de memoria asignado por el asignador de tareas COM no administrada.</summary>
      <returns>Entero que representa un puntero al bloque de memoria asignado a la cadena, o 0 si s es null.</returns>
      <param name="s">Cadena administrada que se va a copiar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">El parámetro <paramref name="s" /> supera la longitud máxima permitida por el sistema operativo.</exception>
      <exception cref="T:System.OutOfMemoryException">No hay memoria suficiente.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalAnsi(System.String)">
      <summary>Copia el contenido de una clase administrada <see cref="T:System.String" /> en la memoria no administrada, convirtiéndolo en formato ANSI mientras realiza la copia.</summary>
      <returns>Dirección, en memoria no administrada, adonde se copió <paramref name="s" /> o 0 si <paramref name="s" /> es null.</returns>
      <param name="s">Cadena administrada que se va a copiar.</param>
      <exception cref="T:System.OutOfMemoryException">No hay memoria suficiente.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El parámetro <paramref name="s" /> supera la longitud máxima permitida por el sistema operativo.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalUni(System.String)">
      <summary>Copia el contenido de un <see cref="T:System.String" /> administrado en la memoria no administrada.</summary>
      <returns>Dirección, en memoria no administrada, donde se copió <paramref name="s" /> o 0 si <paramref name="s" /> es null.</returns>
      <param name="s">Cadena administrada que se va a copiar.</param>
      <exception cref="T:System.OutOfMemoryException">El método no pudo asignar la suficiente memoria de montón nativo.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El parámetro <paramref name="s" /> supera la longitud máxima permitida por el sistema operativo.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr(System.Object,System.IntPtr,System.Boolean)">
      <summary>Calcula las referencias a los datos desde un objeto administrado a un bloque de memoria no administrado.</summary>
      <param name="structure">Objeto administrado que contiene los datos de referencias que se van a calcular.Este objeto debe ser una estructura o una instancia de una clase con formato.</param>
      <param name="ptr">Puntero a un bloque de memoria no administrada que debe ser asignado antes de llamar a este método.</param>
      <param name="fDeleteOld">truepara llamar a la <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)" /> método en el <paramref name="ptr" /> parámetro antes de que este método copia los datos.El bloque debe contener datos válidos.Observe que al pasar false cuando el bloque de memoria ya contiene datos puede provocar una pérdida de memoria.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> es un tipo de referencia que no es una clase con formato. o bien<paramref name="structure" /> es un tipo genérico. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr``1(``0,System.IntPtr,System.Boolean)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Calcula las referencias de un objeto administrado de un tipo especificado a un bloque de memoria no administrado. </summary>
      <param name="structure">Objeto administrado que contiene los datos de referencias que se van a calcular.El objeto debe ser una estructura o una instancia de una clase con formato.</param>
      <param name="ptr">Puntero a un bloque de memoria no administrada que debe ser asignado antes de llamar a este método. </param>
      <param name="fDeleteOld">truepara llamar a la <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)" /> método en el <paramref name="ptr" /> parámetro antes de que este método copia los datos.El bloque debe contener datos válidos.Observe que al pasar false cuando el bloque de memoria ya contiene datos puede provocar una pérdida de memoria.</param>
      <typeparam name="T">Tipo del objeto administrado. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> es un tipo de referencia que no es una clase con formato. </exception>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemDefaultCharSize">
      <summary>Representa el tamaño de carácter predeterminado del sistema; el valor predeterminado es 2 para los sistemas Unicode y 1 para los sistemas ANSI.Este campo es de solo lectura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemMaxDBCSCharSize">
      <summary>Representa el tamaño máximo de un juego de caracteres de doble byte (DBCS), expresado en bytes, para el actual sistema operativo.Este campo es de solo lectura.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32)">
      <summary>Se produce una excepción con un valor HRESULT de error específico.</summary>
      <param name="errorCode">HRESULT correspondiente a la excepción deseada.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)">
      <summary>Inicia una excepción con un valor HRESULT de error concreto, basado en la interfaz IErrorInfo especificada.</summary>
      <param name="errorCode">HRESULT correspondiente a la excepción deseada.</param>
      <param name="errorInfo">Puntero a la interfaz IErrorInfo que proporciona más información sobre el error.Puede especificar IntPtr(0) para utilizar la interfaz IErrorInfo actual o IntPtr(-1) para omitir la interfaz IErrorInfo actual y construir la excepción del código de error únicamente.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement(System.Array,System.Int32)">
      <summary>Obtiene la dirección del elemento en el índice especificado dentro de la matriz especificada.</summary>
      <returns>Dirección de <paramref name="index" /> dentro de <paramref name="arr" />.</returns>
      <param name="arr">Matriz que contiene el elemento deseado.</param>
      <param name="index">Índice en el parámetro <paramref name="arr" /> del elemento deseado.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement``1(``0[],System.Int32)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Obtiene la dirección del elemento en el índice especificado en una matriz de tipo especificado. </summary>
      <returns>Dirección de <paramref name="index" /> en <paramref name="arr" />. </returns>
      <param name="arr">Matriz que contiene el elemento deseado. </param>
      <param name="index">Índice del elemento deseado en la matriz <paramref name="arr" />. </param>
      <typeparam name="T">Tipo de matriz. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Byte)">
      <summary>Escribe un único byte en la memoria no administrada.</summary>
      <param name="ptr">Dirección de la memoria no administrada en la que se va a escribir.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null.o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Int32,System.Byte)">
      <summary>Escribe un valor de un solo byte en la memoria no administrada en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base de la memoria no administrada en la que se va a escribir.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.Object,System.Int32,System.Byte)">
      <summary>Escribe un valor de un solo byte en la memoria no administrada en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de destino.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Char)">
      <summary>Escribe un carácter como un valor entero de 16 bits en la memoria no administrada.</summary>
      <param name="ptr">Dirección de la memoria no administrada en la que se va a escribir.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null.o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int16)">
      <summary>Escribe un valor entero de 16 bits en la memoria no administrada.</summary>
      <param name="ptr">Dirección de la memoria no administrada en la que se va a escribir.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null.o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Char)">
      <summary>Escribe un valor entero de 16 bits con signo en la memoria no administrada en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base del montón nativo en la que se va a escribir.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Int16)">
      <summary>Escribe un valor entero de 16 bits con signo en la memoria no administrada en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base de la memoria no administrada en la que se va a escribir.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Char)">
      <summary>Escribe un valor entero de 16 bits con signo en la memoria no administrada en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de destino.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Int16)">
      <summary>Escribe un valor entero de 16 bits con signo en la memoria no administrada en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de destino.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura. </param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32)">
      <summary>Escribe un valor entero de 32 bits con signo en la memoria no administrada.</summary>
      <param name="ptr">Dirección de la memoria no administrada en la que se va a escribir.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null. o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32,System.Int32)">
      <summary>Escribe un valor entero de 32 bits con signo en la memoria no administrada en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base de la memoria no administrada en la que se va a escribir.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.Object,System.Int32,System.Int32)">
      <summary>Escribe un valor entero de 32 bits con signo en la memoria no administrada en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de destino.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int32,System.Int64)">
      <summary>Escribe un valor entero de 64 bits con signo en la memoria no administrada en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base de la memoria no administrada en la que se va a escribir.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int64)">
      <summary>Escribe un valor entero de 64 bits con signo en la memoria no administrada.</summary>
      <param name="ptr">Dirección de la memoria no administrada en la que se va a escribir.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null.o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.Object,System.Int32,System.Int64)">
      <summary>Escribe un valor entero de 64 bits con signo en la memoria no administrada en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de destino.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.Int32,System.IntPtr)">
      <summary>Escribe en la memoria no administrada un valor de tipo entero cuyo tamaño en bytes es propio del procesador nativo en un desplazamiento especificado.</summary>
      <param name="ptr">Dirección base de la memoria no administrada en la que se va a escribir.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.IntPtr)">
      <summary>Escribe en la memoria no administrada un valor de tipo entero cuyo tamaño en bytes es propio del procesador nativo.</summary>
      <param name="ptr">Dirección de la memoria no administrada en la que se va a escribir.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> no es un formato reconocido.o bien<paramref name="ptr" />is null.o bien<paramref name="ptr" /> no es válido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.Object,System.Int32,System.IntPtr)">
      <summary>Escribe en la memoria no administrada un valor de tipo entero cuyo tamaño en bytes es propio del procesador nativo.</summary>
      <param name="ptr">Dirección base en la memoria no administrada del objeto de destino.</param>
      <param name="ofs">Desplazamiento de bytes adicional, que se agrega al parámetro <paramref name="ptr" /> antes de la escritura.</param>
      <param name="val">Valor que se va a escribir.</param>
      <exception cref="T:System.AccessViolationException">La dirección base (<paramref name="ptr" />) más el byte de desplazamiento (<paramref name="ofs" />) produce un valor null o una dirección no válida.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> es un objeto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Este método no acepta parámetros <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(System.IntPtr)">
      <summary>Libera un puntero BSTR que se asignó utilizando el método <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToBSTR(System.Security.SecureString)" />.</summary>
      <param name="s">Dirección del BSTR que se va a liberar.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemAnsi(System.IntPtr)">
      <summary>Libera un puntero a una cadena no administrada que se ha asignado con el método <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemAnsi(System.Security.SecureString)" />.</summary>
      <param name="s">La dirección de la cadena no administrada que se va a liberar.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemUnicode(System.IntPtr)">
      <summary>Libera un puntero a una cadena no administrada que se ha asignado con el método <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemUnicode(System.Security.SecureString)" />.</summary>
      <param name="s">La dirección de la cadena no administrada que se va a liberar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocAnsi(System.IntPtr)">
      <summary>Libera un puntero a una cadena no administrada que se ha asignado con el método <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocAnsi(System.Security.SecureString)" />.</summary>
      <param name="s">La dirección de la cadena no administrada que se va a liberar.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocUnicode(System.IntPtr)">
      <summary>Libera un puntero a una cadena no administrada que se ha asignado con el método <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocUnicode(System.Security.SecureString)" />.</summary>
      <param name="s">La dirección de la cadena no administrada que se va a liberar.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalAsAttribute">
      <summary>Indica la forma de calcular las referencias de los datos entre el código administrado y el código no administrado.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Int16)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> con el valor de <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> especificado.</summary>
      <param name="unmanagedType">Valor por el que se calcularán las referencias de los datos. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Runtime.InteropServices.UnmanagedType)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> con el miembro de enumeración <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> especificado.</summary>
      <param name="unmanagedType">Valor por el que se calcularán las referencias de los datos. </param>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType">
      <summary>Especifica el tipo de elemento del campo <see cref="F:System.Runtime.InteropServices.UnmanagedType.LPArray" /> o del campo <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValArray" /> no administrado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.IidParameterIndex">
      <summary>Especifica el índice de parámetro del atributo no administrado iid_is utilizado por COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalCookie">
      <summary>Proporciona información adicional a un contador de referencias personalizado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType">
      <summary>Especifica el nombre completo de un contador de referencias personalizado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalTypeRef">
      <summary>Implementa <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType" /> como un tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType">
      <summary>Indica el tipo de elemento de <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArrayUserDefinedSubType">
      <summary>Indica el tipo de elemento definido por el usuario de <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst">
      <summary>Indica el número de elementos de la matriz de longitud fija o el número de caracteres (no bytes) de una cadena que se van a importar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex">
      <summary>Indica el parámetro de base cero que contiene el recuento de elementos de matriz, similar a size_is en COM.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.MarshalAsAttribute.Value">
      <summary>Obtiene el valor de <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> a partir del cual se van a calcular las referencias de los datos.</summary>
      <returns>Valor de <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> a partir del cual se van a calcular las referencias de los datos.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalDirectiveException">
      <summary>La excepción que produce el contador de referencias cuando encuentra un atributo <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> que no admite.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor">
      <summary>Inicializa una nueva instancia de la clase MarshalDirectiveException con propiedades predeterminadas.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase MarshalDirectiveException con el mensaje de error especificado.</summary>
      <param name="message">Mensaje de error que especifica la razón de la excepción. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.MarshalDirectiveException" /> con un mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="inner" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.OptionalAttribute">
      <summary>Indica que un parámetro es opcional.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.OptionalAttribute.#ctor">
      <summary>Inicializa una nueva instancia de la clase OptionalAttribute con valores predeterminados.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.PreserveSigAttribute">
      <summary>Indica que debe suprimirse la transformación de la firma de retval o la transformación del valor HRESULT que tiene lugar durante las llamadas de interoperabilidad COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.PreserveSigAttribute.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.PreserveSigAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayRankMismatchException">
      <summary>Excepción que se produce cuando el rango de una matriz SAFEARRAY entrante no coincide con el rango especificado en la firma administrada.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor">
      <summary>Inicializa una nueva instancia de la clase SafeArrayTypeMismatchException con valores predeterminados.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase SafeArrayRankMismatchException con el mensaje especificado.</summary>
      <param name="message">Mensaje que indica el motivo de la excepción. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.SafeArrayRankMismatchException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="inner" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException">
      <summary>Excepción que se produce cuando el tipo de una matriz SAFEARRAY entrante no coincide con el tipo especificado en la firma administrada.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor">
      <summary>Inicializa una nueva instancia de la clase SafeArrayTypeMismatchException con valores predeterminados.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase SafeArrayTypeMismatchException con el mensaje especificado.</summary>
      <param name="message">Mensaje que indica el motivo de la excepción. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="inner" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeBuffer">
      <summary>Proporciona un búfer de memoria controlado que se puede usar para lectura y escritura.Los intentos de obtener acceso a memoria fuera del búfer controlado provocan excepciones (agotamientos y saturaciones).</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.#ctor(System.Boolean)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> y especifica si el identificador del búfer se va a liberar de forma segura. </summary>
      <param name="ownsHandle">Se establece en true para liberar de forma confiable el identificador durante la fase de finalización; se establece en false para impedir que se libere de forma confiable (no se recomienda).</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)">
      <summary>Obtiene un puntero de un objeto <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> para un bloque de memoria.</summary>
      <param name="pointer">Puntero de byte, pasado por referencia, para recibir el puntero desde dentro del objeto <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.Debe establecer este puntero en null antes de llamar a este método.</param>
      <exception cref="T:System.InvalidOperationException">No se ha llamado al método <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />. </exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.ByteLength">
      <summary>Obtiene el tamaño del búfer, en bytes.</summary>
      <returns>Número de bytes del búfer de memoria.</returns>
      <exception cref="T:System.InvalidOperationException">No se ha llamado al método <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize``1(System.UInt32)">
      <summary>Define el tamaño de asignación del área de memoria especificando el número de tipos de valor.Debe llamar a este método antes de usar la instancia de <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numElements">Número de elementos del tipo de valor para el que se va a asignar memoria.</param>
      <typeparam name="T">Tipo de valor para el que se va a asignar memoria.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> es menor que cero.O bien<paramref name="numElements" /> multiplicado por el tamaño de cada elemento es mayor que el espacio de direcciones disponible.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt32,System.UInt32)">
      <summary>Especifica el tamaño de asignación del búfer de memoria utilizando el número especificado de elementos y tamaño de elemento.Debe llamar a este método antes de usar la instancia de <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numElements">Número de elementos que hay en el búfer.</param>
      <param name="sizeOfEachElement">Tamaño de cada elemento del búfer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> es menor que cero. O bien<paramref name="sizeOfEachElement" /> es menor que cero.O bien<paramref name="numElements" /> multiplicado por <paramref name="sizeOfEachElement" /> es mayor que el espacio de direcciones disponible.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt64)">
      <summary>Define el tamaño de asignación del área de memoria en bytes.Debe llamar a este método antes de usar la instancia de <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numBytes">Número de bytes del búfer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numBytes" /> es menor que cero.O bien<paramref name="numBytes" /> es mayor que el espacio de direcciones disponible.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.IsInvalid"></member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Read``1(System.UInt64)">
      <summary>Lee un tipo de valor de la memoria en el desplazamiento especificado.</summary>
      <returns>Tipo de valor leído de la memoria.</returns>
      <param name="byteOffset">Ubicación desde la que se va a leer el tipo de valor.Quizás tenga que tener en cuenta los problemas de alineación.</param>
      <typeparam name="T">Tipo de valor que se va a leer.</typeparam>
      <exception cref="T:System.InvalidOperationException">No se ha llamado al método <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReadArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>Lee el número especificado de tipos de valor de la memoria empezando en el desplazamiento y los escribe en una matriz empezando en el índice. </summary>
      <param name="byteOffset">Ubicación desde la que se va a iniciar la lectura.</param>
      <param name="array">Matriz de salida en la que se va a escribir.</param>
      <param name="index">Ubicación en la matriz de salida en la que se va a empezar a escribir.</param>
      <param name="count">Número de tipos de valor que se van a leer de la matriz de entrada y que se van a escribir en la matriz de salida.</param>
      <typeparam name="T">Tipo de valor que se va a leer.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.O bien<paramref name="count" /> es menor que cero.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentException">La longitud de la matriz menos el índice es menor que <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">No se ha llamado al método <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReleasePointer">
      <summary>Libera un puntero obtenido por el método <see cref="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)" />.</summary>
      <exception cref="T:System.InvalidOperationException">No se ha llamado al método <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Write``1(System.UInt64,``0)">
      <summary>Escribe un tipo de valor en la memoria en la ubicación dada.</summary>
      <param name="byteOffset">Ubicación en la que se va a empezar a escribir.Quizás tenga que tener en cuenta los problemas de alineación.</param>
      <param name="value">Valor que se va a escribir.</param>
      <typeparam name="T">Tipo de valor que se va a escribir.</typeparam>
      <exception cref="T:System.InvalidOperationException">No se ha llamado al método <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.WriteArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>Escribe el número especificado de tipos de valor en una ubicación de memoria leyendo bytes a partir de la ubicación especificada en la matriz de entrada.</summary>
      <param name="byteOffset">Ubicación de la memoria en la que se va a escribir.</param>
      <param name="array">Matriz de entrada.</param>
      <param name="index">Desplazamiento en la matriz a partir del cual se va a empezar a leer.</param>
      <param name="count">Número de tipos de valor que se van a escribir.</param>
      <typeparam name="T">Tipo de valor que se va a escribir.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="index" /> o <paramref name="count" /> es menor que cero.</exception>
      <exception cref="T:System.ArgumentException">La longitud de la matriz de entrada menos <paramref name="index" /> es menor que <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">No se ha llamado al método <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.SEHException">
      <summary>Representa errores de control de excepciones estructurado (SEH). </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.SEHException" />. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.SEHException" /> con el mensaje especificado.</summary>
      <param name="message">Mensaje que indica el motivo de la excepción. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.SEHException" /> con un mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="inner" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.CanResume">
      <summary>Indica si la excepción puede recuperarse o no, y si el código puede continuar ejecutándose a partir del punto en el que se inició la excepción.</summary>
      <returns>Siempre false, ya que no se implementan excepciones reanudables.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.TypeIdentifierAttribute">
      <summary>Proporciona compatibilidad con la equivalencia de tipos.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" />. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> con el ámbito y el identificador especificados. </summary>
      <param name="scope">Primera cadena de equivalencia de tipos.</param>
      <param name="identifier">Segunda cadena de equivalencia de tipos.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Identifier">
      <summary>Obtiene el valor del parámetro <paramref name="identifier" /> que se pasó al constructor <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" />.</summary>
      <returns>Valor del parámetro <paramref name="identifier" /> del constructor.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Scope">
      <summary>Obtiene el valor del parámetro <paramref name="scope" /> que se pasó al constructor <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" />.</summary>
      <returns>Valor del parámetro <paramref name="scope" /> del constructor.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnknownWrapper">
      <summary>Encapsula los objetos cuyas referencias se van a calcular como VT_UNKNOWN.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnknownWrapper.#ctor(System.Object)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.UnknownWrapper" /> con el objeto que se va a incluir en el contenedor.</summary>
      <param name="obj">Objeto que se va a incluir en el contenedor. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.UnknownWrapper.WrappedObject">
      <summary>Obtiene el objeto incluido en este contenedor.</summary>
      <returns>Objeto contenido en un contenedor.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute">
      <summary>Controla el comportamiento del cálculo de referencias de una firma de delegado pasada como un puntero a función no administrado al código no administrado o desde este.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute" /> con la convención de llamada especificada. </summary>
      <param name="callingConvention">Convención de llamada especificada.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.BestFitMapping">
      <summary>Habilita o deshabilita el comportamiento de asignación de ajuste perfecto al convertir caracteres Unicode en caracteres ANSI.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CallingConvention">
      <summary>Obtiene el valor de la convención de llamada.</summary>
      <returns>El valor de la convención de llamada especificado por el constructor <see cref="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)" />.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CharSet">
      <summary>Indica la forma de calcular las referencias de los parámetros de cadena al método, y controla los daños en los nombres.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.SetLastError">
      <summary>Indica que el destinatario de la llamada llamará a la función SetLastError de la API Win32 antes de volver del método con atributos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.ThrowOnUnmappableChar">
      <summary>Habilita o deshabilita el inicio de una excepción cuando un carácter Unicode que no se puede asignar se convierte en un carácter ANSI "?".</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedType">
      <summary>Identifica la forma de calcular las referencias de parámetros o campos en el código no administrado. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AnsiBStr">
      <summary>Cadena de caracteres ANSI que tiene una longitud fija de un solo byte.Este miembro puede usarse en el tipo de datos <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AsAny">
      <summary>Tipo dinámico que determina el tipo de un objeto en tiempo de ejecución y calcula las referencias del objeto como de dicho tipo.Este miembro es válido para los métodos de invocación de plataforma únicamente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Bool">
      <summary>Valor booleano de 4 bytes (true != 0, false = 0).Se trata del tipo BOOL de Win32.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.BStr">
      <summary>Cadena de caracteres Unicode que tiene una longitud fija de doble byte.Este miembro, que es la cadena predeterminada en COM, puede utilizarse en el tipo de datos <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValArray">
      <summary>Cuando la propiedad <see cref="P:System.Runtime.InteropServices.MarshalAsAttribute.Value" /> se establece en ByValArray, el campo <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> debe establecerse de modo que indique el número de elementos de la matriz.El campo <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType" /> también puede contener el <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> de los elementos de la matriz cuando sea necesario diferenciar los tipos de cadenas.<see cref="T:System.Runtime.InteropServices.UnmanagedType" /> solo puede usarse en una matriz cuyos elementos aparezcan en forma de campos de una estructura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr">
      <summary>Se utiliza para matrices en línea de caracteres de longitud fija que aparecen dentro de una estructura.El tipo de caracteres usado con <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr" /> viene determinado por el argumento <see cref="T:System.Runtime.InteropServices.CharSet" /> del atributo <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> que se aplica a la estructura contenedora.Debe utilizarse siempre el campo <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> para indicar el tamaño de la matriz.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Currency">
      <summary>Tipo de divisa.Se utiliza en <see cref="T:System.Decimal" /> para calcular las referencias del valor decimal en forma de tipo de divisa COM en lugar de Decimal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Error">
      <summary>Tipo nativo asociado a <see cref="F:System.Runtime.InteropServices.UnmanagedType.I4" /> o a <see cref="F:System.Runtime.InteropServices.UnmanagedType.U4" /> y que hace que el parámetro se exporte como un valor HRESULT a la biblioteca de tipos exportada.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.FunctionPtr">
      <summary>Entero que puede utilizarse como un puntero de función de estilo C.Este miembro puede usarse en un tipo de datos <see cref="T:System.Delegate" /> o en un tipo que se herede de <see cref="T:System.Delegate" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.HString">
      <summary>Cadena de Windows en tiempo de ejecución.Este miembro puede usarse en el tipo de datos <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I1">
      <summary>Entero de 1 byte con signo.Este miembro puede utilizarse para transformar un valor booleano en un valor bool de estilo C de 1 byte (true = 1, false = 0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I2">
      <summary>Entero de 2 bytes con signo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I4">
      <summary>Entero de 4 bytes con signo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I8">
      <summary>Entero de 8 bytes con signo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IDispatch">
      <summary>Puntero de interfaz IDispatch COM (Object en Microsoft Visual Basic 6.0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IInspectable">
      <summary>Puntero a interfaz Windows en tiempo de ejecución.Este miembro puede usarse en el tipo de datos <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Interface">
      <summary>Puntero a interfaz COM.<see cref="T:System.Guid" /> de la interfaz se obtiene a partir de los metadatos de la clase.Este miembro se utiliza para especificar el tipo exacto de interfaz o el tipo predeterminado de interfaz si se aplica a una clase.Este miembro se comporta como <see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" /> cuando se aplica al tipo de datos <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IUnknown">
      <summary>Puntero a IUnknown COM.Este miembro puede usarse en el tipo de datos <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPArray">
      <summary>Puntero al primer elemento de una matriz de estilo C.Cuando se calculan las referencias de código administrado a no administrado, la longitud de la matriz viene determinada por la longitud de la matriz administrada.Cuando se calculan las referencias de código no administrado a código administrado, la longitud de la matriz se determina a partir de los campos <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> y <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex" /> seguidos, de forma opcional, del tipo no administrado de los elementos de la matriz cuando es necesario distinguir los tipos de cadena.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStr">
      <summary>Cadena de caracteres ANSI de un solo byte terminada en null.Este miembro puede usarse en el tipo de datos <see cref="T:System.String" /> y en el tipo de datos <see cref="T:System.Text.StringBuilder" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStruct">
      <summary>Puntero a una estructura de estilo C que se utiliza para calcular las referencias de clases con formato administradas.Este miembro es válido para los métodos de invocación de plataforma únicamente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPTStr">
      <summary>Cadena de caracteres que depende de la plataforma: ANSI en Windows 98 y Unicode en Windows NT y Windows XP.Este valor solo se admite para llamadas a la plataforma y no para la interoperabilidad COM, porque no se admite la exportación de una cadena de tipo LPTStr.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPWStr">
      <summary>Cadena de caracteres Unicode de 2 bytes terminada en null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R4">
      <summary>Número de punto flotante de 4 bytes.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R8">
      <summary>Número de punto flotante de 8 bytes.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SafeArray">
      <summary>Matriz SafeArray que se describe a sí misma y que contiene el tipo, rango y límites de los datos de la matriz asociada.Este miembro puede utilizarse con el campo <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType" /> para reemplazar el tipo de elemento predeterminado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Struct">
      <summary>VARIANT que se utiliza para calcular las referencias de clases con formato administradas y tipos de valor.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysInt">
      <summary>Entero con signo que depende de una plataforma: 4 bytes en Windows de 32 bits, 8 bytes en Windows de 64 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysUInt">
      <summary>Entero sin signo que depende de una plataforma: 4 bytes en Windows de 32 bits, 8 bytes en Windows de 64 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.TBStr">
      <summary>Cadena de char de longitud fija que depende de la plataforma: ANSI en Windows 98, Unicode en Windows NT.Este miembro de tipo BSTR se utiliza en raras ocasiones.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U1">
      <summary>Entero de 1 byte sin signo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U2">
      <summary>Entero de 2 bytes sin signo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U4">
      <summary>Entero de 4 bytes sin signo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U8">
      <summary>Entero de 8 bytes sin signo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VariantBool">
      <summary>Tipo VARIANT_BOOL de 2 bytes definido por OLE (true = -1, false = 0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VBByRefStr">
      <summary>Un valor que permite a Visual Basic cambiar una cadena del código no administrado y reflejar los resultados en código administrado.Este valor solo es compatible para la invocación de plataforma.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VarEnum">
      <summary>Indica cómo calcular las referencias de los elementos de matriz cuando se calculan las referencias de matriz de código administrado a código no administrado como <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ARRAY">
      <summary>Indica un puntero SAFEARRAY.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB">
      <summary>Indica la longitud de los bytes con prefijo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB_OBJECT">
      <summary>Indica que un objeto binario contiene un objeto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BOOL">
      <summary>Indica un valor booleano.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BSTR">
      <summary>Indica una cadena BSTR.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BYREF">
      <summary>Indica que un valor es una referencia.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CARRAY">
      <summary>Indica una matriz de estilo C.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CF">
      <summary>Indica el formato del portapapeles.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CLSID">
      <summary>Indica el identificador de una clase.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CY">
      <summary>Indica un valor de moneda.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DATE">
      <summary>Indica un valor de DATE.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DECIMAL">
      <summary>Indica un valor decimal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH">
      <summary>Indica un puntero IDispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_EMPTY">
      <summary>Indica que no se ha especificado un valor.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ERROR">
      <summary>Indica un SCODE.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_FILETIME">
      <summary>Indica un valor de FILETIME.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_HRESULT">
      <summary>Indica un HRESULT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I1">
      <summary>Indica un valor char.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I2">
      <summary>Indica un entero short.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I4">
      <summary>Indica un entero long.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I8">
      <summary>Indica un entero de 64 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_INT">
      <summary>Indica un valor entero.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPSTR">
      <summary>Indica una cadena terminada en un carácter null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPWSTR">
      <summary>Indica una cadena grande terminada en null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_NULL">
      <summary>Indica un valor null, similar al valor Null en SQL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_PTR">
      <summary>Indica un tipo de puntero.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R4">
      <summary>Indica un valor float.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R8">
      <summary>Indica un valor double.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_RECORD">
      <summary>Indica un tipo definido por el usuario.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_SAFEARRAY">
      <summary>Indica una SAFEARRAY.No es válida en un tipo VARIANT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORAGE">
      <summary>Indica que continúa el nombre de un almacenamiento.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORED_OBJECT">
      <summary>Indica que un almacenamiento contiene un objeto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAM">
      <summary>Indica que continúa el nombre de una secuencia.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAMED_OBJECT">
      <summary>Indica que una secuencia contiene un objeto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI1">
      <summary>Indica un byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI2">
      <summary>Indica un valor unsignedshort.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI4">
      <summary>Indica un valor unsignedlong.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI8">
      <summary>Indica un entero de 64 bits sin signo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UINT">
      <summary>Indica un valor entero unsigned.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UNKNOWN">
      <summary>Indica un puntero IUnknown.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_USERDEFINED">
      <summary>Indica un tipo definido por el usuario.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VARIANT">
      <summary>Indica un puntero far VARIANT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VECTOR">
      <summary>Indica una matriz simple y contada.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VOID">
      <summary>Indica una matriz void de estilo C.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VariantWrapper">
      <summary>Calcula las referencias de datos de tipo VT_VARIANT | VT_BYREF de código administrado a no administrado.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.VariantWrapper.#ctor(System.Object)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> para el parámetro <see cref="T:System.Object" /> especificado.</summary>
      <param name="obj">Objeto cuyas referencias se van a calcular. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.VariantWrapper.WrappedObject">
      <summary>Obtiene el objeto ajustado por el objeto <see cref="T:System.Runtime.InteropServices.VariantWrapper" />.</summary>
      <returns>Objeto ajustado por el objeto <see cref="T:System.Runtime.InteropServices.VariantWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ADVF">
      <summary>Especifica el comportamiento solicitado al configurar un receptor con notificación o una conexión de almacenamiento en caché con un objeto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_DATAONSTOP">
      <summary>Asegura la accesibilidad a los datos en las conexiones de consulta. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_NODATA">
      <summary>Por lo que respecta a los datos de las conexiones de consulta (<see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.DAdvise(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.ADVF,System.Runtime.InteropServices.ComTypes.IAdviseSink,System.Int32@)" /> o <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />), este marcador solicita del objeto de datos que no envíe datos al llamar a <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_ONLYONCE">
      <summary>Solicita que el objeto sólo haga una notificación de cambios o una actualización de la caché antes de eliminar la conexión.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_PRIMEFIRST">
      <summary>Solicita que el objeto no espere a que los datos o la vista cambien antes de realizar la llamada inicial a <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> (en las conexiones de consultas de vistas o datos) ni a que se actualice la caché (en las conexiones de caché).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN">
      <summary>Las aplicaciones del objeto DLL y los controladores de objetos que dibujan sus objetos utilizan este valor.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_NOHANDLER">
      <summary>Sinónimo de <see cref="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN" />, que se utiliza más a menudo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_ONSAVE">
      <summary>En las conexiones de caché, este marcador actualiza la representación almacenada en la memoria caché, pero sólo cuando se guarda el objeto que contiene la caché.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BIND_OPTS">
      <summary>Almacena los parámetros que se utilizan durante una operación de enlace de moniker.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.cbStruct">
      <summary>Especifica el tamaño en bytes de la estructura BIND_OPTS.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.dwTickCountDeadline">
      <summary>Indica la cantidad de tiempo (tiempo de reloj en milisegundos, tal y como lo devuelve la función GetTickCount) que el llamador especificó para completar la operación de enlace.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfFlags">
      <summary>Controla algunos aspectos de las operaciones de enlace de moniker.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfMode">
      <summary>Representa los marcadores que deben utilizarse al abrir el archivo que contiene el objeto identificado por el moniker.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BINDPTR">
      <summary>Contiene un puntero a una estructura <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> o <see cref="T:System.Runtime.InteropServices.VARDESC" />, a las que está enlazado, o a una interfaz ITypeComp.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpfuncdesc">
      <summary>Representa un puntero a una estructura <see cref="T:System.Runtime.InteropServices.FUNCDESC" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lptcomp">
      <summary>Representa un puntero a una interfaz <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeComp" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpvardesc">
      <summary>Representa un puntero a una estructura <see cref="T:System.Runtime.InteropServices.VARDESC" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CALLCONV">
      <summary>Identifica la convención de llamada utilizada por un método descrito en una estructura METHODDATA.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_CDECL">
      <summary>Indica que se utiliza la convención de llamada de la declaración de C (CDECL) para un método. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MACPASCAL">
      <summary>Indica que se utiliza la convención de llamada Macintosh Pascal (MACPASCAL) para un método.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MAX">
      <summary>Indica el fin de la enumeración <see cref="T:System.Runtime.InteropServices.ComTypes.CALLCONV" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWCDECL">
      <summary>Indica que se utiliza la convención de llamada CDECL de Macintosh Programmers' Workbench (MPW) para un método.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWPASCAL">
      <summary>Indica que se utiliza la convención de llamada PASCAL de Macintosh Programmers' Workbench (MPW) para un método.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MSCPASCAL">
      <summary>Indica que se utiliza la convención de llamada MSC Pascal (MSCPASCAL) para un método.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_PASCAL">
      <summary>Indica que se utiliza la convención de llamada Pascal para un método.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_RESERVED">
      <summary>Este valor se reserva para un uso futuro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_STDCALL">
      <summary>Indica que se utiliza la convención de llamada estándar (STDCALL) para un método.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_SYSCALL">
      <summary>Indica que se utiliza la convención de llamada SYSCALL estándar para un método.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CONNECTDATA">
      <summary>Describe una conexión existente a un punto de conexión determinado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.dwCookie">
      <summary>Representa un símbolo (token) de conexión que se devuelve desde una llamada a <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.pUnk">
      <summary>Representa un puntero a la interfaz IUnknown en un receptor de consulta conectado.El llamador debe llamar a IUnknown::Release en este puntero cuando la estructura CONNECTDATA deja de ser necesaria.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DATADIR">
      <summary>Especifica la dirección del flujo de datos del parámetro <paramref name="dwDirection" /> del método <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" />.Esto último determina los formatos que el enumerador resultante puede utilizar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_GET">
      <summary>Solicita que el método <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> proporcione un enumerador para los formatos que pueden especificarse en el método <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.GetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_SET">
      <summary>Solicita que el método <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> proporcione un enumerador para los formatos que pueden especificarse en el método <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.SetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@,System.Boolean)" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DESCKIND">
      <summary>Identifica la descripción de tipo que se enlaza.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_FUNCDESC">
      <summary>Indica que se devolvió una estructura <see cref="T:System.Runtime.InteropServices.FUNCDESC" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_IMPLICITAPPOBJ">
      <summary>Indica que se devolvió un IMPLICITAPPOBJ.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_MAX">
      <summary>Indica un marcador de fin de enumeración.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_NONE">
      <summary>Indica que no se encontró ninguna coincidencia.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_TYPECOMP">
      <summary>Indica que se devolvió un objeto TYPECOMP.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_VARDESC">
      <summary>Indica que se devolvió un objeto VARDESC.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DISPPARAMS">
      <summary>Contiene los argumentos pasados a un método o a una propiedad mediante IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cArgs">
      <summary>Representa el recuento de argumentos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cNamedArgs">
      <summary>Representa el recuento de argumentos con nombre. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgdispidNamedArgs">
      <summary>Representa los identificadores de envío de argumentos con nombre.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgvarg">
      <summary>Representa una referencia a la matriz de argumentos.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DVASPECT">
      <summary>Especifica el aspecto deseado de la vista o los datos del objeto al dibujar u obtener los datos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_CONTENT">
      <summary>Una representación de un objeto que permite mostrarlo como objeto incrustado dentro de un contenedor.Este valor se suele especificar para los objetos de documento compuesto.La presentación se puede proporcionar para la pantalla o la impresora.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_DOCPRINT">
      <summary>Representación de un objeto en pantalla como si se imprimiera utilizando el comando Print del menú Archivo.Los datos descritos pueden representar una secuencia de páginas.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_ICON">
      <summary>Representación icónica de un objeto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_THUMBNAIL">
      <summary>Representación en miniatura de un objeto que permite éste se muestre en una herramienta de exploración.La miniatura es un bitmap de 16 colores (se recomienda) independiente del dispositivo que puede estar contenido en un metarchivo y que tiene un tamaño aproximado de 120 x 120 píxeles.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC">
      <summary>Contiene la descripción de tipo y la información de transferencia del proceso para una variable, función o un parámetro de función.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.desc">
      <summary>Contiene información de un elemento.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.tdesc">
      <summary>Identifica el tipo del elemento.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION">
      <summary>Contiene información de un elemento. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.idldesc">
      <summary>Contiene información para la comunicación remota del elemento.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.paramdesc">
      <summary>Contiene información del parámetro.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.EXCEPINFO">
      <summary>Describe las excepciones que ocurren durante IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrDescription">
      <summary>Describe el error destinado al cliente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrHelpFile">
      <summary>Contiene la unidad, la ruta de acceso y el nombre de archivo completos de un archivo de la Ayuda con más información acerca del error.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrSource">
      <summary>Indica el nombre del origen de la excepción.Normalmente se trata del nombre de una aplicación.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.dwHelpContext">
      <summary>Indica el identificador de contexto del tema de Ayuda dentro del archivo de Ayuda.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pfnDeferredFillIn">
      <summary>Representa un puntero a una función que toma una estructura <see cref="T:System.Runtime.InteropServices.EXCEPINFO" /> como argumento y devuelve un valor HRESULT.Si no se desea un relleno aplazado, este campo se establece en null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pvReserved">
      <summary>Este campo está reservado; se debe establecer en null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.scode">
      <summary>Valor devuelto que describe el error.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wCode">
      <summary>Representa un código de error que identifica el error.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wReserved">
      <summary>Este campo está reservado; se debe establecer en 0.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FILETIME">
      <summary>Representa el número de intervalos de 100 nanosegundos desde el 1 de enero de 1601.Esta estructura es un valor de 64 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwHighDateTime">
      <summary>Especifica los 32 bits superiores de FILETIME.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwLowDateTime">
      <summary>Especifica los 32 bits superiores de FILETIME.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FORMATETC">
      <summary>Representa un formato generalizado del Portapapeles. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.cfFormat">
      <summary>Especifica el formato de interés en concreto del portapapeles.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.dwAspect">
      <summary>Especifica una de las constantes de la enumeración <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> que indica el nivel de detalle que debe contener la representación.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.lindex">
      <summary>Especifica una parte del aspecto cuando los datos se deben dividir en los límites de páginas. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.ptd">
      <summary>Especifica un puntero a una estructura DVTARGETDEVICE que contiene información sobre el dispositivo de destino para el que se están creando los datos. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.tymed">
      <summary>Especifica una de las constantes de la enumeración <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" />, que indican el tipo de medio de almacenamiento utilizado para transferir los datos del objeto. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCDESC">
      <summary>Define una descripción de función.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.callconv">
      <summary>Especifica la convención de llamada de una función.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParams">
      <summary>Cuenta el número total de parámetros.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParamsOpt">
      <summary>Cuenta los parámetros opcionales.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cScodes">
      <summary>Cuenta los valores devueltos permitidos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.elemdescFunc">
      <summary>Contiene el tipo devuelto de la función.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.funckind">
      <summary>Especifica si la función es virtual, estática o de sólo distribución.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.invkind">
      <summary>Especifica el tipo de una función de propiedad.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgelemdescParam">
      <summary>Indica el tamaño de <see cref="F:System.Runtime.InteropServices.FUNCDESC.cParams" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgscode">
      <summary>Almacena el número de errores que una función puede devolver en un sistema de 16 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.memid">
      <summary>Identifica el identificador del miembro de función.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.oVft">
      <summary>Especifica el desplazamiento en la VTBL para <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_VIRTUAL" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.wFuncFlags">
      <summary>Indica el <see cref="T:System.Runtime.InteropServices.FUNCFLAGS" /> de una función.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCFLAGS">
      <summary>Identifica las constantes que definen las propiedades de una función.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FBINDABLE">
      <summary>Función que admite el enlace de datos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTBIND">
      <summary>Función que mejor representa al objeto.Sólo una función de un tipo puede tener este atributo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTCOLLELEM">
      <summary>Permite llevar a cabo una optimización en la que el compilador busca un miembro denominado "xyz" en el tipo "abc".Si se encuentra un miembro de este tipo y se marca como función de descriptor de acceso para un elemento de la colección predeterminada, entonces se genera una llamada a dicha función miembro.Se permite en miembros de interfaces y de interfaces Dispinterface. No se permite en módulos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDISPLAYBIND">
      <summary>Función que se muestra al usuario como enlazable.<see cref="F:System.Runtime.InteropServices.FUNCFLAGS.FUNCFLAG_FBINDABLE" /> también debe establecerse.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FHIDDEN">
      <summary>No debe mostrarse la función al usuario, aunque exista y sea enlazable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FIMMEDIATEBIND">
      <summary>Asignadas como propiedades enlazables individuales.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FNONBROWSABLE">
      <summary>La propiedad aparece en un examinador de objetos, pero no en un examinador de propiedades.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREPLACEABLE">
      <summary>Etiqueta la interfaz indicando que tiene comportamientos predeterminados.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREQUESTEDIT">
      <summary>Cuando se establece, cualquier llamada a un método que establezca la propiedad dará como resultado, en primer lugar, una llamada a IPropertyNotifySink::OnRequestEdit.La implementación de OnRequestEdit determina si está permitido que la llamada establezca la propiedad.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FRESTRICTED">
      <summary>No se debe permitir el acceso a la función desde lenguajes de macros.El marcador está destinado a funciones en el nivel del sistema o a funciones que los exploradores de tipos no deben mostrar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FSOURCE">
      <summary>La función devuelve un objeto que es un origen de eventos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUIDEFAULT">
      <summary>El miembro de la información de tipos es el miembro predeterminado que se mostrará en la interfaz de usuario.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUSESGETLASTERROR">
      <summary>La función admite GetLastError.Si ocurre un error durante la función, el llamador puede llamar a GetLastError para recuperar el código de error.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCKIND">
      <summary>Define cómo tener acceso a una función.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_DISPATCH">
      <summary>Sólo se puede tener acceso a la función a través de IDispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_NONVIRTUAL">
      <summary>La dirección static obtiene acceso a la función y toma un puntero this implícito.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_PUREVIRTUAL">
      <summary>Se obtiene acceso a la función mediante la tabla de función virtual (VTBL) y utilizando un puntero implícito this.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_STATIC">
      <summary>La dirección static obtiene acceso a la función y no toma un puntero this implícito.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_VIRTUAL">
      <summary>Se obtiene acceso a la función de la misma forma que <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_PUREVIRTUAL" />, excepto que la función tiene una implementación.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IAdviseSink">
      <summary>Proporciona una definición administrada de la interfaz IAdviseSink.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnClose">
      <summary>Notifica a todos los receptores de consultas registrados que el objeto ha pasado del estado de ejecución al estado de carga.  Este método es invocado por un servidor.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)">
      <summary>Notifica a todos los receptores de consulta registrados de los objetos de datos que los datos del objeto han cambiado.</summary>
      <param name="format">Estructura <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />, pasada mediante referencia, que describe la información de almacenamiento, representación, dispositivo de destino y formato del objeto de datos de llamada.</param>
      <param name="stgmedium">Estructura <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />, pasada mediante referencia, que define el medio de almacenamiento (memoria global, archivo de disco, objeto de almacenamiento, objeto de secuencia, interfaz de dispositivo gráfico (GDI) o indefinido) y la propiedad de ese medio para el objeto de datos de llamada.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnRename(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Notifica a todos los receptores de consultas registrados que el nombre del objeto ha cambiado.Este método es invocado por un servidor.</summary>
      <param name="moniker">Puntero a la interfaz IMoniker en el nuevo moniker completo del objeto.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnSave">
      <summary>Notifica a todos los receptores de consultas registrados que se ha guardado el objeto.Este método es invocado por un servidor.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnViewChange(System.Int32,System.Int32)">
      <summary>Notifica a los receptores de consultas registrados de un objeto que su vista ha cambiado.Este método es invocado por un servidor.</summary>
      <param name="aspect">Aspecto, o vista, del objeto.Contiene un valor tomado de la enumeración <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" />.</param>
      <param name="index">Parte de la vista que ha cambiado.Actualmente, sólo -1 es válido.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IBindCtx">
      <summary>Proporciona la definición administrada de la interfaz IBindCtx.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.EnumObjectParam(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>Enumera las cadenas que constituyen las claves de la tabla de parámetros de objetos contextuales mantenida internamente.</summary>
      <param name="ppenum">El resultado que devuelve este método contiene una referencia al enumerador de parámetros de objetos.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>Devuelve las opciones de enlace actuales almacenadas en el contexto actual del enlace.</summary>
      <param name="pbindopts">Puntero a la estructura que recibirá las opciones de enlace. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetObjectParam(System.String,System.Object@)">
      <summary>Busca una clave determinada en la tabla de parámetros de objetos contextuales mantenida internamente y devuelve el objeto correspondiente, si es que existe.</summary>
      <param name="pszKey">Nombre del objeto que se va a buscar. </param>
      <param name="ppunk">El resultado que devuelve este método contiene el puntero de interfaz del objeto.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetRunningObjectTable(System.Runtime.InteropServices.ComTypes.IRunningObjectTable@)">
      <summary>Devuelve el acceso a la Tabla de objetos en ejecución (ROT) que se aplica a este proceso de enlace.</summary>
      <param name="pprot">El resultado que devuelve este método contiene una referencia a la Tabla de objetos en ejecución (ROT).Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)">
      <summary>Registra el objeto que se ha pasado como uno de los objetos enlazados durante una operación de moniker y que se debería liberar cuando ésta finalice.</summary>
      <param name="punk">Objeto que se va a registrar para su liberación. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectParam(System.String,System.Object)">
      <summary>Registra el puntero de un objeto determinado con el nombre especificado en la tabla de punteros de objetos mantenida internamente.</summary>
      <param name="pszKey">Nombre con el que se va a registrar <paramref name="punk" />. </param>
      <param name="punk">Objeto que se va a registrar. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.ReleaseBoundObjects">
      <summary>Libera todos los objetos registrados actualmente con el contexto de enlace mediante el método <see cref="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectBound(System.Object)">
      <summary>Quita del conjunto de objetos registrados el objeto que debe ser liberado.</summary>
      <param name="punk">Objeto cuyo registro se va a anular para su liberación. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectParam(System.String)">
      <summary>Revoca el registro del objeto que se encuentra actualmente bajo la clave especificada en la tabla de parámetros de objetos contextuales mantenida internamente, si dicha clave está registrada en ese momento.</summary>
      <returns>Devuelve un valor S_OKHRESULT si la clave especificada se ha quitado correctamente de la tabla; de lo contrario, devuelve un valor S_FALSEHRESULT.</returns>
      <param name="pszKey">Clave cuyo registro se va a anular. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.SetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>Almacena un bloque de parámetros en el contexto del enlace.Estos parámetros se aplicarán a operaciones UCOMIMoniker posteriores que utilicen este contexto de enlace.</summary>
      <param name="pbindopts">Estructura que contiene las opciones de enlace que se van a establecer. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPoint">
      <summary>Proporciona la definición administrada de la interfaz IConnectionPoint.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)">
      <summary>Establece una conexión de consulta entre el punto de conexión y el objeto receptor del llamador.</summary>
      <param name="pUnkSink">Referencia al receptor para que reciba las llamadas para la interfaz de salida administrada por este punto de conexión. </param>
      <param name="pdwCookie">El resultado que devuelve este método contiene la cookie de la conexión.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.EnumConnections(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>Crea un objeto enumerador para recorrer en iteración las conexiones que existen a este punto de conexión.</summary>
      <param name="ppEnum">El resultado que devuelve este método contiene el enumerador recién creado.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionInterface(System.Guid@)">
      <summary>Devuelve el IID de la interfaz de salida administrada por este punto de conexión.</summary>
      <param name="pIID">El resultado que devuelve este parámetro contiene el IID de la interfaz de salida administrada por este punto de conexión.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionPointContainer(System.Runtime.InteropServices.ComTypes.IConnectionPointContainer@)">
      <summary>Recupera el puntero de interfaz IConnectionPointContainer al objeto conectable que, conceptualmente, posee este punto de conexión.</summary>
      <param name="ppCPC">El resultado que contiene este parámetro contiene la interfaz IConnectionPointContainer del objeto conectable.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Unadvise(System.Int32)">
      <summary>Finaliza una conexión de consulta previamente establecida mediante el método <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />.</summary>
      <param name="dwCookie">Cookie de conexión previamente devuelta por el método <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer">
      <summary>Proporciona la definición administrada de la interfaz IConnectionPointContainer.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.EnumConnectionPoints(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>Crea un enumerador de todos los puntos de conexión que admite el objeto conectable, un punto de conexión por cada IID.</summary>
      <param name="ppEnum">El resultado de este método contiene el nombre del puntero de interfaz del enumerador.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.FindConnectionPoint(System.Guid@,System.Runtime.InteropServices.ComTypes.IConnectionPoint@)">
      <summary>Solicita al objeto conectable si tiene un punto de conexión para un IID determinado y, en caso afirmativo, devuelve el puntero de interfaz IConnectionPoint a ese punto de conexión.</summary>
      <param name="riid">Referencia al IDD de la interfaz de salida cuyo punto de conexión se solicita. </param>
      <param name="ppCP">El resultado de este método contiene el punto de conexión que administra la interfaz de salida <paramref name="riid" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLDESC">
      <summary>Contiene la información necesaria para transferir un valor devuelto por una función, un parámetro o un elemento de estructura de un proceso a otro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.dwReserved">
      <summary>Reservado; se establece en null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.wIDLFlags">
      <summary>Indica un valor <see cref="T:System.Runtime.InteropServices.IDLFLAG" /> que describe el tipo.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLFLAG">
      <summary>Describe cómo transferir un elemento de estructura, un parámetro o un valor devuelto por una función de un proceso a otro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FIN">
      <summary>El parámetro pasa información del que llama al llamado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FLCID">
      <summary>El parámetro es el identificador local de una aplicación cliente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FOUT">
      <summary>El parámetro devuelve información del llamado al que llama.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FRETVAL">
      <summary>El parámetro es el valor devuelto del miembro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_NONE">
      <summary>No especifica si el parámetro pasa o recibe información.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints">
      <summary>Administra la definición de la interfaz IEnumConnectionPoints.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>Crea otro enumerador que contiene el mismo estado de enumeración que el actual.</summary>
      <param name="ppenum">El resultado que devuelve este método contiene una referencia al enumerador recién creado.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IConnectionPoint[],System.IntPtr)">
      <summary>Recupera un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el parámetro <paramref name="pceltFetched" /> es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de referencias a IConnectionPoint que se van a devolver en <paramref name="rgelt" />. </param>
      <param name="rgelt">El resultado que devuelve este método contiene una referencia a las conexiones enumeradas.Este parámetro se pasa sin inicializar.</param>
      <param name="pceltFetched">El resultado que devuelve este método contiene una referencia al número real de conexiones enumeradas en <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Reset">
      <summary>Restablece la secuencia de enumeración al principio.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Skip(System.Int32)">
      <summary>Omite un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el número de elementos omitidos es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de elementos de la enumeración que se van a omitir. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnections">
      <summary>Administra la definición de la interfaz IEnumConnections.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>Crea otro enumerador que contiene el mismo estado de enumeración que el actual.</summary>
      <param name="ppenum">El resultado que devuelve este método contiene una referencia al enumerador recién creado.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Next(System.Int32,System.Runtime.InteropServices.ComTypes.CONNECTDATA[],System.IntPtr)">
      <summary>Recupera un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el parámetro <paramref name="pceltFetched" /> es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de estructuras <see cref="T:System.Runtime.InteropServices.CONNECTDATA" /> que se van a devolver en <paramref name="rgelt" />. </param>
      <param name="rgelt">El resultado que devuelve este método contiene una referencia a las conexiones enumeradas.Este parámetro se pasa sin inicializar.</param>
      <param name="pceltFetched">El resultado que devuelve este método contiene una referencia al número real de conexiones enumeradas en <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Reset">
      <summary>Restablece la secuencia de enumeración al principio.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Skip(System.Int32)">
      <summary>Omite un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el número de elementos omitidos es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de elementos de la enumeración que se van a omitir. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC">
      <summary>Proporciona la definición administrada de la interfaz IEnumFORMATETC.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Clone(System.Runtime.InteropServices.ComTypes.IEnumFORMATETC@)">
      <summary>Crea otro enumerador que contiene el mismo estado de enumeración que el enumerador actual.</summary>
      <param name="newEnum">El resultado que devuelve este método contiene una referencia al enumerador recién creado.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Next(System.Int32,System.Runtime.InteropServices.ComTypes.FORMATETC[],System.Int32[])">
      <summary>Recupera un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el parámetro <paramref name="pceltFetched" /> es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de referencias a <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> que se van a devolver en <paramref name="rgelt" />.</param>
      <param name="rgelt">El resultado que devuelve este método contiene una referencia a las referencias enumeradas <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />.Este parámetro se pasa sin inicializar.</param>
      <param name="pceltFetched">El resultado que devuelve este método contiene una referencia al número real de referencias enumeradas en <paramref name="rgelt" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Reset">
      <summary>Restablece la secuencia de enumeración al principio.</summary>
      <returns>HRESULT con el valor S_OK.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Skip(System.Int32)">
      <summary>Omite un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el número de elementos omitidos es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de elementos de la enumeración que se van a omitir.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumMoniker">
      <summary>Administra la definición de la interfaz IEnumMoniker.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Clone(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Crea otro enumerador que contiene el mismo estado de enumeración que el actual.</summary>
      <param name="ppenum">El resultado que devuelve este método contiene una referencia al enumerador recién creado.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker[],System.IntPtr)">
      <summary>Recupera un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el parámetro <paramref name="pceltFetched" /> es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de monikers que se van a devolver en <paramref name="rgelt" />. </param>
      <param name="rgelt">El resultado que devuelve este método contiene una referencia a los monikers enumeradas.Este parámetro se pasa sin inicializar.</param>
      <param name="pceltFetched">El resultado que devuelve este método contiene una referencia al número real de monikers enumeradas en <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Reset">
      <summary>Restablece la secuencia de enumeración al principio.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Skip(System.Int32)">
      <summary>Omite un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el número de elementos omitidos es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de elementos de la enumeración que se van a omitir. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumString">
      <summary>Administra la definición de la interfaz IEnumString.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Clone(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>Crea otro enumerador que contiene el mismo estado de enumeración que el actual.</summary>
      <param name="ppenum">El resultado que devuelve este método contiene una referencia al enumerador recién creado.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Next(System.Int32,System.String[],System.IntPtr)">
      <summary>Recupera un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el parámetro <paramref name="pceltFetched" /> es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de cadenas que se van a devolver en <paramref name="rgelt" />. </param>
      <param name="rgelt">El resultado que devuelve este método contiene una referencia a las cadenas enumeradas.Este parámetro se pasa sin inicializar.</param>
      <param name="pceltFetched">El resultado que devuelve este método contiene una referencia al número real de cadenas enumeradas en <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Reset">
      <summary>Restablece la secuencia de enumeración al principio.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Skip(System.Int32)">
      <summary>Omite un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el número de elementos omitidos es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de elementos de la enumeración que se van a omitir. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT">
      <summary>Administra la definición de la interfaz IEnumVARIANT.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Clone">
      <summary>Crea otro enumerador que contiene el mismo estado de enumeración que el actual.</summary>
      <returns>Referencia de <see cref="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT" /> al enumerador que se acaba de crear.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Next(System.Int32,System.Object[],System.IntPtr)">
      <summary>Recupera un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el parámetro <paramref name="pceltFetched" /> es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de elementos que se van a devolver en <paramref name="rgelt" />. </param>
      <param name="rgVar">El resultado que devuelve este método contiene una referencia a elementos enumerados.Este parámetro se pasa sin inicializar.</param>
      <param name="pceltFetched">El resultado que devuelve este método contiene una referencia al número real de elementos enumerados en <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Reset">
      <summary>Restablece la secuencia de enumeración al principio.</summary>
      <returns>HRESULT con el valor S_OK.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Skip(System.Int32)">
      <summary>Omite un número especificado de elementos en la secuencia de enumeración.</summary>
      <returns>Es S_OK si el número de elementos omitidos es igual al parámetro <paramref name="celt" />; en caso contrario, es S_FALSE.</returns>
      <param name="celt">Número de elementos de la enumeración que se van a omitir. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMoniker">
      <summary>Proporciona la definición administrada de la interfaz IMoniker, con funcionalidad COM de IPersist e IPersistStream.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToObject(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>Utiliza el moniker para enlazar el objeto que identifica.</summary>
      <param name="pbc">Referencia a la interfaz IBindCtx en el objeto de contexto de enlace utilizado en esta operación de enlace. </param>
      <param name="pmkToLeft">Referencia al moniker a la izquierda del actual, si es que forma parte de un moniker compuesto. </param>
      <param name="riidResult">Identificador (IID) de la interfaz que intenta utilizar el cliente para comunicar con el objeto identificado por el moniker. </param>
      <param name="ppvResult">El resultado que devuelve este método contiene una referencia a la interfaz solicitada por <paramref name="riidResult" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToStorage(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>Recupera un puntero de interfaz al almacenamiento que contiene el objeto identificado por el moniker.</summary>
      <param name="pbc">Referencia a la interfaz IBindCtx en el objeto de contexto de enlace utilizado durante esta operación de enlace. </param>
      <param name="pmkToLeft">Referencia al moniker a la izquierda del actual, si es que forma parte de un moniker compuesto. </param>
      <param name="riid">Identificador de interfaz (IID) de la interfaz de almacenamiento solicitada. </param>
      <param name="ppvObj">El resultado que devuelve este método contiene una referencia a la interfaz solicitada por <paramref name="riid" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.CommonPrefixWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Crea un nuevo moniker basado en el prefijo común que el moniker comparte con otro moniker.</summary>
      <param name="pmkOther">Referencia a la interfaz IMoniker en otro moniker para comparar la presencia de algún prefijo común en el moniker actual. </param>
      <param name="ppmkPrefix">El resultado que devuelve este método contiene el moniker que es prefijo común del moniker actual y <paramref name="pmkOther" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ComposeWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Boolean,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Combina el moniker actual con otro moniker, creando un nuevo moniker compuesto.</summary>
      <param name="pmkRight">Referencia a la interfaz IMoniker en un moniker para anexar al final del moniker actual. </param>
      <param name="fOnlyIfNotGeneric">true para indicar que el llamador requiere una composición no genérica.La operación continúa solo si <paramref name="pmkRight" /> es una clase moniker con la que se puede combinar el moniker actual de alguna forma que no sea la de crear un compuesto genérico.false para indicar que el método puede crear un compuesto genérico si es necesario.</param>
      <param name="ppmkComposite">El resultado que devuelve este método contiene una referencia al moniker compuesto resultante.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Enum(System.Boolean,System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Proporciona un puntero a un enumerador que puede enumerar los componentes de un moniker compuesto.</summary>
      <param name="fForward">true para enumerar los monikers de izquierda a derecha.false para enumerarlos de derecha a izquierda.</param>
      <param name="ppenumMoniker">El resultado que devuelve este método contiene una referencia al objeto enumerador del moniker.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetClassID(System.Guid@)">
      <summary>Recupera el identificador de clase (CLSID) de un objeto.</summary>
      <param name="pClassID">El resultado que devuelve este método contiene el CLSID.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String@)">
      <summary>Obtiene el nombre para mostrar, que es una representación del moniker actual que puede leer el usuario.</summary>
      <param name="pbc">Referencia al contexto de enlace que se utiliza en esta operación. </param>
      <param name="pmkToLeft">Referencia al moniker a la izquierda del actual, si es que forma parte de un moniker compuesto. </param>
      <param name="ppszDisplayName">El resultado que devuelve este método contiene la cadena del nombre para mostrar.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetSizeMax(System.Int64@)">
      <summary>Devuelve el tamaño en bytes de la secuencia necesaria para guardar el objeto.</summary>
      <param name="pcbSize">El resultado que devuelve este método contiene un valor long que indica el tamaño en bytes de la secuencia necesaria para guardar el objeto.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Proporciona un número que representa la hora en que se modificó por última vez el objeto identificado por el moniker actual.</summary>
      <param name="pbc">Referencia al contexto que se utiliza en esta operación de enlace. </param>
      <param name="pmkToLeft">Referencia al moniker a la izquierda del actual, si es que forma parte de un moniker compuesto. </param>
      <param name="pFileTime">El resultado que devuelve este método contiene la hora de la última modificación.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Hash(System.Int32@)">
      <summary>Calcula un entero de 32 bits utilizando el estado interno del moniker.</summary>
      <param name="pdwHash">El resultado que devuelve este método contiene el valor hash de este moniker.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Inverse(System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Proporciona un moniker que, al unirse a la derecha del actual o de uno de estructura similar, no crea un moniker compuesto.</summary>
      <param name="ppmk">El resultado que devuelve este método contiene un moniker que es el inverso del actual.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsDirty">
      <summary>Comprueba si el objeto ha sufrido cambios desde la última vez que se guardó.</summary>
      <returns>Devuelve un valor S_OKHRESULT si el objeto ha cambiado; de lo contrario, devuelve un valor S_FALSEHRESULT.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsEqual(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Compara el moniker actual con otro especificado e indica si son iguales.</summary>
      <returns>Devuelve un valor S_OKHRESULT si los monikers son idénticos; de lo contrario, devuelve un valor S_FALSEHRESULT.  </returns>
      <param name="pmkOtherMoniker">Referencia al moniker utilizado para la comparación. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsRunning(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Determina si el objeto identificado por este moniker está actualmente cargado y en ejecución.</summary>
      <returns>Devuelve un valor S_OKHRESULT si el moniker se está ejecutando; devuelve un valor S_FALSEHRESULT si el moniker no se está ejecutando; o bien, devuelve un valor E_UNEXPECTEDHRESULT.</returns>
      <param name="pbc">Referencia al contexto que se utiliza en esta operación de enlace. </param>
      <param name="pmkToLeft">Referencia al moniker a la izquierda del actual, si es que éste forma parte de un moniker compuesto. </param>
      <param name="pmkNewlyRunning">Referencia al último moniker agregado a la tabla de objetos en ejecución (ROT). </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsSystemMoniker(System.Int32@)">
      <summary>Indica si este moniker es de una de las clases moniker suministradas por el sistema.</summary>
      <returns>Devuelve un valor S_OKHRESULT si el moniker es del sistema; de lo contrario, devuelve un valor S_FALSEHRESULT.</returns>
      <param name="pdwMksys">El resultado que devuelve este método contiene un puntero a un entero que es uno de los valores de la enumeración MKSYS y hace referencia a una de las clases moniker COM.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Load(System.Runtime.InteropServices.ComTypes.IStream)">
      <summary>Inicializa un objeto desde la secuencia donde se guardó previamente.</summary>
      <param name="pStm">Secuencia desde donde se carga el objeto. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Lee todos los caracteres del nombre para mostrar que es capaz de entender <see cref="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)" /> y crea un moniker correspondiente a la parte leída.</summary>
      <param name="pbc">Referencia al contexto que se utiliza en esta operación de enlace. </param>
      <param name="pmkToLeft">Referencia al moniker creado hasta este punto a partir del nombre para mostrar. </param>
      <param name="pszDisplayName">Referencia a la cadena que contiene el resto del nombre de presentación que se va a analizar. </param>
      <param name="pchEaten">El resultado que devuelve este método contiene el número de caracteres consumidos al analizar <paramref name="pszDisplayName" />.Este parámetro se pasa sin inicializar.</param>
      <param name="ppmkOut">El resultado que devuelve este método contiene una referencia al moniker creado a partir de <paramref name="pszDisplayName" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Reduce(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Devuelve un moniker reducido, es decir, un moniker que, haciendo referencia al mismo objeto que el moniker actual, puede enlazarse con igual o más eficiencia.</summary>
      <param name="pbc">Referencia a la interfaz IBindCtx en el objeto de contexto que se ha de utilizar en esta operación de enlace. </param>
      <param name="dwReduceHowFar">Valor que especifica en qué medida ha de reducirse el moniker actual. </param>
      <param name="ppmkToLeft">Referencia al moniker a la izquierda del actual. </param>
      <param name="ppmkReduced">El resultado que devuelve este método contiene una referencia a la forma reducida del moniker actual; ésta podría ser null en caso de que se produzca un error o de que el moniker quede reducido a nada.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.RelativePathTo(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Proporciona un moniker que, al anexarse al actual (o a uno de estructura similar), ofrece el moniker especificado.</summary>
      <param name="pmkOther">Referencia al moniker del que se debería obtener la ruta relativa. </param>
      <param name="ppmkRelPath">El resultado que devuelve este método contiene una referencia al moniker relativo.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Save(System.Runtime.InteropServices.ComTypes.IStream,System.Boolean)">
      <summary>Guarda un objeto en la secuencia especificada.</summary>
      <param name="pStm">Secuencia en la que se guarda el objeto. </param>
      <param name="fClearDirty">true para borrar la marca modificada una vez guardado el objeto; de lo contrario, false</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS">
      <summary>Define los atributos de una interfaz implementada o heredada de un tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULT">
      <summary>La interfaz o la interfaz dispinterface representa el valor predeterminado para el origen o el receptor.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULTVTABLE">
      <summary>Los receptores reciben eventos a través de la tabla de función virtual (VTBL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FRESTRICTED">
      <summary>Los usuarios no deben mostrar ni programar los miembros.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FSOURCE">
      <summary>Este miembro de una coclase es llamado y no implementado.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND">
      <summary>Especifica cómo invocar una función mediante IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_FUNC">
      <summary>Se llama al miembro mediante una sintaxis normal de llamada a una función.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYGET">
      <summary>Se invoca la función mediante una sintaxis normal de acceso a la propiedad.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUT">
      <summary>Se invoca la función mediante una sintaxis de asignación de valor de propiedad.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUTREF">
      <summary>Se invoca la función mediante una sintaxis de asignación de referencia de propiedad.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IPersistFile">
      <summary>Proporciona la definición administrada de la interfaz IPersistFile, con funcionalidad desde IPersist.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetClassID(System.Guid@)">
      <summary>Recupera el identificador de clase (CLSID) de un objeto.</summary>
      <param name="pClassID">El resultado que devuelve este método contiene una referencia al CLSID.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetCurFile(System.String@)">
      <summary>Recupera la ruta absoluta del archivo de trabajo actual del objeto o, si no existe ningún archivo de trabajo, la extensión del nombre de archivo predeterminado del objeto.</summary>
      <param name="ppszFileName">El resultado que devuelve este método contiene la dirección de un puntero a una cadena terminada en cero con la ruta del archivo actual o la extensión del nombre de archivo predeterminado (por ejemplo, *.txt).Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.IsDirty">
      <summary>Comprueba si un objeto se ha modificado desde la última vez que se guardó en el archivo actual.</summary>
      <returns>S_OK si el archivo se ha modificado desde que se guardó por última vez; S_FALSE en caso contrario.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Load(System.String,System.Int32)">
      <summary>Abre el archivo especificado e inicializa un objeto del contenido del archivo.</summary>
      <param name="pszFileName">Cadena terminada en cero que contiene la ruta de acceso absoluta al archivo que se va a abrir. </param>
      <param name="dwMode">Combinación de valores de la enumeración STGM para indicar el modo de acceso para abrir <paramref name="pszFileName" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Save(System.String,System.Boolean)">
      <summary>Guarda una copia del objeto en el archivo especificado.</summary>
      <param name="pszFileName">Cadena terminada en cero que contiene la ruta de acceso absoluta del archivo en el que se va a guardar el objeto. </param>
      <param name="fRemember">Es true para utilizar el parámetro <paramref name="pszFileName" /> como el archivo de trabajo actual; de lo contrario es false. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.SaveCompleted(System.String)">
      <summary>Notifica al objeto que puede escribir en el archivo.</summary>
      <param name="pszFileName">Ruta de acceso absoluta del archivo donde se guardó anteriormente el objeto. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IRunningObjectTable">
      <summary>Proporciona la definición administrada de la interfaz IRunningObjectTable.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.EnumRunning(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Enumera los objetos actualmente registrados como en ejecución.</summary>
      <param name="ppenumMoniker">El resultado que devuelve este método contiene el nuevo enumerador de la Tabla de objetos en ejecución (ROT).Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetObject(System.Runtime.InteropServices.ComTypes.IMoniker,System.Object@)">
      <summary>Devuelve el objeto registrado si el nombre de objeto suministrado está registrado como en ejecución.</summary>
      <returns>Valor HRESULT que indica si la operación se ha realizado correctamente o con errores. </returns>
      <param name="pmkObjectName">Referencia al moniker que se va a buscar en la Tabla de objetos en ejecución (ROT). </param>
      <param name="ppunkObject">El resultado que devuelve este método contiene el objeto en ejecución solicitado.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Busca este moniker en la Tabla de objetos en ejecución (ROT) y notifica la hora de modificación registrada, si existe.</summary>
      <returns>Valor HRESULT que indica si la operación se ha realizado correctamente o con errores.</returns>
      <param name="pmkObjectName">Referencia al moniker que se va a buscar en la Tabla de objetos en ejecución (ROT). </param>
      <param name="pfiletime">El resultado que devuelve este objeto contiene la hora del último cambio aplicado a los objetos.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.IsRunning(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Determina si el moniker especificado está actualmente registrado en la Tabla de objetos en ejecución (ROT).</summary>
      <returns>Valor HRESULT que indica si la operación se ha realizado correctamente o con errores.</returns>
      <param name="pmkObjectName">Referencia al moniker que se va a buscar en la Tabla de objetos en ejecución (ROT). </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Anota la hora a la que se modificó un objeto determinado de modo que IMoniker::GetTimeOfLastChange pueda informar de la hora de modificación correcta.</summary>
      <param name="dwRegister">Entrada de la Tabla de objetos en ejecución (ROT) correspondiente al objeto modificado. </param>
      <param name="pfiletime">Referencia a la hora en que se modificó el objeto por última vez. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Register(System.Int32,System.Object,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Registra que el objeto suministrado ha entrado en estado de ejecución.</summary>
      <returns>Valor que se puede usar para identificar esta entrada de la tabla ROT en llamadas posteriores a <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)" /> o <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)" />.</returns>
      <param name="grfFlags">Especifica si la referencia de la Tabla de objetos en ejecución (ROT) a <paramref name="punkObject" /> es fuerte o débil, y controla el acceso al objeto mediante su entrada en la tabla ROT. </param>
      <param name="punkObject">Referencia al objeto que se va a registrar como en ejecución. </param>
      <param name="pmkObjectName">Referencia al moniker que identifica <paramref name="punkObject" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)">
      <summary>Elimina del registro el objeto especificado en la Tabla de objetos en ejecución (ROT).</summary>
      <param name="dwRegister">Entrada de la Tabla de objetos en ejecución (ROT) que se va a revocar. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IStream">
      <summary>Proporciona la definición administrada de la interfaz IStream, con funcionalidad ISequentialStream.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
      <summary>Crea un nuevo objeto de secuencia con su propio puntero de búsqueda que hace referencia a los mismos bytes que la secuencia original.</summary>
      <param name="ppstm">El resultado que devuelve este método contiene el nuevo objeto de secuencia.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)">
      <summary>Garantiza que los cambios realizados en un objeto de secuencia abierto en modo de transacción se reflejen en el almacenamiento principal.</summary>
      <param name="grfCommitFlags">Controla la forma en que se confirman los cambios realizados en un objeto de secuencia. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
      <summary>Copia un número de bytes especificado desde el puntero de búsqueda actual de la secuencia hasta el puntero de búsqueda actual de otra secuencia.</summary>
      <param name="pstm">Referencia a la secuencia de destino. </param>
      <param name="cb">Número de bytes que se van a copiar desde la secuencia de origen. </param>
      <param name="pcbRead">Contiene el número real de bytes leídos en el origen, si la devolución es correcta. </param>
      <param name="pcbWritten">Contiene el número real de bytes escritos en el destino, si la devolución es correcta. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>Restringe el acceso a un intervalo de bytes especificado de la secuencia.</summary>
      <param name="libOffset">Desplazamiento de bytes al comienzo del intervalo. </param>
      <param name="cb">Longitud, en bytes, del intervalo que se va a restringir. </param>
      <param name="dwLockType">Restricciones solicitadas al tener acceso al intervalo. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Read(System.Byte[],System.Int32,System.IntPtr)">
      <summary>Lee un número específico de bytes del objeto de secuencia en memoria, comenzando por el puntero de búsqueda actual.</summary>
      <param name="pv">El resultado que devuelve este método contiene la lectura de los datos de la secuencia.Este parámetro se pasa sin inicializar.</param>
      <param name="cb">Número de bytes que se van a leer en el objeto de secuencia. </param>
      <param name="pcbRead">Puntero a una variable ULONG que recibe el número real de bytes leídos en el objeto de secuencia. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Revert">
      <summary>Descarta todos los cambios realizados en una secuencia de transacción desde la última llamada a <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Seek(System.Int64,System.Int32,System.IntPtr)">
      <summary>Cambia el puntero de búsqueda a una nueva ubicación relativa al principio de la secuencia, al final de la secuencia o al puntero de búsqueda actual.</summary>
      <param name="dlibMove">Desplazamiento que se va a agregar a <paramref name="dwOrigin" />. </param>
      <param name="dwOrigin">Especifica el origen de la búsqueda.El origen puede ser el principio del archivo, el puntero de búsqueda actual o el final del archivo.</param>
      <param name="plibNewPosition">Contiene el desplazamiento del puntero de búsqueda desde el principio de la secuencia, si la devolución es correcta. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.SetSize(System.Int64)">
      <summary>Cambia el tamaño del objeto de secuencia.</summary>
      <param name="libNewSize">Especifica el nuevo tamaño de la secuencia como un número de bytes. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
      <summary>Recupera la estructura <see cref="T:System.Runtime.InteropServices.STATSTG" /> de esta secuencia.</summary>
      <param name="pstatstg">El resultado que devuelve este método contiene una estructura STATSTG que describe este objeto de secuencia.Este parámetro se pasa sin inicializar.</param>
      <param name="grfStatFlag">Miembros de la estructura STATSTG que no devuelve este método, de modo que se ahorran algunas operaciones de asignación de memoria. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.UnlockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>Quita la restricción de acceso de un intervalo de bytes previamente restringido con el método <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)" />.</summary>
      <param name="libOffset">Desplazamiento de bytes al comienzo del intervalo. </param>
      <param name="cb">Longitud, en bytes, del intervalo que se va a restringir. </param>
      <param name="dwLockType">Restricciones de acceso que existían previamente en el intervalo. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Write(System.Byte[],System.Int32,System.IntPtr)">
      <summary>Escribe un número especificado de bytes en el objeto de secuencia, comenzando por el puntero de búsqueda actual.</summary>
      <param name="pv">Búfer donde se va a escribir esta secuencia. </param>
      <param name="cb">Número de bytes que se van a escribir en la secuencia. </param>
      <param name="pcbWritten">Contiene el número real de bytes escritos en el objeto de secuencia, si la devolución es correcta.Si el llamador establece este puntero en <see cref="F:System.IntPtr.Zero" />, este método no proporciona el número real de bytes escritos.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeComp">
      <summary>Proporciona la definición administrada de la interfaz ITypeComp.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.Bind(System.String,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.DESCKIND@,System.Runtime.InteropServices.ComTypes.BINDPTR@)">
      <summary>Asigna un nombre a un miembro de un tipo o enlaza variables y funciones globales de una biblioteca de tipos.</summary>
      <param name="szName">Nombre que se va a enlazar. </param>
      <param name="lHashVal">Valor hash para <paramref name="szName" /> calculado por LHashValOfNameSys. </param>
      <param name="wFlags">Palabra de marcadores que contiene uno o varios de los marcadores de llamada definidos en la enumeración INVOKEKIND. </param>
      <param name="ppTInfo">El resultado que devuelve este método contiene una referencia a la descripción del tipo que contiene el elemento al que está enlazado, si el valor devuelto es FUNCDESC o VARDESC.Este parámetro se pasa sin inicializar.</param>
      <param name="pDescKind">El resultado que devuelve este método contiene una referencia a un enumerador DESCKIND que indica si el nombre enlazado es VARDESC, FUNCDESC o TYPECOMP.Este parámetro se pasa sin inicializar.</param>
      <param name="pBindPtr">El resultado que devuelve este método contiene una referencia a VARDESC o FUNCDESC, a los que está enlazado, o a la interfaz ITypeComp.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.BindType(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Enlaza con las descripciones de tipo que se encuentran dentro de una biblioteca de tipos.</summary>
      <param name="szName">Nombre que se va a enlazar. </param>
      <param name="lHashVal">Valor hash para <paramref name="szName" /> determinado por LHashValOfNameSys. </param>
      <param name="ppTInfo">El resultado que devuelve este método contiene una referencia a ITypeInfo del tipo al que se enlazó <paramref name="szName" />.Este parámetro se pasa sin inicializar.</param>
      <param name="ppTComp">El resultado que devuelve este método contiene una referencia a una variable ITypeComp.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo">
      <summary>Proporciona la definición administrada de la interfaz ITypeInfo de Automatización de componentes.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>Recupera las direcciones de variables o funciones estáticas, como las que se definen en un archivo DLL.</summary>
      <param name="memid">Identificador de miembro de la dirección del miembro static que se va a recuperar. </param>
      <param name="invKind">Uno de los valores <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> que especifica si el miembro es una propiedad, y en tal caso, de qué tipo. </param>
      <param name="ppv">El resultado que devuelve este método contiene una referencia al miembro static.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>Crea una nueva instancia de un tipo que describe una clase de componentes (coclase).</summary>
      <param name="pUnkOuter">Objeto que actúa como IUnknown de control. </param>
      <param name="riid">IID de la interfaz que el llamador utiliza para comunicarse con el objeto resultante. </param>
      <param name="ppvObj">El resultado que devuelve este método contiene una referencia al objeto creado.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>Recupera la biblioteca de tipos que contiene esta descripción de tipos y su índice en dicha biblioteca.</summary>
      <param name="ppTLB">El resultado que devuelve este método incluye una referencia a la biblioteca de tipos contenedora.Este parámetro se pasa sin inicializar.</param>
      <param name="pIndex">El resultado que devuelve este método incluye una referencia al índice de la descripción de tipos de la biblioteca de tipos contenedora.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>Recupera una descripción o especificación de un punto de entrada para una función de un archivo DLL.</summary>
      <param name="memid">Identificador de la función miembro cuya descripción de entrada de archivo DLL se va a devolver. </param>
      <param name="invKind">Uno de los valores <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> que especifica el tipo de miembro identificado por <paramref name="memid" />. </param>
      <param name="pBstrDllName">Si no es null, la función establece <paramref name="pBstrDllName" /> en un BSTR que contiene el nombre del archivo DLL. </param>
      <param name="pBstrName">Si no es null, la función establece <paramref name="lpbstrName" /> en un BSTR que contiene el nombre del punto de entrada. </param>
      <param name="pwOrdinal">Si no es null y la función está definida por un ordinal, <paramref name="lpwOrdinal" /> se establece de forma que apunte al ordinal. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la cadena de documentación, el nombre y la ruta de acceso completos del archivo de Ayuda y el identificador de contexto del tema de Ayuda de una descripción de tipos especificada.</summary>
      <param name="index">Identificador del miembro cuya documentación se va a devolver. </param>
      <param name="strName">El resultado que devuelve este método contiene el nombre del método del elemento.Este parámetro se pasa sin inicializar.</param>
      <param name="strDocString">El resultado que devuelve este método contiene la cadena de documentación para el elemento especificado.Este parámetro se pasa sin inicializar.</param>
      <param name="dwHelpContext">El resultado que devuelve este método incluye una referencia al contexto de Ayuda asociado al elemento especificado.Este parámetro se pasa sin inicializar.</param>
      <param name="strHelpFile">El resultado que devuelve este método contiene el nombre completo del archivo de Ayuda.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>Recupera la estructura <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> que contiene información acerca de una función especificada.</summary>
      <param name="index">Índice de la descripción de función que se va a devolver. </param>
      <param name="ppFuncDesc">El resultado que devuelve este método contiene una referencia a la estructura FUNCDESC que describe la función especificada.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>Realiza asignaciones entre nombres e identificadores de miembro, y nombres e identificadores de parámetros.</summary>
      <param name="rgszNames">Matriz de nombres que se van a asignar. </param>
      <param name="cNames">Número de nombres que se van a asignar. </param>
      <param name="pMemId">El resultado que devuelve este método contiene una referencia a la matriz en la que se encuentran las asignaciones de nombre.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>Recupera el valor de <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> para una interfaz implementada o interfaz base en una descripción de tipos.</summary>
      <param name="index">Índice de la interfaz implementada o interfaz base. </param>
      <param name="pImplTypeFlags">El resultado que devuelve este método contiene una referencia a la enumeración IMPLTYPEFLAGS.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetMops(System.Int32,System.String@)">
      <summary>Recupera información sobre el cálculo de referencias.</summary>
      <param name="memid">Identificador de miembro que indica la información sobre cálculo de referencias que es necesaria. </param>
      <param name="pBstrMops">El resultado que devuelve este método contiene una referencia a la cadena opcode utilizada para calcular las referencias de los campos de la estructura descrita por la descripción del tipo de referencia, o bien es null si no hay ninguna información que devolver.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>Recupera la variable con el identificador de miembro especificado (o el nombre de la propiedad o método y sus parámetros) que se corresponde con el identificador de función especificado.</summary>
      <param name="memid">Identificador del miembro cuyo nombre o nombres se van a devolver. </param>
      <param name="rgBstrNames">El resultado que devuelve este método contiene el nombre o nombres asociados al miembro.Este parámetro se pasa sin inicializar.</param>
      <param name="cMaxNames">Longitud de la matriz <paramref name="rgBstrNames" />. </param>
      <param name="pcNames">El resultado que devuelve este método contiene el número de nombres que hay en la matriz <paramref name="rgBstrNames" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera las descripciones del tipo de referencia si una descripción de tipo hace referencia a otras descripciones de tipo.</summary>
      <param name="hRef">Identificador de la descripción del tipo de referencia que se va a devolver. </param>
      <param name="ppTI">El resultado que devuelve este método contiene la descripción del tipo de referencia.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>Recupera la descripción de tipo de los tipos de interfaz implementados si una descripción de tipo describe una clase COM.</summary>
      <param name="index">Índice del tipo implementado cuyo identificador se devuelve. </param>
      <param name="href">El resultado que devuelve este método contiene una referencia a un identificador para la interfaz implementada.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)">
      <summary>Recupera una estructura <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> que contiene los atributos de la descripción de tipo.</summary>
      <param name="ppTypeAttr">El resultado que devuelve este método incluye una referencia a la estructura que contiene los atributos de esta descripción de tipo.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Recupera la interfaz ITypeComp de la descripción de tipo, que permite a un compilador cliente enlazarse a los miembros de la descripción de tipo.</summary>
      <param name="ppTComp">El resultado que devuelve este método incluye una referencia a la interfaz ITypeComp de la biblioteca de tipos contenedora.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>Recupera una estructura VARDESC que describe la variable especificada.</summary>
      <param name="index">Índice de la descripción de variable que se va a devolver. </param>
      <param name="ppVarDesc">El resultado que devuelve este método contiene una referencia a la estructura VARDESC que describe la variable especificada.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>Invoca a un método u obtiene acceso a una propiedad de un objeto, que implementa la interfaz descrita por la descripción de tipo.</summary>
      <param name="pvInstance">Referencia a la interfaz descrita por esta descripción de tipo. </param>
      <param name="memid">Valor que identifica el miembro de interfaz. </param>
      <param name="wFlags">Marcadores que describen el contexto de la llamada de invocación. </param>
      <param name="pDispParams">Referencia a una estructura que contiene una matriz de argumentos, una matriz de identificadores de envío (DISPID) para argumentos con nombre y recuentos del número de elementos de cada matriz. </param>
      <param name="pVarResult">Referencia a la ubicación donde se va a almacenar el resultado.Si <paramref name="wFlags" /> especifica DISPATCH_PROPERTYPUT o DISPATCH_PROPERTYPUTREF, <paramref name="pVarResult" /> se omite.Se establece en null si no se desea ningún resultado.</param>
      <param name="pExcepInfo">Puntero a una estructura de información sobre excepciones, que sólo se rellena si se devuelve DISP_E_EXCEPTION. </param>
      <param name="puArgErr">Si Invoke devuelve DISP_E_TYPEMISMATCH, <paramref name="puArgErr" /> indica el índice en <paramref name="rgvarg" /> del argumento con el tipo incorrecto.Si hay más de un argumento que devuelva un error, <paramref name="puArgErr" /> indica sólo el primer argumento con error.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseFuncDesc(System.IntPtr)">
      <summary>Libera una estructura <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> previamente devuelta por el método <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pFuncDesc">Referencia a la estructura FUNCDESC que se va a liberar. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseTypeAttr(System.IntPtr)">
      <summary>Libera una estructura <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> previamente devuelta por el método <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />.</summary>
      <param name="pTypeAttr">Referencia a la estructura TYPEATTR que se va a liberar. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseVarDesc(System.IntPtr)">
      <summary>Libera una estructura VARDESC previamente devuelta por el método <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pVarDesc">Referencia a la estructura VARDESC que se va a liberar. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo2">
      <summary>Proporciona la definición administrada de la interfaz ITypeInfo2.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>Recupera las direcciones de variables o funciones estáticas, como las que se definen en un archivo DLL.</summary>
      <param name="memid">Identificador de miembro de la dirección del miembro static que se va a recuperar. </param>
      <param name="invKind">Uno de los valores <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> que especifica si el miembro es una propiedad, y en tal caso, de qué tipo. </param>
      <param name="ppv">El resultado que devuelve este método contiene una referencia al miembro static.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>Crea una nueva instancia de un tipo que describe una clase de componentes (coclase).</summary>
      <param name="pUnkOuter">Objeto que actúa como IUnknown de control. </param>
      <param name="riid">IID de la interfaz que el llamador utiliza para comunicarse con el objeto resultante. </param>
      <param name="ppvObj">El resultado que devuelve este método contiene una referencia al objeto creado.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllCustData(System.IntPtr)">
      <summary>Obtiene todos los elementos de datos personalizados de la biblioteca.</summary>
      <param name="pCustData">Puntero a CUSTDATA que contiene todos los elementos de datos personalizados. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllFuncCustData(System.Int32,System.IntPtr)">
      <summary>Obtiene todos los datos personalizados de la función especificada.</summary>
      <param name="index">Índice de la función para la que se obtienen los datos personalizados. </param>
      <param name="pCustData">Puntero a CUSTDATA que contiene todos los elementos de datos personalizados. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllImplTypeCustData(System.Int32,System.IntPtr)">
      <summary>Obtiene todos los datos personalizados del tipo de implementación especificado.</summary>
      <param name="index">Índice del tipo de implementación de los datos personalizados. </param>
      <param name="pCustData">Puntero a CUSTDATA que contiene todos los elementos de datos personalizados. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllParamCustData(System.Int32,System.Int32,System.IntPtr)">
      <summary>Obtiene todos los datos personalizados del parámetro de función especificado.</summary>
      <param name="indexFunc">Índice de la función para la que se obtienen los datos personalizados. </param>
      <param name="indexParam">Índice del parámetro de esta función para el que se obtienen los datos personalizados. </param>
      <param name="pCustData">Puntero a CUSTDATA que contiene todos los elementos de datos personalizados. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllVarCustData(System.Int32,System.IntPtr)">
      <summary>Obtiene la variable de los datos personalizados.</summary>
      <param name="index">Índice de la variable para la que se obtienen los datos personalizados. </param>
      <param name="pCustData">Puntero a CUSTDATA que contiene todos los elementos de datos personalizados. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>Recupera la biblioteca de tipos que contiene esta descripción de tipos y su índice en dicha biblioteca.</summary>
      <param name="ppTLB">El resultado que devuelve este método incluye una referencia a la biblioteca de tipos contenedora.Este parámetro se pasa sin inicializar.</param>
      <param name="pIndex">El resultado que devuelve este método incluye una referencia al índice de la descripción de tipos de la biblioteca de tipos contenedora.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetCustData(System.Guid@,System.Object@)">
      <summary>Obtiene los datos personalizados.</summary>
      <param name="guid">GUID utilizado para identificar los datos. </param>
      <param name="pVarVal">Cuando este método devuelve un resultado, contiene un Object que especifica dónde se colocan los datos recuperados.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>Recupera una descripción o especificación de un punto de entrada para una función de un archivo DLL.</summary>
      <param name="memid">Identificador de la función miembro cuya descripción de entrada de archivo DLL se va a devolver. </param>
      <param name="invKind">Uno de los valores <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> que especifica el tipo de miembro identificado por <paramref name="memid" />. </param>
      <param name="pBstrDllName">Si no es null, la función establece <paramref name="pBstrDllName" /> en un BSTR que contiene el nombre del archivo DLL. </param>
      <param name="pBstrName">Si no es null, la función establece <paramref name="lpbstrName" /> en un BSTR que contiene el nombre del punto de entrada. </param>
      <param name="pwOrdinal">Si no es null y la función está definida por un ordinal, <paramref name="lpwOrdinal" /> se establece de forma que apunte al ordinal. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la cadena de documentación, el nombre y la ruta de acceso completos del archivo de Ayuda y el identificador de contexto del tema de Ayuda de una descripción de tipos especificada.</summary>
      <param name="index">Identificador del miembro cuya documentación se va a devolver. </param>
      <param name="strName">El resultado que devuelve este método contiene el nombre del método del elemento.Este parámetro se pasa sin inicializar.</param>
      <param name="strDocString">El resultado que devuelve este método contiene la cadena de documentación para el elemento especificado.Este parámetro se pasa sin inicializar.</param>
      <param name="dwHelpContext">El resultado que devuelve este método incluye una referencia al contexto de Ayuda asociado al elemento especificado.Este parámetro se pasa sin inicializar.</param>
      <param name="strHelpFile">El resultado que devuelve este método contiene el nombre completo del archivo de Ayuda.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la cadena de documentación, el nombre completo del archivo de Ayuda con su ruta de acceso, el contexto de localización que se utiliza y el identificador de contexto del tema de Ayuda de la biblioteca en el archivo de Ayuda.</summary>
      <param name="memid">Identificador de miembro de la descripción de tipo. </param>
      <param name="pbstrHelpString">Cuando este método devuelve un resultado, contiene un BSTR que incluye el nombre del elemento especificado.Si el llamador no necesita el nombre del elemento, el valor de <paramref name="pbstrHelpString" /> puede ser null.Este parámetro se pasa sin inicializar.</param>
      <param name="pdwHelpStringContext">El resultado que devuelve este método contiene el contexto de localización de la Ayuda.Si el llamador no necesita el contexto de la Ayuda, el valor de <paramref name="pdwHelpStringContext" /> puede ser null.Este parámetro se pasa sin inicializar.</param>
      <param name="pbstrHelpStringDll">Cuando este método devuelve un resultado, contiene un BSTR que incluye el nombre completo del archivo que contiene la biblioteca DLL utilizado para el archivo de Ayuda.Si el llamador no necesita el nombre del archivo, el valor de <paramref name="pbstrHelpStringDll" /> puede ser null.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Obtiene los datos personalizados de la función especificada.</summary>
      <param name="index">Índice de la función para la que se obtienen los datos personalizados. </param>
      <param name="guid">GUID utilizado para identificar los datos. </param>
      <param name="pVarVal">Cuando este método finaliza, contiene un Object que especifica dónde se colocan los datos.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>Recupera la estructura <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> que contiene información acerca de una función especificada.</summary>
      <param name="index">Índice de la descripción de función que se va a devolver. </param>
      <param name="ppFuncDesc">El resultado que devuelve este método contiene una referencia a la estructura FUNCDESC que describe la función especificada.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncIndexOfMemId(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.Int32@)">
      <summary>Enlaza a un miembro concreto basado en un DISPID conocido, donde el nombre de miembro no se conoce (por ejemplo, al enlazar a un miembro predeterminado).</summary>
      <param name="memid">Identificador de miembro. </param>
      <param name="invKind">Uno de los valores de <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> que especifica el tipo de miembro identificado por memid.</param>
      <param name="pFuncIndex">Cuando este método devuelve un resultado, contiene un índice en la función.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>Realiza asignaciones entre nombres e identificadores de miembro, y nombres e identificadores de parámetros.</summary>
      <param name="rgszNames">Matriz de nombres que se van a asignar. </param>
      <param name="cNames">Número de nombres que se van a asignar. </param>
      <param name="pMemId">El resultado que devuelve este método contiene una referencia a la matriz en la que se encuentran las asignaciones de nombre.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Obtiene el tipo de implementación de los datos personalizados.</summary>
      <param name="index">Índice del tipo de implementación de los datos personalizados. </param>
      <param name="guid">GUID utilizado para identificar los datos. </param>
      <param name="pVarVal">Cuando este método devuelve un resultado, contiene un Object que especifica dónde se colocan los datos recuperados.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>Recupera el valor de <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> para una interfaz implementada o interfaz base en una descripción de tipos.</summary>
      <param name="index">Índice de la interfaz implementada o interfaz base. </param>
      <param name="pImplTypeFlags">El resultado que devuelve este método contiene una referencia a la enumeración IMPLTYPEFLAGS.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetMops(System.Int32,System.String@)">
      <summary>Recupera información sobre el cálculo de referencias.</summary>
      <param name="memid">Identificador de miembro que indica la información sobre cálculo de referencias que es necesaria. </param>
      <param name="pBstrMops">El resultado que devuelve este método contiene una referencia a la cadena opcode utilizada para calcular las referencias de los campos de la estructura descrita por la descripción del tipo de referencia, o bien es null si no hay ninguna información que devolver.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>Recupera la variable con el identificador de miembro especificado (o el nombre de la propiedad o método y sus parámetros) que se corresponde con el identificador de función especificado.</summary>
      <param name="memid">Identificador del miembro cuyo nombre o nombres se van a devolver. </param>
      <param name="rgBstrNames">El resultado que devuelve este método contiene el nombre o nombres asociados al miembro.Este parámetro se pasa sin inicializar.</param>
      <param name="cMaxNames">Longitud de la matriz <paramref name="rgBstrNames" />. </param>
      <param name="pcNames">El resultado que devuelve este método contiene el número de nombres que hay en la matriz <paramref name="rgBstrNames" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetParamCustData(System.Int32,System.Int32,System.Guid@,System.Object@)">
      <summary>Obtiene el parámetro de datos personalizados especificado.</summary>
      <param name="indexFunc">Índice de la función para la que se obtienen los datos personalizados. </param>
      <param name="indexParam">Índice del parámetro de esta función para el que se obtienen los datos personalizados. </param>
      <param name="guid">GUID utilizado para identificar los datos. </param>
      <param name="pVarVal">Cuando este método devuelve un resultado, contiene un Object que especifica dónde se colocan los datos recuperados.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera las descripciones de tipo a las que se hace referencia si una descripción de tipo hace referencia a otras descripciones de tipo.</summary>
      <param name="hRef">Identificador de la descripción del tipo de referencia que se va a devolver. </param>
      <param name="ppTI">El resultado que devuelve este método contiene la descripción del tipo de referencia.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>Recupera la descripción de tipo de los tipos de interfaz implementados si una descripción de tipo describe una clase COM.</summary>
      <param name="index">Índice del tipo implementado cuyo identificador se devuelve. </param>
      <param name="href">El resultado que devuelve este método contiene una referencia a un identificador para la interfaz implementada.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeAttr(System.IntPtr@)">
      <summary>Recupera una estructura <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> que contiene los atributos de la descripción de tipo.</summary>
      <param name="ppTypeAttr">El resultado que devuelve este método incluye una referencia a la estructura que contiene los atributos de esta descripción de tipo.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Recupera la interfaz ITypeComp de la descripción de tipo, que permite a un compilador cliente enlazarse a los miembros de la descripción de tipo.</summary>
      <param name="ppTComp">Cuando este método devuelve un resultado, contiene una referencia a la interfaz ITypeComp de la biblioteca de tipos contenedora.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeFlags(System.Int32@)">
      <summary>Devuelve los marcadores de tipo sin ninguna asignación.Este método devuelve un marcador de tipo DWORD que expande los marcadores de tipo sin aumentar el TYPEATTR (atributo de tipo).</summary>
      <param name="pTypeFlags">Cuando este método devuelve un resultado, contiene una referencia DWORD a un TYPEFLAG.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeKind(System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Devuelve rápidamente la enumeración TYPEKIND, sin hacer ninguna asignación.</summary>
      <param name="pTypeKind">Cuando este método devuelve un resultado, contiene una referencia a una enumeración TYPEKIND.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Obtiene la variable de los datos personalizados.</summary>
      <param name="index">Índice de la variable para la que se obtienen los datos personalizados. </param>
      <param name="guid">GUID utilizado para identificar los datos. </param>
      <param name="pVarVal">Cuando este método devuelve un resultado, contiene un Object que especifica dónde se colocan los datos recuperados.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>Recupera una estructura VARDESC que describe la variable especificada.</summary>
      <param name="index">Índice de la descripción de variable que se va a devolver. </param>
      <param name="ppVarDesc">El resultado que devuelve este método contiene una referencia a la estructura VARDESC que describe la variable especificada.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarIndexOfMemId(System.Int32,System.Int32@)">
      <summary>Enlaza a un miembro concreto basado en un DISPID conocido, donde el nombre de miembro no se conoce (por ejemplo, al enlazar a un miembro predeterminado).</summary>
      <param name="memid">Identificador de miembro. </param>
      <param name="pVarIndex">Cuando este método devuelve un resultado, contiene un índice de <paramref name="memid" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>Invoca a un método u obtiene acceso a una propiedad de un objeto, que implementa la interfaz descrita por la descripción de tipo.</summary>
      <param name="pvInstance">Referencia a la interfaz descrita por esta descripción de tipo. </param>
      <param name="memid">Identificador del miembro de interfaz. </param>
      <param name="wFlags">Marcadores que describen el contexto de la llamada Invoke. </param>
      <param name="pDispParams">Referencia a una estructura que contiene una matriz de argumentos, una matriz de identificadores de envío (DISPID) para argumentos con nombre y recuentos del número de elementos de cada matriz. </param>
      <param name="pVarResult">Referencia a la ubicación donde se va a almacenar el resultado.Si <paramref name="wFlags" /> especifica DISPATCH_PROPERTYPUT o DISPATCH_PROPERTYPUTREF, <paramref name="pVarResult" /> se omite.Se establece en null si no se desea ningún resultado.</param>
      <param name="pExcepInfo">Puntero a una estructura de información sobre excepciones, que sólo se rellena si se devuelve DISP_E_EXCEPTION. </param>
      <param name="puArgErr">Si Invoke devuelve DISP_E_TYPEMISMATCH, <paramref name="puArgErr" />, indica el índice del argumento con el tipo incorrecto.Si hay más de un argumento que devuelva un error, <paramref name="puArgErr" /> indica sólo el primer argumento con error.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseFuncDesc(System.IntPtr)">
      <summary>Libera una estructura <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> previamente devuelta por el método <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pFuncDesc">Referencia a la estructura FUNCDESC que se va a liberar. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseTypeAttr(System.IntPtr)">
      <summary>Libera una estructura <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> previamente devuelta por el método <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />.</summary>
      <param name="pTypeAttr">Referencia a la estructura TYPEATTR que se va a liberar. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseVarDesc(System.IntPtr)">
      <summary>Libera una estructura VARDESC previamente devuelta por el método <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pVarDesc">Referencia a la estructura VARDESC que se va a liberar. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib">
      <summary>Proporciona la definición administrada de la interfaz ITypeLib.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>Busca apariciones de una descripción de tipo en una biblioteca de tipos.</summary>
      <param name="szNameBuf">Nombre que se va a buscar.Este es un parámetro de entrada/salida.</param>
      <param name="lHashVal">Valor hash para acelerar la búsqueda, calculado por la función LHashValOfNameSys.Si <paramref name="lHashVal" /> es 0, se calcula un valor.</param>
      <param name="ppTInfo">El resultado que devuelve este método contiene una matriz de punteros a las descripciones de tipo que contienen el nombre especificado en <paramref name="szNameBuf" />.Este parámetro se pasa sin inicializar.</param>
      <param name="rgMemId">Matriz de estructuras MEMBERID de los elementos encontrados; <paramref name="rgMemId" /> [i] es la estructura MEMBERID que se indiza en la descripción de tipo especificada por <paramref name="ppTInfo" />[i].No puede ser null.</param>
      <param name="pcFound">En la entrada, indica el número de instancias que se van a buscar.Por ejemplo, se puede llamar a <paramref name="pcFound" /> = 1 para que busque la primera aparición.La búsqueda se detiene cuando se encuentra una instancia.En la salida, indica el número de instancias encontradas.Si los valores in y out de <paramref name="pcFound" /> son idénticos, puede haber más descripciones de tipo que contengan el nombre.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la cadena de documentación de la biblioteca, el nombre y la ruta de acceso completos del archivo de Ayuda y el identificador de contexto del tema de Ayuda relativo a la biblioteca en el archivo de Ayuda.</summary>
      <param name="index">Índice de la descripción de tipo cuya documentación se va a devolver. </param>
      <param name="strName">El resultado que devuelve este método contiene una cadena que representa el nombre del elemento especificado.Este parámetro se pasa sin inicializar.</param>
      <param name="strDocString">El resultado que devuelve este método contiene una cadena que representa la cadena de documentación del elemento especificado.Este parámetro se pasa sin inicializar.</param>
      <param name="dwHelpContext">El resultado que devuelve este método contiene el identificador de contexto de la Ayuda asociado al elemento especificado.Este parámetro se pasa sin inicializar.</param>
      <param name="strHelpFile">El resultado que devuelve este método contiene una cadena que representa el nombre completo del archivo de Ayuda.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)">
      <summary>Recupera la estructura que contiene los atributos de la biblioteca.</summary>
      <param name="ppTLibAttr">El resultado que devuelve este método contiene una estructura con los atributos de la biblioteca.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Habilita un compilador cliente para enlazarse a los tipos, variables, constantes y funciones globales de una biblioteca.</summary>
      <param name="ppTComp">El resultado que devuelve este método contiene una instancia de una instancia de ITypeComp para este ITypeLib.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera la descripción de tipo especificada en la biblioteca.</summary>
      <param name="index">Índice de la interfaz ITypeInfo que se ha de devolver. </param>
      <param name="ppTI">El resultado que devuelve este método contiene ITypeInfo, que describe el tipo al que hace referencia <paramref name="index" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoCount">
      <summary>Devuelve el número de descripciones de tipo de la biblioteca de tipos.</summary>
      <returns>Número de descripciones de tipo de la biblioteca de tipos.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera la descripción de tipo que se corresponde con el GUID especificado.</summary>
      <param name="guid">IID de la interfaz o CLSID de la clase cuya información de tipos se solicita. </param>
      <param name="ppTInfo">El resultado que devuelve este método contiene la interfaz ITypeInfo solicitada.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Recupera el tipo de una descripción de tipos.</summary>
      <param name="index">Índice de la descripción de tipo en la biblioteca de tipos. </param>
      <param name="pTKind">El resultado que devuelve este método contiene una referencia a la enumeración TYPEKIND para la descripción de tipo.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.IsName(System.String,System.Int32)">
      <summary>Indica si una cadena que se ha pasado contiene el nombre de un tipo o miembro descrito en la biblioteca.</summary>
      <returns>Será true si se encuentra <paramref name="szNameBuf" /> en la biblioteca de tipos; en caso contrario, será false.</returns>
      <param name="szNameBuf">Cadena que se va a comprobar.Este es un parámetro de entrada/salida.</param>
      <param name="lHashVal">Valor hash de <paramref name="szNameBuf" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.ReleaseTLibAttr(System.IntPtr)">
      <summary>Libera la estructura <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> obtenida originalmente del método <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" />.</summary>
      <param name="pTLibAttr">Estructura TLIBATTR que se ha de liberar. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib2">
      <summary>Proporciona una definición administrada de la interfaz ITypeLib2.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>Busca apariciones de una descripción de tipo en una biblioteca de tipos.</summary>
      <param name="szNameBuf">Nombre que se va a buscar. </param>
      <param name="lHashVal">Valor hash para acelerar la búsqueda, calculado por la función LHashValOfNameSys.Si <paramref name="lHashVal" /> es 0, se calcula un valor.</param>
      <param name="ppTInfo">El resultado que devuelve este método contiene una matriz de punteros a las descripciones de tipo que contienen el nombre especificado en <paramref name="szNameBuf" />.Este parámetro se pasa sin inicializar.</param>
      <param name="rgMemId">Cuando vuelve este método, contiene una matriz de los MEMBERID de los elementos encontrados; <paramref name="rgMemId" />[i] es el MEMBERID que se indiza en la descripción de tipo especificada por <paramref name="ppTInfo" />[i].Este parámetro no puede ser null.Este parámetro se pasa sin inicializar.</param>
      <param name="pcFound">En la entrada, valor, pasado por referencia, que indica el número de instancias que se van a buscar.Por ejemplo, se puede llamar a <paramref name="pcFound" /> = 1 para que busque la primera aparición.La búsqueda se detiene cuando se encuentra una instancia.En la salida, indica el número de instancias encontradas.Si los valores in y out de <paramref name="pcFound" /> son idénticos, puede haber más descripciones de tipo que contengan el nombre.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetAllCustData(System.IntPtr)">
      <summary>Obtiene todos los elementos de datos personalizados de la biblioteca.</summary>
      <param name="pCustData">Puntero a CUSTDATA que contiene todos los elementos de datos personalizados. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetCustData(System.Guid@,System.Object@)">
      <summary>Obtiene los datos personalizados.</summary>
      <param name="guid">
        <see cref="T:System.Guid" />, pasado por referencia, que se utiliza para identificar los datos. </param>
      <param name="pVarVal">El resultado que devuelve este método contiene un objeto que especifica dónde se colocan los datos recuperados.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la cadena de documentación de la biblioteca, el nombre y la ruta de acceso completos del archivo de Ayuda y el identificador de contexto del tema de Ayuda relativo a la biblioteca en el archivo de Ayuda.</summary>
      <param name="index">Índice de la descripción de tipo cuya documentación se va a devolver. </param>
      <param name="strName">El resultado que devuelve este método contiene una cadena que indica el nombre del elemento especificado.Este parámetro se pasa sin inicializar.</param>
      <param name="strDocString">El resultado que devuelve este método contiene la cadena de documentación para el elemento especificado.Este parámetro se pasa sin inicializar.</param>
      <param name="dwHelpContext">El resultado que devuelve este método contiene el identificador de contexto de la Ayuda asociado al elemento especificado.Este parámetro se pasa sin inicializar.</param>
      <param name="strHelpFile">El resultado que devuelve este método contiene una cadena que especifica el nombre completo del archivo de Ayuda.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la cadena de documentación, el nombre completo del archivo de Ayuda con su ruta de acceso, el contexto de localización que se utiliza y el identificador de contexto del tema de Ayuda de la biblioteca en el archivo de Ayuda.</summary>
      <param name="index">Índice de la descripción de tipo cuya documentación se va a devolver; si <paramref name="index" /> es -1, se devuelve la documentación de la biblioteca. </param>
      <param name="pbstrHelpString">El resultado que devuelve este método contiene un BSTR que especifica el nombre del elemento especificado.Si el llamador no necesita el nombre del elemento, el valor de <paramref name="pbstrHelpString" /> puede ser null.Este parámetro se pasa sin inicializar.</param>
      <param name="pdwHelpStringContext">El resultado que devuelve este método contiene el contexto de localización de la Ayuda.Si el llamador no necesita el contexto de la Ayuda, el valor de <paramref name="pdwHelpStringContext" /> puede ser null.Este parámetro se pasa sin inicializar.</param>
      <param name="pbstrHelpStringDll">El resultado que devuelve este método contiene un BSTR que indica el nombre completo del archivo que contiene la biblioteca DLL utilizada para el archivo de Ayuda.Si el llamador no necesita el nombre del archivo, el valor de <paramref name="pbstrHelpStringDll" /> puede ser null.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibAttr(System.IntPtr@)">
      <summary>Recupera la estructura que contiene los atributos de la biblioteca.</summary>
      <param name="ppTLibAttr">El resultado que devuelve este método contiene una estructura con los atributos de la biblioteca.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibStatistics(System.IntPtr,System.Int32@)">
      <summary>Devuelve las estadísticas sobre una biblioteca de tipos que se requieren para cambiar el tamaño de las tablas hash de forma eficaz.</summary>
      <param name="pcUniqueNames">Puntero a un recuento de nombres únicos.Si el llamador no necesita esta información, se establece en null.</param>
      <param name="pcchUniqueNames">El resultado que devuelve este método contiene un puntero a un cambio en el recuento de nombres únicos.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Habilita un compilador cliente para enlazarse a los tipos, variables, constantes y funciones globales de una biblioteca.</summary>
      <param name="ppTComp">El resultado que devuelve este método contiene una instancia de ITypeComp para esta interfaz ITypeLib.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera la descripción de tipo especificada en la biblioteca.</summary>
      <param name="index">Índice de la interfaz ITypeInfo que se ha de devolver. </param>
      <param name="ppTI">El resultado que devuelve este método contiene ITypeInfo, que describe el tipo al que hace referencia <paramref name="index" />.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoCount">
      <summary>Devuelve el número de descripciones de tipo de la biblioteca de tipos.</summary>
      <returns>Número de descripciones de tipo de la biblioteca de tipos.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera la descripción de tipo que se corresponde con el GUID especificado.</summary>
      <param name="guid">
        <see cref="T:System.Guid" />, pasado por referencia, que representa el identificador de la interfaz CLSID de la clase cuya información de tipo se solicita. </param>
      <param name="ppTInfo">El resultado que devuelve este método contiene la interfaz ITypeInfo solicitada.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Recupera el tipo de una descripción de tipos.</summary>
      <param name="index">Índice de la descripción de tipo en la biblioteca de tipos. </param>
      <param name="pTKind">El resultado que devuelve este método contiene una referencia a la enumeración TYPEKIND para la descripción de tipo.Este parámetro se pasa sin inicializar.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.IsName(System.String,System.Int32)">
      <summary>Indica si una cadena que se ha pasado contiene el nombre de un tipo o miembro descrito en la biblioteca.</summary>
      <returns>Será true si se encuentra <paramref name="szNameBuf" /> en la biblioteca de tipos; en caso contrario, será false.</returns>
      <param name="szNameBuf">Cadena que se va a comprobar. </param>
      <param name="lHashVal">Valor hash de <paramref name="szNameBuf" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.ReleaseTLibAttr(System.IntPtr)">
      <summary>Libera la estructura <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> obtenida originalmente del método <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" />.</summary>
      <param name="pTLibAttr">Estructura TLIBATTR que se ha de liberar. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.LIBFLAGS">
      <summary>Define los marcadores que se aplican a las bibliotecas de tipos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FCONTROL">
      <summary>La biblioteca de tipos describe los controles, y no debe mostrarse en exploradores de tipos destinados a objetos no visuales.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHASDISKIMAGE">
      <summary>La biblioteca de tipos permanece almacenada en disco.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHIDDEN">
      <summary>Aunque el uso de la biblioteca de tipos no está restringido, ésta no debe mostrarse a los usuarios.La biblioteca de tipos se debe utilizar con los controles.Los hosts deben crear una nueva biblioteca de tipos que incluya el control con propiedades extendidas.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FRESTRICTED">
      <summary>La biblioteca de tipos está restringida y no debe mostrarse a los usuarios.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMDESC">
      <summary>Contiene la información necesaria para transferir un valor devuelto por una función, un parámetro o un elemento de estructura de un proceso a otro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.lpVarValue">
      <summary>Representa un puntero a un valor que se está pasando de un proceso a otro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.wParamFlags">
      <summary>Representa valores de máscara de bits que describen el elemento de estructura, el parámetro o el valor devuelto.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMFLAG">
      <summary>Describe cómo transferir un elemento de estructura, un parámetro o un valor devuelto por una función de un proceso a otro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASCUSTDATA">
      <summary>El parámetro tiene datos personalizados.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASDEFAULT">
      <summary>El parámetro tiene definidos comportamientos predeterminados.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FIN">
      <summary>El parámetro pasa información del que llama al llamado.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FLCID">
      <summary>El parámetro es el identificador local de una aplicación cliente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOPT">
      <summary>El parámetro es opcional.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOUT">
      <summary>El parámetro devuelve información del llamado al que llama.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FRETVAL">
      <summary>El parámetro es el valor devuelto del miembro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_NONE">
      <summary>No especifica si el parámetro pasa o recibe información.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATDATA">
      <summary>Proporciona la definición administrada de la estructura STATDATA.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advf">
      <summary>Representa el valor de la enumeración <see cref="T:System.Runtime.InteropServices.ComTypes.ADVF" /> que determina el momento en que se debe notificar al receptor de notificaciones los cambios en los datos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advSink">
      <summary>Representa la interfaz <see cref="T:System.Runtime.InteropServices.ComTypes.IAdviseSink" /> que recibirá las notificaciones de los cambios.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.connection">
      <summary>Representa el símbolo (token) que identifica de forma única la conexión de consulta.El método devuelve el símbolo (token) que establece la conexión de consulta.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.formatetc">
      <summary>Representa la estructura <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> de los datos de interés para el receptor de notificaciones.El receptor de notificaciones recibe las notificaciones de los cambios realizados en los datos que especifica esta estructura de <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATSTG">
      <summary>Contiene información estadística acerca de un objeto de almacenamiento abierto, secuencia o matriz de bytes.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.atime">
      <summary>Especifica la hora del último acceso a este almacenamiento, secuencia o matriz de bytes. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.cbSize">
      <summary>Especifica el tamaño, en bytes, de la secuencia o de la matriz de bytes.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.clsid">
      <summary>Indica el identificador de clase del objeto de almacenamiento.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.ctime">
      <summary>Indica la hora de creación de este almacenamiento, secuencia o matriz de bytes.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfLocksSupported">
      <summary>Indica los tipos de bloqueo de región admitidos por la secuencia o matriz de bytes.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfMode">
      <summary>Indica el modo de acceso que se especificó cuando se abrió el objeto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfStateBits">
      <summary>Indica los bits de estado actuales del objeto de almacenamiento (el valor más reciente establecido por el método IStorage::SetStateBits).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.mtime">
      <summary>Indica la hora de la última modificación de este almacenamiento, secuencia o matriz de bytes.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.pwcsName">
      <summary>Representa un puntero a una cadena terminada en null que contiene el nombre del objeto descrito por esta estructura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.reserved">
      <summary>Reservado para un uso futuro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.type">
      <summary>Indica el tipo de objeto de almacenamiento, que es uno de los valores de la enumeración STGTY.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM">
      <summary>Proporciona la definición administrada de la estructura STGMEDIUM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease">
      <summary>Representa un puntero a una instancia de interfaz que permite al proceso de envío controlar la forma en que se libera el almacenamiento cuando el proceso de recepción llama a la función ReleaseStgMedium.Si <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> es null, ReleaseStgMedium utiliza los procedimientos predeterminados para liberar el almacenamiento; de lo contrario, ReleaseStgMedium utiliza la interfaz IUnknown especificada.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.tymed">
      <summary>Especifica el tipo del medio de almacenamiento.Las rutinas de cálculo de referencias y de deserialización utilizan este valor para determinar qué miembro de unión se ha utilizado.Este valor debe ser uno de los elementos de la enumeración <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.unionmember">
      <summary>Representa un identificador, cadena o puntero de interfaz que el proceso de recepción puede utilizar para tener acceso a los datos que se transfieren.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.SYSKIND">
      <summary>Identifica la plataforma del sistema operativo de destino.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_MAC">
      <summary>El sistema operativo de destino de la biblioteca de tipos es Apple Macintosh.De forma predeterminada, todos los campos de datos se alinean en límites de bytes pares.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN16">
      <summary>El sistema operativo de destino de la biblioteca de tipos es Windows de 16 bits.De forma predeterminada, los campos de datos se empaquetan.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN32">
      <summary>El sistema operativo de destino de la biblioteca de tipos es Windows de 32 bits.De forma predeterminada, los campos de datos se alinean de forma natural; por ejemplo, los números enteros de 2 bytes se alinean en límites de bytes pares, los enteros de 4 bytes se alinean en límites de cuatro palabras, etc.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN64">
      <summary>El sistema operativo de destino de la biblioteca de tipos es Windows de 64 bits.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYMED">
      <summary>Proporciona la definición administrada de la estructura TYMED.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ENHMF">
      <summary>El medio de almacenamiento es un metarchivo mejorado.Si el miembro <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> de <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /> es null, el proceso de destino debe utilizar DeleteEnhMetaFile para eliminar el mapa de bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_FILE">
      <summary>El medio de almacenamiento es un archivo de disco identificado mediante una ruta de acceso.Si el miembro <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> de STGMEDIUM es null, el proceso de destino debe utilizar OpenFile para eliminar el archivo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_GDI">
      <summary>El medio de almacenamiento es un componente (HBITMAP) de una interfaz de dispositivo gráfico (GDI).Si el miembro <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> de <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /> es null, el proceso de destino debe utilizar DeleteObject para eliminar el mapa de bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_HGLOBAL">
      <summary>El medio de almacenamiento es un identificador de memoria global (HGLOBAL).Asigne el identificador global con el marcador GMEM_SHARE.Si el miembro <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> de <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /> es null, el proceso de destino debe utilizar GlobalFree para liberar la memoria.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTORAGE">
      <summary>El medio de almacenamiento es un componente de almacenamiento identificado mediante un puntero IStorage.Los datos están en las secuencias y almacenamientos contenidos en esta estancia de IStorage.Si el miembro <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> de <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /> no es null, el proceso de destino debe utilizar IStorage::Release para liberar el componente de almacenamiento.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTREAM">
      <summary>El medio de almacenamiento es un objeto de secuencia identificado mediante un puntero IStream.Utilice ISequentialStream::Read para leer los datos.Si el miembro <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> de <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /> no es null, el proceso de destino debe utilizar IStream::Release para liberar el componente de secuencia.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_MFPICT">
      <summary>El medio de almacenamiento es un metarchivo (HMETAFILE).Utilice las funciones de Windows o WIN32 para obtener acceso a los datos del metarchivo.Si el miembro <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> de <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /> es null, el proceso de destino debe utilizar DeleteMetaFile para eliminar el mapa de bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_NULL">
      <summary>No se pasan datos.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEATTR">
      <summary>Contiene los atributos de UCOMITypeInfo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbAlignment">
      <summary>Especifica la alineación de bytes de una instancia de este tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeInstance">
      <summary>Tamaño de una instancia de este tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeVft">
      <summary>Tamaño de la tabla de métodos virtuales (VTBL) de este tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cFuncs">
      <summary>Indica el número de funciones que hay en la interfaz descrita por esta estructura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cImplTypes">
      <summary>Indica el número de interfaces implementadas que hay en la interfaz descrita por esta estructura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cVars">
      <summary>Indica el número de variables y campos de datos que hay en la interfaz descrita por esta estructura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.dwReserved">
      <summary>Reservado para un uso futuro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.guid">
      <summary>GUID de la información del tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.idldescType">
      <summary>Atributos IDL del tipo descrito.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lcid">
      <summary>Configuración regional de los nombres de miembro y las cadenas de documentación.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lpstrSchema">
      <summary>Reservado para un uso futuro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.MEMBER_ID_NIL">
      <summary>Constante que se utiliza con los campos <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidConstructor" /> y <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidDestructor" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidConstructor">
      <summary>Id. del constructor; o <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" /> si no hay ninguno.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidDestructor">
      <summary>Id. del destructor; o <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" /> si no hay ninguno.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.tdescAlias">
      <summary>Si <see cref="F:System.Runtime.InteropServices.TYPEATTR.typekind" /> == <see cref="F:System.Runtime.InteropServices.TYPEKIND.TKIND_ALIAS" />, especifica el tipo del que este tipo es un alias.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.typekind">
      <summary>Valor de <see cref="T:System.Runtime.InteropServices.TYPEKIND" /> que describe el tipo descrito por esta información.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMajorVerNum">
      <summary>Número de versión principal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMinorVerNum">
      <summary>Número de versión secundaria.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wTypeFlags">
      <summary>Valor de <see cref="T:System.Runtime.InteropServices.TYPEFLAGS" /> descrito por esta información.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEDESC">
      <summary>Describe el tipo de una variable, el tipo de valor devuelto por una función o el tipo de un parámetro de una función.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.lpValue">
      <summary>Si la variable es VT_SAFEARRAY o VT_PTR, el campo lpValue contiene un puntero a TYPEDESC que especifica el tipo de elemento.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.vt">
      <summary>Indica el tipo Variant del elemento descrito por TYPEDESC.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEFLAGS">
      <summary>Define las propiedades y atributos de la descripción de un tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAGGREGATABLE">
      <summary>La clase admite agregación.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAPPOBJECT">
      <summary>Descripción de tipo que describe un objeto Application.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCANCREATE">
      <summary>Se pueden crear instancias del tipo mediante ITypeInfo::CreateInstance.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCONTROL">
      <summary>El tipo es un control a partir del cual se derivarán otros tipos y no se debe mostrar a los usuarios.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDISPATCHABLE">
      <summary>Indica que la interfaz se deriva de IDispatch, ya sea directa o indirectamente.Este marcador se calcula; no hay ningún Lenguaje de descripción de objetos para él.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDUAL">
      <summary>La interfaz proporciona tanto enlace VTBL como IDispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FHIDDEN">
      <summary>El tipo no debe mostrarse a los exploradores.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FLICENSED">
      <summary>El tipo tiene licencia.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FNONEXTENSIBLE">
      <summary>La interfaz no puede agregar miembros en tiempo de ejecución.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FOLEAUTOMATION">
      <summary>Los tipos utilizados en la interfaz son totalmente compatibles con la automatización, incluida la compatibilidad con el enlace VTBL.Al establecer una interfaz como dual, se establecen este marcador y <see cref="F:System.Runtime.InteropServices.TYPEFLAGS.TYPEFLAG_FDUAL" />.No se permite este marcador en interfaces Dispinterface.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPREDECLID">
      <summary>El tipo está predefinido.La aplicación cliente debe crear automáticamente una única instancia del objeto que tenga este atributo.El nombre de la variable que apunta al objeto es el mismo que el nombre de la clase del objeto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPROXY">
      <summary>Indica que la interfaz utilizará un proxy/código auxiliar de biblioteca de vínculos dinámicos.Este marcador especifica que el registro del proxy de la biblioteca de tipos no se anulará al anular el registro de la biblioteca de tipos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREPLACEABLE">
      <summary>El objeto admite IConnectionPointWithDefault, y tiene comportamientos predeterminados.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FRESTRICTED">
      <summary>No se debe permitir el acceso desde lenguajes de macros.El marcador está destinado a tipos en el nivel del sistema o a tipos que los exploradores de tipos no deben mostrar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREVERSEBIND">
      <summary>Establece que se debe comprobar la resolución de nombres en las interfaces base antes que en las secundarias, al contrario de lo que ocurre en el comportamiento predeterminado.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEKIND">
      <summary>Especifica varios tipos de datos y funciones.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ALIAS">
      <summary>Tipo que es un alias de otro tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_COCLASS">
      <summary>Conjunto de interfaces de componentes implementados.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_DISPATCH">
      <summary>Conjunto de métodos y propiedades a los que se puede tener acceso mediante IDispatch::Invoke.De manera predeterminada, las interfaces dobles devuelven TKIND_DISPATCH.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ENUM">
      <summary>Conjunto de enumeradores.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_INTERFACE">
      <summary>Tipo que tiene funciones virtuales, todas puras.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MAX">
      <summary>Marcador de fin de enumeración.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MODULE">
      <summary>Módulo que sólo puede tener funciones y datos estáticos (por ejemplo, un archivo DLL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_RECORD">
      <summary>Estructura sin métodos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_UNION">
      <summary>Unión de todos los miembros que tienen desplazamiento cero.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPELIBATTR">
      <summary>Identifica una biblioteca de tipos concreta y proporciona compatibilidad de localización para los nombres de miembros.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.guid">
      <summary>Representa un identificador de biblioteca único global de una biblioteca de tipos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.lcid">
      <summary>Representa un identificador local de una biblioteca de tipos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.syskind">
      <summary>Representa la plataforma de hardware de destino de una biblioteca de tipos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wLibFlags">
      <summary>Representa marcadores de biblioteca.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMajorVerNum">
      <summary>Representa el número de la versión principal de la biblioteca de tipos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMinorVerNum">
      <summary>Representa el número de la versión secundaria de la biblioteca de tipos.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC">
      <summary>Describe una variable, una constante o un miembro de datos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.desc">
      <summary>Contiene información de una variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.elemdescVar">
      <summary>Contiene el tipo de variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.lpstrSchema">
      <summary>Este campo está reservado para un uso futuro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.memid">
      <summary>Indica el identificador de miembro de una variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.varkind">
      <summary>Define cómo calcular las referencias de una variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.wVarFlags">
      <summary>Define las propiedades de una variable.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION">
      <summary>Contiene información de una variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.lpvarValue">
      <summary>Describe una constante simbólica.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.oInst">
      <summary>Indica el desplazamiento de la variable dentro de la instancia.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARFLAGS">
      <summary>Identifica las constantes que definen las propiedades de una variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FBINDABLE">
      <summary>La variable admite enlace de datos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTBIND">
      <summary>La variable es la propiedad que mejor representa al objeto.Sólo una variable de una información de tipos puede tener este atributo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTCOLLELEM">
      <summary>Permite llevar a cabo una optimización en la que el compilador busca un miembro denominado "xyz" en el tipo "abc".Si se encuentra un miembro de este tipo y se marca como función de descriptor de acceso para un elemento de la colección predeterminada, entonces se genera una llamada a dicha función miembro.Se permite en miembros de interfaces y de interfaces Dispinterface. No se permite en módulos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDISPLAYBIND">
      <summary>La variable se muestra al usuario como enlazable.<see cref="F:System.Runtime.InteropServices.VARFLAGS.VARFLAG_FBINDABLE" /> también debe establecerse.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FHIDDEN">
      <summary>La variable no debe mostrarse al usuario en un explorador, aunque exista y sea enlazable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FIMMEDIATEBIND">
      <summary>La variable se asigna del mismo modo que las propiedades enlazables individuales.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FNONBROWSABLE">
      <summary>La variable aparece en un examinador de objetos, pero no en un examinador de propiedades.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREADONLY">
      <summary>No debe permitirse que se realicen asignaciones a la variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREPLACEABLE">
      <summary>Etiqueta la interfaz indicando que tiene comportamientos predeterminados.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREQUESTEDIT">
      <summary>Cuando se establece, cualquier intento de cambiar directamente la propiedad da como resultado una llamada a IPropertyNotifySink::OnRequestEdit.La implementación de OnRequestEdit determina si se acepta el cambio.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FRESTRICTED">
      <summary>No se debe permitir el acceso a la variable desde lenguajes de macros.El marcador está destinado a variables en el nivel del sistema o a variables que los exploradores de tipos no deben mostrar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FSOURCE">
      <summary>La variable devuelve un objeto que es un origen de eventos.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FUIDEFAULT">
      <summary>La variable es la presentación predeterminada en la interfaz de usuario.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARKIND">
      <summary>Define el tipo de variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_CONST">
      <summary>La estructura VARDESC describe una constante simbólica.No tiene asociada ninguna memoria.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_DISPATCH">
      <summary>Sólo se puede tener acceso a la variable a través de IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_PERINSTANCE">
      <summary>La variable es un campo o miembro del tipo.Existe en un desplazamiento fijo dentro de cada instancia del tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_STATIC">
      <summary>Sólo hay una instancia de la variable.</summary>
    </member>
  </members>
</doc>