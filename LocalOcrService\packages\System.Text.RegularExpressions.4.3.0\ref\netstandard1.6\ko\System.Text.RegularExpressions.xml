﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Text.RegularExpressions</name>
  </assembly>
  <members>
    <member name="T:System.Text.RegularExpressions.Capture">
      <summary>성공한 단일 하위 식 캡처 결과를 나타냅니다. </summary>
    </member>
    <member name="P:System.Text.RegularExpressions.Capture.Index">
      <summary>원래 문자열에서 캡처된 부분 문자열의 첫째 문자를 찾은 위치입니다.</summary>
      <returns>원래 문자열에서 캡처된 부분 문자열을 찾은 0부터 시작하는 시작 위치입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.Capture.Length">
      <summary>캡처된 부분 문자열의 길이를 가져옵니다.</summary>
      <returns>캡처된 부분 문자열의 길이입니다.</returns>
    </member>
    <member name="M:System.Text.RegularExpressions.Capture.ToString">
      <summary>
        <see cref="P:System.Text.RegularExpressions.Capture.Value" /> 속성을 호출하여 입력 문자열로부터 캡처된 하위 문자열을 검색합니다. </summary>
      <returns>일치 항목으로 캡처한 부분 문자열입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.Capture.Value">
      <summary>입력 문자열에서 캡처된 부분 문자열을 가져옵니다.</summary>
      <returns>일치 항목으로 캡처한 부분 문자열입니다.</returns>
    </member>
    <member name="T:System.Text.RegularExpressions.CaptureCollection">
      <summary>단일 캡처링 그룹에서 만든 캡처 집합을 나타냅니다. </summary>
    </member>
    <member name="P:System.Text.RegularExpressions.CaptureCollection.Count">
      <summary>그룹에서 캡처한 부분 문자열의 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Text.RegularExpressions.CaptureCollection" />의 항목 수입니다.</returns>
    </member>
    <member name="M:System.Text.RegularExpressions.CaptureCollection.GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 제공합니다.</summary>
      <returns>
        <see cref="T:System.Text.RegularExpressions.CaptureCollection" />의 모든 <see cref="T:System.Text.RegularExpressions.Capture" /> 개체를 포함하는 개체입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.CaptureCollection.Item(System.Int32)">
      <summary>컬렉션의 개별 멤버를 가져옵니다.</summary>
      <returns>컬렉션의 위치 <paramref name="i" />에서 캡처된 부분 문자열입니다.</returns>
      <param name="i">캡처 컬렉션의 인덱스입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" />을를은 0 미만이거나 <see cref="P:System.Text.RegularExpressions.CaptureCollection.Count" />보다 큽니다. </exception>
    </member>
    <member name="M:System.Text.RegularExpressions.CaptureCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>지정된 인덱스에서 시작하여 지정된 배열에 컬렉션의 요소를 모두 복사합니다.</summary>
      <param name="array">컬렉션이 복사되는 1차원 배열입니다.</param>
      <param name="arrayIndex">대상 배열에서 복사를 시작할 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" />가 <paramref name="array" />의 범위 밖인 경우또는<paramref name="arrayIndex" />에 <see cref="P:System.Text.RegularExpressions.CaptureCollection.Count" />를 더한 값이 <paramref name="array" />의 범위 밖인 경우</exception>
    </member>
    <member name="P:System.Text.RegularExpressions.CaptureCollection.System#Collections#ICollection#IsSynchronized">
      <summary>해당 컬렉션에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>모든 경우에 false입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.CaptureCollection.System#Collections#ICollection#SyncRoot">
      <summary>컬렉션에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>컬렉션에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.</returns>
    </member>
    <member name="T:System.Text.RegularExpressions.Group">
      <summary>단일 캡처링 그룹의 결과를 나타냅니다. </summary>
    </member>
    <member name="P:System.Text.RegularExpressions.Group.Captures">
      <summary>안쪽-왼쪽 우선 순서로 캡처링 그룹을 사용하여 일치시킨 모든 캡처의 컬렉션을 가져옵니다. 정규식을 <see cref="F:System.Text.RegularExpressions.RegexOptions.RightToLeft" /> 옵션으로 수정한 경우에는 안쪽-오른쪽 우선 순서로 가져올 수 있습니다.컬렉션에는 0이상의 항목이 있을 수 있습니다.</summary>
      <returns>그룹을 사용하여 일치시킨 부분 문자열의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.Group.Success">
      <summary>일치 작업이 성공적이었는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>일치가 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="T:System.Text.RegularExpressions.GroupCollection">
      <summary>단일 일치 항목의 캡처된 그룹 집합을 반환합니다.</summary>
    </member>
    <member name="P:System.Text.RegularExpressions.GroupCollection.Count">
      <summary>컬렉션의 그룹 수를 반환합니다.</summary>
      <returns>컬렉션의 그룹 수입니다.</returns>
    </member>
    <member name="M:System.Text.RegularExpressions.GroupCollection.GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 제공합니다.</summary>
      <returns>
        <see cref="T:System.Text.RegularExpressions.GroupCollection" />의 모든 <see cref="T:System.Text.RegularExpressions.Group" /> 개체를 포함하는 열거자입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.GroupCollection.Item(System.Int32)">
      <summary>정수 인덱스에 따라 컬렉션의 멤버에 대한 액세스를 가능하게 합니다.</summary>
      <returns>
        <paramref name="groupnum" />으로 지정한 컬렉션의 멤버입니다.</returns>
      <param name="groupnum">검색할 컬렉션 멤버의 인덱스(0부터 시작)입니다. </param>
    </member>
    <member name="P:System.Text.RegularExpressions.GroupCollection.Item(System.String)">
      <summary>문자열 인덱스에 따라 컬렉션의 멤버에 대한 액세스를 가능하게 합니다.</summary>
      <returns>
        <paramref name="groupname" />으로 지정한 컬렉션의 멤버입니다.</returns>
      <param name="groupname">캡처링 그룹의 이름입니다. </param>
    </member>
    <member name="M:System.Text.RegularExpressions.GroupCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>지정된 인덱스에서 시작하여 지정된 배열에 컬렉션의 요소를 모두 복사합니다.</summary>
      <param name="array">컬렉션이 복사되는 1차원 배열입니다.</param>
      <param name="arrayIndex">대상 배열에서 복사를 시작할 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.IndexOutOfRangeException">
        <paramref name="arrayIndex" />가 <paramref name="array" />의 범위 밖인 경우또는<paramref name="arrayIndex" />에 <see cref="P:System.Text.RegularExpressions.GroupCollection.Count" />를 더한 값이 <paramref name="array" />의 범위 밖인 경우</exception>
    </member>
    <member name="P:System.Text.RegularExpressions.GroupCollection.System#Collections#ICollection#IsSynchronized">
      <summary>해당 컬렉션에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지를 나타내는 값을 가져옵니다.</summary>
      <returns>모든 경우에 false입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.GroupCollection.System#Collections#ICollection#SyncRoot">
      <summary>컬렉션에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>컬렉션에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.</returns>
    </member>
    <member name="T:System.Text.RegularExpressions.Match">
      <summary>정규식으로 찾은 단일 일치 항목의 결과를 나타냅니다.</summary>
    </member>
    <member name="P:System.Text.RegularExpressions.Match.Empty">
      <summary>빈 그룹을 가져옵니다.일치 항목을 찾지 못하는 모든 경우에는 이 빈 일치 항목이 반환됩니다.</summary>
      <returns>빈 일치 항목입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.Match.Groups">
      <summary>정규식으로 일치시킨 그룹의 컬렉션을 가져옵니다.</summary>
      <returns>해당 패턴으로 일치시킨 문자 그룹입니다.</returns>
    </member>
    <member name="M:System.Text.RegularExpressions.Match.NextMatch">
      <summary>마지막 일치 항목이 끝나는 위치(마지막으로 일치한 문자 다음 문자)에서 시작하여 다음 일치 항목에 대한 결과와 함께 새로운 <see cref="T:System.Text.RegularExpressions.Match" /> 개체를 반환합니다.</summary>
      <returns>다음 정규식 일치 항목입니다.</returns>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Match.Result(System.String)">
      <summary>지정된 바꾸기 패턴의 확장을 반환합니다. </summary>
      <returns>
        <paramref name="replacement" /> 매개 변수의 확장 버전입니다.</returns>
      <param name="replacement">사용할 바꾸기 패턴입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="replacement" />가 null인 경우</exception>
      <exception cref="T:System.NotSupportedException">이 패턴에 대해 확장이 허용되지 않는 경우</exception>
    </member>
    <member name="T:System.Text.RegularExpressions.MatchCollection">
      <summary>정규식 패턴을 입력 문자열에 반복적으로 적용하여 찾은 성공적인 일치 집합을 나타냅니다.</summary>
    </member>
    <member name="P:System.Text.RegularExpressions.MatchCollection.Count">
      <summary>일치하는 항목의 개수를 가져옵니다.</summary>
      <returns>일치하는 항목의 개수입니다.</returns>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.MatchCollection.GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 제공합니다.</summary>
      <returns>
        <see cref="T:System.Text.RegularExpressions.MatchCollection" />의 모든 <see cref="T:System.Text.RegularExpressions.Match" /> 개체를 포함하는 개체입니다.</returns>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.</exception>
    </member>
    <member name="P:System.Text.RegularExpressions.MatchCollection.Item(System.Int32)">
      <summary>컬렉션의 개별 멤버를 가져옵니다.</summary>
      <returns>컬렉션의 위치 <paramref name="i" />에서 캡처된 부분 문자열입니다.</returns>
      <param name="i">
        <see cref="T:System.Text.RegularExpressions.Match" /> 컬렉션의 인덱스입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" />가 0보다 작거나 <see cref="P:System.Text.RegularExpressions.MatchCollection.Count" />보다 크거나 같은 경우 </exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.</exception>
    </member>
    <member name="M:System.Text.RegularExpressions.MatchCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>컬렉션의 모든 요소를 지정한 배열의 지정한 인덱스에서 시작하는 위치에 복사합니다.</summary>
      <param name="array">컬렉션이 복사되는 1차원 배열입니다.</param>
      <param name="arrayIndex">배열에서 복사를 시작할 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열일 경우</exception>
      <exception cref="T:System.IndexOutOfRangeException">
        <paramref name="arrayIndex" />가 배열의 범위 밖에 있는 경우또는<paramref name="arrayIndex" />에 <see cref="P:System.Text.RegularExpressions.MatchCollection.Count" />를 더한 값이 <paramref name="array" />의 범위 밖인 경우</exception>
    </member>
    <member name="P:System.Text.RegularExpressions.MatchCollection.System#Collections#ICollection#IsSynchronized">
      <summary>해당 컬렉션에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지를 나타내는 값을 가져옵니다.</summary>
      <returns>모든 경우에 false입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.MatchCollection.System#Collections#ICollection#SyncRoot">
      <summary>컬렉션에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>컬렉션에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.이 속성은 항상 개체 자체를 반환합니다.</returns>
    </member>
    <member name="T:System.Text.RegularExpressions.MatchEvaluator">
      <summary>
        <see cref="Overload:System.Text.RegularExpressions.Regex.Replace" /> 메서드 작업을 하는 동안 정규식을 사용하여 일치 항목을 찾을 때마다 호출되는 메서드를 나타냅니다.</summary>
      <returns>
        <see cref="T:System.Text.RegularExpressions.MatchEvaluator" /> 대리자가 나타내는 메서드에서 반환된 문자열입니다.</returns>
      <param name="match">
        <see cref="Overload:System.Text.RegularExpressions.Regex.Replace" /> 메서드 작업을 하는 동안 단일 정규식 일치 항목을 나타내는 <see cref="T:System.Text.RegularExpressions.Match" /> 개체입니다. </param>
    </member>
    <member name="T:System.Text.RegularExpressions.Regex">
      <summary>변경할 수 없는 정규식을 나타냅니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 참조 원본.</summary>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.#ctor">
      <summary>
        <see cref="T:System.Text.RegularExpressions.Regex" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.#ctor(System.String)">
      <summary>지정된 정규 식에 대해 <see cref="T:System.Text.RegularExpressions.Regex" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="pattern" />가 null인 경우</exception>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.#ctor(System.String,System.Text.RegularExpressions.RegexOptions)">
      <summary>패턴을 수정할 수 있는 옵션을 사용하여 <see cref="T:System.Text.RegularExpressions.Regex" /> 클래스의 새 인스턴스를 지정된 정규식에 대해 초기화합니다.</summary>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <param name="options">정규식을 수정하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />에 잘못된 플래그가 포함된 경우</exception>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.#ctor(System.String,System.Text.RegularExpressions.RegexOptions,System.TimeSpan)">
      <summary>시간이 초과되기 전에 패턴 일치 메서드가 일치하도록 시도해야 하는 시간을 지정한 패턴과 값을 수정할 수 있는 옵션을 사용하여 <see cref="T:System.Text.RegularExpressions.Regex" /> 클래스의 새 인스턴스를 지정된 정규식에 대해 초기화하고 컴파일합니다.</summary>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다.</param>
      <param name="options">정규식을 수정하는 열거형 값의 비트 조합입니다.</param>
      <param name="matchTimeout">시간 제한 간격이거나, 메서드에 시간 제한이 없어야 함을 나타내는 <see cref="F:System.Text.RegularExpressions.Regex.InfiniteMatchTimeout" />입니다.</param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />은(는) 올바른 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값이 아닙니다.또는<paramref name="matchTimeout" />이 음수, 0 또는 약 24일보다 큰 경우</exception>
    </member>
    <member name="P:System.Text.RegularExpressions.Regex.CacheSize">
      <summary>컴파일된 정규식에 대한 현재 정적 캐시의 최대 엔트리 수를 가져오거나 설정합니다.</summary>
      <returns>현재 정적 캐시의 최대 엔트리 수입니다.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">set 작업의 값이 0보다 작은 경우</exception>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Escape(System.String)">
      <summary>문자(\, *, +, ?, |, {, [, (,), ^, $,., # 및 공백)의 최소 집합을 자체 이스케이프 코드로 대체하여 이스케이프합니다.이렇게 하면 정규식 엔진은 이러한 문자를 메타문자가 아니라 문자 그대로 해석합니다.</summary>
      <returns>메타문자가 이스케이프 서식으로 변환된 문자열입니다.</returns>
      <param name="str">변환할 텍스트가 포함된 입력 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="str" />가 null인 경우</exception>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.GetGroupNames">
      <summary>정규식에 대한 캡처링 그룹 이름의 배열을 반환합니다.</summary>
      <returns>그룹 이름의 문자열 배열입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.GetGroupNumbers">
      <summary>배열 내의 그룹 이름에 해당하는 캡처링 그룹 번호의 배열을 반환합니다.</summary>
      <returns>그룹 번호의 정수 배열입니다.</returns>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.GroupNameFromNumber(System.Int32)">
      <summary>지정된 그룹 번호에 해당하는 그룹 이름을 가져옵니다.</summary>
      <returns>지정된 그룹 번호에 연결되어 있는 그룹 이름이 포함된 문자열입니다.<paramref name="i" />에 해당하는 그룹 이름이 없는 경우 이 메서드는 <see cref="F:System.String.Empty" />를 반환합니다.</returns>
      <param name="i">해당 그룹 이름으로 변환할 그룹 번호입니다. </param>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.GroupNumberFromName(System.String)">
      <summary>지정된 그룹 이름에 해당하는 그룹 번호를 반환합니다.</summary>
      <returns>지정된 그룹 이름에 해당하는 그룹 번호이거나 <paramref name="name" />이 유효한 그룹 이름이 아닌 경우 -1입니다.</returns>
      <param name="name">해당 그룹 번호로 변환할 그룹 이름입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우</exception>
    </member>
    <member name="F:System.Text.RegularExpressions.Regex.InfiniteMatchTimeout">
      <summary>패턴 일치 작업의 시간이 초과되지 않도록 지정합니다.</summary>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.IsMatch(System.String)">
      <summary>
        <see cref="T:System.Text.RegularExpressions.Regex" /> 생성자에 지정된 정규식이 지정된 입력 문자열에서 일치하는 항목을 찾을 것인지 여부를 나타냅니다.</summary>
      <returns>정규식에서 일치하는 항목을 찾으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.IsMatch(System.String,System.Int32)">
      <summary>
        <see cref="T:System.Text.RegularExpressions.Regex" /> 생성자에 지정된 정규식이 지정된 입력 문자열의 지정된 시작 위치에서부터 일치하는 항목을 찾을 것인지 여부를 나타냅니다.</summary>
      <returns>정규식에서 일치하는 항목을 찾으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="startat">검색을 시작할 문자 위치입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startat" />이 0보다 작거나 <paramref name="input" />의 길이보다 큰 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.IsMatch(System.String,System.String)">
      <summary>지정된 정규식이 지정된 입력 문자열에서 일치하는 항목을 찾을 것인지를 나타냅니다.</summary>
      <returns>정규식에서 일치하는 항목을 찾으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우 </exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.IsMatch(System.String,System.String,System.Text.RegularExpressions.RegexOptions)">
      <summary>지정된 정규식이 지정된 일치 옵션을 사용하여 지정된 입력 문자열에서 일치하는 항목을 찾을 것인지를 나타냅니다.</summary>
      <returns>정규식에서 일치하는 항목을 찾으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <param name="options">일치 옵션을 제공하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />은(는) 올바른 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값이 아닙니다.</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.IsMatch(System.String,System.String,System.Text.RegularExpressions.RegexOptions,System.TimeSpan)">
      <summary>지정된 정규식이 지정된 일치 옵션 및 시간 제한 간격을 사용하여 지정된 입력 문자열에서 일치하는 항목을 찾을 것인지를 나타냅니다.</summary>
      <returns>정규식에서 일치하는 항목을 찾으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다.</param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다.</param>
      <param name="options">일치 옵션을 제공하는 열거형 값의 비트 조합입니다.</param>
      <param name="matchTimeout">시간 제한 간격이거나, 메서드에 시간 제한이 없어야 함을 나타내는 <see cref="F:System.Text.RegularExpressions.Regex.InfiniteMatchTimeout" />입니다.</param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />은(는) 올바른 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값이 아닙니다.또는<paramref name="matchTimeout" />이 음수, 0 또는 약 24일보다 큰 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.</exception>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Match(System.String)">
      <summary>지정된 입력 문자열에서 <see cref="T:System.Text.RegularExpressions.Regex" /> 생성자에 지정된 정규식의 처음 발견되는 항목을 검색합니다.</summary>
      <returns>일치에 대한 정보가 포함된 개체입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Match(System.String,System.Int32)">
      <summary>문자열의 지정된 시작 위치에서 시작하여 입력 문자열에서 처음 발견되는 정규식을 검색합니다.</summary>
      <returns>일치에 대한 정보가 포함된 개체입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="startat">검색을 시작할 문자 위치(0부터 시작)입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startat" />이 0보다 작거나 <paramref name="input" />의 길이보다 큰 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Match(System.String,System.Int32,System.Int32)">
      <summary>지정된 시작 위치에서 시작하고 지정된 수의 문자만 검색하여 입력 문자열에서 첫 번째 정규식을 검색합니다.</summary>
      <returns>일치에 대한 정보가 포함된 개체입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="beginning">검색되는 가장 왼쪽 지점을 정의하는 입력 문자열의 문자 위치(0부터 시작)입니다. </param>
      <param name="length">검색에 포함시킬 부분 문자열의 문자 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="beginning" />이 0보다 작거나 <paramref name="input" />의 길이보다 큰 경우또는<paramref name="length" />이 0보다 작거나 <paramref name="input" />의 길이보다 큰 경우또는<paramref name="beginning" />+<paramref name="length" />– 1 identifies a position that is outside the range of <paramref name="input" />.</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Match(System.String,System.String)">
      <summary>지정된 입력 문자열에서 첫 번째 지정된 정규식을 검색합니다.</summary>
      <returns>일치에 대한 정보가 포함된 개체입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Match(System.String,System.String,System.Text.RegularExpressions.RegexOptions)">
      <summary>지정된 일치 옵션을 사용하여 입력 문자열에서 첫 번째 지정된 정규식을 검색합니다.</summary>
      <returns>일치에 대한 정보가 포함된 개체입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <param name="options">일치 옵션을 제공하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />가 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값의 유효한 비트 조합이 아닌 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Match(System.String,System.String,System.Text.RegularExpressions.RegexOptions,System.TimeSpan)">
      <summary>지정된 일치 옵션 및 제한 시간 간격을 사용하여 입력 문자열에서 첫 번째 지정된 정규식을 검색합니다.</summary>
      <returns>일치에 대한 정보가 포함된 개체입니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다.</param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다.</param>
      <param name="options">일치 옵션을 제공하는 열거형 값의 비트 조합입니다.</param>
      <param name="matchTimeout">시간 제한 간격이거나, 메서드에 시간 제한이 없어야 함을 나타내는 <see cref="F:System.Text.RegularExpressions.Regex.InfiniteMatchTimeout" />입니다.</param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />가 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값의 유효한 비트 조합이 아닌 경우또는<paramref name="matchTimeout" />이 음수, 0 또는 약 24일보다 큰 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Matches(System.String)">
      <summary>지정된 입력 문자열에 있는 정규식을 모두 검색합니다.</summary>
      <returns>검색에서 찾은 <see cref="T:System.Text.RegularExpressions.Match" /> 개체의 컬렉션입니다.일치 항목이 없으면 메서드가 빈 컬렉션 개체를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Matches(System.String,System.Int32)">
      <summary>문자열의 지정된 시작 위치에서 시작하여 지정된 입력 문자열에 있는 정규식을 모두 검색합니다.</summary>
      <returns>검색에서 찾은 <see cref="T:System.Text.RegularExpressions.Match" /> 개체의 컬렉션입니다.일치 항목이 없으면 메서드가 빈 컬렉션 개체를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="startat">입력 문자열에서 검색을 시작할 문자 위치입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startat" />이 0보다 작거나 <paramref name="input" />의 길이보다 큰 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Matches(System.String,System.String)">
      <summary>지정된 입력 문자열에서 지정된 정규식을 모두 검색합니다.</summary>
      <returns>검색에서 찾은 <see cref="T:System.Text.RegularExpressions.Match" /> 개체의 컬렉션입니다.일치 항목이 없으면 메서드가 빈 컬렉션 개체를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Matches(System.String,System.String,System.Text.RegularExpressions.RegexOptions)">
      <summary>지정된 일치 옵션을 사용하여 지정된 입력 문자열에서 지정된 정규식을 모두 검색합니다.</summary>
      <returns>검색에서 찾은 <see cref="T:System.Text.RegularExpressions.Match" /> 개체의 컬렉션입니다.일치 항목이 없으면 메서드가 빈 컬렉션 개체를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <param name="options">일치 옵션을 지정하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />가 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값의 유효한 비트 조합이 아닌 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Matches(System.String,System.String,System.Text.RegularExpressions.RegexOptions,System.TimeSpan)">
      <summary>지정된 일치 옵션 및 제한 시간 간격을 사용하여 지정된 입력 문자열에서 지정된 정규식을 모두 검색합니다.</summary>
      <returns>검색에서 찾은 <see cref="T:System.Text.RegularExpressions.Match" /> 개체의 컬렉션입니다.일치 항목이 없으면 메서드가 빈 컬렉션 개체를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다.</param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다.</param>
      <param name="options">일치 옵션을 지정하는 열거형 값의 비트 조합입니다.</param>
      <param name="matchTimeout">시간 제한 간격이거나, 메서드에 시간 제한이 없어야 함을 나타내는 <see cref="F:System.Text.RegularExpressions.Regex.InfiniteMatchTimeout" />입니다.</param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />가 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값의 유효한 비트 조합이 아닌 경우또는<paramref name="matchTimeout" />이 음수, 0 또는 약 24일보다 큰 경우</exception>
    </member>
    <member name="P:System.Text.RegularExpressions.Regex.MatchTimeout">
      <summary>현재 인스턴스의 시간 제한 간격을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException" />이 throw되기 전에 패턴 일치 작업에서 경과할 수 있는 최대 시간 간격이며, 제한 시간이 비활성화된 경우는 <see cref="F:System.Text.RegularExpressions.Regex.InfiniteMatchTimeout" />입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.Regex.Options">
      <summary>
        <see cref="T:System.Text.RegularExpressions.Regex" /> 생성자에 전달된 옵션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Text.RegularExpressions.Regex" /> 생성자에게 전달된 옵션을 나타내는 하나 이상의 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 열거형 멤버입니다. </returns>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.String)">
      <summary>지정된 입력 문자열에서 정규식 패턴과 일치하는 모든 문자열을 지정된 대체 문자열로 바꿉니다. </summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.정규식 패턴이 현재 인스턴스에서 일치하지 않는 경우 메서드는 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="replacement">대체 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="replacement" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.String,System.Int32)">
      <summary>지정된 입력 문자열에서 정규식 패턴과 일치하는 지정된 최대 개수의 문자열을 지정된 대체 문자열로 바꿉니다. </summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.정규식 패턴이 현재 인스턴스에서 일치하지 않는 경우 메서드는 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="replacement">대체 문자열입니다. </param>
      <param name="count">바꾸기를 하는 최대 횟수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="replacement" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.String,System.Int32,System.Int32)">
      <summary>지정된 입력 부분 문자열에서 정규식 패턴과 일치하는 지정된 최대 개수의 문자열을 지정된 대체 문자열로 바꿉니다. </summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.정규식 패턴이 현재 인스턴스에서 일치하지 않는 경우 메서드는 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="replacement">대체 문자열입니다. </param>
      <param name="count">바꾸기를 하는 최대 횟수입니다. </param>
      <param name="startat">입력 문자열에서 검색을 시작할 문자 위치입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="replacement" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startat" />이 0보다 작거나 <paramref name="input" />의 길이보다 큰 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.String,System.String)">
      <summary>지정된 입력 문자열에서 지정된 정규식과 일치하는 모든 문자열을 지정된 대체 문자열로 바꿉니다. </summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.현재 인스턴스에서 <paramref name="pattern" />이 일치하지 않으면 메서드가 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <param name="replacement">대체 문자열입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />, <paramref name="pattern" /> 또는 <paramref name="replacement" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.String,System.String,System.Text.RegularExpressions.RegexOptions)">
      <summary>지정된 입력 문자열에서 지정된 정규식과 일치하는 모든 문자열을 지정된 대체 문자열로 바꿉니다.지정한 옵션에 따라 일치 작업이 수정됩니다.</summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.현재 인스턴스에서 <paramref name="pattern" />이 일치하지 않으면 메서드가 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <param name="replacement">대체 문자열입니다. </param>
      <param name="options">일치 옵션을 제공하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />, <paramref name="pattern" /> 또는 <paramref name="replacement" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />가 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값의 유효한 비트 조합이 아닌 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.String,System.String,System.Text.RegularExpressions.RegexOptions,System.TimeSpan)">
      <summary>지정된 입력 문자열에서 지정된 정규식과 일치하는 모든 문자열을 지정된 대체 문자열로 바꿉니다.추가 매개 변수는 일치하는 항목이 없는 경우 제한 시간 간격과 일치 작업을 수정하는 옵션을 지정합니다.</summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.현재 인스턴스에서 <paramref name="pattern" />이 일치하지 않으면 메서드가 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다.</param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다.</param>
      <param name="replacement">대체 문자열입니다.</param>
      <param name="options">일치 옵션을 제공하는 열거형 값의 비트 조합입니다.</param>
      <param name="matchTimeout">시간 제한 간격이거나, 메서드에 시간 제한이 없어야 함을 나타내는 <see cref="F:System.Text.RegularExpressions.Regex.InfiniteMatchTimeout" />입니다.</param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />, <paramref name="pattern" /> 또는 <paramref name="replacement" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />가 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값의 유효한 비트 조합이 아닌 경우또는<paramref name="matchTimeout" />이 음수, 0 또는 약 24일보다 큰 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.String,System.Text.RegularExpressions.MatchEvaluator)">
      <summary>지정된 입력 문자열에서 지정된 정규식과 일치하는 모든 문자열을 <see cref="T:System.Text.RegularExpressions.MatchEvaluator" /> 대리자가 반환한 문자열로 바꿉니다.</summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.현재 인스턴스에서 <paramref name="pattern" />이 일치하지 않으면 메서드가 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <param name="evaluator">각 항목의 일치 여부를 조사하고 원래 일치 문자열 또는 대체 문자열을 반환하는 사용자 지정 메서드입니다.</param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />, <paramref name="pattern" /> 또는 <paramref name="evaluator" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.String,System.Text.RegularExpressions.MatchEvaluator,System.Text.RegularExpressions.RegexOptions)">
      <summary>지정된 입력 문자열에서 지정된 정규식과 일치하는 모든 문자열을 <see cref="T:System.Text.RegularExpressions.MatchEvaluator" /> 대리자가 반환한 문자열로 바꿉니다.지정한 옵션에 따라 일치 작업이 수정됩니다.</summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.현재 인스턴스에서 <paramref name="pattern" />이 일치하지 않으면 메서드가 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <param name="evaluator">각 항목의 일치 여부를 조사하고 원래 일치 문자열 또는 대체 문자열을 반환하는 사용자 지정 메서드입니다. </param>
      <param name="options">일치 옵션을 제공하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />, <paramref name="pattern" /> 또는 <paramref name="evaluator" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />가 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값의 유효한 비트 조합이 아닌 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.String,System.Text.RegularExpressions.MatchEvaluator,System.Text.RegularExpressions.RegexOptions,System.TimeSpan)">
      <summary>지정된 입력 문자열에서 지정된 정규식과 일치하는 모든 부분 문자열을 <see cref="T:System.Text.RegularExpressions.MatchEvaluator" /> 대리자가 반환한 문자열로 바꿉니다.추가 매개 변수는 일치하는 항목이 없는 경우 제한 시간 간격과 일치 작업을 수정하는 옵션을 지정합니다.</summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.현재 인스턴스에서 <paramref name="pattern" />이 일치하지 않으면 메서드가 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다.</param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다.</param>
      <param name="evaluator">각 항목의 일치 여부를 조사하고 원래 일치 문자열 또는 대체 문자열을 반환하는 사용자 지정 메서드입니다.</param>
      <param name="options">일치 옵션을 제공하는 열거형 값의 비트 조합입니다.</param>
      <param name="matchTimeout">시간 제한 간격이거나, 메서드에 시간 제한이 없어야 함을 나타내는 <see cref="F:System.Text.RegularExpressions.Regex.InfiniteMatchTimeout" />입니다.</param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />, <paramref name="pattern" /> 또는 <paramref name="evaluator" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />가 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값의 유효한 비트 조합이 아닌 경우또는<paramref name="matchTimeout" />이 음수, 0 또는 약 24일보다 큰 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.Text.RegularExpressions.MatchEvaluator)">
      <summary>지정된 입력 문자열에서 지정된 정규식과 일치하는 모든 문자열을 <see cref="T:System.Text.RegularExpressions.MatchEvaluator" /> 대리자가 반환한 문자열로 바꿉니다. </summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.정규식 패턴이 현재 인스턴스에서 일치하지 않는 경우 메서드는 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="evaluator">각 항목의 일치 여부를 조사하고 원래 일치 문자열 또는 대체 문자열을 반환하는 사용자 지정 메서드입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="evaluator" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.Text.RegularExpressions.MatchEvaluator,System.Int32)">
      <summary>지정된 입력 문자열에서 정규식 패턴과 일치하는 지정된 최대 개수의 문자열을 <see cref="T:System.Text.RegularExpressions.MatchEvaluator" /> 대리자가 반환한 문자열로 바꿉니다. </summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.정규식 패턴이 현재 인스턴스에서 일치하지 않는 경우 메서드는 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="evaluator">각 항목의 일치 여부를 조사하고 원래 일치 문자열 또는 대체 문자열을 반환하는 사용자 지정 메서드입니다.</param>
      <param name="count">바꾸기를 하는 최대 횟수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="evaluator" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Replace(System.String,System.Text.RegularExpressions.MatchEvaluator,System.Int32,System.Int32)">
      <summary>지정된 입력 부분 문자열에서 정규식 패턴과 일치하는 지정된 최대 개수의 문자열을 <see cref="T:System.Text.RegularExpressions.MatchEvaluator" /> 대리자가 반환한 문자열로 바꿉니다. </summary>
      <returns>입력 문자열과 동일한 새 문자열입니다. 단, 대체 문자열은 일치하는 각 문자열을 대체합니다.정규식 패턴이 현재 인스턴스에서 일치하지 않는 경우 메서드는 변경되지 않은 현재 인스턴스를 반환합니다.</returns>
      <param name="input">일치 항목을 검색할 문자열입니다. </param>
      <param name="evaluator">각 항목의 일치 여부를 조사하고 원래 일치 문자열 또는 대체 문자열을 반환하는 사용자 지정 메서드입니다.</param>
      <param name="count">바꾸기를 하는 최대 횟수입니다. </param>
      <param name="startat">입력 문자열에서 검색을 시작할 문자 위치입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="evaluator" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startat" />이 0보다 작거나 <paramref name="input" />의 길이보다 큰 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Text.RegularExpressions.Regex.RightToLeft">
      <summary>정규식을 사용하여 오른쪽에서 왼쪽으로 검색하는지를 나타내는 값을 가져옵니다.</summary>
      <returns>정규식을 사용하여 오른쪽에서 왼쪽으로 검색하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Split(System.String)">
      <summary>
        <see cref="T:System.Text.RegularExpressions.Regex" /> 생성자에서 지정된 정규식 패턴에 의해 정의된 위치에서 입력 문자열을 부분 문자열의 배열로 분할합니다.</summary>
      <returns>문자열 배열입니다.</returns>
      <param name="input">분할할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Split(System.String,System.Int32)">
      <summary>입력 문자열을 <see cref="T:System.Text.RegularExpressions.Regex" /> 생성자에서 지정된 정규식에 의해 정의된 위치에서 지정된 최대 수만큼 부분 문자열의 배열로 분할합니다.</summary>
      <returns>문자열 배열입니다.</returns>
      <param name="input">분할될 문자열입니다. </param>
      <param name="count">분할할 수 있는 최대 횟수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Split(System.String,System.Int32,System.Int32)">
      <summary>입력 문자열을 <see cref="T:System.Text.RegularExpressions.Regex" /> 생성자에서 지정된 정규식에 의해 정의된 위치에서 지정된 최대 수만큼 부분 문자열의 배열로 분할합니다.입력 문자열에서 지정된 문자 위치부터 정규식 패턴을 검색합니다.</summary>
      <returns>문자열 배열입니다.</returns>
      <param name="input">분할될 문자열입니다. </param>
      <param name="count">분할할 수 있는 최대 횟수입니다. </param>
      <param name="startat">입력 문자열에서 검색을 시작할 문자 위치입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startat" />이 0보다 작거나 <paramref name="input" />의 길이보다 큰 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Split(System.String,System.String)">
      <summary>정규식 패턴에 의해 정의된 위치에서 부분 문자열로 이루어진 배열로 입력 문자열을 분할합니다.</summary>
      <returns>문자열 배열입니다.</returns>
      <param name="input">분할할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Split(System.String,System.String,System.Text.RegularExpressions.RegexOptions)">
      <summary>지정된 정규식 패턴에 의해 정의된 위치에서 부분 문자열로 이루어진 배열로 입력 문자열을 분할합니다.지정한 옵션에 따라 일치 작업이 수정됩니다.</summary>
      <returns>문자열 배열입니다.</returns>
      <param name="input">분할할 문자열입니다. </param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다. </param>
      <param name="options">일치 옵션을 제공하는 열거형 값의 비트 조합입니다. </param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />가 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값의 유효한 비트 조합이 아닌 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Split(System.String,System.String,System.Text.RegularExpressions.RegexOptions,System.TimeSpan)">
      <summary>지정된 정규식 패턴에 의해 정의된 위치에서 부분 문자열로 이루어진 배열로 입력 문자열을 분할합니다.추가 매개 변수는 일치하는 항목이 없는 경우 제한 시간 간격과 일치 작업을 수정하는 옵션을 지정합니다.</summary>
      <returns>문자열 배열입니다.</returns>
      <param name="input">분할할 문자열입니다.</param>
      <param name="pattern">일치 항목을 찾을 정규식 패턴입니다.</param>
      <param name="options">일치 옵션을 제공하는 열거형 값의 비트 조합입니다.</param>
      <param name="matchTimeout">시간 제한 간격이거나, 메서드에 시간 제한이 없어야 함을 나타내는 <see cref="F:System.Text.RegularExpressions.Regex.InfiniteMatchTimeout" />입니다.</param>
      <exception cref="T:System.ArgumentException">정규식 구문 분석 오류가 발생한 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 또는 <paramref name="pattern" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" />가 <see cref="T:System.Text.RegularExpressions.RegexOptions" /> 값의 유효한 비트 조합이 아닌 경우또는<paramref name="matchTimeout" />이 음수, 0 또는 약 24일보다 큰 경우</exception>
      <exception cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException">제한 시간이 발생했습니다.시간 제한에 대한 자세한 내용은 설명 단원을 참조하십시오.</exception>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.ToString">
      <summary>Regex 생성자로 전달된 정규식 패턴을 반환합니다.</summary>
      <returns>Regex 생성자로 전달된 <paramref name="pattern" /> 매개 변수입니다.</returns>
    </member>
    <member name="M:System.Text.RegularExpressions.Regex.Unescape(System.String)">
      <summary>입력 문자열에서 이스케이프된 문자를 변환합니다.</summary>
      <returns>이스케이프된 문자가 이스케이프되지 않은 형식으로 변환된 문자열입니다.</returns>
      <param name="str">변환할 텍스트가 포함된 입력 문자열입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="str" />에는 인식할 수 없는 이스케이프 시퀀스가 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="str" />가 null인 경우</exception>
    </member>
    <member name="T:System.Text.RegularExpressions.RegexMatchTimeoutException">
      <summary>정적 정규식 패턴 일치 메서드의 실행 시간이 시간 제한 간격을 초과하는 경우 throw되는 예외입니다.</summary>
    </member>
    <member name="M:System.Text.RegularExpressions.RegexMatchTimeoutException.#ctor">
      <summary>시스템 제공 메시지를 사용하여 <see cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Text.RegularExpressions.RegexMatchTimeoutException.#ctor(System.String)">
      <summary>지정된 메시지 문자열을 사용하여 <see cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외를 설명하는 문자열입니다.</param>
    </member>
    <member name="M:System.Text.RegularExpressions.RegexMatchTimeoutException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외를 설명하는 문자열입니다.</param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.</param>
    </member>
    <member name="M:System.Text.RegularExpressions.RegexMatchTimeoutException.#ctor(System.String,System.String,System.TimeSpan)">
      <summary>정규식 패턴, 입력 텍스트 및 시간 초과 간격에 대한 정보를 사용하여 <see cref="T:System.Text.RegularExpressions.RegexMatchTimeoutException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="regexInput">제한 시간이 경과되었을 경우 정규식 엔진에서 처리되는 입력 텍스트입니다.</param>
      <param name="regexPattern">제한 시간이 경과되었을 경우 정규식 엔진에서 사용되는 패턴입니다.</param>
      <param name="matchTimeout">시간 제한 간격입니다.</param>
    </member>
    <member name="P:System.Text.RegularExpressions.RegexMatchTimeoutException.Input">
      <summary>제한 시간이 경과될 때 정규식 엔진에서 처리하고 있었던 입력 텍스트를 가져옵니다.</summary>
      <returns>정규식 입력 텍스트입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.RegexMatchTimeoutException.MatchTimeout">
      <summary>정규식 일치에 대한 시간 제한 간격을 가져옵니다.</summary>
      <returns>시간 제한 간격입니다.</returns>
    </member>
    <member name="P:System.Text.RegularExpressions.RegexMatchTimeoutException.Pattern">
      <summary>제한 시간이 초과될 때 일치하는 작업에서 사용된 정규식 패턴을 가져옵니다.</summary>
      <returns>정규식 패턴입니다.</returns>
    </member>
    <member name="T:System.Text.RegularExpressions.RegexOptions">
      <summary>정규식 옵션을 설정하는 데 사용하는 열거형 값을 제공합니다.</summary>
    </member>
    <member name="F:System.Text.RegularExpressions.RegexOptions.Compiled">
      <summary>정규식이 어셈블리로 컴파일되도록 지정합니다.이렇게 하면 실행은 빨라지지만 시작 시간은 늘어납니다.<see cref="M:System.Text.RegularExpressions.Regex.CompileToAssembly(System.Text.RegularExpressions.RegexCompilationInfo[],System.Reflection.AssemblyName)" /> 메서드를 호출할 때는 <see cref="P:System.Text.RegularExpressions.RegexCompilationInfo.Options" /> 속성에 이 값을 할당하지 말아야 합니다.자세한 내용은 정규식 옵션 항목의 "컴파일된 정규식" 섹션을 참조하세요.</summary>
    </member>
    <member name="F:System.Text.RegularExpressions.RegexOptions.CultureInvariant">
      <summary>언어의 문화권 차이점이 무시되도록 지정합니다.자세한 내용은 정규식 옵션 항목의 "고정 문화권을 사용하여 비교" 섹션을 참조하세요.</summary>
    </member>
    <member name="F:System.Text.RegularExpressions.RegexOptions.ECMAScript">
      <summary>해당 식에 ECMAScript 규격 동작을 사용 가능하게 합니다.이 값은 <see cref="F:System.Text.RegularExpressions.RegexOptions.IgnoreCase" />, <see cref="F:System.Text.RegularExpressions.RegexOptions.Multiline" /> 및 <see cref="F:System.Text.RegularExpressions.RegexOptions.Compiled" /> 값과 함께만 사용할 수 있습니다.이 값을 다른 값과 함께 사용하면 예외가 발생합니다.<see cref="F:System.Text.RegularExpressions.RegexOptions.ECMAScript" /> 옵션에 대한 자세한 내용은 정규식 옵션 항목의 "ECMAScript 일치 동작"을 참조하세요. </summary>
    </member>
    <member name="F:System.Text.RegularExpressions.RegexOptions.ExplicitCapture">
      <summary>(?&lt;name&gt;¡K) 형식의 명시적으로 명명되거나 번호가 매겨진 그룹만 유효한 캡처가 되도록 지정합니다.이렇게 하면 명명되지 않은 괄호가 (?:¡K) 식과 같이 구문적으로 어색한 부분 없이 비캡처링 그룹의 역할을 할 수 있습니다.자세한 내용은 정규식 옵션 항목의 "명시적 캡처의 경우에만"을 참조하세요.</summary>
    </member>
    <member name="F:System.Text.RegularExpressions.RegexOptions.IgnoreCase">
      <summary>대/소문자를 구분하지 않고 일치 항목을 찾도록 지정합니다.자세한 내용은 정규식 옵션 항목의 "대/소문자를 구분하지 않는 일치"를 참조하세요.</summary>
    </member>
    <member name="F:System.Text.RegularExpressions.RegexOptions.IgnorePatternWhitespace">
      <summary>이스케이프되지 않은 공백을 패턴에서 제거하고 주석을 #으로 표시할 수 있게 합니다.그러나 이 값은 문자 클래스, 숫자 수량자 또는 개별 정규식 언어 요소의 시작을 표시하는 토큰에서 영향을 주거나 공백을 제거하지 않습니다.자세한 내용은 정규식 옵션 항목의 "공백 무시"를 참조하세요.</summary>
    </member>
    <member name="F:System.Text.RegularExpressions.RegexOptions.Multiline">
      <summary>여러 줄 모드입니다.전체 문자열의 시작과 끝뿐만 아니라 모든 줄의 시작과 끝에서 각각 일치하도록 ^과 $의 의미를 변경합니다.자세한 내용은 정규식 옵션 항목의 "여러 줄 모드"를 참조하세요.</summary>
    </member>
    <member name="F:System.Text.RegularExpressions.RegexOptions.None">
      <summary>옵션이 설정되지 않도록 지정합니다.정규식 엔진의 기본 동작에 대한 자세한 내용은 정규식 옵션 항목의 "기본 옵션"을 참조하세요.</summary>
    </member>
    <member name="F:System.Text.RegularExpressions.RegexOptions.RightToLeft">
      <summary>왼쪽에서 오른쪽이 아닌 오른쪽에서 왼쪽으로 검색이 진행되도록 지정합니다.자세한 내용은 정규식 옵션 항목의 "오른쪽에서 왼쪽 모드"를 참조하세요.</summary>
    </member>
    <member name="F:System.Text.RegularExpressions.RegexOptions.Singleline">
      <summary>한 줄 모드를 지정합니다.\n을 제외한 모든 문자가 아닌 \n을 포함한 모든 문자와 일치하도록 점(.)의 의미를 변경합니다.자세한 내용은 정규식 옵션 항목의 "한 줄 모드"를 참조하세요.</summary>
    </member>
  </members>
</doc>