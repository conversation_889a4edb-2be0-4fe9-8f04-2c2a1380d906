﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks</name>
  </assembly>
  <members>
    <member name="T:System.AggregateException">
      <summary>Représente une ou plusieurs erreurs qui se produisent lors de l'exécution de l'application.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.AggregateException" /> avec un message système qui décrit l'erreur.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.AggregateException" /> avec des références aux exceptions internes qui sont la cause de cette exception.</summary>
      <param name="innerExceptions">Exceptions qui ont provoqué l'exception actuelle.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="innerExceptions" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Un élément d'<paramref name="innerExceptions" /> est null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Exception[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.AggregateException" /> avec des références aux exceptions internes qui sont la cause de cette exception.</summary>
      <param name="innerExceptions">Exceptions qui ont provoqué l'exception actuelle.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="innerExceptions" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Un élément d'<paramref name="innerExceptions" /> est null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.AggregateException" /> avec un message spécifié décrivant l'erreur.</summary>
      <param name="message">Message qui décrit l'exception.L'appelant de ce constructeur doit vérifier que cette chaîne a été localisée pour la culture du système en cours.</param>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.AggregateException" /> avec un message d'erreur spécifié et des références aux exceptions internes qui sont la cause de cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception.</param>
      <param name="innerExceptions">Exceptions qui ont provoqué l'exception actuelle.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="innerExceptions" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Un élément d'<paramref name="innerExceptions" /> est null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.AggregateException" /> avec un message d'erreur spécifié et une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="message">Message qui décrit l'exception.L'appelant de ce constructeur doit vérifier que cette chaîne a été localisée pour la culture du système en cours.</param>
      <param name="innerException">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="innerException" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="innerException" /> a la valeur null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.AggregateException" /> avec un message d'erreur spécifié et des références aux exceptions internes qui sont la cause de cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception.</param>
      <param name="innerExceptions">Exceptions qui ont provoqué l'exception actuelle.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="innerExceptions" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Un élément d'<paramref name="innerExceptions" /> est null.</exception>
    </member>
    <member name="M:System.AggregateException.Flatten">
      <summary>Aplatit des instances <see cref="T:System.AggregateException" /> en une nouvelle instance unique.</summary>
      <returns>Nouvelle instance <see cref="T:System.AggregateException" /> aplatie.</returns>
    </member>
    <member name="M:System.AggregateException.GetBaseException">
      <summary>Retourne <see cref="T:System.AggregateException" /> qui est la cause première de cette exception.</summary>
      <returns>Retourne <see cref="T:System.AggregateException" /> qui est la cause première de cette exception.</returns>
    </member>
    <member name="M:System.AggregateException.Handle(System.Func{System.Exception,System.Boolean})">
      <summary>Appelle un gestionnaire sur chaque <see cref="T:System.Exception" /> contenu par ce <see cref="T:System.AggregateException" />.</summary>
      <param name="predicate">Prédicat à exécuter pour chaque exception.Le prédicat accepte l'objet <see cref="T:System.Exception" /> à traiter en tant qu'argument et retourne une valeur booléenne pour indiquer si l'exception a été gérée.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="predicate" /> a la valeur null.</exception>
      <exception cref="T:System.AggregateException">Une exception contenue dans ce <see cref="T:System.AggregateException" /> n'a pas été gérée.</exception>
    </member>
    <member name="P:System.AggregateException.InnerExceptions">
      <summary>Obtient une collection en lecture seule des instances <see cref="T:System.Exception" /> qui ont provoqué l'exception actuelle.</summary>
      <returns>Retourne une collection en lecture seule des instances <see cref="T:System.Exception" /> qui ont provoqué l'exception actuelle.</returns>
    </member>
    <member name="M:System.AggregateException.ToString">
      <summary>Crée et retourne une chaîne représentant le <see cref="T:System.AggregateException" /> en cours.</summary>
      <returns>Représentation sous forme de chaîne de l'exception en cours.</returns>
    </member>
    <member name="T:System.OperationCanceledException">
      <summary>Exception levée dans un thread lors de l'annulation d'une opération que le thread exécutait.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.OperationCanceledException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.OperationCanceledException" /> avec un message d'erreur système.</summary>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.OperationCanceledException" /> avec un message d'erreur spécifié.</summary>
      <param name="message">
        <see cref="T:System.String" /> qui décrit l'erreur.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.OperationCanceledException" /> avec un message d'erreur spécifié et une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="innerException">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="innerException" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception,System.Threading.CancellationToken)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.OperationCanceledException" /> avec un message d'erreur spécifié, une référence à l'exception interne qui est à l'origine de cette exception et un jeton d'annulation.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="innerException">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="innerException" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
      <param name="token">Jeton d'annulation associé à l'opération annulée.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Threading.CancellationToken)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.OperationCanceledException" /> avec un message d'erreur spécifié et un jeton d'annulation.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception.</param>
      <param name="token">Jeton d'annulation associé à l'opération annulée.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.Threading.CancellationToken)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.OperationCanceledException" /> avec un jeton d'annulation.</summary>
      <param name="token">Jeton d'annulation associé à l'opération annulée.</param>
    </member>
    <member name="P:System.OperationCanceledException.CancellationToken">
      <summary>Obtient un jeton associé à l'opération annulée.</summary>
      <returns>Jeton associé à l'opération annulée ou jeton par défaut.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder">
      <summary>Représente un concepteur pour les méthodes asynchrones qui retournent une tâche.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Planifie la machine à états de continuer à l'action suivante lorsque l'awaiter spécifié se termine.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Machine à états.</param>
      <typeparam name="TAwaiter">Type d'awaiter.</typeparam>
      <typeparam name="TStateMachine">Type de la machine à états.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Planifie la machine à états de continuer à l'action suivante lorsque l'awaiter spécifié se termine.Cette méthode peut être appelée depuis du code d'un niveau de confiance partiel.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Machine à états.</param>
      <typeparam name="TAwaiter">Type d'awaiter.</typeparam>
      <typeparam name="TStateMachine">Type de la machine à états.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Create">
      <summary>Crée une instance de la classe <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder" />.</summary>
      <returns>Nouvelle instance du générateur.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetException(System.Exception)">
      <summary>Marque la tâche comme ayant échoué et lie l'exception spécifiée à la tâche.</summary>
      <param name="exception">Exception à lier à la tâche.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">La tâche est déjà terminée.ouLe générateur n'est pas initialisé.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult">
      <summary>Marque que la tâche s'est déroulée correctement.</summary>
      <exception cref="T:System.InvalidOperationException">La tâche est déjà terminée.ouLe générateur n'est pas initialisé.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Associe le concepteur à la machine à états spécifiée.</summary>
      <param name="stateMachine">Instance de machine à états à associer au concepteur.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">La machine à états a été définie précédemment.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Start``1(``0@)">
      <summary>Commence l'exécution du générateur avec la machine à états associée.</summary>
      <param name="stateMachine">Instance de machine à états, passée par la référence.</param>
      <typeparam name="TStateMachine">Type de la machine à états.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> a la valeur null.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Task">
      <summary>Obtient la tâche pour ce générateur.</summary>
      <returns>Tâche pour ce générateur.</returns>
      <exception cref="T:System.InvalidOperationException">Le générateur n'est pas initialisé.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1">
      <summary>Représente un concepteur pour les méthodes asynchrones qui retourne une tâche et fournit un paramètre pour le résultat.</summary>
      <typeparam name="TResult">Résultat à utiliser pour effectuer la tâche.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Planifie la machine à états de continuer à l'action suivante lorsque l'awaiter spécifié se termine.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Machine à états.</param>
      <typeparam name="TAwaiter">Type d'awaiter.</typeparam>
      <typeparam name="TStateMachine">Type de la machine à états.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Planifie la machine à états de continuer à l'action suivante lorsque l'awaiter spécifié se termine.Cette méthode peut être appelée depuis du code d'un niveau de confiance partiel.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Machine à états.</param>
      <typeparam name="TAwaiter">Type d'awaiter.</typeparam>
      <typeparam name="TStateMachine">Type de la machine à états.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Create">
      <summary>Crée une instance de la classe <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1" />.</summary>
      <returns>Nouvelle instance du générateur.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetException(System.Exception)">
      <summary>Marque la tâche comme ayant échoué et lie l'exception spécifiée à la tâche.</summary>
      <param name="exception">Exception à lier à la tâche.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">La tâche est déjà terminée.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetResult(`0)">
      <summary>Marque que la tâche s'est déroulée correctement.</summary>
      <param name="result">Résultat à utiliser pour effectuer la tâche.</param>
      <exception cref="T:System.InvalidOperationException">La tâche est déjà terminée.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Associe le concepteur à la machine à états spécifiée.</summary>
      <param name="stateMachine">Instance de machine à états à associer au concepteur.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">La machine à états a été définie précédemment.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Start``1(``0@)">
      <summary>Commence l'exécution du générateur avec la machine à états associée.</summary>
      <param name="stateMachine">Instance de machine à états, passée par la référence.</param>
      <typeparam name="TStateMachine">Type de la machine à états.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> a la valeur null.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Task">
      <summary>Obtient la tâche pour ce générateur.</summary>
      <returns>Tâche pour ce générateur.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder">
      <summary>Représente un concepteur pour les méthodes asynchrones qui ne retournent aucune valeur.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Planifie la machine à états de continuer à l'action suivante lorsque l'awaiter spécifié se termine.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Machine à états.</param>
      <typeparam name="TAwaiter">Type d'awaiter.</typeparam>
      <typeparam name="TStateMachine">Type de la machine à états.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Planifie la machine à états de continuer à l'action suivante lorsque l'awaiter spécifié se termine.Cette méthode peut être appelée depuis du code d'un niveau de confiance partiel.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Machine à états.</param>
      <typeparam name="TAwaiter">Type d'awaiter.</typeparam>
      <typeparam name="TStateMachine">Type de la machine à états.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Create">
      <summary>Crée une instance de la classe <see cref="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder" />.</summary>
      <returns>Nouvelle instance du générateur.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetException(System.Exception)">
      <summary>Lie une exception au générateur de méthode.</summary>
      <param name="exception">Exception à lier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">Le générateur n'est pas initialisé.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetResult">
      <summary>Marque le générateur de méthode comme exécuté avec succès.</summary>
      <exception cref="T:System.InvalidOperationException">Le générateur n'est pas initialisé.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Associe le concepteur à la machine à états spécifiée.</summary>
      <param name="stateMachine">Instance de machine à états à associer au concepteur.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">La machine à états a été définie précédemment.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start``1(``0@)">
      <summary>Commence l'exécution du générateur avec la machine à états associée.</summary>
      <param name="stateMachine">Instance de machine à états, passée par la référence.</param>
      <typeparam name="TStateMachine">Type de la machine à états.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> a la valeur null.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable">
      <summary>Fournit un objet awaitable qui active les attentes configurées sur une tâche.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.GetAwaiter">
      <summary>Retourne un awaiter pour cet objet pouvant être attendu.</summary>
      <returns>Awaiter.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1">
      <summary>Fournit un objet awaitable qui active les attentes configurées sur une tâche.</summary>
      <typeparam name="TResult">Type du résultat produit par ce <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.GetAwaiter">
      <summary>Retourne un awaiter pour cet objet pouvant être attendu.</summary>
      <returns>Awaiter.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter">
      <summary>Fournit un awaiter pour un objet pouvant être attendu (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1" />).</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.GetResult">
      <summary>Met fin à l'attente sur la tâche terminée.</summary>
      <returns>Résultat de la tâche terminée.</returns>
      <exception cref="T:System.NullReferenceException">L'élément awaiter n'a pas été initialisé correctement.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">La tâche a été annulée.</exception>
      <exception cref="T:System.Exception">Tâche terminée avec un état d'erreur.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.IsCompleted">
      <summary>Obtient une valeur qui spécifie si la tâche attendue est terminée.</summary>
      <returns>true si la tâche attendue est terminée ; sinon, false.</returns>
      <exception cref="T:System.NullReferenceException">L'élément awaiter n'a pas été initialisé correctement.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>Planifie l'action de continuation pour la tâche associée à cet awaiter.</summary>
      <param name="continuation">Action à rappeler lorsque l'opération d'attente se termine.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="continuation" /> a la valeur null.</exception>
      <exception cref="T:System.NullReferenceException">L'élément awaiter n'a pas été initialisé correctement.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Planifie l'action de continuation pour la tâche associée à cet awaiter. </summary>
      <param name="continuation">Action à rappeler lorsque l'opération d'attente se termine.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="continuation" /> a la valeur null.</exception>
      <exception cref="T:System.NullReferenceException">L'élément awaiter n'a pas été initialisé correctement.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter">
      <summary>Fournit un awaiter pour un objet pouvant être attendu (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable" />).</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.GetResult">
      <summary>Met fin à l'attente sur la tâche terminée.</summary>
      <exception cref="T:System.NullReferenceException">L'élément awaiter n'a pas été initialisé correctement.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">La tâche a été annulée.</exception>
      <exception cref="T:System.Exception">Tâche terminée avec un état d'erreur.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.IsCompleted">
      <summary>Obtient une valeur qui spécifie si la tâche attendue est terminée.</summary>
      <returns>true si la tâche attendue est terminée ; sinon, false.</returns>
      <exception cref="T:System.NullReferenceException">L'élément awaiter n'a pas été initialisé correctement.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>Planifie l'action de continuation pour la tâche associée à cet awaiter.</summary>
      <param name="continuation">Action à rappeler lorsque l'opération d'attente se termine.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="continuation" /> a la valeur null.</exception>
      <exception cref="T:System.NullReferenceException">L'élément awaiter n'a pas été initialisé correctement.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Planifie l'action de continuation pour la tâche associée à cet awaiter. </summary>
      <param name="continuation">Action à rappeler lorsque l'opération d'attente se termine.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="continuation" /> a la valeur null.</exception>
      <exception cref="T:System.NullReferenceException">L'élément awaiter n'a pas été initialisé correctement.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.IAsyncStateMachine">
      <summary>Représente les machines à états générées pour les méthodes asynchrones.Ce type est exclusivement destiné aux compilateurs.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.MoveNext">
      <summary>Déplace la machine à états vers son prochain état.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Configure la machine à états avec un réplica alloué par tas.</summary>
      <param name="stateMachine">Réplica alloué par tas.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.ICriticalNotifyCompletion">
      <summary>Représente un awaiter qui planifie des suites lorsqu'une opération d'attente se termine.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ICriticalNotifyCompletion.UnsafeOnCompleted(System.Action)">
      <summary>Planifie l'action de continuation qui est appelée lorsque l'instance se termine.</summary>
      <param name="continuation">Action à appeler lorsque l'opération se termine.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="continuation" /> est null (Nothing en Visual Basic).</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.INotifyCompletion">
      <summary>Représente une opération qui planifie les suites lorsqu'elle se termine.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.INotifyCompletion.OnCompleted(System.Action)">
      <summary>Planifie l'action de continuation qui est appelée lorsque l'instance se termine.</summary>
      <param name="continuation">Action à appeler lorsque l'opération se termine.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="continuation" /> est null (Nothing en Visual Basic).</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter">
      <summary>Fournit un objet qui attend la fin d'une tâche asynchrone. </summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.GetResult">
      <summary>Met fin à l'attente de la fin de la tâche asynchrone.</summary>
      <exception cref="T:System.NullReferenceException">L'objet <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> n'a pas été initialisé correctement.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">La tâche a été annulée.</exception>
      <exception cref="T:System.Exception">Tâche terminée dans un état <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter.IsCompleted">
      <summary>Obtient une valeur qui indique si la tâche asynchrone est terminée.</summary>
      <returns>true si la tâche s'est terminée ; sinon, false.</returns>
      <exception cref="T:System.NullReferenceException">L'objet <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> n'a pas été initialisé correctement.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.OnCompleted(System.Action)">
      <summary>Définit l'action à exécuter lorsque l'objet <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> cesse d'attendre que la tâche asynchrone se termine.</summary>
      <param name="continuation">Action à exécuter quand l'opération d'attente se termine.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> a la valeur null.</exception>
      <exception cref="T:System.NullReferenceException">L'objet <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> n'a pas été initialisé correctement.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Planifie l'action de continuation pour la tâche asynchrone qui est associée à cet élément en attente.</summary>
      <param name="continuation">Action à appeler quand l'opération d'attente se termine.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">L'élément awaiter n'a pas été initialisé correctement.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter`1">
      <summary>Représente un objet qui attend la fin d'une tâche asynchrone et fournit un paramètre pour le résultat.</summary>
      <typeparam name="TResult">Résultat de la tâche.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.GetResult">
      <summary>Met fin à l'attente de la fin de la tâche asynchrone.</summary>
      <returns>Résultat de la tâche terminée.</returns>
      <exception cref="T:System.NullReferenceException">L'objet <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> n'a pas été initialisé correctement.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">La tâche a été annulée.</exception>
      <exception cref="T:System.Exception">Tâche terminée dans un état <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter`1.IsCompleted">
      <summary>Obtient une valeur qui indique si la tâche asynchrone est terminée.</summary>
      <returns>true si la tâche s'est terminée ; sinon, false.</returns>
      <exception cref="T:System.NullReferenceException">L'objet <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> n'a pas été initialisé correctement.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.OnCompleted(System.Action)">
      <summary>Définit l'action à exécuter lorsque l'objet <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> cesse d'attendre que la tâche asynchrone se termine.</summary>
      <param name="continuation">Action à exécuter quand l'opération d'attente se termine.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> a la valeur null.</exception>
      <exception cref="T:System.NullReferenceException">L'objet <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> n'a pas été initialisé correctement.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.UnsafeOnCompleted(System.Action)">
      <summary>Planifie l'action de continuation pour la tâche asynchrone associée à cet élément en attente.</summary>
      <param name="continuation">Action à appeler quand l'opération d'attente se termine.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">L'élément awaiter n'a pas été initialisé correctement.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable">
      <summary>Fournit le contexte pour l'attente lors d'un basculement asynchrone dans un environnement cible.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.GetAwaiter">
      <summary>Récupère un objet <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> pour cette instance de la classe.</summary>
      <returns>Objet utilisé pour suivre l'état d'achèvement de l'opération asynchrone.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter">
      <summary>Fournit un awaiter pour basculer dans un environnement cible.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.GetResult">
      <summary>Met fin à l'opération d'attente.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.IsCompleted">
      <summary>Obtient une valeur qui indique si aucune cession n'est requise.</summary>
      <returns>Toujours false, ce qui indique qu'une instruction yield est systématiquement requise pour <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" />.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.OnCompleted(System.Action)">
      <summary>Définit la continuation à appeler.</summary>
      <param name="continuation">Action à appeler de façon asynchrone.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Publie le paramètre <paramref name="continuation" /> dans le contexte actuel.</summary>
      <param name="continuation">Action à appeler de façon asynchrone.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="continuation" /> a la valeur null.</exception>
    </member>
    <member name="T:System.Threading.CancellationToken">
      <summary>Propage une notification selon laquelle les opérations doivent être annulées.</summary>
    </member>
    <member name="M:System.Threading.CancellationToken.#ctor(System.Boolean)">
      <summary>Initialise le <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="canceled">État d'annulation du jeton.</param>
    </member>
    <member name="P:System.Threading.CancellationToken.CanBeCanceled">
      <summary>Détermine si ce jeton peut être à l'état Annulé.</summary>
      <returns>True si ce jeton peut être à l'état Annulé ; sinon, false.</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Object)">
      <summary>Détermine si l'instance actuelle de <see cref="T:System.Threading.CancellationToken" /> est égale au <see cref="T:System.Object" /> spécifié.</summary>
      <returns>True si <paramref name="other" /> est un <see cref="T:System.Threading.CancellationToken" /> et si les deux instances sont égales ; sinon, false.Deux jetons sont égaux s'ils sont associés au même <see cref="T:System.Threading.CancellationTokenSource" /> ou s'ils ont tous les deux été construits à partir de constructeurs CancellationToken publics et que leurs valeurs <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> sont égales.</returns>
      <param name="other">Autre objet auquel comparer cette instance.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Threading.CancellationToken)">
      <summary>Détermine si l'instance actuelle de <see cref="T:System.Threading.CancellationToken" /> est égale au jeton spécifié.</summary>
      <returns>true si les instances sont égales ; sinon, false.Deux jetons sont égaux s'ils sont associés au même <see cref="T:System.Threading.CancellationTokenSource" /> ou s'ils ont tous les deux été construits à partir de constructeurs CancellationToken publics et que leurs valeurs <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> sont égales.</returns>
      <param name="other">Autre <see cref="T:System.Threading.CancellationToken" /> auquel comparer cette instance.</param>
    </member>
    <member name="M:System.Threading.CancellationToken.GetHashCode">
      <summary>Sert de fonction de hachage pour un <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Code de hachage pour l'instance actuelle de <see cref="T:System.Threading.CancellationToken" />.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.IsCancellationRequested">
      <summary>Détermine si l'annulation a été demandée pour ce jeton.</summary>
      <returns>True si l'annulation a été demandée pour ce jeton ; sinon, False.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.None">
      <summary>Retourne une valeur <see cref="T:System.Threading.CancellationToken" /> vide.</summary>
      <returns>Jeton d'annulation vide. </returns>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Equality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Détermine si deux instances <see cref="T:System.Threading.CancellationToken" /> sont égales.</summary>
      <returns>True si les instances sont égales ; sinon, False.</returns>
      <param name="left">Première instance.</param>
      <param name="right">Deuxième instance.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Inequality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Détermine si les deux instances de <see cref="T:System.Threading.CancellationToken" /> ne sont pas égales.</summary>
      <returns>True si les instances ne sont pas égales ; sinon, False.</returns>
      <param name="left">Première instance.</param>
      <param name="right">Deuxième instance.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action)">
      <summary>Inscrit un délégué qui sera appelé lors de l'annulation du <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Instance de <see cref="T:System.Threading.CancellationTokenRegistration" /> qui peut être utilisée pour annuler l'inscription du rappel.</returns>
      <param name="callback">Délégué à exécuter lorsque <see cref="T:System.Threading.CancellationToken" /> est annulé.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action,System.Boolean)">
      <summary>Inscrit un délégué qui sera appelé lors de l'annulation du <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Instance de <see cref="T:System.Threading.CancellationTokenRegistration" /> qui peut être utilisée pour annuler l'inscription du rappel.</returns>
      <param name="callback">Délégué à exécuter lorsque <see cref="T:System.Threading.CancellationToken" /> est annulé.</param>
      <param name="useSynchronizationContext">Valeur booléenne qui indique s'il faut capturer le <see cref="T:System.Threading.SynchronizationContext" /> actuel et l'utiliser lors de l'appel de <paramref name="callback" />.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object)">
      <summary>Inscrit un délégué qui sera appelé lors de l'annulation du <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Instance de <see cref="T:System.Threading.CancellationTokenRegistration" /> qui peut être utilisée pour annuler l'inscription du rappel.</returns>
      <param name="callback">Délégué à exécuter lorsque <see cref="T:System.Threading.CancellationToken" /> est annulé.</param>
      <param name="state">État à passer à <paramref name="callback" /> lorsque le délégué est appelé.Il peut s'agir d'une valeur null.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object,System.Boolean)">
      <summary>Inscrit un délégué qui sera appelé lors de l'annulation du <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Instance de <see cref="T:System.Threading.CancellationTokenRegistration" /> qui peut être utilisée pour annuler l'inscription du rappel.</returns>
      <param name="callback">Délégué à exécuter lorsque <see cref="T:System.Threading.CancellationToken" /> est annulé.</param>
      <param name="state">État à passer à <paramref name="callback" /> lorsque le délégué est appelé.Il peut s'agir d'une valeur null.</param>
      <param name="useSynchronizationContext">Valeur booléenne qui indique s'il faut capturer le <see cref="T:System.Threading.SynchronizationContext" /> actuel et l'utiliser lors de l'appel de <paramref name="callback" />.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.ThrowIfCancellationRequested">
      <summary>Lève une <see cref="T:System.OperationCanceledException" /> si l'annulation de ce jeton a été demandée.</summary>
      <exception cref="T:System.OperationCanceledException">The token has had cancellation requested.</exception>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.CancellationToken.WaitHandle">
      <summary>Obtient un <see cref="T:System.Threading.WaitHandle" /> qui est signalé lors de l'annulation du jeton.</summary>
      <returns>
        <see cref="T:System.Threading.WaitHandle" /> signalé lors de l'annulation du jeton.</returns>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.CancellationTokenRegistration">
      <summary>Représente un délégué de rappel inscrit avec un <see cref="T:System.Threading.CancellationToken" />. </summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Dispose">
      <summary>Libère toutes les ressources utilisées par l'instance actuelle de la classe <see cref="T:System.Threading.CancellationTokenRegistration" />.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Object)">
      <summary>Détermine si l'instance actuelle de <see cref="T:System.Threading.CancellationTokenRegistration" /> est égale à <see cref="T:System.Threading.CancellationTokenRegistration" /> spécifiée.</summary>
      <returns>True si cet objet et <paramref name="obj" /> sont égaux.False dans le cas contraire.Deux instances de <see cref="T:System.Threading.CancellationTokenRegistration" /> sont égales si elles font toutes les deux référence à la sortie d'un appel unique à la même méthode d'inscription d'un <see cref="T:System.Threading.CancellationToken" />.</returns>
      <param name="obj">Autre objet avec lequel comparer cette instance.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Threading.CancellationTokenRegistration)">
      <summary>Détermine si l'instance actuelle de <see cref="T:System.Threading.CancellationTokenRegistration" /> est égale à <see cref="T:System.Threading.CancellationTokenRegistration" /> spécifiée.</summary>
      <returns>True si cet objet et <paramref name="other" /> sont égaux.False dans le cas contraire. Deux instances de <see cref="T:System.Threading.CancellationTokenRegistration" /> sont égales si elles font toutes les deux référence à la sortie d'un appel unique à la même méthode d'inscription d'un <see cref="T:System.Threading.CancellationToken" />.</returns>
      <param name="other">Autre <see cref="T:System.Threading.CancellationTokenRegistration" /> avec lequel comparer cette instance.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.GetHashCode">
      <summary>Sert de fonction de hachage pour un <see cref="T:System.Threading.CancellationTokenRegistration" />.</summary>
      <returns>Code de hachage pour l'instance actuelle de <see cref="T:System.Threading.CancellationTokenRegistration" />.</returns>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Equality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>Détermine si les deux instances de <see cref="T:System.Threading.CancellationTokenRegistration" /> sont identiques.</summary>
      <returns>True si les instances sont égales ; sinon, false.</returns>
      <param name="left">Première instance.</param>
      <param name="right">Deuxième instance.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Inequality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>Détermine si les deux instances de <see cref="T:System.Threading.CancellationTokenRegistration" /> ne sont pas identiques.</summary>
      <returns>True si les instances ne sont pas égales ; sinon, False.</returns>
      <param name="left">Première instance.</param>
      <param name="right">Deuxième instance.</param>
    </member>
    <member name="T:System.Threading.CancellationTokenSource">
      <summary>Signale à un <see cref="T:System.Threading.CancellationToken" /> qu'il doit être annulé.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.CancellationTokenSource" /> qui sera annulée après le délai spécifié en millisecondes.</summary>
      <param name="millisecondsDelay">Intervalle de temps d'attente en millisecondes avant d'annuler cet élément <see cref="T:System.Threading.CancellationTokenSource" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsDelay" /> is less than -1. </exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.TimeSpan)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.CancellationTokenSource" /> qui sera annulée après l'intervalle de temps spécifié.</summary>
      <param name="delay">Intervalle de temps d'attente avant d'annuler cet élément <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" />.<see cref="P:System.TimeSpan.TotalMilliseconds" /> is less than -1 or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel">
      <summary>Transmet une demande d'annulation.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel(System.Boolean)">
      <summary>Transmet une demande d'annulation, et spécifie si les rappels restants et les opérations annulables doivent être traités.</summary>
      <param name="throwOnFirstException">true si les exceptions doivent se propager immédiatement ; sinon, false.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.Int32)">
      <summary>Planifie une opération d'annulation sur cette <see cref="T:System.Threading.CancellationTokenSource" /> après le nombre spécifié de millisecondes.</summary>
      <param name="millisecondsDelay">Intervalle de temps avant l'annulation de cette <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception thrown when <paramref name="millisecondsDelay" /> is less than -1.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.TimeSpan)">
      <summary>Planifie une opération d'annulation sur cette <see cref="T:System.Threading.CancellationTokenSource" /> après l'intervalle de temps spécifié.</summary>
      <param name="delay">Intervalle de temps avant l'annulation de cette <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when <paramref name="delay" /> is less than -1 or greater than Int32.MaxValue.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Crée une <see cref="T:System.Threading.CancellationTokenSource" /> qui est à l'état annulé quand l'un des jetons source est à l'état annulé.</summary>
      <returns>
        <see cref="T:System.Threading.CancellationTokenSource" /> qui est liée aux jetons sources.</returns>
      <param name="token1">Premier jeton d'annulation à observer.</param>
      <param name="token2">Second jeton d'annulation à observer.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken[])">
      <summary>Crée une <see cref="T:System.Threading.CancellationTokenSource" /> qui est à l'état « annulé » quand l'un des jetons sources du tableau spécifié est à l'état « annulé ».</summary>
      <returns>
        <see cref="T:System.Threading.CancellationTokenSource" /> qui est liée aux jetons sources.</returns>
      <param name="tokens">Tableau qui contient les instances de jeton d'annulation à observer.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tokens" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tokens" /> is empty.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose">
      <summary>Libère toutes les ressources utilisées par l'instance actuelle de la classe <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par la classe <see cref="T:System.Threading.CancellationTokenSource" /> et libère éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour ne libérer que les ressources non managées.</param>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.IsCancellationRequested">
      <summary>Détermine si l'annulation a été demandée pour cette <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
      <returns>true si l'annulation a été demandée pour cette <see cref="T:System.Threading.CancellationTokenSource" /> ; sinon, false.</returns>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.Token">
      <summary>Obtient l'<see cref="T:System.Threading.CancellationToken" /> associée à cette <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
      <returns>
        <see cref="T:System.Threading.CancellationToken" /> associé à ce <see cref="T:System.Threading.CancellationTokenSource" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The token source has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair">
      <summary>Fournit les planificateurs de tâches qui se coordonnent pour exécuter les tâches tout en garantissant que les tâches simultanées peuvent s'exécuter simultanément et que les tâches exclusives ne s'exécutent jamais simultanément.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> qui cible le planificateur spécifié.</summary>
      <param name="taskScheduler">Planificateur cible sur lequel cette paire doit s'exécuter.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> qui cible le planificateur spécifié avec un niveau de concurrence maximal.</summary>
      <param name="taskScheduler">Planificateur cible sur lequel cette paire doit s'exécuter.</param>
      <param name="maxConcurrencyLevel">Nombre maximal de tâches à exécuter simultanément.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> qui cible le planificateur spécifié avec un niveau de concurrence maximal et un nombre maximal de tâches planifiées qui peuvent être traités en tant qu'unité.</summary>
      <param name="taskScheduler">Planificateur cible sur lequel cette paire doit s'exécuter.</param>
      <param name="maxConcurrencyLevel">Nombre maximal de tâches à exécuter simultanément.</param>
      <param name="maxItemsPerTask">Nombre maximal de tâches à traiter pour chaque tâche planifiée sous-jacente utilisée par la paire.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Complete">
      <summary>Informe la paire de planificateur qu'elle ne doit plus accepter de tâches.</summary>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Completion">
      <summary>Obtient une <see cref="T:System.Threading.Tasks.Task" /> qui ne se termine lorsque le planificateur est achevé.</summary>
      <returns>Opération asynchrone qui se termine lorsque le planificateur termine le traitement.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ConcurrentScheduler">
      <summary>Obtient un <see cref="T:System.Threading.Tasks.TaskScheduler" /> qui peut être utilisé pour planifier des tâches à cette paire qui peut s'exécuter simultanément avec d'autres tâches sur cette paire.</summary>
      <returns>Objet qui peut être utilisé pour planifier des tâches simultanément.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ExclusiveScheduler">
      <summary>Obtient un <see cref="T:System.Threading.Tasks.TaskScheduler" /> qui peut être utilisé pour planifier des tâches à cette paire qui doit s'exécuter de manière exclusive par rapport à d'autres tâches sur cette paire.</summary>
      <returns>Objet qui peut être utilisé pour planifier les tâches qui ne sont pas exécutés simultanément avec d'autres tâches.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task">
      <summary>Représente une opération asynchrone.Pour parcourir le code source de .NET Framework pour ce type, consultez la Source de référence.</summary>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action)">
      <summary>Initialise un nouveau <see cref="T:System.Threading.Tasks.Task" /> avec l'action spécifiée.</summary>
      <param name="action">Délégué qui représente le code à exécuter dans la tâche.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken)">
      <summary>Initialise un nouveau <see cref="T:System.Threading.Tasks.Task" /> avec l'action spécifiée et <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="action">Délégué qui représente le code à exécuter dans la tâche.</param>
      <param name="cancellationToken">Le <see cref="T:System.Threading.CancellationToken" /> que la nouvelle tâche observera.</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task" /> avec l'action et les options de création spécifiées.</summary>
      <param name="action">Délégué qui représente le code à exécuter dans la tâche.</param>
      <param name="cancellationToken">Le <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que la nouvelle tâche observera.</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> utilisé pour personnaliser le comportement de la tâche.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task" /> avec l'action et les options de création spécifiées.</summary>
      <param name="action">Délégué qui représente le code à exécuter dans la tâche.</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> utilisé pour personnaliser le comportement de la tâche. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object)">
      <summary>Initialise un nouveau <see cref="T:System.Threading.Tasks.Task" /> avec l'action et l'état spécifiés.</summary>
      <param name="action">Délégué qui représente le code à exécuter dans la tâche.</param>
      <param name="state">Objet représentant les données que l'action doit utiliser.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task" /> avec l'action, l'état et les options spécifiés.</summary>
      <param name="action">Délégué qui représente le code à exécuter dans la tâche.</param>
      <param name="state">Objet représentant les données que l'action doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que la nouvelle tâche observera.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task" /> avec l'action, l'état et les options spécifiés.</summary>
      <param name="action">Délégué qui représente le code à exécuter dans la tâche.</param>
      <param name="state">Objet représentant les données que l'action doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que la nouvelle tâche observera.</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> utilisé pour personnaliser le comportement de la tâche.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task" /> avec l'action, l'état et les options spécifiés.</summary>
      <param name="action">Délégué qui représente le code à exécuter dans la tâche.</param>
      <param name="state">Objet représentant les données que l'action doit utiliser.</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> utilisé pour personnaliser le comportement de la tâche.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.AsyncState">
      <summary>Obtient l'objet d'état fourni quand la <see cref="T:System.Threading.Tasks.Task" /> a été créée, ou null si aucune n'a été fournie.</summary>
      <returns>
        <see cref="T:System.Object" /> représentant les données d'état passées à la tâche lors de sa création.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CompletedTask">
      <summary>Obtient une tâche qui s'est déjà terminée correctement. </summary>
      <returns>Tâche terminée avec succès. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.ConfigureAwait(System.Boolean)">
      <summary>Configure un élément awaiter utilisé pour attendre cette <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Objet utilisé pour attendre cette tâche.</returns>
      <param name="continueOnCapturedContext">true en cas de tentative de marshaling de la continuation vers le contexte d'origine capturé ; sinon, false.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand la <see cref="T:System.Threading.Tasks.Task" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Crée une continuation qui reçoit un jeton d'annulation et s'exécute de façon asynchrone quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute quand la tâche cible se termine conformément au <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> spécifié.La continuation reçoit un jeton d'annulation et utilise un planificateur spécifié.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter selon le <paramref name="continuationOptions" />spécifié.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une continuation qui s'exécute quand la tâche cible se termine conformément au <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> spécifié.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter selon le <paramref name="continuationOptions" />spécifié.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand la <see cref="T:System.Threading.Tasks.Task" /> cible se termine.La continuation utilise un planificateur spécifié.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. -or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object)">
      <summary>Crée une continuation qui reçoit des informations d'état fournies par l'appelant et s'exécute quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine. </summary>
      <returns>Nouvelle tâche de continuation. </returns>
      <param name="continuationAction">Action à exécuter quand la tâche se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés comme arguments au délégué.</param>
      <param name="state">Objet représentant les données que l'action de continuation doit utiliser. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Crée une continuation qui reçoit des informations d'état fournies par l'appelant et un jeton d'annulation, et qui s'exécute de façon asynchrone quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que l'action de continuation doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui reçoit des informations d'état fournies par l'appelant et un jeton d'annulation, et qui s'exécute quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine.La continuation s'exécute selon un ensemble de conditions spécifiées et utilise un planificateur spécifié.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que l'action de continuation doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une continuation qui reçoit des informations d'état fournies par l'appelant et s'exécute quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine.La continuation s'exécute selon un ensemble de conditions spécifiées.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que l'action de continuation doit utiliser.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui reçoit des informations d'état fournies par l'appelant et s'exécute de façon asynchrone quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine.La continuation utilise un planificateur spécifié.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que l'action de continuation doit utiliser.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0})">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand le <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine et retourne une valeur. </summary>
      <returns>Nouvelle tâche de continuation. </returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <typeparam name="TResult"> Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine et retourne une valeur.La continuation reçoit un jeton d'annulation.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <typeparam name="TResult"> Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute en fonction des options de continuation spécifiées et retourne une valeur.La continuation reçoit un jeton d'annulation et utilise un planificateur spécifié.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter selon le <paramref name="continuationOptions." /> spécifié. Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <typeparam name="TResult"> Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une continuation qui s'exécute en fonction des options de continuation spécifiées et retourne une valeur. </summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter selon la condition spécifiée dans <paramref name="continuationOptions" />.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TResult"> Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine et retourne une valeur.La continuation utilise un planificateur spécifié.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <typeparam name="TResult"> Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object)">
      <summary>Crée une continuation qui reçoit des informations d'état fournies par l'appelant et s'exécute de façon asynchrone quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine et retourne une valeur. </summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que la fonction de continuation doit utiliser.</param>
      <typeparam name="TResult">Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine et retourne une valeur.La continuation reçoit des informations d'état fournies par l'appelant et un jeton d'annulation.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que la fonction de continuation doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <typeparam name="TResult">Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute en fonction des options de continuation de tâche spécifiées quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine et retourne une valeur.La continuation reçoit des informations d'état fournies par l'appelant et un jeton d'annulation et elle utilise un planificateur spécifié.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que la fonction de continuation doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <typeparam name="TResult">Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une continuation qui s'exécute en fonction des options de continuation de tâche spécifiées quand le <see cref="T:System.Threading.Tasks.Task" /> cible se termine et retourne une valeur.La continuation reçoit des informations d'état fournies par l'appelant.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que la fonction de continuation doit utiliser.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TResult">Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand la <see cref="T:System.Threading.Tasks.Task" /> cible se termine.La continuation reçoit des informations d'état fournies par l'appelant et utilise un planificateur spécifié.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que la fonction de continuation doit utiliser.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <typeparam name="TResult">Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.CreationOptions">
      <summary>Obtient les <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> utilisées pour créer cette tâche.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> utilisées pour créer cette tâche.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CurrentId">
      <summary>Retourne l'ID unique de la <see cref="T:System.Threading.Tasks.Task" /> en cours d'exécution.</summary>
      <returns>Entier assigné par le système à la tâche en cours d'exécution.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32)">
      <summary>Crée une tâche qui se termine après un certain délai. </summary>
      <returns>Tâche qui représente le délai. </returns>
      <param name="millisecondsDelay">Nombre de millisecondes à attendre avant la fin de la tâche retournée ou -1 pour attendre indéfiniment. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32,System.Threading.CancellationToken)">
      <summary>Crée une tâche pouvant être annulée qui se termine après un certain délai. </summary>
      <returns>Tâche qui représente le délai. </returns>
      <param name="millisecondsDelay">Nombre de millisecondes à attendre avant la fin de la tâche retournée ou -1 pour attendre indéfiniment. </param>
      <param name="cancellationToken">Jeton d'annulation qui sera vérifié avant de terminer la tâche retournée. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled. </exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan)">
      <summary>Crée une tâche qui se termine après un intervalle de temps spécifié. </summary>
      <returns>Tâche qui représente le délai. </returns>
      <param name="delay">Intervalle de temps à attendre avant la fin de la tâche retournée ou TimeSpan.FromMilliseconds(-1) pour attendre indéfiniment. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Crée une tâche pouvant être annulée qui se termine après un intervalle de temps spécifié. </summary>
      <returns>Tâche qui représente le délai. </returns>
      <param name="delay">Intervalle de temps à attendre avant la fin de la tâche retournée ou TimeSpan.FromMilliseconds(-1) pour attendre indéfiniment. </param>
      <param name="cancellationToken">Jeton d'annulation qui sera vérifié avant de terminer la tâche retournée. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Exception">
      <summary>Obtient le <see cref="T:System.AggregateException" /> qui a provoqué l'arrêt prématuré de <see cref="T:System.Threading.Tasks.Task" />.Si la <see cref="T:System.Threading.Tasks.Task" /> s'est terminée avec succès ou n'a pas encore levé d'exception, la valeur null est retournée.</summary>
      <returns>
        <see cref="T:System.AggregateException" /> qui a provoqué la fin prématurée de <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Factory">
      <summary>Fournit l'accès aux méthodes de fabrique pour la création d'instances de <see cref="T:System.Threading.Tasks.Task" /> et de <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objet de fabrique pouvant créer une variété d'objets <see cref="T:System.Threading.Tasks.Task" /> et <see cref="T:System.Threading.Tasks.Task`1" />. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled(System.Threading.CancellationToken)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> qui s'est terminée en raison de l'annulation avec un jeton d'annulation spécifié.</summary>
      <returns>Tâche annulée. </returns>
      <param name="cancellationToken">Jeton d'annulation avec lequel terminer la tâche. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled``1(System.Threading.CancellationToken)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> qui s'est terminée en raison de l'annulation avec un jeton d'annulation spécifié.</summary>
      <returns>Tâche annulée. </returns>
      <param name="cancellationToken">Jeton d'annulation avec lequel terminer la tâche. </param>
      <typeparam name="TResult">Type du résultat retourné par la tâche. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException``1(System.Exception)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> qui s'est terminée avec une exception spécifiée. </summary>
      <returns>Tâche ayant échoué. </returns>
      <param name="exception">Exception avec laquelle terminer la tâche. </param>
      <typeparam name="TResult">Type du résultat retourné par la tâche. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException(System.Exception)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> qui s'est terminée avec une exception spécifiée. </summary>
      <returns>Tâche ayant échoué. </returns>
      <param name="exception">Exception avec laquelle terminer la tâche. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromResult``1(``0)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> qui s'est terminée correctement avec le résultat spécifié.</summary>
      <returns>Tâche terminée avec succès.</returns>
      <param name="result">Résultat à enregistrer dans la tâche terminée. </param>
      <typeparam name="TResult">Type du résultat retourné par la tâche. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.GetAwaiter">
      <summary>Obtient un élément awaiter utilisé pour attendre cette <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Instance d'élément awaiter.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Id">
      <summary>Obtient un ID unique pour cette instance de <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Entier assigné par le système à cette instance de tâche. </returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCanceled">
      <summary>Indique si cette instance de <see cref="T:System.Threading.Tasks.Task" /> s'est exécutée avec succès suite à une annulation.</summary>
      <returns>true si la tâche s'est terminée suite à son annulation ; sinon false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCompleted">
      <summary>Indique si cette <see cref="T:System.Threading.Tasks.Task" /> s'est terminée.</summary>
      <returns>true si la tâche s'est terminée ; sinon, false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsFaulted">
      <summary>Indique si la <see cref="T:System.Threading.Tasks.Task" /> s'est terminée suite à une exception non gérée.</summary>
      <returns>true si la tâche a levé une exception non gérée ; sinon, false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action)">
      <summary>Met en file d'attente le travail spécifié à exécuter dans le ThreadPool et retourne un handle de tâche pour ce travail.</summary>
      <returns>Tâche qui représente le travail mis en file d'attente à exécuter dans le ThreadPool.</returns>
      <param name="action">Travail à exécuter de façon asynchrone</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action,System.Threading.CancellationToken)">
      <summary>Met en file d'attente le travail spécifié à exécuter dans le ThreadPool et retourne un handle de tâche pour ce travail.</summary>
      <returns>Tâche qui représente le travail mis en file d'attente à exécuter dans le ThreadPool.</returns>
      <param name="action">Travail à exécuter de façon asynchrone</param>
      <param name="cancellationToken">Jeton d'annulation qui doit être utilisé pour annuler le travail</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
      <summary>Met en file d'attente le travail spécifié à exécuter dans le ThreadPool et retourne un proxy pour la Task(TResult) retournée par <paramref name="function" />.</summary>
      <returns>Task(TResult) qui représente un proxy de la Task(TResult) retournée par <paramref name="function" />.</returns>
      <param name="function">Travail à exécuter de façon asynchrone</param>
      <typeparam name="TResult">Type du résultat retourné par la tâche de proxy.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>Met en file d'attente le travail spécifié à exécuter dans le ThreadPool et retourne un proxy pour la Task(TResult) retournée par <paramref name="function" />.</summary>
      <returns>Task(TResult) qui représente un proxy de la Task(TResult) retournée par <paramref name="function" />.</returns>
      <param name="function">Travail à exécuter de façon asynchrone</param>
      <param name="cancellationToken">Jeton d'annulation qui doit être utilisé pour annuler le travail</param>
      <typeparam name="TResult">Type du résultat retourné par la tâche de proxy.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task})">
      <summary>Met en file d'attente le travail spécifié à exécuter dans le ThreadPool et retourne un proxy pour la tâche retournée par <paramref name="function" />.</summary>
      <returns>Tâche qui représente un proxy de la tâche retournée par <paramref name="function" />.</returns>
      <param name="function">Travail à exécuter de façon asynchrone</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Met en file d'attente le travail spécifié à exécuter dans le ThreadPool et retourne un proxy pour la tâche retournée par <paramref name="function" />.</summary>
      <returns>Tâche qui représente un proxy de la tâche retournée par <paramref name="function" />.</returns>
      <param name="function">Travail à exécuter de façon asynchrone. </param>
      <param name="cancellationToken">Jeton d'annulation qui doit être utilisé pour annuler le travail. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0})">
      <summary>Met en file d'attente le travail spécifié à exécuter dans le pool de threads et retourne un objet <see cref="T:System.Threading.Tasks.Task`1" /> qui représente ce travail. </summary>
      <returns>Objet de tâche qui représente le travail mis en file d'attente à exécuter dans le pool de threads. </returns>
      <param name="function">Travail à exécuter de façon asynchrone. </param>
      <typeparam name="TResult">Type de retour de la tâche. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>Met en file d'attente le travail spécifié à exécuter dans le pool de threads et retourne un handle de Task(TResult) pour ce travail.</summary>
      <returns>Task(TResult) qui représente le travail mis en file d'attente à exécuter dans le ThreadPool.</returns>
      <param name="function">Travail à exécuter de façon asynchrone</param>
      <param name="cancellationToken">Jeton d'annulation qui doit être utilisé pour annuler le travail</param>
      <typeparam name="TResult">Type de résultat de la tâche.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously">
      <summary>Exécute de façon synchrone la <see cref="T:System.Threading.Tasks.Task" /> sur le <see cref="T:System.Threading.Tasks.TaskScheduler" /> actuel.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously(System.Threading.Tasks.TaskScheduler)">
      <summary>Exécute de façon synchrone le <see cref="T:System.Threading.Tasks.Task" /> sur le <see cref="T:System.Threading.Tasks.TaskScheduler" /> fourni.</summary>
      <param name="scheduler">Planificateur sur lequel tenter d'exécuter cette tâche inline.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start">
      <summary>Démarre la <see cref="T:System.Threading.Tasks.Task" />, en planifiant son exécution selon le <see cref="T:System.Threading.Tasks.TaskScheduler" /> actuel.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start(System.Threading.Tasks.TaskScheduler)">
      <summary>Démarre la <see cref="T:System.Threading.Tasks.Task" />, en planifiant son exécution sur le <see cref="T:System.Threading.Tasks.TaskScheduler" /> spécifié.</summary>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> avec lequel associer et exécuter cette tâche.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Status">
      <summary>Obtient le <see cref="T:System.Threading.Tasks.TaskStatus" /> de cette tâche.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.TaskStatus" /> actuel de cette instance de tâche.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#AsyncWaitHandle">
      <summary>Obtient un <see cref="T:System.Threading.WaitHandle" /> qui peut être utilisé en attendant la fin de la tâche.</summary>
      <returns>
        <see cref="T:System.Threading.WaitHandle" /> qui peut être utilisé pour attendre la fin de la tâche.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#CompletedSynchronously">
      <summary>Obtient une indication précisant si l'opération s'est terminée de manière synchrone.</summary>
      <returns>true si l'opération s'est terminée de manière synchrone ; sinon, false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait">
      <summary>Attend la fin de l'exécution de <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32)">
      <summary>Attend la fin de l'exécution de la <see cref="T:System.Threading.Tasks.Task" /> en un nombre de millisecondes spécifié.</summary>
      <returns>true si l'exécution de <see cref="T:System.Threading.Tasks.Task" /> s'est terminée dans le délai imparti ; sinon, false.</returns>
      <param name="millisecondsTimeout">Nombre de millisecondes à attendre, ou <see cref="F:System.Threading.Timeout.Infinite" /> (-1) pour un délai d'attente infini.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Attend la fin de l'exécution de <see cref="T:System.Threading.Tasks.Task" />.L'attente se termine si un intervalle de délai est écoulé ou si un jeton d'annulation est annulé avant la fin de la tâche.</summary>
      <returns>true si l'exécution de <see cref="T:System.Threading.Tasks.Task" /> s'est terminée dans le délai imparti ; sinon, false.</returns>
      <param name="millisecondsTimeout">Nombre de millisecondes à attendre, ou <see cref="F:System.Threading.Timeout.Infinite" /> (-1) pour un délai d'attente infini. </param>
      <param name="cancellationToken">Jeton d'annulation à observer en attendant que la tâche se termine. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Threading.CancellationToken)">
      <summary>Attend la fin de l'exécution de <see cref="T:System.Threading.Tasks.Task" />.L'attente se termine si un jeton d'annulation est annulé avant la fin de la tâche.</summary>
      <param name="cancellationToken">Jeton d'annulation à observer en attendant que la tâche se termine. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The task has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.TimeSpan)">
      <summary>Attend la fin de l'exécution de la <see cref="T:System.Threading.Tasks.Task" /> dans un intervalle de temps spécifié.</summary>
      <returns>true si l'exécution de <see cref="T:System.Threading.Tasks.Task" /> s'est terminée dans le délai imparti ; sinon, false.</returns>
      <param name="timeout">
        <see cref="T:System.TimeSpan" /> qui représente le nombre de millisecondes à attendre ou <see cref="T:System.TimeSpan" /> qui représente -1 milliseconde de seconde, pour attendre indéfiniment.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[])">
      <summary>Attend la fin de l'exécution de tous les objets <see cref="T:System.Threading.Tasks.Task" /> fournis.</summary>
      <param name="tasks">Tableau d'instances de <see cref="T:System.Threading.Tasks.Task" /> sur lesquelles attendre.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.-or-The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> exception contains an <see cref="T:System.OperationCanceledException" /> exception in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32)">
      <summary>Attend la fin de l'exécution de tous les objets <see cref="T:System.Threading.Tasks.Task" /> fournis en un nombre de millisecondes spécifié.</summary>
      <returns>true si l'exécution de toutes les instances <see cref="T:System.Threading.Tasks.Task" /> s'est terminée dans le délai imparti ; sinon, false.</returns>
      <param name="tasks">Tableau d'instances de <see cref="T:System.Threading.Tasks.Task" /> sur lesquelles attendre.</param>
      <param name="millisecondsTimeout">Nombre de millisecondes à attendre, ou <see cref="F:System.Threading.Timeout.Infinite" /> (-1) pour un délai d'attente infini.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>Attend la fin de l'exécution de tous les objets <see cref="T:System.Threading.Tasks.Task" /> fournis en un nombre de millisecondes spécifié ou jusqu'à ce que l'attente soit annulée.</summary>
      <returns>true si l'exécution de toutes les instances <see cref="T:System.Threading.Tasks.Task" /> s'est terminée dans le délai imparti ; sinon, false.</returns>
      <param name="tasks">Tableau d'instances de <see cref="T:System.Threading.Tasks.Task" /> sur lesquelles attendre.</param>
      <param name="millisecondsTimeout">Nombre de millisecondes à attendre, ou <see cref="F:System.Threading.Timeout.Infinite" /> (-1) pour un délai d'attente infini.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> à observer en attendant que les tâches se terminent.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>Attend la fin de l'exécution de tous les objets <see cref="T:System.Threading.Tasks.Task" /> fournis sauf si l'attente est annulée. </summary>
      <param name="tasks">Tableau d'instances de <see cref="T:System.Threading.Tasks.Task" /> sur lesquelles attendre.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> à observer en attendant que les tâches se terminent.</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>Attend la fin de l'exécution de tous les objets <see cref="T:System.Threading.Tasks.Task" /> pouvant être annulés fournis dans un intervalle de temps spécifié.</summary>
      <returns>true si l'exécution de toutes les instances <see cref="T:System.Threading.Tasks.Task" /> s'est terminée dans le délai imparti ; sinon, false.</returns>
      <param name="tasks">Tableau d'instances de <see cref="T:System.Threading.Tasks.Task" /> sur lesquelles attendre.</param>
      <param name="timeout">
        <see cref="T:System.TimeSpan" /> qui représente le nombre de millisecondes à attendre ou <see cref="T:System.TimeSpan" /> qui représente -1 milliseconde de seconde, pour attendre indéfiniment.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null. </exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[])">
      <summary>Attend la fin de l'exécution de l'un des objets <see cref="T:System.Threading.Tasks.Task" /> fournis.</summary>
      <returns>Index de la tâche achevée dans l'argument de tableau <paramref name="tasks" />.</returns>
      <param name="tasks">Tableau d'instances de <see cref="T:System.Threading.Tasks.Task" /> sur lesquelles attendre.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32)">
      <summary>Attend la fin de l'exécution de l'un des objets <see cref="T:System.Threading.Tasks.Task" /> fournis en un nombre de millisecondes spécifié.</summary>
      <returns>Index de la tâche achevée dans l'argument de tableau <paramref name="tasks" /> ou -1, si le délai a expiré.</returns>
      <param name="tasks">Tableau d'instances de <see cref="T:System.Threading.Tasks.Task" /> sur lesquelles attendre.</param>
      <param name="millisecondsTimeout">Nombre de millisecondes à attendre, ou <see cref="F:System.Threading.Timeout.Infinite" /> (-1) pour un délai d'attente infini.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>Attend la fin de l'exécution de l'un des objets <see cref="T:System.Threading.Tasks.Task" /> fournis en un nombre de millisecondes spécifié ou jusqu'à ce qu'un jeton d'annulation soit annulé.</summary>
      <returns>Index de la tâche achevée dans l'argument de tableau <paramref name="tasks" /> ou -1, si le délai a expiré.</returns>
      <param name="tasks">Tableau d'instances de <see cref="T:System.Threading.Tasks.Task" /> sur lesquelles attendre. </param>
      <param name="millisecondsTimeout">Nombre de millisecondes à attendre, ou <see cref="F:System.Threading.Timeout.Infinite" /> (-1) pour un délai d'attente infini. </param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> à observer en attendant qu'une tâche se termine. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>Attend la fin de l'exécution de l'un des objets <see cref="T:System.Threading.Tasks.Task" /> fournis sauf si l'attente est annulée.</summary>
      <returns>Index de la tâche achevée dans l'argument de tableau <paramref name="tasks" />.</returns>
      <param name="tasks">Tableau d'instances de <see cref="T:System.Threading.Tasks.Task" /> sur lesquelles attendre. </param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> à observer en attendant qu'une tâche se termine. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>Attend la fin de l'exécution de n'importe lequel des objets <see cref="T:System.Threading.Tasks.Task" /> fournis dans un intervalle de temps spécifié.</summary>
      <returns>Index de la tâche achevée dans l'argument de tableau <paramref name="tasks" /> ou -1, si le délai a expiré.</returns>
      <param name="tasks">Tableau d'instances de <see cref="T:System.Threading.Tasks.Task" /> sur lesquelles attendre.</param>
      <param name="timeout">
        <see cref="T:System.TimeSpan" /> qui représente le nombre de millisecondes à attendre ou <see cref="T:System.TimeSpan" /> qui représente -1 milliseconde de seconde, pour attendre indéfiniment.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>Crée une tâche qui s'achève quand tous les objets <see cref="T:System.Threading.Tasks.Task`1" /> d'une collection énumérable sont terminés. </summary>
      <returns>Tâche qui représente l'achèvement de toutes les tâches fournies. </returns>
      <param name="tasks">Tâches restantes à exécuter avant la fin. </param>
      <typeparam name="TResult">Type de la tâche terminée. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>Crée une tâche qui s'achève quand tous les objets <see cref="T:System.Threading.Tasks.Task" /> d'une collection énumérable sont terminés.</summary>
      <returns>Tâche qui représente l'achèvement de toutes les tâches fournies. </returns>
      <param name="tasks">Tâches restantes à exécuter avant la fin.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Threading.Tasks.Task[])">
      <summary>Crée une tâche qui s'achève quand tous les objets <see cref="T:System.Threading.Tasks.Task" /> d'un tableau sont terminés. </summary>
      <returns>Tâche qui représente l'achèvement de toutes les tâches fournies.</returns>
      <param name="tasks">Tâches restantes à exécuter avant la fin.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Threading.Tasks.Task{``0}[])">
      <summary>Crée une tâche qui s'achève quand tous les objets <see cref="T:System.Threading.Tasks.Task`1" /> d'un tableau sont terminés. </summary>
      <returns>Tâche qui représente l'achèvement de toutes les tâches fournies.</returns>
      <param name="tasks">Tâches restantes à exécuter avant la fin.</param>
      <typeparam name="TResult">Type de la tâche terminée.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>Crée une tâche qui s'achève quand l'une des tâches fournies est terminée.</summary>
      <returns>Tâche qui représente l'achèvement de l'une des tâches fournies.Le résultat de la tâche retour est la tâche qui s'est terminée.</returns>
      <param name="tasks">Tâches restantes à exécuter avant la fin.</param>
      <typeparam name="TResult">Type de la tâche terminée.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>Crée une tâche qui s'achève quand l'une des tâches fournies est terminée.</summary>
      <returns>Tâche qui représente l'achèvement de l'une des tâches fournies.Le résultat de la tâche retour est la tâche qui s'est terminée.</returns>
      <param name="tasks">Tâches restantes à exécuter avant la fin.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Threading.Tasks.Task[])">
      <summary>Crée une tâche qui s'achève quand l'une des tâches fournies est terminée.</summary>
      <returns>Tâche qui représente l'achèvement de l'une des tâches fournies.Le résultat de la tâche retour est la tâche qui s'est terminée.</returns>
      <param name="tasks">Tâches restantes à exécuter avant la fin.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Threading.Tasks.Task{``0}[])">
      <summary>Crée une tâche qui s'achève quand l'une des tâches fournies est terminée.</summary>
      <returns>Tâche qui représente l'achèvement de l'une des tâches fournies.Le résultat de la tâche retour est la tâche qui s'est terminée.</returns>
      <param name="tasks">Tâches restantes à exécuter avant la fin.</param>
      <typeparam name="TResult">Type de la tâche terminée.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Yield">
      <summary>Crée une tâche pouvant être attendue qui se produit de manière asynchrone dans le contexte actuel pendant l'attente.</summary>
      <returns>Contexte qui, quand il est attendu, reviendra de façon asynchrone au contexte qui était le sien au moment de l'attente.Si le <see cref="T:System.Threading.SynchronizationContext" /> actuel n'a pas la valeur null, il est traité comme le contexte actuel.Sinon, le Planificateur de tâches associé à la tâche en cours d'exécution est traité comme le contexte actuel.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task`1">
      <summary>Représente une opération asynchrone qui peut retourner une valeur.</summary>
      <typeparam name="TResult">Type du résultat produit par ce <see cref="T:System.Threading.Tasks.Task`1" />. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0})">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> avec la fonction spécifiée.</summary>
      <param name="function">Délégué qui représente le code à exécuter dans la tâche.Quand cette fonction aura terminé, la propriété <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tâche sera configurée pour retourner la valeur de résultat de la fonction.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> avec la fonction spécifiée.</summary>
      <param name="function">Délégué qui représente le code à exécuter dans la tâche.Quand cette fonction aura terminé, la propriété <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tâche sera configurée pour retourner la valeur de résultat de la fonction.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> à assigner à cette tâche.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> avec la fonction et les options de création spécifiées.</summary>
      <param name="function">Délégué qui représente le code à exécuter dans la tâche.Quand cette fonction aura terminé, la propriété <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tâche sera configurée pour retourner la valeur de résultat de la fonction.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche.</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> utilisé pour personnaliser le comportement de la tâche.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> avec la fonction et les options de création spécifiées.</summary>
      <param name="function">Délégué qui représente le code à exécuter dans la tâche.Quand cette fonction aura terminé, la propriété <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tâche sera configurée pour retourner la valeur de résultat de la fonction.</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> utilisé pour personnaliser le comportement de la tâche.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object)">
      <summary>Initialise un nouveau <see cref="T:System.Threading.Tasks.Task`1" /> avec la fonction et l'état spécifiés.</summary>
      <param name="function">Délégué qui représente le code à exécuter dans la tâche.Quand cette fonction aura terminé, la propriété <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tâche sera configurée pour retourner la valeur de résultat de la fonction.</param>
      <param name="state">Objet représentant les données que l'action doit utiliser.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> avec l'action, l'état et les options spécifiés.</summary>
      <param name="function">Délégué qui représente le code à exécuter dans la tâche.Quand cette fonction aura terminé, la propriété <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tâche sera configurée pour retourner la valeur de résultat de la fonction.</param>
      <param name="state">Objet représentant les données que la fonction doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> à assigner à la nouvelle tâche.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> avec l'action, l'état et les options spécifiés.</summary>
      <param name="function">Délégué qui représente le code à exécuter dans la tâche.Quand cette fonction aura terminé, la propriété <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tâche sera configurée pour retourner la valeur de résultat de la fonction.</param>
      <param name="state">Objet représentant les données que la fonction doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> à assigner à la nouvelle tâche.</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> utilisé pour personnaliser le comportement de la tâche.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialise une nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> avec l'action, l'état et les options spécifiés.</summary>
      <param name="function">Délégué qui représente le code à exécuter dans la tâche.Quand cette fonction aura terminé, la propriété <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tâche sera configurée pour retourner la valeur de résultat de la fonction.</param>
      <param name="state">Objet représentant les données que la fonction doit utiliser.</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> utilisé pour personnaliser le comportement de la tâche.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ConfigureAwait(System.Boolean)">
      <summary>Configure un élément awaiter utilisé pour attendre cette <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objet utilisé pour attendre cette tâche.</returns>
      <param name="continueOnCapturedContext">True en cas de tentative de marshaling de la continuation vers le contexte d'origine capturé ; sinon, false.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}})">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand la tâche cible se termine. </summary>
      <returns>Nouvelle tâche de continuation. </returns>
      <param name="continuationAction">Action à exécuter quand la <see cref="T:System.Threading.Tasks.Task`1" /> antécédente se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken)">
      <summary>Crée une continuation pouvant être annulée qui s'exécute de façon asynchrone quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle tâche de continuation. </returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="cancellationToken">Jeton d'annulation passé à la nouvelle tâche de continuation. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute en fonction de la condition spécifiée dans <paramref name="continuationOptions" />.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter selon la condition spécifiée dans <paramref name="continuationOptions" />.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une continuation qui s'exécute en fonction de la condition spécifiée dans <paramref name="continuationOptions" />.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter selon la condition spécifiée dans <paramref name="continuationOptions" />.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object)">
      <summary>Crée une continuation à laquelle sont passées des informations d'état et qui s'exécute quand le <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine. </summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés comme arguments au délégué.</param>
      <param name="state">Objet représentant les données que l'action de continuation doit utiliser. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Crée une continuation qui s'exécute quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que l'action de continuation doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que l'action de continuation doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une continuation qui s'exécute quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que l'action de continuation doit utiliser.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="continuationAction">Action à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que l'action de continuation doit utiliser.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0})">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <typeparam name="TNewResult"> Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken)">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche.</param>
      <typeparam name="TNewResult"> Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute en fonction de la condition spécifiée dans <paramref name="continuationOptions" />.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter selon la condition spécifiée dans <paramref name="continuationOptions" />.Une fois exécuté, le délégué passera cette tâche achevée en tant qu'argument.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <typeparam name="TNewResult"> Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une continuation qui s'exécute en fonction de la condition spécifiée dans <paramref name="continuationOptions" />.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter selon la condition spécifiée dans <paramref name="continuationOptions" />.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TNewResult"> Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute de façon asynchrone quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Quand elle est exécutée, la tâche achevée est passée au délégué en tant qu'argument.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <typeparam name="TNewResult"> Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object)">
      <summary>Crée une continuation qui s'exécute quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que la fonction de continuation doit utiliser.</param>
      <typeparam name="TNewResult">Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Crée une continuation qui s'exécute quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que la fonction de continuation doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche.</param>
      <typeparam name="TNewResult">Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que la fonction de continuation doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <typeparam name="TNewResult">Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The  <paramref name="continuationOptions" />  argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une continuation qui s'exécute quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que la fonction de continuation doit utiliser.</param>
      <param name="continuationOptions">Options applicables quand la continuation est planifiée et qui en régissent le comportement.Cela inclut des critères, tels que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, ainsi que des options d'exécution, telles que <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TNewResult">Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une continuation qui s'exécute quand la <see cref="T:System.Threading.Tasks.Task`1" /> cible se termine.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="continuationFunction">Fonction à exécuter quand <see cref="T:System.Threading.Tasks.Task`1" /> se termine.Lors de l'exécution, la tâche achevée et l'objet d'état fourni par l'appelant sont passés au délégué sous la forme d'arguments.</param>
      <param name="state">Objet représentant les données que la fonction de continuation doit utiliser.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à associer à la tâche de continuation et à utiliser pour son exécution.</param>
      <typeparam name="TNewResult">Type du résultat produit par la continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Factory">
      <summary>Fournit l'accès aux méthodes de fabrique pour la création et la configuration d'instances de <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objet de fabrique pouvant créer une variété d'objets <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.GetAwaiter">
      <summary>Obtient un élément awaiter utilisé pour attendre cette <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Instance d'élément awaiter.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Result">
      <summary>Obtient la valeur de résultat de cette <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Valeur de résultat de cette <see cref="T:System.Threading.Tasks.Task`1" />, qui est du même type que le paramètre de type de la tâche.</returns>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskCanceledException">
      <summary>Représente une exception utilisée pour communiquer l'annulation d'une tâche.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.TaskCanceledException" /> avec un message système qui décrit l'erreur.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.TaskCanceledException" /> avec un message spécifié décrivant l'erreur.</summary>
      <param name="message">Message qui décrit l'exception.L'appelant de ce constructeur doit vérifier que cette chaîne a été localisée pour la culture du système en cours.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.TaskCanceledException" /> avec un message d'erreur spécifié et une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="message">Message qui décrit l'exception.L'appelant de ce constructeur doit vérifier que cette chaîne a été localisée pour la culture du système en cours.</param>
      <param name="innerException">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="innerException" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.Threading.Tasks.Task)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.TaskCanceledException" /> avec une référence à la <see cref="T:System.Threading.Tasks.Task" /> qui a été annulée.</summary>
      <param name="task">Tâche qui a été annulée.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskCanceledException.Task">
      <summary>Obtient la tâche associée à cette exception.</summary>
      <returns>Référence à la <see cref="T:System.Threading.Tasks.Task" /> associée à cette exception.</returns>
    </member>
    <member name="T:System.Threading.Tasks.TaskCompletionSource`1">
      <summary>Représente le côté producteur d'un <see cref="T:System.Threading.Tasks.Task`1" /> indépendant d'un délégué, en fournissant l'accès au côté consommateur via la propriété <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />.</summary>
      <typeparam name="TResult">Type de la valeur de résultat associée à ce <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor">
      <summary>Crée un <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> avec l'état spécifié.</summary>
      <param name="state">État à utiliser comme AsyncState du <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> avec l'état et les options spécifiés.</summary>
      <param name="state">État à utiliser comme AsyncState du <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent.</param>
      <param name="creationOptions">Options à utiliser lors de la création du <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Les <paramref name="creationOptions" /> représentent des options non valides pour une utilisation avec un <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> avec les options spécifiées.</summary>
      <param name="creationOptions">Options à utiliser lors de la création du <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Les <paramref name="creationOptions" /> représentent des options non valides pour une utilisation avec un <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetCanceled">
      <summary>Fait passer le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent à l'état <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</summary>
      <exception cref="T:System.InvalidOperationException">Le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent est déjà dans l'un des trois états définitifs : <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> ou <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />, ou si le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent a déjà été supprimé.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Fait passer le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent à l'état <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <param name="exceptions">Collection d'exceptions à lier à ce <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="exceptions" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Il y a un ou plusieurs éléments null dans <paramref name="exceptions" />.</exception>
      <exception cref="T:System.InvalidOperationException">Le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent est déjà dans l'un des trois états définitifs : <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> ou <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Exception)">
      <summary>Fait passer le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent à l'état <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <param name="exception">Exception à lier à ce <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="exception" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">Le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent est déjà dans l'un des trois états définitifs : <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> ou <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetResult(`0)">
      <summary>Fait passer le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent à l'état <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.</summary>
      <param name="result">Valeur de résultat à lier à ce <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> a été supprimé.</exception>
      <exception cref="T:System.InvalidOperationException">Le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent est déjà dans l'un des trois états définitifs : <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> ou <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskCompletionSource`1.Task">
      <summary>Obtient le <see cref="T:System.Threading.Tasks.Task`1" /> créé par ce <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</summary>
      <returns>Retourne le <see cref="T:System.Threading.Tasks.Task`1" /> créé par ce <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled">
      <summary>Tente de faire passer le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent à l'état <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</summary>
      <returns>True si l'opération a réussi ; false si l'opération a échoué ou si l'objet a déjà été supprimé.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled(System.Threading.CancellationToken)">
      <summary>Tente de faire passer sous-jacent <see cref="T:System.Threading.Tasks.Task`1" /> dans les <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> d'état et permet à un jeton d'annulation à stocker dans la tâche annulée. </summary>
      <returns>true si l'opération réussit ; sinon false. </returns>
      <param name="cancellationToken">Jeton d'annulation. </param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Tente de faire passer le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent à l'état <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <returns>True si l'opération a réussi ; sinon, false.</returns>
      <param name="exceptions">Collection d'exceptions à lier à ce <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="exceptions" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Il y a un ou plusieurs éléments null dans <paramref name="exceptions" />.ouLa collection <paramref name="exceptions" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Exception)">
      <summary>Tente de faire passer le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent à l'état <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <returns>True si l'opération a réussi ; sinon, false.</returns>
      <param name="exception">Exception à lier à ce <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="exception" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetResult(`0)">
      <summary>Tente de faire passer le <see cref="T:System.Threading.Tasks.Task`1" /> sous-jacent à l'état <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.</summary>
      <returns>True si l'opération a réussi ; sinon, false. </returns>
      <param name="result">Valeur de résultat à lier à ce <see cref="T:System.Threading.Tasks.Task`1" />.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskContinuationOptions">
      <summary>Spécifie le comportement pour une tâche créée à l'aide de la méthode <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)" /> ou <see cref="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)" />.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent">
      <summary>Spécifie que la continuation, s'il s'agit d'une tâche enfant, est jointe à un parent dans la hiérarchie des tâches.La continuation peut être une tâche enfant uniquement si son antécédent est également une tâche enfant.Par défaut, une tâche enfant (autrement dit, une tâche interne créée par une tâche externe) est exécutée indépendamment de son parent.Vous pouvez utiliser l'option <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> pour que les tâches parente et enfant soient synchronisées.Notez que si une tâche parente est configurée avec l'option <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" />, l'option <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> de la tâche enfant n'a aucun effet, et la tâche enfant s'exécute en tant que tâche enfant détachée. Pour plus d'informations, consultez Tâches enfants attachées et détachées. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.DenyChildAttach">
      <summary>Indique qu'une tâche enfant (c'est-à-dire toute tâche interne imbriquée  créée par cette continuation) créée avec l'option <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> et qui tente de s'exécuter comme une tâche enfant détachée ne peut pas être attachée à la tâche parente et s'exécute à la place comme tâche enfant détachée.Pour plus d'informations, consultez Tâches enfants attachées et détachées.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously">
      <summary>Spécifie que la tâche de continuation doit être exécutée de façon synchrone.Quand cette option est activée, la continuation est exécutée sur le thread qui provoque le passage de la tâche antérieure à son état final.Si l'antécédent est déjà terminé lors de la création de la continuation, celle-ci s'exécute sur le thread qui crée la continuation.Si le <see cref="T:System.Threading.CancellationTokenSource" /> de l'antécédent est supprimé dans un bloc finally (Finally en Visual Basic), une continuation avec cette option s'exécutera dans ce bloc finally.Seules les continuations très brèves doivent être exécutées de façon synchrone.Étant donné que la tâche s'exécute de façon synchrone, il n'est pas nécessaire d'appeler une méthode telle que <see cref="M:System.Threading.Tasks.Task.Wait" /> pour vous assurer que le thread appelant attend la fin de la tâche. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.HideScheduler">
      <summary>Spécifie que les tâches créées par la continuation en appelant des méthodes comme <see cref="M:System.Threading.Tasks.Task.Run(System.Action)" /> ou <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})" /> voient le planificateur par défaut (<see cref="P:System.Threading.Tasks.TaskScheduler.Default" />) plutôt que le planificateur sur lequel cette continuation s'exécute comme planificateur actuel.  </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LazyCancellation">
      <summary>Dans le cas d'une annulation de continuation, empêche l'achèvement de la continuation tant que l'antécédent n'est pas terminé.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LongRunning">
      <summary>Spécifie qu'une continuation sera une opération de longue durée et de granulosité grossière.Conseille au <see cref="T:System.Threading.Tasks.TaskScheduler" /> de garantir le surabonnement.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.None">
      <summary>Quand aucune option de continuation n'est spécifiée, indique que le comportement par défaut doit être utilisé lors de l'exécution d'une continuation.La continuation s'exécute de façon asynchrone quand la tâche antécédente se termine, quelle que soit la valeur de propriété <see cref="P:System.Threading.Tasks.Task.Status" /> finale de l'antécédent.Si la continuation est une tâche enfant, elle est créée en tant que tâche imbriquée détachée.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnCanceled">
      <summary>Spécifie que la tâche de continuation ne doit pas être planifiée si son antécédent a été annulé.Un antécédent est annulé si sa propriété <see cref="P:System.Threading.Tasks.Task.Status" />  à l'achèvement est <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.Cette option n'est pas valide pour les continuations multitâches.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnFaulted">
      <summary>Spécifie que la tâche de continuation ne doit pas être planifiée si son antécédent a levé une exception non gérée.Un antécédent lève une exception non gérée si sa propriété <see cref="P:System.Threading.Tasks.Task.Status" />  à l'achèvement est <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.Cette option n'est pas valide pour les continuations multitâches.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnRanToCompletion">
      <summary>Spécifie que la tâche de continuation ne doit pas être planifiée si son antécédent s'est terminé.Un antécédent s'exécute jusqu'à son achèvement si sa propriété <see cref="P:System.Threading.Tasks.Task.Status" />  à l'achèvement est <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.Cette option n'est pas valide pour les continuations multitâches.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled">
      <summary>Spécifie que la continuation doit être planifiée uniquement si son antécédent a été annulé.Un antécédent est annulé si sa propriété <see cref="P:System.Threading.Tasks.Task.Status" />  à l'achèvement est <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.Cette option n'est pas valide pour les continuations multitâches.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted">
      <summary>Spécifie que la tâche de continuation doit être planifiée uniquement si son antécédent a levé une exception non gérée.Un antécédent lève une exception non gérée si sa propriété <see cref="P:System.Threading.Tasks.Task.Status" />  à l'achèvement est <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.L'option <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted" />  garantit que la propriété <see cref="P:System.Threading.Tasks.Task.Exception" /> de l'antécédent n'est pas null.Vous pouvez utiliser cette propriété pour intercepter l'exception et voir quelle exception a provoqué l'erreur de la tâche.Si vous n'accédez pas à la propriété <see cref="P:System.Threading.Tasks.Task.Exception" />, l'exception n'est pas gérée.De plus, si vous essayez d'accéder à la propriété <see cref="P:System.Threading.Tasks.Task`1.Result" /> d'une tâche qui été annulée ou a rencontré une erreur, une nouvelle exception est levée.Cette option n'est pas valide pour les continuations multitâches. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnRanToCompletion">
      <summary>Spécifie que la continuation doit être planifiée uniquement si son antécédent s'est terminé.Un antécédent s'exécute jusqu'à son achèvement si sa propriété <see cref="P:System.Threading.Tasks.Task.Status" />  à l'achèvement est <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.Cette option n'est pas valide pour les continuations multitâches.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.PreferFairness">
      <summary>Conseil à un <see cref="T:System.Threading.Tasks.TaskScheduler" /> pour planifier des tâches dans l'ordre dans lequel elles ont été planifiées. Les tâches planifiées plus tôt sont plus susceptibles de s'exécuter plus tôt et celles planifiées plus tard sont plus susceptibles de s'exécuter ultérieurement. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.RunContinuationsAsynchronously">
      <summary>Indique que la tâche de continuation doit être exécutée de façon synchrone.Cette option est prioritaire sur <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskCreationOptions">
      <summary>Spécifie les indicateurs qui contrôlent le comportement facultatif pour la création et l'exécution de tâches. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent">
      <summary>Spécifie qu'une tâche est associée à un parent dans la hiérarchie des tâches.Par défaut, une tâche enfant (autrement dit, une tâche interne créée par une tâche externe) est exécutée indépendamment de son parent.Vous pouvez utiliser l'option <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> pour que les tâches parente et enfant soient synchronisées.Notez que si une tâche parente est configurée avec l'option <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" />, l'option <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> de la tâche enfant n'a aucun effet, et la tâche enfant s'exécute en tant que tâche enfant détachée. Pour plus d'informations, consultez Tâches enfants attachées et détachées. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach">
      <summary>Indique qu'une tâche enfant qui tente de s'exécuter comme une tâche enfant détachée (c'est-à-dire créée avec l'option <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" />) ne peut pas être attachée à la tâche parente et s'exécute à la place comme tâche enfant détachée.Pour plus d'informations, consultez Tâches enfants attachées et détachées.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.HideScheduler">
      <summary>Empêche le planificateur ambiant d'être considéré comme le planificateur actuel dans la tâche créée.Cela signifie que les opérations telles que StartNew ou ContinueWith effectuées dans la tâche créée considéreront <see cref="P:System.Threading.Tasks.TaskScheduler.Default" /> comme planificateur actuel.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.LongRunning">
      <summary>Indique qu'une tâche sera une opération de longue durée et de granulosité grossière impliquant moins de composants importants que les systèmes affinés.Conseille au <see cref="T:System.Threading.Tasks.TaskScheduler" /> de garantir le surabonnement.Le surabonnement vous permet de créer un nombre de threads plus important que le nombre de threads matériels disponibles.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.None">
      <summary>Indique que le comportement par défaut doit être utilisé.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.PreferFairness">
      <summary>Conseil à un <see cref="T:System.Threading.Tasks.TaskScheduler" /> pour planifier une tâche d'une façon aussi juste que possible. Les tâches planifiées plus tôt seront probablement exécutées plus tôt et celles planifiées plus tard seront probablement exécutées ultérieurement.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.RunContinuationsAsynchronously">
      <summary>Force l'exécution asynchrone des continuations ajoutées à la tâche actuelle. </summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskExtensions">
      <summary>Fournit un ensemble de méthodes statiques (méthodes partagées en Visual Basic) pour l'utilisation de types spécifiques d'instances de <see cref="T:System.Threading.Tasks.Task" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap``1(System.Threading.Tasks.Task{System.Threading.Tasks.Task{``0}})">
      <summary>Crée un proxy <see cref="T:System.Threading.Tasks.Task" /> qui représente l'opération asynchrone d'un Task&lt;Task&lt;T&gt;&gt; (en C#) ou Task (Of Task(Of T)) (en Visual Basic).</summary>
      <returns>Une <see cref="T:System.Threading.Tasks.Task" /> qui représente l'opération asynchrone du Task&lt;Task&lt;T&gt;&gt; fourni (en C#) ou Task (Of Task(Of T)) (en Visual Basic).</returns>
      <param name="task">Le Task&lt;Task&lt;T&gt;&gt; (C#) ou Task (Of Task(Of T)) (Visual Basic) à défaire.</param>
      <typeparam name="TResult">Type du résultat de la tâche.</typeparam>
      <exception cref="T:System.ArgumentNullException">L'exception levée si l'argument <paramref name="task" /> est null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap(System.Threading.Tasks.Task{System.Threading.Tasks.Task})">
      <summary>Crée un proxy <see cref="T:System.Threading.Tasks.Task" /> qui représente l'opération asynchrone d'une <see cref="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)" />.</summary>
      <returns>Tâche qui représente l'opération asynchrone de la System.Threading.Tasks.Task(Of Task) fournie.</returns>
      <param name="task">Le Task&lt;Task&gt; (C#) ou Task (Of Task) (Visual Basic) à défaire.</param>
      <exception cref="T:System.ArgumentNullException">L'exception levée si l'argument <paramref name="task" /> est null.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory">
      <summary>Prend en charge la création et la planification d'objets <see cref="T:System.Threading.Tasks.Task" />. </summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor">
      <summary>Initialise une instance de <see cref="T:System.Threading.Tasks.TaskFactory" /> avec la configuration par défaut.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken)">
      <summary>Initialise une instance de <see cref="T:System.Threading.Tasks.TaskFactory" /> avec la configuration spécifiée.</summary>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné aux tâches créées par ce <see cref="T:System.Threading.Tasks.TaskFactory" />, sauf si un autre CancellationToken est explicitement spécifié quand les méthodes de fabrique sont appelées.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Initialise une instance de <see cref="T:System.Threading.Tasks.TaskFactory" /> avec la configuration spécifiée.</summary>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> par défaut qui sera assigné aux tâches créées par ce <see cref="T:System.Threading.Tasks.TaskFactory" />, sauf si un autre CancellationToken est explicitement spécifié quand les méthodes de fabrique sont appelées.</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> par défaut à utiliser pour créer des tâches avec ce TaskFactory.</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> par défaut à utiliser pour créer des tâches de continuation avec ce TaskFactory.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> par défaut à utiliser pour planifier des tâches créées avec ce TaskFactory.Une valeur Null indique que TaskScheduler.Current doit être utilisé.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="creationOptions" /> spécifie une valeur <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> non valide.Pour plus d'informations, consultez la section Notes pour <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.ouL'argument <paramref name="continuationOptions" /> spécifie une valeur non valide.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Initialise une instance de <see cref="T:System.Threading.Tasks.TaskFactory" /> avec la configuration spécifiée.</summary>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> par défaut à utiliser pour créer des tâches avec ce TaskFactory.</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> par défaut à utiliser pour créer des tâches de continuation avec ce TaskFactory.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="creationOptions" /> spécifie une valeur <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> non valide.Pour plus d'informations, consultez la section Notes pour <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.ouL'argument <paramref name="continuationOptions" /> spécifie une valeur non valide.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Initialise une instance de <see cref="T:System.Threading.Tasks.TaskFactory" /> avec la configuration spécifiée.</summary>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> à utiliser pour planifier des tâches créées avec ce TaskFactory.Une valeur Null indique que le TaskScheduler actif doit être utilisé.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CancellationToken">
      <summary>Obtient le jeton d'annulation par défaut pour cette fabrique de tâches.</summary>
      <returns>Jeton d'annulation de tâches par défaut pour cette fabrique de tâches.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.ContinuationOptions">
      <summary>Obtient les options de continuation de tâches par défaut pour cette fabrique de tâches.</summary>
      <returns>Options de continuation de tâches par défaut pour cette fabrique de tâches.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]})">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé. </summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationAction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation à associer à la nouvelle tâche de continuation.</param>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.ouLe <see cref="T:System.Threading.CancellationTokenSource" /> qui a créé <paramref name="cancellationToken" /> a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationAction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation à associer à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Combinaison d'opérations de bits des valeurs d'énumération qui contrôlent le comportement de la nouvelle tâche de continuation.</param>
      <param name="scheduler">Objet utilisé pour planifier la nouvelle tâche de continuation.</param>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationAction" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="continuationOptions">Combinaison d'opérations de bits des valeurs d'énumération qui contrôlent le comportement de la nouvelle tâche de continuation.Les membres NotOn* et OnlyOn* ne sont pas pris en charge.</param>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationAction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur non valide. </exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0})">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé à la tâche créée.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation à associer à la nouvelle tâche de continuation.</param>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé à la tâche créée.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.ouLe <see cref="T:System.Threading.CancellationTokenSource" /> qui a créé <paramref name="cancellationToken" /> a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation à associer à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Combinaison d'opérations de bits des valeurs d'énumération qui contrôlent le comportement de la nouvelle tâche de continuation.Les membres NotOn* et OnlyOn* ne sont pas pris en charge.</param>
      <param name="scheduler">Objet utilisé pour planifier la nouvelle tâche de continuation.</param>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé à la tâche créée.</typeparam>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="continuationOptions">Combinaison d'opérations de bits des valeurs d'énumération qui contrôlent le comportement de la nouvelle tâche de continuation.Les membres NotOn* et OnlyOn* ne sont pas pris en charge.</param>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé à la tâche créée.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur non valide. </exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]})">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationAction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation à associer à la nouvelle tâche de continuation.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.ouLe <see cref="T:System.Threading.CancellationTokenSource" /> qui a créé <paramref name="cancellationToken" /> a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationAction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation à associer à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Combinaison d'opérations de bits des valeurs d'énumération qui contrôlent le comportement de la nouvelle tâche de continuation.Les membres NotOn* et OnlyOn* ne sont pas pris en charge.</param>
      <param name="scheduler">Objet utilisé pour planifier la nouvelle tâche de continuation.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationAction" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="continuationOptions">Combinaison d'opérations de bits des valeurs d'énumération qui contrôlent le comportement de la nouvelle tâche de continuation.Les membres NotOn* et OnlyOn* ne sont pas pris en charge.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationAction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur non valide. </exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1})">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé à la tâche créée.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation à associer à la nouvelle tâche de continuation.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé à la tâche créée.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.ouLes <see cref="T:System.Threading.CancellationTokenSource" /> créés<paramref name=" cancellationToken" /> a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation à associer à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Combinaison d'opérations de bits des valeurs d'énumération qui contrôlent le comportement de la nouvelle tâche de continuation.Les membres NotOn* et OnlyOn* ne sont pas pris en charge.</param>
      <param name="scheduler">Objet utilisé pour planifier la nouvelle tâche de continuation.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé à la tâche créée.</typeparam>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur non valide. </exception>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.ouLe <see cref="T:System.Threading.CancellationTokenSource" /> qui a créé <paramref name="cancellationToken" /> a déjà été supprimé.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une tâche de continuation qui démarre lorsqu'un ensemble de tâches spécifiées est terminé.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="continuationOptions">Combinaison d'opérations de bits des valeurs d'énumération qui contrôlent le comportement de la nouvelle tâche de continuation.Les membres NotOn* et OnlyOn* ne sont pas pris en charge.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé à la tâche créée.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un élément du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur non valide.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> est vide ou contient une valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task})">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé. </exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null. ouLe le <paramref name="continuationAction" /> argument est null. </exception>
      <exception cref="T:System.ArgumentException">Le <paramref name="tasks" /> tableau contient un null valeur. ouLe <paramref name="tasks" /> tableau est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé. ou<paramref name="cancellationToken" /> a déjà été supprimé. </exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null. ouL'argument <paramref name="continuationAction" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentException">Le <paramref name="tasks" /> tableau contient un null valeur. ouLe <paramref name="tasks" /> tableau est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Valeur <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task" /> de continuation créée.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la <see cref="T:System.Threading.Tasks.Task" /> de continuation créée.</param>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationAction" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="scheduler" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="continuationOptions">Valeur <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task" /> de continuation créée.</param>
      <exception cref="T:System.ObjectDisposedException">Exception levée lorsque l'un des éléments dans le tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationAction" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="continuationOptions" /> spécifie une valeur TaskContinuationOptions non valide.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0})">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé au <see cref="T:System.Threading.Tasks.Task`1" /> créé.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Exception levée lorsque l'un des éléments dans le tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationFunction" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé au <see cref="T:System.Threading.Tasks.Task`1" /> créé.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Exception levée lorsque l'un des éléments dans le tableau <paramref name="tasks" /> a été supprimé.ouLe <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationFunction" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Valeur <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> de continuation créée.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la <see cref="T:System.Threading.Tasks.Task`1" /> de continuation créée.</param>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé au <see cref="T:System.Threading.Tasks.Task`1" /> créé.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationFunction" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="scheduler" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="continuationOptions">Valeur <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> de continuation créée.</param>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé au <see cref="T:System.Threading.Tasks.Task`1" /> créé.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Exception levée lorsque l'un des éléments dans le tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationFunction" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="continuationOptions" /> spécifie une valeur TaskContinuationOptions non valide.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}})">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Exception levée lorsque l'un des éléments dans le tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationAction" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Exception levée lorsque l'un des éléments dans le tableau <paramref name="tasks" /> a été supprimé.ouLe <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationAction" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Valeur <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task" /> de continuation créée.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la <see cref="T:System.Threading.Tasks.Task`1" /> de continuation créée.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationAction" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="scheduler" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationAction">Délégué d'action à exécuter quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="continuationOptions">Valeur <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task" /> de continuation créée.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Exception levée lorsque l'un des éléments dans le tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationAction" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="continuationOptions" /> spécifie une valeur TaskContinuationOptions non valide.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1})">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé au <see cref="T:System.Threading.Tasks.Task`1" /> créé.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Exception levée lorsque l'un des éléments dans le tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationFunction" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé au <see cref="T:System.Threading.Tasks.Task`1" /> créé.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Exception levée lorsque l'un des éléments dans le tableau <paramref name="tasks" /> a été supprimé.ouLe <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationFunction" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">Valeur <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> de continuation créée.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la <see cref="T:System.Threading.Tasks.Task`1" /> de continuation créée.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé au <see cref="T:System.Threading.Tasks.Task`1" /> créé.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationFunction" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="scheduler" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task`1" /> de continuation qui démarrera quand l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="continuationOptions">Valeur <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> de continuation créée.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <typeparam name="TResult">Type du résultat retourné par le délégué <paramref name="continuationFunction" /> et associé au <see cref="T:System.Threading.Tasks.Task`1" /> créé.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Exception levée lorsque l'un des éléments dans le tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque le tableau <paramref name="tasks" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="continuationFunction" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="continuationOptions" /> spécifie une valeur TaskContinuationOptions non valide.</exception>
      <exception cref="T:System.ArgumentException">Exception levée lorsque le tableau <paramref name="tasks" /> contient une valeur Null.ouException levée lorsque le tableau <paramref name="tasks" /> est vide.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CreationOptions">
      <summary>Obtient les options de création de tâches par défaut pour cette fabrique de tâches.</summary>
      <returns>Options de création de tâches par défaut pour cette fabrique de tâches.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task" /> créée.</param>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> créée.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <typeparam name="TArg1">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task" /> créée.</param>
      <typeparam name="TArg1">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <typeparam name="TArg1">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> créée.</param>
      <typeparam name="TArg1">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task" /> créée.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> créée.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg3">Troisième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du troisième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg3">Troisième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task" /> créée.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du troisième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg3">Troisième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du troisième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg3">Troisième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> créée.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du troisième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="beginMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult})">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> qui exécute une action de la méthode End quand un <see cref="T:System.IAsyncResult" /> spécifié se termine.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> qui représente l'opération asynchrone.</returns>
      <param name="asyncResult">IAsyncResult dont l'achèvement doit déclencher le traitement du <paramref name="endMethod" />.</param>
      <param name="endMethod">Délégué d'action qui traite le <paramref name="asyncResult" /> terminé.</param>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="asyncResult" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> qui exécute une action de la méthode End quand un <see cref="T:System.IAsyncResult" /> spécifié se termine.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> qui représente l'opération asynchrone.</returns>
      <param name="asyncResult">IAsyncResult dont l'achèvement doit déclencher le traitement du <paramref name="endMethod" />.</param>
      <param name="endMethod">Délégué d'action qui traite le <paramref name="asyncResult" /> terminé.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task" /> créée.</param>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="asyncResult" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une <see cref="T:System.Threading.Tasks.Task" /> qui exécute une action de la méthode End quand un <see cref="T:System.IAsyncResult" /> spécifié se termine.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> créée qui représente l'opération asynchrone.</returns>
      <param name="asyncResult">IAsyncResult dont l'achèvement doit déclencher le traitement du <paramref name="endMethod" />.</param>
      <param name="endMethod">Délégué d'action qui traite le <paramref name="asyncResult" /> terminé.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task" /> créée.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la tâche qui exécute la méthode End.</param>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="asyncResult" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="scheduler" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0})">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui exécute une fonction de méthode End quand un <see cref="T:System.IAsyncResult" /> spécifié se termine.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> qui représente l'opération asynchrone.</returns>
      <param name="asyncResult">IAsyncResult dont l'achèvement doit déclencher le traitement du <paramref name="endMethod" />.</param>
      <param name="endMethod">Délégué de fonction qui traite l'<paramref name="asyncResult" /> terminé.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="asyncResult" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui exécute une fonction de méthode End quand un <see cref="T:System.IAsyncResult" /> spécifié se termine.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> qui représente l'opération asynchrone.</returns>
      <param name="asyncResult">IAsyncResult dont l'achèvement doit déclencher le traitement du <paramref name="endMethod" />.</param>
      <param name="endMethod">Délégué de fonction qui traite l'<paramref name="asyncResult" /> terminé.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> créée.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="asyncResult" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée un <see cref="T:System.Threading.Tasks.Task`1" /> qui exécute une fonction de méthode End quand un <see cref="T:System.IAsyncResult" /> spécifié se termine.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> qui représente l'opération asynchrone.</returns>
      <param name="asyncResult">IAsyncResult dont l'achèvement doit déclencher le traitement du <paramref name="endMethod" />.</param>
      <param name="endMethod">Délégué de fonction qui traite l'<paramref name="asyncResult" /> terminé.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> créée.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la tâche qui exécute la méthode End.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="asyncResult" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="endMethod" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="scheduler" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.Scheduler">
      <summary>Obtient le Planificateur de tâches par défaut pour cette fabrique de tâches.</summary>
      <returns>Planificateur de tâches par défaut pour cette fabrique de tâches.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action)">
      <summary>Crée et lance une tâche.</summary>
      <returns>Tâche démarrée.</returns>
      <param name="action">Délégué d'action à exécuter de façon asynchrone.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="action" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> démarrée.</returns>
      <param name="action">Délégué d'action à exécuter de façon asynchrone.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné à la nouvelle tâche.</param>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="action" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> démarrée.</returns>
      <param name="action">Délégué d'action à exécuter de façon asynchrone.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné au nouveau <see cref="T:System.Threading.Tasks.Task" /></param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement du <see cref="T:System.Threading.Tasks.Task" /> créé.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la <see cref="T:System.Threading.Tasks.Task" /> créée.</param>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="action" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="scheduler" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> démarrée.</returns>
      <param name="action">Délégué d'action à exécuter de façon asynchrone.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement du <see cref="T:System.Threading.Tasks.Task" /> créé.</param>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="action" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task" />. </summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> démarrée. </returns>
      <param name="action">Délégué d'action à exécuter de façon asynchrone. </param>
      <param name="state">Objet contenant des données que le délégué <paramref name="action" /> doit utiliser. </param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="action" /> a la valeur null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> démarrée.</returns>
      <param name="action">Délégué d'action à exécuter de façon asynchrone.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="action" /> doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné au nouveau <see cref="T:System.Threading.Tasks.Task" /></param>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="action" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> démarrée.</returns>
      <param name="action">Délégué d'action à exécuter de façon asynchrone.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="action" /> doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné à la nouvelle tâche.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement du <see cref="T:System.Threading.Tasks.Task" /> créé.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la <see cref="T:System.Threading.Tasks.Task" /> créée.</param>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="action" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="scheduler" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> démarrée.</returns>
      <param name="action">Délégué d'action à exécuter de façon asynchrone.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="action" /> doit utiliser.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement du <see cref="T:System.Threading.Tasks.Task" /> créé.</param>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="action" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0})">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />. </typeparam>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="function" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné au nouveau <see cref="T:System.Threading.Tasks.Task" /></param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="function" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné à la nouvelle tâche.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement du <see cref="T:System.Threading.Tasks.Task`1" /> créé.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la <see cref="T:System.Threading.Tasks.Task`1" /> créée.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="function" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="scheduler" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement du <see cref="T:System.Threading.Tasks.Task`1" /> créé.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="function" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="function" /> doit utiliser.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="function" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="function" /> doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné au nouveau <see cref="T:System.Threading.Tasks.Task" /></param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="function" /> a la valeur Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="function" /> doit utiliser.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> qui sera assigné à la nouvelle tâche.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement du <see cref="T:System.Threading.Tasks.Task`1" /> créé.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la <see cref="T:System.Threading.Tasks.Task`1" /> créée.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Le <see cref="T:System.Threading.CancellationToken" /> fourni a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="function" /> a la valeur Null.ouException levée lorsque l'argument <paramref name="scheduler" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée et lance un <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="function" /> doit utiliser.</param>
      <param name="creationOptions">Valeur TaskCreationOptions qui contrôle le comportement du <see cref="T:System.Threading.Tasks.Task`1" /> créé.</param>
      <typeparam name="TResult">Type du résultat disponible via le <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Exception levée lorsque l'argument <paramref name="function" /> a la valeur Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Exception levée lorsque l'argument <paramref name="creationOptions" /> spécifie une valeur TaskCreationOptions non valide.Pour plus d'informations, consultez la section Notes de la rubrique <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory`1">
      <summary>Prend en charge la création et la planification d'objets <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <typeparam name="TResult">Valeur de renvoi des objets <see cref="T:System.Threading.Tasks.Task`1" /> que créent les méthodes de cette classe. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor">
      <summary>Initialise une instance de <see cref="T:System.Threading.Tasks.TaskFactory`1" /> avec la configuration par défaut.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken)">
      <summary>Initialise une instance de <see cref="T:System.Threading.Tasks.TaskFactory`1" /> avec la configuration par défaut.</summary>
      <param name="cancellationToken">Jeton d'annulation par défaut qui sera assigné aux tâches créées par cette <see cref="T:System.Threading.Tasks.TaskFactory" />, sauf si un autre jeton d'annulation est spécifié explicitement quand les méthodes de fabrique sont appelées.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Initialise une instance de <see cref="T:System.Threading.Tasks.TaskFactory`1" /> avec la configuration spécifiée.</summary>
      <param name="cancellationToken">Jeton d'annulation par défaut qui sera assigné aux tâches créées par cette <see cref="T:System.Threading.Tasks.TaskFactory" />, sauf si un autre jeton d'annulation est spécifié explicitement quand les méthodes de fabrique sont appelées.</param>
      <param name="creationOptions">Options par défaut à utiliser pour créer des tâches avec cette <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="continuationOptions">Options par défaut à utiliser pour créer des tâches de continuation avec cette <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="scheduler">Planificateur par défaut à utiliser pour planifier les tâches créées avec cette <see cref="T:System.Threading.Tasks.TaskFactory`1" />.Une valeur null indique que <see cref="P:System.Threading.Tasks.TaskScheduler.Current" /> doit être utilisé.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> ou <paramref name="continuationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Initialise une instance de <see cref="T:System.Threading.Tasks.TaskFactory`1" /> avec la configuration spécifiée.</summary>
      <param name="creationOptions">Options par défaut à utiliser pour créer des tâches avec cette <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="continuationOptions">Options par défaut à utiliser pour créer des tâches de continuation avec cette <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> ou <paramref name="continuationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Initialise une instance de <see cref="T:System.Threading.Tasks.TaskFactory`1" /> avec la configuration spécifiée.</summary>
      <param name="scheduler">Planificateur à utiliser pour planifier les tâches créées avec cette <see cref="T:System.Threading.Tasks.TaskFactory`1" />.Une valeur null indique que le <see cref="T:System.Threading.Tasks.TaskScheduler" /> actuel doit être utilisé.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CancellationToken">
      <summary>Obtient le jeton d'annulation par défaut pour cette fabrique de tâches.</summary>
      <returns>Le jeton d'annulation par défaut pour cette fabrique de tâches.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.ContinuationOptions">
      <summary>Obtient la valeur d'énumération <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> de cette fabrique de tâches.</summary>
      <returns>Une des valeurs d'énumération qui spécifie les options de continuation par défaut pour cette fabrique de tâches.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0})">
      <summary>Crée une tâche de continuation qui démarrera lorsqu'un ensemble de tâches fournies se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ou<paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null ou est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken)">
      <summary>Crée une tâche de continuation qui démarrera lorsqu'un ensemble de tâches fournies se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche de continuation.</param>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.ouLes <see cref="T:System.Threading.CancellationTokenSource" /> créés<paramref name=" cancellationToken" /> a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ou<paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null ou est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une tâche de continuation qui démarrera lorsqu'un ensemble de tâches fournies se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche de continuation créée.Les valeurs NotOn* et OnlyOn* ne sont pas valides.</param>
      <param name="scheduler">Planificateur utilisé pour planifier la tâche de continuation créée.</param>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null ou est vide.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> spécifie une valeur non valide.</exception>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.ouLes <see cref="T:System.Threading.CancellationTokenSource" /> créés<paramref name=" cancellationToken" /> a déjà été supprimé.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une tâche de continuation qui démarrera lorsqu'un ensemble de tâches fournies se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="continuationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche de continuation créée.Les valeurs NotOn* et OnlyOn* ne sont pas valides.</param>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur non valide.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null ou est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0})">
      <summary>Crée une tâche de continuation qui démarrera lorsqu'un ensemble de tâches fournies se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null ou est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken)">
      <summary>Crée une tâche de continuation qui démarrera lorsqu'un ensemble de tâches fournies se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche de continuation.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.ouLes <see cref="T:System.Threading.CancellationTokenSource" /> créés<paramref name=" cancellationToken" /> a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null ou est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une tâche de continuation qui démarrera lorsqu'un ensemble de tâches fournies se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche de continuation créée.Les valeurs NotOn* et OnlyOn* ne sont pas valides.</param>
      <param name="scheduler">Planificateur utilisé pour planifier la tâche de continuation créée.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null ou est vide.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur non valide.</exception>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.ouLes <see cref="T:System.Threading.CancellationTokenSource" /> créés<paramref name=" cancellationToken" /> a déjà été supprimé.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une tâche de continuation qui démarrera lorsqu'un ensemble de tâches fournies se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand toutes les tâches du tableau <paramref name="tasks" /> sont terminées.</param>
      <param name="continuationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche de continuation créée.Les valeurs NotOn* et OnlyOn* ne sont pas valides.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur non valide.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null ou est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0})">
      <summary>Crée une tâche de continuation qui démarrera lorsque l'une des tâches de l'ensemble fourni se terminera. </summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null ou est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken)">
      <summary>Crée une tâche de continuation qui démarrera lorsque l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche de continuation.</param>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.ouLes <see cref="T:System.Threading.CancellationTokenSource" /> créés<paramref name=" cancellationToken" /> a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null.ouLe <paramref name="tasks" /> tableau est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une tâche de continuation qui démarrera lorsque l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche de continuation créée.Les valeurs NotOn* et OnlyOn* ne sont pas valides.</param>
      <param name="scheduler">Planificateur de tâches utilisé pour planifier la tâche de continuation créée.</param>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null.ouLe <paramref name="tasks" /> tableau est vide.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> non valide.</exception>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.ouLes <see cref="T:System.Threading.CancellationTokenSource" /> créés<paramref name=" cancellationToken" /> a déjà été supprimé. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une tâche de continuation qui démarrera lorsque l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="continuationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche de continuation créée.Les valeurs NotOn* et OnlyOn* ne sont pas valides.</param>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur d'énumération non valide.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null.ouLe <paramref name="tasks" /> tableau est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0})">
      <summary>Crée une tâche de continuation qui démarrera lorsque l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null.ouLe <paramref name="tasks" /> tableau est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken)">
      <summary>Crée une tâche de continuation qui démarrera lorsque l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle tâche de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche de continuation.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.ouLes <see cref="T:System.Threading.CancellationTokenSource" /> créés<paramref name=" cancellationToken" /> a déjà été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null.ouLe <paramref name="tasks" /> tableau est vide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une tâche de continuation qui démarrera lorsque l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche de continuation.</param>
      <param name="continuationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche de continuation créée.Les valeurs NotOn* et OnlyOn* ne sont pas valides.</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> utilisé pour planifier la <see cref="T:System.Threading.Tasks.Task`1" /> de continuation créée.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null.ouLe <paramref name="tasks" /> tableau est vide.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur TaskContinuationOptions non valide.</exception>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.ouLes <see cref="T:System.Threading.CancellationTokenSource" /> créés<paramref name=" cancellationToken" /> a déjà été supprimé. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crée une tâche de continuation qui démarrera lorsque l'une des tâches de l'ensemble fourni se terminera.</summary>
      <returns>Nouvelle <see cref="T:System.Threading.Tasks.Task`1" /> de continuation.</returns>
      <param name="tasks">Tableau de tâches à partir duquel continuer lorsqu'une tâche se termine.</param>
      <param name="continuationFunction">Délégué de fonction à exécuter en mode asynchrone quand une tâche du tableau <paramref name="tasks" /> est terminée.</param>
      <param name="continuationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche de continuation créée.Les valeurs NotOn* et OnlyOn* ne sont pas valides.</param>
      <typeparam name="TAntecedentResult">Type du résultat du <paramref name="tasks" /> antécédent.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un des éléments du tableau <paramref name="tasks" /> a été supprimé.</exception>
      <exception cref="T:System.ArgumentNullException">Le tableau <paramref name="tasks" /> est null.ouL'argument <paramref name="continuationFunction" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="continuationOptions" /> spécifie une valeur d'énumération non valide.</exception>
      <exception cref="T:System.ArgumentException">Le tableau <paramref name="tasks" /> contient une valeur null.ouLe <paramref name="tasks" /> tableau est vide.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CreationOptions">
      <summary>Obtient la valeur d'énumération <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> de cette fabrique de tâches.</summary>
      <returns>Une des valeurs d'énumération qui spécifie les options de création par défaut pour cette fabrique de tâches.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object)">
      <summary>Crée une tâche qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>Tâche créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="beginMethod" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée une tâche qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche créée.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="beginMethod" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="creationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object)">
      <summary>Crée une tâche qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>Tâche créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <typeparam name="TArg1">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="beginMethod" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée une tâche qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>Tâche créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche créée.</param>
      <typeparam name="TArg1">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="beginMethod" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="creationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object)">
      <summary>Crée une tâche qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>Tâche créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="beginMethod" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée une tâche qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>Tâche créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">Objet qui contrôle le comportement de la <see cref="T:System.Threading.Tasks.Task`1" /> créée.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="beginMethod" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="creationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object)">
      <summary>Crée une tâche qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>Tâche créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg3">Troisième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du troisième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="beginMethod" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée une tâche qui représente une paire de méthodes Begin et End conformes au modèle de programmation asynchrone.</summary>
      <returns>Tâche créée qui représente l'opération asynchrone.</returns>
      <param name="beginMethod">Délégué qui commence l'opération asynchrone.</param>
      <param name="endMethod">Délégué qui termine l'opération asynchrone.</param>
      <param name="arg1">Premier argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg2">Deuxième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="arg3">Troisième argument passé au délégué de <paramref name="beginMethod" />.</param>
      <param name="state">Objet contenant des données que le délégué <paramref name="beginMethod" /> doit utiliser.</param>
      <param name="creationOptions">Objet qui contrôle le comportement de la tâche créée.</param>
      <typeparam name="TArg1">Type du deuxième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Type du troisième argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Type du premier argument passé au délégué de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="beginMethod" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="creationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0})">
      <summary>Crée une tâche qui exécute une fonction de méthode End quand un <see cref="T:System.IAsyncResult" /> spécifié se termine.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> qui représente l'opération asynchrone.</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" /> dont l'achèvement doit déclencher le traitement du <paramref name="endMethod" />.</param>
      <param name="endMethod">Délégué de fonction qui traite l'<paramref name="asyncResult" /> terminé.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="asyncResult" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée une tâche qui exécute une fonction de méthode End quand un <see cref="T:System.IAsyncResult" /> spécifié se termine.</summary>
      <returns>Tâche qui représente l'opération asynchrone.</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" /> dont l'achèvement doit déclencher le traitement du <paramref name="endMethod" />.</param>
      <param name="endMethod">Délégué de fonction qui traite l'<paramref name="asyncResult" /> terminé.</param>
      <param name="creationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche créée.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="asyncResult" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argument <paramref name="creationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée une tâche qui exécute une fonction de méthode End quand un <see cref="T:System.IAsyncResult" /> spécifié se termine.</summary>
      <returns>Tâche créée qui représente l'opération asynchrone.</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" /> dont l'achèvement doit déclencher le traitement du <paramref name="endMethod" />.</param>
      <param name="endMethod">Délégué de fonction qui traite l'<paramref name="asyncResult" /> terminé.</param>
      <param name="creationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche créée.</param>
      <param name="scheduler">Planificateur de tâches utilisé pour planifier la tâche qui exécute la méthode End.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="asyncResult" /> a la valeur null.ouL'argument <paramref name="endMethod" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="creationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.Scheduler">
      <summary>Retourne le planificateur de tâches pour cette fabrique.</summary>
      <returns>Planificateur de tâches pour cette fabrique.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0})">
      <summary>Crée et lance une tâche.</summary>
      <returns>Tâche démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via la tâche.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="function" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken)">
      <summary>Crée et lance une tâche.</summary>
      <returns>Tâche démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via la tâche.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche.</param>
      <exception cref="T:System.ObjectDisposedException">La source du jeton d'annulation qui a créé<paramref name="cancellationToken" /> a déjà été supprimée.</exception>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="function" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée et lance une tâche.</summary>
      <returns>Tâche démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via la tâche.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche.</param>
      <param name="creationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche créée.</param>
      <param name="scheduler">Planificateur de tâche utilisé pour planifier la tâche de continuation créée.</param>
      <exception cref="T:System.ObjectDisposedException">La source du jeton d'annulation qui a créé<paramref name="cancellationToken" /> a déjà été supprimée.</exception>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="function" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="creationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée et lance une tâche.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via la tâche.</param>
      <param name="creationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche créée.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="function" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="creationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object)">
      <summary>Crée et lance une tâche.</summary>
      <returns>Tâche démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via la tâche.</param>
      <param name="state">Objet contenant les données que le délégué <paramref name="function" /> doit utiliser.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="function" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>Crée et lance une tâche.</summary>
      <returns>Tâche démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via la tâche.</param>
      <param name="state">Objet contenant les données que le délégué <paramref name="function" /> doit utiliser.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche.</param>
      <exception cref="T:System.ObjectDisposedException">La source du jeton d'annulation qui a créé<paramref name="cancellationToken" /> a déjà été supprimée.</exception>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="function" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crée et lance une tâche.</summary>
      <returns>Tâche démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via la tâche.</param>
      <param name="state">Objet contenant les données que le délégué <paramref name="function" /> doit utiliser.</param>
      <param name="cancellationToken">Jeton d'annulation qui sera assigné à la nouvelle tâche.</param>
      <param name="creationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche créée.</param>
      <param name="scheduler">Planificateur de tâche utilisé pour planifier la tâche de continuation créée.</param>
      <exception cref="T:System.ObjectDisposedException">La source du jeton d'annulation qui a créé<paramref name="cancellationToken" /> a déjà été supprimée.</exception>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="function" /> a la valeur null.ouL'argument <paramref name="scheduler" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="creationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crée et lance une tâche.</summary>
      <returns>Tâche démarrée.</returns>
      <param name="function">Délégué de fonction qui retourne le résultat à venir qui sera mis à disposition via la tâche.</param>
      <param name="state">Objet contenant les données que le délégué <paramref name="function" /> doit utiliser.</param>
      <param name="creationOptions">L'une des valeurs d'énumération qui contrôlent le comportement de la tâche créée.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="function" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="creationOptions" /> spécifie une valeur non valide.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskScheduler">
      <summary>Représente un objet qui gère les tâches de bas niveau de mise en file d'attente des tâches sur les threads.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.#ctor">
      <summary>Initialise le <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Current">
      <summary>Obtient le <see cref="T:System.Threading.Tasks.TaskScheduler" /> associé à la tâche en cours d'exécution.</summary>
      <returns>Retourne le <see cref="T:System.Threading.Tasks.TaskScheduler" /> associé à la tâche en cours d'exécution.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Default">
      <summary>Obtient l'instance de <see cref="T:System.Threading.Tasks.TaskScheduler" /> par défaut fournie par le .NET Framework.</summary>
      <returns>Retourne l'instance par défaut de <see cref="T:System.Threading.Tasks.TaskScheduler" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext">
      <summary>Crée un <see cref="T:System.Threading.Tasks.TaskScheduler" /> associé au <see cref="T:System.Threading.SynchronizationContext" /> actuel.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> associé au <see cref="T:System.Threading.SynchronizationContext" /> actuel, selon la <see cref="P:System.Threading.SynchronizationContext.Current" />.</returns>
      <exception cref="T:System.InvalidOperationException">Le SynchronizationContext actuel ne peut pas être utilisé en tant que TaskScheduler.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.GetScheduledTasks">
      <summary>Pour la prise en charge du débogueur uniquement, génère un énumérateur d'instances de <see cref="T:System.Threading.Tasks.Task" /> actuellement en attente d'exécution dans la file d'attente sur le planificateur.</summary>
      <returns>Énumérateur qui autorise un débogueur à parcourir les tâches actuellement mises en file d'attente sur ce planificateur.</returns>
      <exception cref="T:System.NotSupportedException">Ce planificateur ne peut pas générer une liste de tâches en file d'attente actuellement.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Id">
      <summary>Obtient l'ID unique pour ce <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <returns>Retourne l'ID unique de ce <see cref="T:System.Threading.Tasks.TaskScheduler" />.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.MaximumConcurrencyLevel">
      <summary>Indique le niveau d'accès concurrentiel maximal que ce <see cref="T:System.Threading.Tasks.TaskScheduler" /> peut prendre en charge.</summary>
      <returns>Retourne un entier qui représente le niveau d'accès concurrentiel maximal.Le planificateur par défaut retourne <see cref="F:System.Int32.MaxValue" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.QueueTask(System.Threading.Tasks.Task)">
      <summary>Met en file d'attente une <see cref="T:System.Threading.Tasks.Task" /> sur le planificateur. </summary>
      <param name="task">
        <see cref="T:System.Threading.Tasks.Task" /> à mettre en file d'attente.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="task" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryDequeue(System.Threading.Tasks.Task)">
      <summary>Tente de sortir de la file d'attente une <see cref="T:System.Threading.Tasks.Task" /> qui était précédemment dans la file d'attente de ce planificateur.</summary>
      <returns>Valeur booléenne qui indique si l'argument <paramref name="task" /> a bien été retiré de la file d'attente.</returns>
      <param name="task">
        <see cref="T:System.Threading.Tasks.Task" /> à sortir de la file d'attente.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="task" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTask(System.Threading.Tasks.Task)">
      <summary>Tente d'exécuter la <see cref="T:System.Threading.Tasks.Task" /> fournie sur ce planificateur.</summary>
      <returns>Valeur booléenne true si la <paramref name="task" /> a été exécutée avec succès ; false, dans le cas contraire.L'échec de l'exécution est souvent dû au fait que la tâche a déjà été exécutée ou qu'elle est en cours d'exécution par un autre thread.</returns>
      <param name="task">Objet <see cref="T:System.Threading.Tasks.Task" /> à exécuter.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="task" /> n'est pas associé à ce planificateur.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
      <summary>Détermine si la <see cref="T:System.Threading.Tasks.Task" /> fournie peut être exécutée de façon synchrone dans cet appel et, si c'est le cas, l'exécute.</summary>
      <returns>Valeur booléenne qui indique si la tâche a été exécutée inline.</returns>
      <param name="task">
        <see cref="T:System.Threading.Tasks.Task" /> à exécuter.</param>
      <param name="taskWasPreviouslyQueued">Valeur booléenne qui indique si une tâche a déjà été mise en file d'attente.Si ce paramètre a la valeur True, la tâche a pu être mise en file d'attente (par planification) précédemment ; s'il a la valeur False, cela signifie que la tâche a été mise en file d'attente et que cet appel est passé pour exécuter la tâche inline sans mise en file d'attente.</param>
      <exception cref="T:System.ArgumentNullException">L'argument <paramref name="task" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="task" /> a déjà été exécuté.</exception>
    </member>
    <member name="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException">
      <summary>Se produit lorsqu'une exception non prise en charge d'une tâche défaillante va déclencher la stratégie d'escalade de l'exception qui, par défaut, arrête le processus.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskSchedulerException">
      <summary>Représente une exception utilisée pour communiquer une opération non valide par un <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> avec un message système qui décrit l'erreur.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> à l'aide du message d'erreur par défaut et d'une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="innerException">Exception qui constitue la cause de l'exception actuelle.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> avec un message spécifié décrivant l'erreur.</summary>
      <param name="message">Message qui décrit l'exception.L'appelant de ce constructeur doit vérifier que cette chaîne a été localisée pour la culture du système en cours.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> avec un message d'erreur spécifié et une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="message">Message qui décrit l'exception.L'appelant de ce constructeur doit vérifier que cette chaîne a été localisée pour la culture du système en cours.</param>
      <param name="innerException">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="innerException" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskStatus">
      <summary>Représente l'étape en cours dans le cycle de vie d'une <see cref="T:System.Threading.Tasks.Task" />.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Canceled">
      <summary>La tâche a accepté l'annulation en levant OperationCanceledException avec son propre CancellationToken, alors que l'état du jeton était défini comme signalé ou que le CancellationToken de la tâche était déjà signalé avant le début de l'exécution de la tâche.Pour plus d'informations, consultez Annulation de tâches.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Created">
      <summary>La tâche a été initialisée mais n'a pas encore été planifiée.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Faulted">
      <summary>Tâche terminée suite à une exception non gérée.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.RanToCompletion">
      <summary>L'exécution de la tâche s'est terminée avec succès.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Running">
      <summary>La tâche est en cours d'exécution mais n'est pas encore terminée.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForActivation">
      <summary>La tâche attend son activation et sa planification en interne par l'infrastructure .NET Framework.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForChildrenToComplete">
      <summary>L'exécution de la tâche est terminée et cette dernière attend implicitement la fin des tâches enfants associées.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingToRun">
      <summary>L'exécution de la tâche a été planifiée mais n'a pas encore démarré.</summary>
    </member>
    <member name="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs">
      <summary>Fournit des données sur l'événement déclenché lorsqu'une exception d'une <see cref="T:System.Threading.Tasks.Task" /> défaillante n'est pas prise en charge.</summary>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.#ctor(System.AggregateException)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs" /> avec l'exception non prise en charge.</summary>
      <param name="exception">Exception qui n'a pas été prise en charge.</param>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception">
      <summary>Exception qui n'a pas été prise en charge.</summary>
      <returns>Exception qui n'a pas été prise en charge.</returns>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Observed">
      <summary>Détermine si cette exception a été signalée comme « observée ».</summary>
      <returns>true si cette exception a été signalée comme « observée » ; sinon, false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.SetObserved">
      <summary>Signale la <see cref="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception" /> comme « observée » et l'empêche ainsi de déclencher la stratégie d'escalade de l'exception qui, par défaut, arrête le processus.</summary>
    </member>
  </members>
</doc>