﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks</name>
  </assembly>
  <members>
    <member name="T:System.AggregateException">
      <summary>Rappresenta uno o più errori che si verificano durante l'esecuzione dell'applicazione.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.AggregateException" /> con un messaggio fornito dal sistema in cui viene descritto l'errore.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.AggregateException" /> con riferimenti alle eccezioni interne che sono la causa dell'eccezione.</summary>
      <param name="innerExceptions">Eccezioni che sono la causa dell'eccezione corrente.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="innerExceptions" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Un elemento di <paramref name="innerExceptions" /> è null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Exception[])">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.AggregateException" /> con riferimenti alle eccezioni interne che sono la causa dell'eccezione.</summary>
      <param name="innerExceptions">Eccezioni che sono la causa dell'eccezione corrente.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="innerExceptions" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Un elemento di <paramref name="innerExceptions" /> è null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.AggregateException" /> con un messaggio specifico in cui viene descritto l'errore.</summary>
      <param name="message">Messaggio in cui viene descritta l'eccezione.È necessario che il chiamante del costruttore assicuri che la stringa sia stata localizzata per le impostazioni cultura correnti del sistema.</param>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.AggregateException" /> con un messaggio di errore specifico e riferimenti alle eccezioni interne che sono la causa dell'eccezione.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione</param>
      <param name="innerExceptions">Eccezioni che sono la causa dell'eccezione corrente.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="innerExceptions" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Un elemento di <paramref name="innerExceptions" /> è null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.AggregateException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa di questa eccezione.</summary>
      <param name="message">Messaggio in cui viene descritta l'eccezione.È necessario che il chiamante del costruttore assicuri che la stringa sia stata localizzata per le impostazioni cultura correnti del sistema.</param>
      <param name="innerException">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="innerException" /> è null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception[])">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.AggregateException" /> con un messaggio di errore specifico e riferimenti alle eccezioni interne che sono la causa dell'eccezione.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione</param>
      <param name="innerExceptions">Eccezioni che sono la causa dell'eccezione corrente.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="innerExceptions" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Un elemento di <paramref name="innerExceptions" /> è null.</exception>
    </member>
    <member name="M:System.AggregateException.Flatten">
      <summary>Semplifica le istanze dell'oggetto <see cref="T:System.AggregateException" /> in una singola nuova istanza.</summary>
      <returns>Nuovo oggetto <see cref="T:System.AggregateException" /> semplificato.</returns>
    </member>
    <member name="M:System.AggregateException.GetBaseException">
      <summary>Restituisce l'oggetto <see cref="T:System.AggregateException" /> che rappresenta la causa principale dell'eccezione corrente.</summary>
      <returns>Restituisce l'oggetto <see cref="T:System.AggregateException" /> che rappresenta la causa principale dell'eccezione corrente.</returns>
    </member>
    <member name="M:System.AggregateException.Handle(System.Func{System.Exception,System.Boolean})">
      <summary>Richiama un gestore su ogni oggetto <see cref="T:System.Exception" /> contenuto da questo oggetto <see cref="T:System.AggregateException" />.</summary>
      <param name="predicate">Predicato da eseguire per ogni eccezione.Il predicato accetta come argomento l'oggetto <see cref="T:System.Exception" /> da elaborare e restituisce un valore booleano per indicare se l'eccezione è stata gestita.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="predicate" /> è null.</exception>
      <exception cref="T:System.AggregateException">Un'eccezione contenuta da questo <see cref="T:System.AggregateException" /> non è stata gestita.</exception>
    </member>
    <member name="P:System.AggregateException.InnerExceptions">
      <summary>Ottiene una raccolta in sola lettura delle istanze di <see cref="T:System.Exception" /> che hanno causato l'eccezione corrente.</summary>
      <returns>Restituisce un insieme di sola lettura delle istanze di <see cref="T:System.Exception" /> che hanno causato l'eccezione corrente.</returns>
    </member>
    <member name="M:System.AggregateException.ToString">
      <summary>Crea e restituisce una rappresentazione in forma di stringa dell'oggetto <see cref="T:System.AggregateException" /> corrente.</summary>
      <returns>Una rappresentazione in forma di stringa dell'oggetto.</returns>
    </member>
    <member name="T:System.OperationCanceledException">
      <summary>Eccezione generata in un thread all'annullamento di un'operazione eseguita dal thread.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.OperationCanceledException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.OperationCanceledException" /> con un messaggio di errore fornito dal sistema.</summary>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.OperationCanceledException" /> con un messaggio di errore specificato.</summary>
      <param name="message">Oggetto <see cref="T:System.String" /> che descrive l'errore.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.OperationCanceledException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa di questa eccezione.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="innerException">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception,System.Threading.CancellationToken)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.OperationCanceledException" /> con un messaggio di errore specificato, un riferimento all'eccezione interna che è la causa di questa eccezione e un token di annullamento.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="innerException">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
      <param name="token">Token di annullamento associato all'operazione annullata.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Threading.CancellationToken)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.OperationCanceledException" /> con un messaggio di errore specificato e un token di annullamento.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione</param>
      <param name="token">Token di annullamento associato all'operazione annullata.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.Threading.CancellationToken)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.OperationCanceledException" /> con un token di annullamento.</summary>
      <param name="token">Token di annullamento associato all'operazione annullata.</param>
    </member>
    <member name="P:System.OperationCanceledException.CancellationToken">
      <summary>Ottiene un token associato all'operazione annullata.</summary>
      <returns>Token associato all'operazione annullata oppure token predefinito.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder">
      <summary>Rappresenta un generatore per i metodi asincroni che restituiscono un'attività.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Programma la macchina a stati per passare all'azione successiva una volta completato l'elemento awaiter specificato.</summary>
      <param name="awaiter">L'elemento awaiter.</param>
      <param name="stateMachine">Macchina a stati.</param>
      <typeparam name="TAwaiter">Tipo dell'elemento awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo della macchina di stati.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Programma la macchina a stati per passare all'azione successiva una volta completato l'elemento awaiter specificato.Questo metodo può essere chiamato da codice parzialmente attendibile.</summary>
      <param name="awaiter">L'elemento awaiter.</param>
      <param name="stateMachine">Macchina a stati.</param>
      <typeparam name="TAwaiter">Tipo dell'elemento awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo della macchina di stati.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Create">
      <summary>Crea un'istanza della classe <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder" />.</summary>
      <returns>Nuova istanza del generatore.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetException(System.Exception)">
      <summary>Contrassegna l'attività come non riuscita e associa l'eccezione specificata all'attività.</summary>
      <param name="exception">Eccezione da associare all'attività.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">L'attività è già stata completata.- oppure -Il generatore non è inizializzato.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult">
      <summary>Contrassegna l'attività come completata correttamente.</summary>
      <exception cref="T:System.InvalidOperationException">L'attività è già stata completata.- oppure -Il generatore non è inizializzato.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Associa il generatore alla macchina a stati specificata.</summary>
      <param name="stateMachine">Istanza della macchina a stati da associare al generatore.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Macchina a stati precedentemente impostata.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Start``1(``0@)">
      <summary>Inizia l'esecuzione del generatore con la macchina a stati associata.</summary>
      <param name="stateMachine">Istanza della macchina a stati passata per riferimento.</param>
      <typeparam name="TStateMachine">Tipo della macchina di stati.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> è null.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Task">
      <summary>Ottiene l'attività per questo generatore.</summary>
      <returns>Attività per questo generatore.</returns>
      <exception cref="T:System.InvalidOperationException">Il generatore non è inizializzato.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1">
      <summary>Rappresenta un generatore per i metodi asincroni che restituisce un'attività e fornisce un parametro per il risultato.</summary>
      <typeparam name="TResult">Risultato da utilizzare per completare l'attività.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Programma la macchina a stati per passare all'azione successiva una volta completato l'elemento awaiter specificato.</summary>
      <param name="awaiter">L'elemento awaiter.</param>
      <param name="stateMachine">Macchina a stati.</param>
      <typeparam name="TAwaiter">Tipo dell'elemento awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo della macchina di stati.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Programma la macchina a stati per passare all'azione successiva una volta completato l'elemento awaiter specificato.Questo metodo può essere chiamato da codice parzialmente attendibile.</summary>
      <param name="awaiter">L'elemento awaiter.</param>
      <param name="stateMachine">Macchina a stati.</param>
      <typeparam name="TAwaiter">Tipo dell'elemento awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo della macchina di stati.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Create">
      <summary>Crea un'istanza della classe <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1" />.</summary>
      <returns>Nuova istanza del generatore.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetException(System.Exception)">
      <summary>Contrassegna l'attività come non riuscita e associa l'eccezione specificata all'attività.</summary>
      <param name="exception">Eccezione da associare all'attività.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">L'attività è già stata completata.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetResult(`0)">
      <summary>Contrassegna l'attività come completata correttamente.</summary>
      <param name="result">Risultato da utilizzare per completare l'attività.</param>
      <exception cref="T:System.InvalidOperationException">L'attività è già stata completata.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Associa il generatore alla macchina a stati specificata.</summary>
      <param name="stateMachine">Istanza della macchina a stati da associare al generatore.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Macchina a stati precedentemente impostata.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Start``1(``0@)">
      <summary>Inizia l'esecuzione del generatore con la macchina a stati associata.</summary>
      <param name="stateMachine">Istanza della macchina a stati passata per riferimento.</param>
      <typeparam name="TStateMachine">Tipo della macchina di stati.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> è null.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Task">
      <summary>Ottiene l'attività per questo generatore.</summary>
      <returns>Attività per questo generatore.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder">
      <summary>Rappresenta un generatore per i metodi asincroni che non restituiscono un valore.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Programma la macchina a stati per passare all'azione successiva una volta completato l'elemento awaiter specificato.</summary>
      <param name="awaiter">L'elemento awaiter.</param>
      <param name="stateMachine">Macchina a stati.</param>
      <typeparam name="TAwaiter">Tipo dell'elemento awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo della macchina di stati.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Programma la macchina a stati per passare all'azione successiva una volta completato l'elemento awaiter specificato.Questo metodo può essere chiamato da codice parzialmente attendibile.</summary>
      <param name="awaiter">L'elemento awaiter.</param>
      <param name="stateMachine">Macchina a stati.</param>
      <typeparam name="TAwaiter">Tipo dell'elemento awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo della macchina di stati.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Create">
      <summary>Crea un'istanza della classe <see cref="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder" />.</summary>
      <returns>Nuova istanza del generatore.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetException(System.Exception)">
      <summary>Associa un'eccezione al generatore di metodo.</summary>
      <param name="exception">Eccezione da associare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Il generatore non è inizializzato.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetResult">
      <summary>Contrassegna il generatore di metodi come correttamente completato.</summary>
      <exception cref="T:System.InvalidOperationException">Il generatore non è inizializzato.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Associa il generatore alla macchina a stati specificata.</summary>
      <param name="stateMachine">Istanza della macchina a stati da associare al generatore.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Macchina a stati precedentemente impostata.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start``1(``0@)">
      <summary>Inizia l'esecuzione del generatore con la macchina a stati associata.</summary>
      <param name="stateMachine">Istanza della macchina a stati passata per riferimento.</param>
      <typeparam name="TStateMachine">Tipo della macchina di stati.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> è null.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable">
      <summary>Fornisce un oggetto awaitable che consente la configurazione delle attese in un'attività.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.GetAwaiter">
      <summary>Restituisce un awaiter per questo oggetto awaitable.</summary>
      <returns>L'elemento awaiter.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1">
      <summary>Fornisce un oggetto awaitable che consente la configurazione delle attese in un'attività.</summary>
      <typeparam name="TResult">Tipo del risultato prodotto da questo oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.GetAwaiter">
      <summary>Restituisce un awaiter per questo oggetto awaitable.</summary>
      <returns>L'elemento awaiter.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter">
      <summary>Fornisce un awaiter per un oggetto awaitable(<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1" />).</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.GetResult">
      <summary>Termina l'attesa sull'attività completata.</summary>
      <returns>Risultato dell'attività completata.</returns>
      <exception cref="T:System.NullReferenceException">L'elemento awaiter non è stato inizializzato correttamente.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">L'attività è stata annullata.</exception>
      <exception cref="T:System.Exception">L'attività è stata completata con uno stato di errore.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.IsCompleted">
      <summary>Ottiene un valore che specifica se l'attività attesa è stata completata.</summary>
      <returns>true se l'attività attesa è stata completata; in caso contrario, false.</returns>
      <exception cref="T:System.NullReferenceException">L'elemento awaiter non è stato inizializzato correttamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>Programma l'azione di continuazione per l'attività associata a questo awaiter.</summary>
      <param name="continuation">Azione da richiamare al completamento dell'operazione di attesa.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="continuation" /> è null.</exception>
      <exception cref="T:System.NullReferenceException">L'elemento awaiter non è stato inizializzato correttamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Programma l'azione di continuazione per l'attività associata a questo awaiter. </summary>
      <param name="continuation">Azione da richiamare al completamento dell'operazione di attesa.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="continuation" /> è null.</exception>
      <exception cref="T:System.NullReferenceException">L'elemento awaiter non è stato inizializzato correttamente.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter">
      <summary>Fornisce un awaiter per un oggetto awaitable (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable" />).</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.GetResult">
      <summary>Termina l'attesa sull'attività completata.</summary>
      <exception cref="T:System.NullReferenceException">L'elemento awaiter non è stato inizializzato correttamente.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">L'attività è stata annullata.</exception>
      <exception cref="T:System.Exception">L'attività è stata completata con uno stato di errore.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.IsCompleted">
      <summary>Ottiene un valore che specifica se l'attività attesa è stata completata.</summary>
      <returns>true se l'attività attesa è stata completata; in caso contrario, false.</returns>
      <exception cref="T:System.NullReferenceException">L'elemento awaiter non è stato inizializzato correttamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>Programma l'azione di continuazione per l'attività associata a questo awaiter.</summary>
      <param name="continuation">Azione da richiamare al completamento dell'operazione di attesa.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="continuation" /> è null.</exception>
      <exception cref="T:System.NullReferenceException">L'elemento awaiter non è stato inizializzato correttamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Programma l'azione di continuazione per l'attività associata a questo awaiter. </summary>
      <param name="continuation">Azione da richiamare al completamento dell'operazione di attesa.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="continuation" /> è null.</exception>
      <exception cref="T:System.NullReferenceException">L'elemento awaiter non è stato inizializzato correttamente.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.IAsyncStateMachine">
      <summary>Rappresenta le macchine a stati generate per i metodi asincroni.Questo tipo è solo per uso del compilatore.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.MoveNext">
      <summary>Sposta la macchina a stati al relativo stato successivo.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Configura la macchina a stati con una replica allocata sull'heap.</summary>
      <param name="stateMachine">La replica allocata sull'heap.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.ICriticalNotifyCompletion">
      <summary>Rappresenta un awaiter che pianifica le continuazioni quando viene completata un'operazione di attesa.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ICriticalNotifyCompletion.UnsafeOnCompleted(System.Action)">
      <summary>Programma l'azione di continuazione che viene richiamata al completamento dell'istanza.</summary>
      <param name="continuation">Azione da richiamare al completamento dell'operazione.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="continuation" /> è null (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.INotifyCompletion">
      <summary>Rappresenta un'operazione che pianifica le continuazioni quando viene completata.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.INotifyCompletion.OnCompleted(System.Action)">
      <summary>Programma l'azione di continuazione che viene richiamata al completamento dell'istanza.</summary>
      <param name="continuation">Azione da richiamare al completamento dell'operazione.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="continuation" /> è null (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter">
      <summary>Fornisce un oggetto che attende il completamento di un'attività asincrona. </summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.GetResult">
      <summary>Termina l'attesa del completamento dell'attività asincrona.</summary>
      <exception cref="T:System.NullReferenceException">L'oggetto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> non è stato inizializzato correttamente.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">L'attività è stata annullata.</exception>
      <exception cref="T:System.Exception">L'attività è stata completata con uno stato di errore <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter.IsCompleted">
      <summary>Ottiene un valore che indica l'eventuale completamento dell'attività asincrona.</summary>
      <returns>true se l'attività è stata completata; in caso contrario, false.</returns>
      <exception cref="T:System.NullReferenceException">L'oggetto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> non è stato inizializzato correttamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.OnCompleted(System.Action)">
      <summary>Imposta l'azione da eseguire quando l'oggetto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> non è più in attesa del completamento dell'attività asincrona.</summary>
      <param name="continuation">Azione da eseguire al completamento dell'operazione di attesa.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> è null.</exception>
      <exception cref="T:System.NullReferenceException">L'oggetto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> non è stato inizializzato correttamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Programma l'azione di continuazione per l'attività asincrona associata a questo awaiter.</summary>
      <param name="continuation">Azione da richiamare al completamento dell'operazione di attesa.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">L'elemento awaiter non è stato inizializzato correttamente.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter`1">
      <summary>Rappresenta un oggetto che attende il completamento di un'attività asincrona e fornisce un parametro per il risultato.</summary>
      <typeparam name="TResult">Risultato dell'attività.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.GetResult">
      <summary>Termina l'attesa del completamento dell'attività asincrona.</summary>
      <returns>Risultato dell'attività completata.</returns>
      <exception cref="T:System.NullReferenceException">L'oggetto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> non è stato inizializzato correttamente.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">L'attività è stata annullata.</exception>
      <exception cref="T:System.Exception">L'attività è stata completata con uno stato di errore <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter`1.IsCompleted">
      <summary>Ottiene un valore che indica l'eventuale completamento dell'attività asincrona.</summary>
      <returns>true se l'attività è stata completata; in caso contrario, false.</returns>
      <exception cref="T:System.NullReferenceException">L'oggetto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> non è stato inizializzato correttamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.OnCompleted(System.Action)">
      <summary>Imposta l'azione da eseguire quando l'oggetto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> non è più in attesa del completamento dell'attività asincrona.</summary>
      <param name="continuation">Azione da eseguire al completamento dell'operazione di attesa.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> è null.</exception>
      <exception cref="T:System.NullReferenceException">L'oggetto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> non è stato inizializzato correttamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.UnsafeOnCompleted(System.Action)">
      <summary>Programma l'azione di continuazione per l'attività asincrona associata a questo awaiter.</summary>
      <param name="continuation">Azione da richiamare al completamento dell'operazione di attesa.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">L'elemento awaiter non è stato inizializzato correttamente.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable">
      <summary>Fornisce il contesto per l'attesa durante il passaggio asincrono a un ambiente di destinazione.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.GetAwaiter">
      <summary>Recupera un oggetto <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> per l'istanza della classe.</summary>
      <returns>Oggetto utilizzato per monitorare il completamento di un'operazione asincrona.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter">
      <summary>Fornisce un awaiter per passare a un ambiente di destinazione.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.GetResult">
      <summary>Termina l'operazione di attesa.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.IsCompleted">
      <summary>Ottiene un valore che indica se non è richiesta una parola chiave yield.</summary>
      <returns>Sempre false, che indica che è sempre necessaria una parola chiave yield per <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" />.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.OnCompleted(System.Action)">
      <summary>Imposta la continuazione da richiamare.</summary>
      <param name="continuation">L'azione da richiamare in modo asincrono.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Inserisce <paramref name="continuation" /> nuovamente nel contesto corrente.</summary>
      <param name="continuation">L'azione da richiamare in modo asincrono.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="continuation" /> è null.</exception>
    </member>
    <member name="T:System.Threading.CancellationToken">
      <summary>Propaga la notifica di richiesta di annullamento delle operazioni.</summary>
    </member>
    <member name="M:System.Threading.CancellationToken.#ctor(System.Boolean)">
      <summary>Inizializza <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="canceled">Stato di annullamento del token.</param>
    </member>
    <member name="P:System.Threading.CancellationToken.CanBeCanceled">
      <summary>Ottiene un valore che indica se questo token è in grado di essere in stato di annullamento.</summary>
      <returns>True se questo token è in grado di essere in stato di annullamento; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Object)">
      <summary>Determina se l'istanza di <see cref="T:System.Threading.CancellationToken" /> corrente è uguale all'oggetto <see cref="T:System.Object" /> specificato.</summary>
      <returns>True se <paramref name="other" /> è <see cref="T:System.Threading.CancellationToken" /> e se le due istanze sono uguali; in caso contrario, false.Due token sono uguali se sono associati allo stesso oggetto <see cref="T:System.Threading.CancellationTokenSource" /> oppure se entrambi sono stati costruiti a partire da costruttori CancellationToken pubblici e i relativi valori <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> sono uguali.</returns>
      <param name="other">L'altro oggetto con cui confrontare questa istanza.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Threading.CancellationToken)">
      <summary>Determina se l'istanza di <see cref="T:System.Threading.CancellationToken" /> corrente è uguale al token specificato.</summary>
      <returns>True se le istanze sono uguali; in caso contrario, false.Due token sono uguali se sono associati allo stesso oggetto <see cref="T:System.Threading.CancellationTokenSource" /> oppure se entrambi sono stati costruiti a partire da costruttori CancellationToken pubblici e i relativi valori <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> sono uguali.</returns>
      <param name="other">Altro oggetto <see cref="T:System.Threading.CancellationToken" /> con cui confrontare questa istanza.</param>
    </member>
    <member name="M:System.Threading.CancellationToken.GetHashCode">
      <summary>Viene usato come funzione hash per un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Codice hash per l'istanza di <see cref="T:System.Threading.CancellationToken" /> corrente.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.IsCancellationRequested">
      <summary>Ottiene un valore che indica se per questo token è stato richiesto l'annullamento.</summary>
      <returns>True se per questo token è stato richiesto l'annullamento; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.None">
      <summary>Restituisce un valore <see cref="T:System.Threading.CancellationToken" /> vuoto.</summary>
      <returns>Token di annullamento vuoto. </returns>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Equality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Determina se due istanze di <see cref="T:System.Threading.CancellationToken" /> sono uguali.</summary>
      <returns>True se le istanze sono uguali; in caso contrario, false.</returns>
      <param name="left">Prima istanza.</param>
      <param name="right">Seconda istanza.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Inequality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Determina se due istanze di <see cref="T:System.Threading.CancellationToken" /> non sono uguali.</summary>
      <returns>True se le istanze non sono uguali; in caso contrario, false.</returns>
      <param name="left">Prima istanza.</param>
      <param name="right">Seconda istanza.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action)">
      <summary>Registra un delegato che verrà chiamato quando questo oggetto <see cref="T:System.Threading.CancellationToken" /> viene annullato.</summary>
      <returns>Istanza di <see cref="T:System.Threading.CancellationTokenRegistration" /> che può essere usata per annullare la registrazione del callback.</returns>
      <param name="callback">Delegato da eseguire quando l'oggetto <see cref="T:System.Threading.CancellationToken" /> viene annullato.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action,System.Boolean)">
      <summary>Registra un delegato che verrà chiamato quando questo oggetto <see cref="T:System.Threading.CancellationToken" /> viene annullato.</summary>
      <returns>Istanza di <see cref="T:System.Threading.CancellationTokenRegistration" /> che può essere usata per annullare la registrazione del callback.</returns>
      <param name="callback">Delegato da eseguire quando l'oggetto <see cref="T:System.Threading.CancellationToken" /> viene annullato.</param>
      <param name="useSynchronizationContext">Valore booleano che indica se acquisire l'oggetto <see cref="T:System.Threading.SynchronizationContext" /> corrente e usarlo quando si richiama l'oggetto <paramref name="callback" />.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object)">
      <summary>Registra un delegato che verrà chiamato quando questo oggetto <see cref="T:System.Threading.CancellationToken" /> viene annullato.</summary>
      <returns>Istanza di <see cref="T:System.Threading.CancellationTokenRegistration" /> che può essere usata per annullare la registrazione del callback.</returns>
      <param name="callback">Delegato da eseguire quando l'oggetto <see cref="T:System.Threading.CancellationToken" /> viene annullato.</param>
      <param name="state">Stato da passare all'oggetto <paramref name="callback" /> quando il delegato viene richiamato.Può assumere valore Null.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object,System.Boolean)">
      <summary>Registra un delegato che verrà chiamato quando questo oggetto <see cref="T:System.Threading.CancellationToken" /> viene annullato.</summary>
      <returns>Istanza di <see cref="T:System.Threading.CancellationTokenRegistration" /> che può essere usata per annullare la registrazione del callback.</returns>
      <param name="callback">Delegato da eseguire quando l'oggetto <see cref="T:System.Threading.CancellationToken" /> viene annullato.</param>
      <param name="state">Stato da passare all'oggetto <paramref name="callback" /> quando il delegato viene richiamato.Può assumere valore Null.</param>
      <param name="useSynchronizationContext">Valore booleano che indica se acquisire l'oggetto <see cref="T:System.Threading.SynchronizationContext" /> corrente e usarlo quando si richiama l'oggetto <paramref name="callback" />.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.ThrowIfCancellationRequested">
      <summary>Genera un oggetto <see cref="T:System.OperationCanceledException" /> se è stato richiesto l'annullamento di questo token.</summary>
      <exception cref="T:System.OperationCanceledException">The token has had cancellation requested.</exception>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.CancellationToken.WaitHandle">
      <summary>Ottiene un oggetto <see cref="T:System.Threading.WaitHandle" /> che viene segnalato quando il token viene annullato.</summary>
      <returns>
        <see cref="T:System.Threading.WaitHandle" /> segnalato quando il token viene annullato.</returns>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.CancellationTokenRegistration">
      <summary>Rappresenta un delegato di callback registrato con <see cref="T:System.Threading.CancellationToken" />. </summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'istanza corrente della classe <see cref="T:System.Threading.CancellationTokenRegistration" />.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Object)">
      <summary>Determina se l'istanza di <see cref="T:System.Threading.CancellationTokenRegistration" /> corrente è uguale all'oggetto <see cref="T:System.Threading.CancellationTokenRegistration" /> specificato.</summary>
      <returns>True, se questa istanza e <paramref name="obj" /> sono uguali.In caso contrario, false.Due istanze di <see cref="T:System.Threading.CancellationTokenRegistration" /> sono uguali se entrambe si riferiscono all'output di una sola chiamata allo stesso metodo Register di <see cref="T:System.Threading.CancellationToken" />.</returns>
      <param name="obj">Altro oggetto con cui confrontare questa istanza.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Threading.CancellationTokenRegistration)">
      <summary>Determina se l'istanza di <see cref="T:System.Threading.CancellationTokenRegistration" /> corrente è uguale all'oggetto <see cref="T:System.Threading.CancellationTokenRegistration" /> specificato.</summary>
      <returns>True, se questa istanza e <paramref name="other" /> sono uguali.In caso contrario, false. Due istanze di <see cref="T:System.Threading.CancellationTokenRegistration" /> sono uguali se entrambe si riferiscono all'output di una sola chiamata allo stesso metodo Register di <see cref="T:System.Threading.CancellationToken" />.</returns>
      <param name="other">Altro oggetto <see cref="T:System.Threading.CancellationTokenRegistration" /> con cui confrontare questa istanza.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.GetHashCode">
      <summary>Funge da funzione hash per <see cref="T:System.Threading.CancellationTokenRegistration" />.</summary>
      <returns>Codice hash per l'istanza di <see cref="T:System.Threading.CancellationTokenRegistration" /> corrente.</returns>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Equality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>Determina se due istanze di <see cref="T:System.Threading.CancellationTokenRegistration" /> sono uguali.</summary>
      <returns>true se le istanze sono uguali; in caso contrario, false.</returns>
      <param name="left">Prima istanza.</param>
      <param name="right">Seconda istanza.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Inequality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>Determina se due istanze di <see cref="T:System.Threading.CancellationTokenRegistration" /> non sono uguali.</summary>
      <returns>true se le istanze non sono uguali; in caso contrario, false.</returns>
      <param name="left">Prima istanza.</param>
      <param name="right">Seconda istanza.</param>
    </member>
    <member name="T:System.Threading.CancellationTokenSource">
      <summary>Segnala a un oggetto <see cref="T:System.Threading.CancellationToken" /> che deve essere annullato.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.CancellationTokenSource" /> che verrà annullata dopo il ritardo specificato in millisecondi.</summary>
      <param name="millisecondsDelay">Intervallo di tempo di attesa, in millisecondi, prima dell'annullamento dell'oggetto <see cref="T:System.Threading.CancellationTokenSource" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsDelay" /> is less than -1. </exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.TimeSpan)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.CancellationTokenSource" /> che verrà annullata dopo l'intervallo di tempo specificato.</summary>
      <param name="delay">Intervallo di tempo di attesa prima dell'annullamento dell'oggetto <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" />.<see cref="P:System.TimeSpan.TotalMilliseconds" /> is less than -1 or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel">
      <summary>Comunica una richiesta di annullamento.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel(System.Boolean)">
      <summary>Passa una richiesta di annullamento e specifica se i callback restanti e le operazioni annullabili devono essere elaborati.</summary>
      <param name="throwOnFirstException">true se le eccezioni devono essere propagate immediatamente; in caso contrario, false.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.Int32)">
      <summary>Programma un'operazione di annullamento in questo oggetto <see cref="T:System.Threading.CancellationTokenSource" /> dopo il numero di millisecondi specificato.</summary>
      <param name="millisecondsDelay">Intervallo di tempo da attendere prima di annullare questo oggetto <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception thrown when <paramref name="millisecondsDelay" /> is less than -1.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.TimeSpan)">
      <summary>Pianifica un'operazione di annullamento su questo oggetto <see cref="T:System.Threading.CancellationTokenSource" /> dopo l'intervallo di tempo specificato.</summary>
      <param name="delay">Intervallo di tempo da attendere prima di annullare questo oggetto <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when <paramref name="delay" /> is less than -1 or greater than Int32.MaxValue.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Crea un oggetto <see cref="T:System.Threading.CancellationTokenSource" /> che sarà in stato annullato quando uno dei token di origine si troverà in stato annullato.</summary>
      <returns>Oggetto <see cref="T:System.Threading.CancellationTokenSource" /> collegato ai token di origine.</returns>
      <param name="token1">Primo token di annullamento da osservare.</param>
      <param name="token2">Secondo token di annullamento da osservare.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken[])">
      <summary>Crea un oggetto <see cref="T:System.Threading.CancellationTokenSource" /> che sarà nello stato annullato quando uno dei token di origine nella matrice specificata si troverà nello stato annullato.</summary>
      <returns>Oggetto <see cref="T:System.Threading.CancellationTokenSource" /> collegato ai token di origine.</returns>
      <param name="tokens">Matrice contenente le istanze del token di annullamento da osservare.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tokens" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tokens" /> is empty.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose">
      <summary>Rilascia tutte le risorse usate dall'istanza corrente della classe <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite usate dalla classe <see cref="T:System.Threading.CancellationTokenSource" /> e facoltativamente le risorse gestite.</summary>
      <param name="disposing">true per rilasciare sia le risorse gestite sia quelle non gestite; false per rilasciare solo le risorse non gestite.</param>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.IsCancellationRequested">
      <summary>Ottiene un valore che indica se per <see cref="T:System.Threading.CancellationTokenSource" /> è stato richiesto l'annullamento.</summary>
      <returns>true se per <see cref="T:System.Threading.CancellationTokenSource" /> è stato richiesto l'annullamento; in caso contrario false.</returns>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.Token">
      <summary>Ottiene l'oggetto <see cref="T:System.Threading.CancellationToken" /> associato a questo oggetto <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.CancellationToken" /> associato a questo oggetto <see cref="T:System.Threading.CancellationTokenSource" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The token source has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair">
      <summary>Fornisce utilità di pianificazione delle attività coordinate per eseguire attività assicurando al contempo che le attività simultanee possano essere eseguite contemporaneamente a differenza delle attività esclusive.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> destinata all'utilità di pianificazione specificata.</summary>
      <param name="taskScheduler">Utilità di pianificazione di destinazione in base alla quale eseguire questa coppia.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> che si rivolge all'utilità di pianificazione specificata con un livello di concorrenza massimo.</summary>
      <param name="taskScheduler">Utilità di pianificazione di destinazione in base alla quale eseguire questa coppia.</param>
      <param name="maxConcurrencyLevel">Numero massimo di attività in esecuzione simultaneamente.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> che si rivolge all'utilità di pianificazione specificata con un livello di concorrenza massimo e un numero massimo di attività pianificate che possono essere elaborate come unità.</summary>
      <param name="taskScheduler">Utilità di pianificazione di destinazione in base alla quale eseguire questa coppia.</param>
      <param name="maxConcurrencyLevel">Numero massimo di attività in esecuzione simultaneamente.</param>
      <param name="maxItemsPerTask">Il numero massimo di attività da elaborare per ogni attività pianificata sottostante utilizzata dalla coppia.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Complete">
      <summary>Indica alla coppia dell'utilità di pianificazione di non accettare altre attività.</summary>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Completion">
      <summary>Ottiene <see cref="T:System.Threading.Tasks.Task" /> che verrà completato al termine dell'elaborazione da parte dell'utilità di pianificazione.</summary>
      <returns>L'operazione asincrona che verrà completata quando l'utilità di pianificazione completa l'elaborazione.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ConcurrentScheduler">
      <summary>Ottiene un oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> che è possibile utilizzare per pianificare le attività per la coppia eseguibili contemporaneamente ad altre attività sulla coppia.</summary>
      <returns>Oggetto che può essere utilizzato per pianificare le attività simultaneamente.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ExclusiveScheduler">
      <summary>Ottiene un oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> che è possibile utilizzare per pianificare le attività per la coppia da eseguire esclusivamente in relazione ad altre attività sulla coppia.</summary>
      <returns>Oggetto che può essere utilizzato per pianificare le attività che non vengono eseguite contemporaneamente ad altre attività.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task">
      <summary>Rappresenta un'operazione asincrona.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere Origine riferimento.</summary>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> con l'azione specificata.</summary>
      <param name="action">Delegato che rappresenta il codice da eseguire nell'attività.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> con l'azione specificata e <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="action">Delegato che rappresenta il codice da eseguire nell'attività.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà considerato dalla nuova attività.</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> con l'azione e le opzioni di creazione specificate.</summary>
      <param name="action">Delegato che rappresenta il codice da eseguire nell'attività.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà considerato dalla nuova attività.</param>
      <param name="creationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usato per personalizzare il comportamento dell'attività.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> con l'azione e le opzioni di creazione specificate.</summary>
      <param name="action">Delegato che rappresenta il codice da eseguire nell'attività.</param>
      <param name="creationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usato per personalizzare il comportamento dell'attività. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> con l'azione e lo stato specificati.</summary>
      <param name="action">Delegato che rappresenta il codice da eseguire nell'attività.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> con l'azione, lo stato e le opzioni specificati.</summary>
      <param name="action">Delegato che rappresenta il codice da eseguire nell'attività.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà osservato dalla nuova attività.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> con l'azione, lo stato e le opzioni specificati.</summary>
      <param name="action">Delegato che rappresenta il codice da eseguire nell'attività.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà osservato dalla nuova attività.</param>
      <param name="creationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usato per personalizzare il comportamento dell'attività.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> con l'azione, lo stato e le opzioni specificati.</summary>
      <param name="action">Delegato che rappresenta il codice da eseguire nell'attività.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione.</param>
      <param name="creationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usato per personalizzare il comportamento dell'attività.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.AsyncState">
      <summary>Ottiene l'oggetto stato fornito quando è stato creato <see cref="T:System.Threading.Tasks.Task" /> oppure null se non ne è stato fornito alcuno.</summary>
      <returns>Oggetto <see cref="T:System.Object" /> che rappresenta i dati relativi allo stato passati all'attività al momento della creazione.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CompletedTask">
      <summary>Ottiene un'attività già completata correttamente. </summary>
      <returns>Attività completata correttamente. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.ConfigureAwait(System.Boolean)">
      <summary>Configura un elemento awaiter usato per attendere questo oggetto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Oggetto usato per attendere questa attività.</returns>
      <param name="continueOnCapturedContext">true per provare a effettuare il marshalling della continuazione nel contesto originale acquisito; in caso contrario, false.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Crea una continuazione che riceve un token di annullamento e viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'attività di destinazione in base all'oggetto <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> specificato.La continuazione riceve un token di annullamento e usa un'utilità di pianificazione specificata.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire in base all'oggetto <paramref name="continuationOptions" /> specificato.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'attività di destinazione in base all'oggetto <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> specificato.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire in base all'oggetto <paramref name="continuationOptions" /> specificato.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione.La continuazione usa un'utilità di pianificazione specificata.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. -or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object)">
      <summary>Crea una continuazione che riceve informazioni sullo stato fornite dal chiamante e viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione. </summary>
      <returns>Nuova attività di continuazione. </returns>
      <param name="continuationAction">Azione da eseguire al completamento dell'attività.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante vengono passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione di continuazione. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Crea una continuazione che riceve informazioni sullo stato fornite dal chiamante e un token di annullamento e che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione di continuazione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che riceve informazioni sullo stato fornite dal chiamante e un token di annullamento e che viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione.La continuazione viene eseguita in base a un set di condizioni specificate e usa un'utilità di pianificazione specificata.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione di continuazione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuazione che riceve informazioni sullo stato fornite dal chiamante e viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione.La continuazione viene eseguita in base a un set di condizioni specificate.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione di continuazione.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che riceve informazioni sullo stato fornite dal chiamante e viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione.La continuazione usa un'utilità di pianificazione specificata.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione di continuazione.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0})">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione e restituisce un valore. </summary>
      <returns>Nuova attività di continuazione. </returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <typeparam name="TResult"> Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione e restituisce un valore.La continuazione riceve un token di annullamento.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <typeparam name="TResult"> Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita in base alle opzioni di continuazione specificate e restituisce un valore.La continuazione riceve un token di annullamento e usa un'utilità di pianificazione specificata.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire in base al parametro specificato <paramref name="continuationOptions." /> Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <typeparam name="TResult"> Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuazione che viene eseguita in base alle opzioni di continuazione specificate e restituisce un valore. </summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire in base alla condizione specificata in <paramref name="continuationOptions" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TResult"> Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione e restituisce un valore.La continuazione usa un'utilità di pianificazione specificata.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <typeparam name="TResult"> Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object)">
      <summary>Crea una continuazione che riceve informazioni sullo stato fornite dal chiamante e viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione, quindi restituisce un valore. </summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione di continuazione.</param>
      <typeparam name="TResult">Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione e restituisce un valore.La continuazione riceve informazioni sullo stato fornite dal chiamante e un token di annullamento.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione di continuazione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <typeparam name="TResult">Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita in base alle opzioni specificate per la continuazione delle attività al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione, quindi restituisce un valore.La continuazione riceve informazioni sullo stato fornite dal chiamante e un token di annullamento, quindi usa l'utilità di pianificazione specificata.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione di continuazione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <typeparam name="TResult">Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuazione che viene eseguita in base alle opzioni specificate per la continuazione delle attività al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione.La continuazione riceve informazioni sullo stato fornite dal chiamante.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione di continuazione.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TResult">Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di destinazione.La continuazione riceve informazioni sullo stato fornite dal chiamante e usa un'utilità di pianificazione specificata.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione di continuazione.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <typeparam name="TResult">Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.CreationOptions">
      <summary>Ottiene l'oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usato per creare questa attività.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usato per creare questa attività.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CurrentId">
      <summary>Restituisce l'ID univoco dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> attualmente in esecuzione.</summary>
      <returns>Intero assegnato dal sistema all'attività attualmente in esecuzione.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32)">
      <summary>Crea un'attività che viene completata dopo un ritardo di tempo. </summary>
      <returns>Attività che rappresenta il ritardo di tempo. </returns>
      <param name="millisecondsDelay">Numero di millisecondi prima del completamento dell'attività restituita oppure -1 per un'attesa indefinita. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32,System.Threading.CancellationToken)">
      <summary>Crea un'attività annullabile che viene completata dopo un ritardo di tempo. </summary>
      <returns>Attività che rappresenta il ritardo di tempo. </returns>
      <param name="millisecondsDelay">Numero di millisecondi prima del completamento dell'attività restituita oppure -1 per un'attesa indefinita. </param>
      <param name="cancellationToken">Token di annullamento che verrà controllato prima del completamento dell'attività restituita. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled. </exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan)">
      <summary>Crea un'attività che viene completata dopo un intervallo di tempo specificato. </summary>
      <returns>Attività che rappresenta il ritardo di tempo. </returns>
      <param name="delay">Intervallo di tempo da attendere prima del completamento dell'attività restituita oppure TimeSpan.FromMilliseconds(-1) per un'attesa indefinita. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Crea un'attività annullabile che viene completata dopo un intervallo di tempo specificato. </summary>
      <returns>Attività che rappresenta il ritardo di tempo. </returns>
      <param name="delay">Intervallo di tempo da attendere prima del completamento dell'attività restituita oppure TimeSpan.FromMilliseconds(-1) per un'attesa indefinita. </param>
      <param name="cancellationToken">Token di annullamento che verrà controllato prima del completamento dell'attività restituita. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Exception">
      <summary>Ottiene l'oggetto <see cref="T:System.AggregateException" /> che ha causato l'interruzione anomala di <see cref="T:System.Threading.Tasks.Task" />.Se l'oggetto <see cref="T:System.Threading.Tasks.Task" /> è stato completato correttamente o non ha ancora generato alcuna eccezione, verrà restituito null.</summary>
      <returns>Oggetto <see cref="T:System.AggregateException" /> che ha causato l'interruzione anomala di <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Factory">
      <summary>Fornisce l'accesso ai metodi factory per la creazione e la configurazione delle istanze di <see cref="T:System.Threading.Tasks.Task" /> e <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto factory in grado di creare una vasta gamma di oggetti <see cref="T:System.Threading.Tasks.Task" /> e <see cref="T:System.Threading.Tasks.Task`1" />. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled(System.Threading.CancellationToken)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che risulta completato a causa dell'annullamento con un token di annullamento specificato.</summary>
      <returns>Attività annullata. </returns>
      <param name="cancellationToken">Token di annullamento con cui completare l'attività. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled``1(System.Threading.CancellationToken)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che risulta completato a causa dell'annullamento con un token di annullamento specificato.</summary>
      <returns>Attività annullata. </returns>
      <param name="cancellationToken">Token di annullamento con cui completare l'attività. </param>
      <typeparam name="TResult">Tipo del risultato restituito dall'attività. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException``1(System.Exception)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che risulta completato con un'eccezione specificata. </summary>
      <returns>Attività in errore. </returns>
      <param name="exception">Eccezione con cui completare l'attività. </param>
      <typeparam name="TResult">Tipo del risultato restituito dall'attività. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException(System.Exception)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che risulta completato con un'eccezione specificata. </summary>
      <returns>Attività in errore. </returns>
      <param name="exception">Eccezione con cui completare l'attività. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromResult``1(``0)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> completato correttamente con il risultato specificato.</summary>
      <returns>Attività completata correttamente.</returns>
      <param name="result">Risultato da archiviare nell'attività completata. </param>
      <typeparam name="TResult">Tipo del risultato restituito dall'attività. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.GetAwaiter">
      <summary>Ottiene un elemento awaiter usato per attendere questo oggetto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Istanza di awaiter.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Id">
      <summary>Ottiene un ID univoco per questa istanza di <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Intero assegnato dal sistema a questa istanza dell'attività. </returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCanceled">
      <summary>Ottiene un valore che indica se l'esecuzione di questa istanza di <see cref="T:System.Threading.Tasks.Task" /> è stata completata perché annullata.</summary>
      <returns>true se l'attività è stata completata perché annullata; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCompleted">
      <summary>Ottiene un valore che indica se questo oggetto <see cref="T:System.Threading.Tasks.Task" /> è stato completato.</summary>
      <returns>true se l'attività è stata completata; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsFaulted">
      <summary>Ottiene un valore che indica se l'oggetto <see cref="T:System.Threading.Tasks.Task" /> è stato completato a causa di un'eccezione non gestita.</summary>
      <returns>true se l'attività ha generato un'eccezione non gestita; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action)">
      <summary>Accoda il lavoro specificato da eseguire in ThreadPool e restituisce un handle di attività per tale lavoro.</summary>
      <returns>Attività che rappresenta il lavoro in coda da eseguire in ThreadPool.</returns>
      <param name="action">Lavoro da eseguire in modo asincrono.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action,System.Threading.CancellationToken)">
      <summary>Accoda il lavoro specificato da eseguire in ThreadPool e restituisce un handle di attività per tale lavoro.</summary>
      <returns>Attività che rappresenta il lavoro in coda da eseguire in ThreadPool.</returns>
      <param name="action">Lavoro da eseguire in modo asincrono.</param>
      <param name="cancellationToken">Token di annullamento da usare per annullare il lavoro.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
      <summary>Accoda il lavoro specificato da eseguire in ThreadPool e restituisce un proxy per l'oggetto Task(TResult) restituito da <paramref name="function" />.</summary>
      <returns>Oggetto Task(TResult) che rappresenta un proxy per l'oggetto Task(TResult) restituito da <paramref name="function" />.</returns>
      <param name="function">Lavoro da eseguire in modo asincrono.</param>
      <typeparam name="TResult">Tipo del risultato restituito dall'attività proxy.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>Accoda il lavoro specificato da eseguire in ThreadPool e restituisce un proxy per l'oggetto Task(TResult) restituito da <paramref name="function" />.</summary>
      <returns>Oggetto Task(TResult) che rappresenta un proxy per l'oggetto Task(TResult) restituito da <paramref name="function" />.</returns>
      <param name="function">Lavoro da eseguire in modo asincrono.</param>
      <param name="cancellationToken">Token di annullamento da usare per annullare il lavoro.</param>
      <typeparam name="TResult">Tipo del risultato restituito dall'attività proxy.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task})">
      <summary>Accoda il lavoro specificato da eseguire in ThreadPool e restituisce un proxy per l'attività restituita da <paramref name="function" />.</summary>
      <returns>Attività che rappresenta un proxy per l'attività restituita da <paramref name="function" />.</returns>
      <param name="function">Lavoro da eseguire in modo asincrono.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Accoda il lavoro specificato da eseguire in ThreadPool e restituisce un proxy per l'attività restituita da <paramref name="function" />.</summary>
      <returns>Attività che rappresenta un proxy per l'attività restituita da <paramref name="function" />.</returns>
      <param name="function">Lavoro da eseguire in modo asincrono. </param>
      <param name="cancellationToken">Token di annullamento da usare per annullare il lavoro. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0})">
      <summary>Accoda il lavoro specificato da eseguire nel pool di thread e restituisce un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta tale lavoro. </summary>
      <returns>Oggetto attività che rappresenta il lavoro in cosa da eseguire nel pool di thread. </returns>
      <param name="function">Lavoro da eseguire in modo asincrono. </param>
      <typeparam name="TResult">Tipo restituito dell'attività. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>Accoda il lavoro specificato da eseguire nel pool di thread e restituisce un handle di Task(TResult) per tale lavoro.</summary>
      <returns>Oggetto Task(TResult) che rappresenta il lavoro in coda da eseguire in ThreadPool.</returns>
      <param name="function">Lavoro da eseguire in modo asincrono.</param>
      <param name="cancellationToken">Token di annullamento da usare per annullare il lavoro.</param>
      <typeparam name="TResult">Tipo di risultato dell'attività.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously">
      <summary>Esegue <see cref="T:System.Threading.Tasks.Task" /> in modo sincrono nell'oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> corrente.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously(System.Threading.Tasks.TaskScheduler)">
      <summary>Esegue <see cref="T:System.Threading.Tasks.Task" /> in modo sincrono nell'oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> fornito.</summary>
      <param name="scheduler">Utilità di pianificazione in cui provare a eseguire questa attività inline.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start">
      <summary>Avvia <see cref="T:System.Threading.Tasks.Task" />, pianificandone l'esecuzione nell'oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> corrente.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start(System.Threading.Tasks.TaskScheduler)">
      <summary>Avvia <see cref="T:System.Threading.Tasks.Task" />, pianificandone l'esecuzione nell'oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> specificato.</summary>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> a cui associare e con cui eseguire questa attività.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Status">
      <summary>Ottiene l'oggetto <see cref="T:System.Threading.Tasks.TaskStatus" /> di questa attività.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.TaskStatus" /> corrente di questa istanza dell'attività.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#AsyncWaitHandle">
      <summary>Ottiene un oggetto <see cref="T:System.Threading.WaitHandle" /> che può essere usato per attendere il completamento dell'attività.</summary>
      <returns>Oggetto <see cref="T:System.Threading.WaitHandle" /> che può essere usato per attendere il completamento dell'attività.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#CompletedSynchronously">
      <summary>Ottiene un valore che indica se l'operazione è stata completata in modo sincrono.</summary>
      <returns>true se l'operazione è stata completata in modo sincrono; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait">
      <summary>Attende il completamento dell'esecuzione di <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32)">
      <summary>Attende il completamento dell'esecuzione di <see cref="T:System.Threading.Tasks.Task" /> entro un numero specificato di millisecondi.</summary>
      <returns>true se <see cref="T:System.Threading.Tasks.Task" /> ha completato l'esecuzione nel tempo consentito; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Attende il completamento dell'esecuzione di <see cref="T:System.Threading.Tasks.Task" />.L'attesa termina se si esaurisce l'intervallo di timeout o se un token di annullamento viene annullato prima del completamento dell'attività.</summary>
      <returns>true se <see cref="T:System.Threading.Tasks.Task" /> ha completato l'esecuzione nel tempo consentito; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita. </param>
      <param name="cancellationToken">Token di annullamento da osservare durante l'attesa del completamento dell'attività. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Threading.CancellationToken)">
      <summary>Attende il completamento dell'esecuzione di <see cref="T:System.Threading.Tasks.Task" />.L'attesa termina se un token di annullamento viene annullato prima del completamento dell'attività.</summary>
      <param name="cancellationToken">Token di annullamento da osservare durante l'attesa del completamento dell'attività. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The task has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.TimeSpan)">
      <summary>Attende il completamento dell'esecuzione di <see cref="T:System.Threading.Tasks.Task" /> entro un intervallo di tempo specificato.</summary>
      <returns>true se <see cref="T:System.Threading.Tasks.Task" /> ha completato l'esecuzione nel tempo consentito; in caso contrario, false.</returns>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[])">
      <summary>Attende il completamento dell'esecuzione di tutti gli oggetti <see cref="T:System.Threading.Tasks.Task" /> forniti.</summary>
      <param name="tasks">Matrice delle istanze di <see cref="T:System.Threading.Tasks.Task" /> per cui attendere.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.-or-The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> exception contains an <see cref="T:System.OperationCanceledException" /> exception in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32)">
      <summary>Attende il completamento dell'esecuzione di tutti gli oggetti <see cref="T:System.Threading.Tasks.Task" /> forniti entro un numero specificato di millisecondi.</summary>
      <returns>true se tutte le istanze di <see cref="T:System.Threading.Tasks.Task" /> hanno completato l'esecuzione nel tempo consentito; in caso contrario, false.</returns>
      <param name="tasks">Matrice delle istanze di <see cref="T:System.Threading.Tasks.Task" /> per cui attendere.</param>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>Attende il completamento dell'esecuzione di tutti gli oggetti <see cref="T:System.Threading.Tasks.Task" /> forniti entro un numero specificato di millisecondi o finché l'attesa non viene annullata.</summary>
      <returns>true se tutte le istanze di <see cref="T:System.Threading.Tasks.Task" /> hanno completato l'esecuzione nel tempo consentito; in caso contrario, false.</returns>
      <param name="tasks">Matrice delle istanze di <see cref="T:System.Threading.Tasks.Task" /> per cui attendere.</param>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> da osservare durante l'attesa del completamento delle attività.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>Attende il completamento dell'esecuzione di tutti gli oggetti <see cref="T:System.Threading.Tasks.Task" /> forniti a meno che l'attesa non venga annullata. </summary>
      <param name="tasks">Matrice delle istanze di <see cref="T:System.Threading.Tasks.Task" /> per cui attendere.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> da osservare durante l'attesa del completamento delle attività.</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>Attende il completamento dell'esecuzione di tutti gli oggetti <see cref="T:System.Threading.Tasks.Task" /> forniti annullabili entro un intervallo di tempo specificato.</summary>
      <returns>true se tutte le istanze di <see cref="T:System.Threading.Tasks.Task" /> hanno completato l'esecuzione nel tempo consentito; in caso contrario, false.</returns>
      <param name="tasks">Matrice delle istanze di <see cref="T:System.Threading.Tasks.Task" /> per cui attendere.</param>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null. </exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[])">
      <summary>Attende il completamento dell'esecuzione di uno qualsiasi degli oggetti <see cref="T:System.Threading.Tasks.Task" /> forniti.</summary>
      <returns>Indice dell'attività completata nell'argomento di matrice di <paramref name="tasks" />.</returns>
      <param name="tasks">Matrice delle istanze di <see cref="T:System.Threading.Tasks.Task" /> per cui attendere.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32)">
      <summary>Attende il completamento dell'esecuzione di uno qualsiasi degli oggetti <see cref="T:System.Threading.Tasks.Task" /> forniti entro un numero specificato di millisecondi.</summary>
      <returns>Indice dell'attività completata nell'argomento di matrice di <paramref name="tasks" />, oppure -1 in caso di timeout.</returns>
      <param name="tasks">Matrice delle istanze di <see cref="T:System.Threading.Tasks.Task" /> per cui attendere.</param>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>Attende il completamento dell'esecuzione di uno qualsiasi degli oggetti <see cref="T:System.Threading.Tasks.Task" /> forniti entro un numero specificato di millisecondi o finché un token di annullamento non viene annullato.</summary>
      <returns>Indice dell'attività completata nell'argomento di matrice di <paramref name="tasks" />, oppure -1 in caso di timeout.</returns>
      <param name="tasks">Matrice delle istanze di <see cref="T:System.Threading.Tasks.Task" /> per cui attendere. </param>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita. </param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> da osservare durante l'attesa del completamento di un'attività. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>Attende il completamento dell'esecuzione di uno qualsiasi degli oggetti <see cref="T:System.Threading.Tasks.Task" /> forniti a meno che l'attesa non venga annullata.</summary>
      <returns>Indice dell'attività completata nell'argomento di matrice di <paramref name="tasks" />.</returns>
      <param name="tasks">Matrice delle istanze di <see cref="T:System.Threading.Tasks.Task" /> per cui attendere. </param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> da osservare durante l'attesa del completamento di un'attività. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>Attende il completamento dell'esecuzione di uno qualsiasi degli oggetti <see cref="T:System.Threading.Tasks.Task" /> forniti entro un intervallo di tempo specificato.</summary>
      <returns>Indice dell'attività completata nell'argomento di matrice di <paramref name="tasks" />, oppure -1 in caso di timeout.</returns>
      <param name="tasks">Matrice delle istanze di <see cref="T:System.Threading.Tasks.Task" /> per cui attendere.</param>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>Crea un'attività che verrà completata in seguito al completamento di tutti gli oggetti <see cref="T:System.Threading.Tasks.Task`1" /> di una raccolta enumerabile. </summary>
      <returns>Attività che rappresenta il completamento di tutte le attività fornite. </returns>
      <param name="tasks">Attività in attesa del completamento. </param>
      <typeparam name="TResult">Tipo dell'attività completata. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>Crea un'attività che verrà completata in seguito al completamento di tutti gli oggetti <see cref="T:System.Threading.Tasks.Task" /> di una raccolta enumerabile.</summary>
      <returns>Attività che rappresenta il completamento di tutte le attività fornite. </returns>
      <param name="tasks">Attività in attesa del completamento.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Threading.Tasks.Task[])">
      <summary>Crea un'attività che verrà completata in seguito al completamento di tutti gli oggetti <see cref="T:System.Threading.Tasks.Task" /> di una matrice. </summary>
      <returns>Attività che rappresenta il completamento di tutte le attività fornite.</returns>
      <param name="tasks">Attività in attesa del completamento.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Threading.Tasks.Task{``0}[])">
      <summary>Crea un'attività che verrà completata in seguito al completamento di tutti gli oggetti <see cref="T:System.Threading.Tasks.Task`1" /> di una matrice. </summary>
      <returns>Attività che rappresenta il completamento di tutte le attività fornite.</returns>
      <param name="tasks">Attività in attesa del completamento.</param>
      <typeparam name="TResult">Tipo dell'attività completata.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>Crea un'attività che verrà completata quando una delle attività fornite sarà completata.</summary>
      <returns>Attività che rappresenta il completamento di una delle attività fornite.Il risultato dell'attività restituita è l'attività completata.</returns>
      <param name="tasks">Attività in attesa del completamento.</param>
      <typeparam name="TResult">Tipo dell'attività completata.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>Crea un'attività che verrà completata quando una delle attività fornite sarà completata.</summary>
      <returns>Attività che rappresenta il completamento di una delle attività fornite.Il risultato dell'attività restituita è l'attività completata.</returns>
      <param name="tasks">Attività in attesa del completamento.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Threading.Tasks.Task[])">
      <summary>Crea un'attività che verrà completata quando una delle attività fornite sarà completata.</summary>
      <returns>Attività che rappresenta il completamento di una delle attività fornite.Il risultato dell'attività restituita è l'attività completata.</returns>
      <param name="tasks">Attività in attesa del completamento.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Threading.Tasks.Task{``0}[])">
      <summary>Crea un'attività che verrà completata quando una delle attività fornite sarà completata.</summary>
      <returns>Attività che rappresenta il completamento di una delle attività fornite.Il risultato dell'attività restituita è l'attività completata.</returns>
      <param name="tasks">Attività in attesa del completamento.</param>
      <typeparam name="TResult">Tipo dell'attività completata.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Yield">
      <summary>Crea un'attività awaitable che consente il passaggio in modo asincrono al contesto corrente, quando atteso.</summary>
      <returns>Contesto che, quando atteso, eseguirà una transizione in modo asincrono al contesto corrente al momento dell'attesa.Se l'oggetto <see cref="T:System.Threading.SynchronizationContext" /> corrente è diverso da Null, verrà considerato come contesto corrente.In caso contrario, sarà l'utilità di pianificazione associata all'attività in esecuzione a essere considerata come contesto corrente.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task`1">
      <summary>Rappresenta un'operazione asincrona in grado di restituire un valore.</summary>
      <typeparam name="TResult">Tipo del risultato prodotto da questo oggetto <see cref="T:System.Threading.Tasks.Task`1" />. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0})">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> con la funzione specificata.</summary>
      <param name="function">Delegato che rappresenta il codice da eseguire nell'attività.Quando la funzione è stata completata, la proprietà <see cref="P:System.Threading.Tasks.Task`1.Result" /> dell'attività verrà impostata affinché restituisca il valore del risultato della funzione.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> con la funzione specificata.</summary>
      <param name="function">Delegato che rappresenta il codice da eseguire nell'attività.Quando la funzione è stata completata, la proprietà <see cref="P:System.Threading.Tasks.Task`1.Result" /> dell'attività verrà impostata affinché restituisca il valore del risultato della funzione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da assegnare a questa attività.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> con la funzione e le opzioni di creazione specificate.</summary>
      <param name="function">Delegato che rappresenta il codice da eseguire nell'attività.Quando la funzione è stata completata, la proprietà <see cref="P:System.Threading.Tasks.Task`1.Result" /> dell'attività verrà impostata affinché restituisca il valore del risultato della funzione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività.</param>
      <param name="creationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usato per personalizzare il comportamento dell'attività.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> con la funzione e le opzioni di creazione specificate.</summary>
      <param name="function">Delegato che rappresenta il codice da eseguire nell'attività.Quando la funzione è stata completata, la proprietà <see cref="P:System.Threading.Tasks.Task`1.Result" /> dell'attività verrà impostata affinché restituisca il valore del risultato della funzione.</param>
      <param name="creationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usato per personalizzare il comportamento dell'attività.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> con la funzione e lo stato specificati.</summary>
      <param name="function">Delegato che rappresenta il codice da eseguire nell'attività.Quando la funzione è stata completata, la proprietà <see cref="P:System.Threading.Tasks.Task`1.Result" /> dell'attività verrà impostata affinché restituisca il valore del risultato della funzione.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> con l'azione, lo stato e le opzioni specificati.</summary>
      <param name="function">Delegato che rappresenta il codice da eseguire nell'attività.Quando la funzione è stata completata, la proprietà <see cref="P:System.Threading.Tasks.Task`1.Result" /> dell'attività verrà impostata affinché restituisca il valore del risultato della funzione.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da assegnare alla nuova attività.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> con l'azione, lo stato e le opzioni specificati.</summary>
      <param name="function">Delegato che rappresenta il codice da eseguire nell'attività.Quando la funzione è stata completata, la proprietà <see cref="P:System.Threading.Tasks.Task`1.Result" /> dell'attività verrà impostata affinché restituisca il valore del risultato della funzione.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da assegnare alla nuova attività.</param>
      <param name="creationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usato per personalizzare il comportamento dell'attività.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> con l'azione, lo stato e le opzioni specificati.</summary>
      <param name="function">Delegato che rappresenta il codice da eseguire nell'attività.Quando la funzione è stata completata, la proprietà <see cref="P:System.Threading.Tasks.Task`1.Result" /> dell'attività verrà impostata affinché restituisca il valore del risultato della funzione.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione.</param>
      <param name="creationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usato per personalizzare il comportamento dell'attività.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ConfigureAwait(System.Boolean)">
      <summary>Configura un elemento awaiter usato per attendere questo oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto usato per attendere questa attività.</returns>
      <param name="continueOnCapturedContext">true per tentare di eseguire il marshalling della continuazione nel contesto originale acquisito; in caso contrario, false.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}})">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'attività di destinazione. </summary>
      <returns>Nuova attività di continuazione. </returns>
      <param name="continuationAction">Azione da eseguire al completamento dell'attività <see cref="T:System.Threading.Tasks.Task`1" /> precedente.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken)">
      <summary>Crea una continuazione annullabile che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuova attività di continuazione. </returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata viene passata come argomento al delegato.</param>
      <param name="cancellationToken">Token di annullamento che viene passato alla nuova attività di continuazione. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita in base alla condizione specificata in <paramref name="continuationOptions" />.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire in base alla condizione specificata in <paramref name="continuationOptions" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuazione che viene eseguita in base alla condizione specificata in <paramref name="continuationOptions" />.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire in base alla condizione specificata in <paramref name="continuationOptions" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object)">
      <summary>Crea una continuazione che riceve informazioni sullo stato e viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione. </summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante vengono passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione di continuazione. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione di continuazione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione di continuazione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione di continuazione.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="continuationAction">Azione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dall'azione di continuazione.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0})">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <typeparam name="TNewResult"> Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken)">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività.</param>
      <typeparam name="TNewResult"> Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita in base alla condizione specificata in <paramref name="continuationOptions" />.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire in base alla condizione specificata in <paramref name="continuationOptions" />.Quando tale funzione viene eseguita, questa attività completata verrà passata come argomento al delegato.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <typeparam name="TNewResult"> Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuazione che viene eseguita in base alla condizione specificata in <paramref name="continuationOptions" />.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire in base alla condizione specificata in <paramref name="continuationOptions" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TNewResult"> Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita in modo asincrono al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata verrà passata come argomento al delegato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <typeparam name="TNewResult"> Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione di continuazione.</param>
      <typeparam name="TNewResult">Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione di continuazione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività.</param>
      <typeparam name="TNewResult">Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione di continuazione.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <typeparam name="TNewResult">Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The  <paramref name="continuationOptions" />  argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione di continuazione.</param>
      <param name="continuationOptions">Opzioni relative alla pianificazione e al comportamento della continuazione.Ciò comprende criteri, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, nonché opzioni di esecuzione, ad esempio <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TNewResult">Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuazione che viene eseguita al completamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di destinazione.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="continuationFunction">Funzione da eseguire al completamento di <see cref="T:System.Threading.Tasks.Task`1" />.Durante l'esecuzione, l'attività completata e l'oggetto stato fornito dal chiamante verranno passati come argomenti al delegato.</param>
      <param name="state">Oggetto che rappresenta i dati che devono essere usati dalla funzione di continuazione.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da associare all'attività di continuazione e da usare per l'esecuzione.</param>
      <typeparam name="TNewResult">Tipo del risultato prodotto dalla continuazione.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Factory">
      <summary>Fornisce l'accesso ai metodi factory per la creazione e la configurazione delle istanze di <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto factory in grado di creare una vasta gamma di oggetti <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.GetAwaiter">
      <summary>Ottiene un elemento awaiter usato per attendere questo oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Istanza di awaiter.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Result">
      <summary>Ottiene il valore del risultato di <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Valore del risultato di <see cref="T:System.Threading.Tasks.Task`1" />, ovvero lo stesso tipo del parametro di tipo dell'attività.</returns>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskCanceledException">
      <summary>Rappresenta un'eccezione utilizzata per comunicare l'annullamento di un'attività.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.TaskCanceledException" /> con un messaggio fornito dal sistema in cui viene descritto l'errore.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.TaskCanceledException" /> con un messaggio specifico in cui viene descritto l'errore.</summary>
      <param name="message">Messaggio in cui viene descritta l'eccezione.È necessario che il chiamante del costruttore assicuri che la stringa sia stata localizzata per le impostazioni cultura correnti del sistema.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.TaskCanceledException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa di questa eccezione.</summary>
      <param name="message">Messaggio in cui viene descritta l'eccezione.È necessario che il chiamante del costruttore assicuri che la stringa sia stata localizzata per le impostazioni cultura correnti del sistema.</param>
      <param name="innerException">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.Threading.Tasks.Task)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.TaskCanceledException" /> con un riferimento all'oggetto <see cref="T:System.Threading.Tasks.Task" /> che è stato annullato.</summary>
      <param name="task">Attività che è stata annullata.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskCanceledException.Task">
      <summary>Ottiene l'attività associata a questa eccezione.</summary>
      <returns>Riferimento all'oggetto <see cref="T:System.Threading.Tasks.Task" /> associato a questa eccezione.</returns>
    </member>
    <member name="T:System.Threading.Tasks.TaskCompletionSource`1">
      <summary>Rappresenta il lato producer di un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> non associato a un delegato, fornendo l'accesso al lato consumer tramite la proprietà <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />.</summary>
      <typeparam name="TResult">Tipo del valore del risultato associato a questo oggetto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> con lo stato specificato.</summary>
      <param name="state">Stato da usare come AsyncState dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> con lo stato e le opzioni specificate.</summary>
      <param name="state">Stato da usare come AsyncState dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante.</param>
      <param name="creationOptions">Opzioni da usare quando si crea l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> rappresenta le opzioni non valide per l'utilizzo con <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> con le opzioni specificate.</summary>
      <param name="creationOptions">Opzioni da usare quando si crea l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> rappresenta le opzioni non valide per l'utilizzo con <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetCanceled">
      <summary>Esegue la transizione dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante allo stato <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</summary>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante si trova già in uno dei tre stati finali: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> o <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />, se l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante è già stato eliminato.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Esegue la transizione dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante allo stato <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <param name="exceptions">Raccolta di eccezioni da associare a questo oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="exceptions" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Vi sono uno o più elementi Null in <paramref name="exceptions" />.</exception>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante si trova già in uno dei tre stati finali: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> o <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Exception)">
      <summary>Esegue la transizione dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante allo stato <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <param name="exception">Eccezione da associare a questo oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="exception" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante si trova già in uno dei tre stati finali: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> o <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetResult(`0)">
      <summary>Esegue la transizione dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante allo stato <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.</summary>
      <param name="result">Valore del risultato da associare a questo oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> è stato eliminato.</exception>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante si trova già in uno dei tre stati finali: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> o <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskCompletionSource`1.Task">
      <summary>Ottiene l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato da questo oggetto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</summary>
      <returns>Restituisce l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato da questo oggetto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled">
      <summary>Tenta di eseguire la transizione dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante allo stato <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</summary>
      <returns>True se l'operazione è stata completata; false se l'operazione non è stata completata o se l'oggetto è già stato eliminato.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled(System.Threading.CancellationToken)">
      <summary>Tenta di eseguire la transizione dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante allo stato <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> e abilita l'archiviazione di un token di annullamento nell'attività annullata. </summary>
      <returns>true se l'operazione riesce; in caso contrario, false. </returns>
      <param name="cancellationToken">Token di annullamento. </param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Tenta di eseguire la transizione dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante allo stato <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <returns>True se l'operazione è stata completata; in caso contrario, false.</returns>
      <param name="exceptions">Raccolta di eccezioni da associare a questo oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="exceptions" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Vi sono uno o più elementi Null in <paramref name="exceptions" />.-oppure-La raccolta <paramref name="exceptions" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Exception)">
      <summary>Tenta di eseguire la transizione dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante allo stato <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <returns>True se l'operazione è stata completata; in caso contrario, false.</returns>
      <param name="exception">Eccezione da associare a questo oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="exception" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetResult(`0)">
      <summary>Tenta di eseguire la transizione dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> sottostante allo stato <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.</summary>
      <returns>True se l'operazione è stata completata; in caso contrario, false. </returns>
      <param name="result">Valore del risultato da associare a questo oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskContinuationOptions">
      <summary>Specifica il comportamento di un'attività creata tramite il metodo <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)" /> o <see cref="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)" />.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent">
      <summary>Specifica che la continuazione, se è un'attività figlio, è connessa a un elemento padre della gerarchia delle attività.La continuazione può essere un'attività figlio solo se anche la relativa attività precedente è un'attività figlio.Per impostazione predefinita, un'attività figlio, ovvero un'attività interna creata da un'attività esterna, viene eseguita indipendentemente dalla relativa attività padre.È possibile usare l'opzione <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> per mantenere sincronizzate le attività padre e figlio.Si noti che se un'attività padre viene configurata con l'opzione <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" />, l'opzione <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> nell'attività figlio non avrà effetto e quest'ultima verrà eseguita come attività figlio disconnessa. Per altre informazioni, vedere Attività figlio connesse e disconnesse. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.DenyChildAttach">
      <summary>Specifica che qualsiasi attività figlio  (ovvero qualsiasi attività interna annidata creata da questa continuazione) che venga creata con l'opzione <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> e tenti l'esecuzione come attività figlio connessa non potrà collegarsi all'attività padre e verrà invece eseguita come attività figlio disconnessa.Per altre informazioni, vedere Attività figlio connesse e disconnesse.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously">
      <summary>Specifica che l'attività di continuazione deve essere eseguita in modo sincrono.Se si specifica questa opzione, la continuazione viene eseguita nello stesso thread che comporta la transizione dell'attività precedente allo stato finale.Se quando la continuazione viene creata l'attività precedente è già completa, la continuazione verrà eseguita nel thread che crea la continuazione.Se <see cref="T:System.Threading.CancellationTokenSource" /> dell'attività precedente viene collocato in un blocco finally (Finally in Visual Basic), verrà eseguita una continuazione con questa opzione in tale blocco finally.Eseguire in modo sincrono soltanto le continuazioni che presentano un'esecuzione molto breve.Dal momento che l'attività viene eseguita in modo asincrono, non è necessario chiamare un metodo come <see cref="M:System.Threading.Tasks.Task.Wait" /> per garantire che il thread chiamante attenda il completamento dell'attività. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.HideScheduler">
      <summary>Specifica le attività create dalla continuazione chiamando i metodi come <see cref="M:System.Threading.Tasks.Task.Run(System.Action)" /> o <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})" /> Vedere l'utilità di pianificazione predefinita (<see cref="P:System.Threading.Tasks.TaskScheduler.Default" />) anziché l'utilità di pianificazione in cui è in esecuzione la continuazione come utilità di pianificazione corrente.  </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LazyCancellation">
      <summary>In caso di annullamento della continuazione, impedisce il completamento della continuazione finché l'attività precedente non è stata completata.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LongRunning">
      <summary>Specifica che la continuazione sarà un'operazione a bassa precisione di lunga durata.Fornisce a <see cref="T:System.Threading.Tasks.TaskScheduler" /> un'indicazione in merito alla possibilità di dover ricorrere all'oversubscription.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.None">
      <summary>Quando non viene specificata alcuna opzione di continuazione, specifica che deve essere usato un comportamento predefinito quando si esegue una continuazione.La continuazione viene eseguita in modo asincrono al completamento dell'attività precedente, indipendentemente dal valore della proprietà <see cref="P:System.Threading.Tasks.Task.Status" /> finale dell'attività precedente.Se la continuazione è un'attività figlio, viene creata come attività annidata disconnessa.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnCanceled">
      <summary>Specifica che l'attività di continuazione non deve essere pianificata se l'attività precedente è stata annullata.Un'attività precedente viene annullata se la relativa proprietà <see cref="P:System.Threading.Tasks.Task.Status" /> al completamento è <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.Questa opzione non è valida per le continuazioni multiattività.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnFaulted">
      <summary>Specifica che l'attività di continuazione non deve essere pianificata se la relativa attività precedente ha generato un'eccezione non gestita.Un'attività precedente genera un'eccezione non gestita se la relativa proprietà <see cref="P:System.Threading.Tasks.Task.Status" /> al completamento è <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.Questa opzione non è valida per le continuazioni multiattività.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnRanToCompletion">
      <summary>Specifica che l'attività di continuazione non deve essere pianificata se l'esecuzione della relativa attività precedente è stata completata.Un'attività precedente viene eseguita fino al completamento se la relativa proprietà <see cref="P:System.Threading.Tasks.Task.Status" /> al completamento è <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.Questa opzione non è valida per le continuazioni multiattività.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled">
      <summary>Specifica che la continuazione deve essere pianificata solo se l'attività precedente è stata annullata.Un'attività precedente viene annullata se la relativa proprietà <see cref="P:System.Threading.Tasks.Task.Status" /> al completamento è <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.Questa opzione non è valida per le continuazioni multiattività.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted">
      <summary>Specifica che l'attività di continuazione deve essere pianificata solo se la relativa attività precedente ha generato un'eccezione non gestita.Un'attività precedente genera un'eccezione non gestita se la relativa proprietà <see cref="P:System.Threading.Tasks.Task.Status" /> al completamento è <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.L'opzione <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted" /> garantisce che la proprietà <see cref="P:System.Threading.Tasks.Task.Exception" /> dell'attività precedente non sia null.È possibile usare tale proprietà per rilevare l'eccezione e determinare quale eccezione ha comportato errori nell'attività.Se non si accede alla proprietà <see cref="P:System.Threading.Tasks.Task.Exception" />, l'eccezione non viene gestita.Inoltre, se si tenta di accedere alla proprietà <see cref="P:System.Threading.Tasks.Task`1.Result" /> di un'attività che è stata annullata o in cui si sono verificati errori, verrà generata una nuova eccezione.Questa opzione non è valida per le continuazioni multiattività. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnRanToCompletion">
      <summary>Specifica che la continuazione deve essere pianificata solo se l'esecuzione della relativa attività precedente è stata completata.Un'attività precedente viene eseguita fino al completamento se la relativa proprietà <see cref="P:System.Threading.Tasks.Task.Status" /> al completamento è <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.Questa opzione non è valida per le continuazioni multiattività.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.PreferFairness">
      <summary>Indicazione fornita a un oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> affinché pianifichi un'attività nell'ordine in cui sono state pianificate, in modo che le attività pianificate prima abbiano più possibilità di essere eseguite prima delle attività pianificate in un secondo momento. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.RunContinuationsAsynchronously">
      <summary>Specifica che l'attività di continuazione deve essere eseguita in modo asincrono.Questa opzione ha la precedenza rispetto a <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskCreationOptions">
      <summary>Specifica i flag che controllano il comportamento facoltativo per la creazione e l'esecuzione delle attività. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent">
      <summary>Specifica che un'attività è associata a un elemento padre nella gerarchia delle attività.Per impostazione predefinita, un'attività figlio, ovvero un'attività interna creata da un'attività esterna, viene eseguita indipendentemente dalla relativa attività padre.È possibile usare l'opzione <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> per mantenere sincronizzate le attività padre e figlio.Si noti che se un'attività padre viene configurata con l'opzione <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" />, l'opzione <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> nell'attività figlio non avrà effetto e quest'ultima verrà eseguita come attività figlio disconnessa. Per altre informazioni, vedere Attività figlio connesse e disconnesse. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach">
      <summary>Specifica che qualsiasi attività figlio che tenti l'esecuzione come attività figlio connessa (ovvero che venga creata con l'opzione <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" />) non potrà collegarsi all'attività padre e verrà invece eseguita come attività figlio disconnessa.Per altre informazioni, vedere Attività figlio connesse e disconnesse.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.HideScheduler">
      <summary>Impedisce all'utilità di pianificazione dell'ambiente di essere considerata l'utilità di pianificazione corrente nell'attività creata.Ciò significa che le operazioni come StartNew o ContinueWith eseguite nell'attività creata visualizzeranno <see cref="P:System.Threading.Tasks.TaskScheduler.Default" /> come utilità di pianificazione corrente.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.LongRunning">
      <summary>Specifica che un'attività sarà un'operazione di lunga durata e con granularità grossolana che include meno componenti di dimensioni maggiori rispetto ai sistemi più precisi.Fornisce a <see cref="T:System.Threading.Tasks.TaskScheduler" /> un'indicazione in merito alla possibilità di dover ricorrere all'oversubscription.L'oversubscription consente di creare un numero di thread superiore a quello dei thread hardware disponibili.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.None">
      <summary>Specifica che deve essere usato il comportamento predefinito.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.PreferFairness">
      <summary>Indicazione fornita a un oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> affinché pianifichi un'attività nel modo più giusto possibile, ovvero garantendo che le attività pianificate prima abbiano più possibilità di essere eseguite prima delle attività pianificate in un secondo momento.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.RunContinuationsAsynchronously">
      <summary>Forza l'esecuzione asincrona delle continuazioni aggiunte all'attività corrente. </summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskExtensions">
      <summary>Fornisce un set di metodi statici (Shared in Visual Basic) per l'utilizzo di tipi specifici di istanze di <see cref="T:System.Threading.Tasks.Task" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap``1(System.Threading.Tasks.Task{System.Threading.Tasks.Task{``0}})">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> proxy che rappresenta l'operazione asincrona di un oggetto Task&lt;Task&lt;T&gt;&gt; (C#) o Task (Of Task(Of T)) (Visual Basic).</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> che rappresenta l'operazione asincrona dell'oggetto Task&lt;Task&lt;T&gt;&gt; (C#) o Task (Of Task(Of T)) (Visual Basic) fornito.</returns>
      <param name="task">Oggetto Task&lt;Task&lt;T&gt;&gt; (C#) o Task (Of Task(Of T)) (Visual Basic) di cui annullare il wrapping.</param>
      <typeparam name="TResult">Tipo di risultato dell'attività.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata se l'argomento <paramref name="task" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap(System.Threading.Tasks.Task{System.Threading.Tasks.Task})">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> proxy che rappresenta l'operazione asincrona di <see cref="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)" />.</summary>
      <returns>Attività che rappresenta l'operazione asincrona dell'oggetto System.Threading.Tasks.Task(Of Task) fornito.</returns>
      <param name="task">Oggetto Task&lt;Task&gt; (C#) o Task (Of Task) (Visual Basic) di cui annullare il wrapping.</param>
      <exception cref="T:System.ArgumentNullException">Eccezione generata se l'argomento <paramref name="task" /> è Null.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory">
      <summary>Fornisce supporto per la creazione e la pianificazione di oggetti <see cref="T:System.Threading.Tasks.Task" />. </summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor">
      <summary>Inizializza un'istanza di <see cref="T:System.Threading.Tasks.TaskFactory" /> con la configurazione predefinita.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken)">
      <summary>Inizializza un'istanza di <see cref="T:System.Threading.Tasks.TaskFactory" /> con la configurazione specificata.</summary>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato alle attività create da questo oggetto <see cref="T:System.Threading.Tasks.TaskFactory" /> a meno che durante la chiamata dei metodi factory non venga specificato in modo esplicito un altro oggetto CancellationToken.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Inizializza un'istanza di <see cref="T:System.Threading.Tasks.TaskFactory" /> con la configurazione specificata.</summary>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> predefinito che verrà assegnato alle attività create da questo oggetto <see cref="T:System.Threading.Tasks.TaskFactory" /> a meno che durante la chiamata dei metodi factory non venga specificato in modo esplicito un altro oggetto CancellationToken.</param>
      <param name="creationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> predefinito da usare quando si creano attività con questo oggetto TaskFactory.</param>
      <param name="continuationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> predefinito da usare quando si creano attività di continuazione con questo oggetto TaskFactory.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> predefinito da usare per pianificare le attività create con questo oggetto TaskFactory.Un valore Null indica che occorre usare TaskScheduler.Current.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="creationOptions" /> specifica un valore <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> non valido.Per ulteriori informazioni, vedere la sezione Osservazioni per <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.-oppure-L'argomento <paramref name="continuationOptions" /> specifica un valore non valido.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Inizializza un'istanza di <see cref="T:System.Threading.Tasks.TaskFactory" /> con la configurazione specificata.</summary>
      <param name="creationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> predefinito da usare quando si creano attività con questo oggetto TaskFactory.</param>
      <param name="continuationOptions">Oggetto <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> predefinito da usare quando si creano attività di continuazione con questo oggetto TaskFactory.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="creationOptions" /> specifica un valore <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> non valido.Per ulteriori informazioni, vedere la sezione Osservazioni per <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.-oppure-L'argomento <paramref name="continuationOptions" /> specifica un valore non valido.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Inizializza un'istanza di <see cref="T:System.Threading.Tasks.TaskFactory" /> con la configurazione specificata.</summary>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> da usare per pianificare le attività create con questo oggetto TaskFactory.Un valore Null indica che occorre usare l'oggetto TaskScheduler corrente.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CancellationToken">
      <summary>Ottiene il token di annullamento predefinito per questa factory delle attività.</summary>
      <returns>Token di annullamento attività predefinito per questa factory delle attività.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.ContinuationOptions">
      <summary>Ottiene le opzioni di continuazione attività predefinite per questa factory delle attività.</summary>
      <returns>Opzioni di continuazione attività predefinite per questa factory delle attività.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]})">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato. </summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationAction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento da assegnare alla nuova attività di continuazione.</param>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-L'oggetto <see cref="T:System.Threading.CancellationTokenSource" /> che ha creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationAction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento da assegnare alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Combinazione bit per bit dei valori di enumerazione che controllano il comportamento della nuova attività di continuazione.</param>
      <param name="scheduler">Oggetto usato per pianificare la nuova attività di continuazione.</param>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationAction" /> è null.-oppure-Il valore dell'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="continuationOptions">Combinazione bit per bit dei valori di enumerazione che controllano il comportamento della nuova attività di continuazione.I membri NotOn* e OnlyOn* non sono supportati.</param>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationAction" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="continuationOptions" /> specifica un valore non valido. </exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0})">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'attività creata.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento da assegnare alla nuova attività di continuazione.</param>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'attività creata.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-L'oggetto <see cref="T:System.Threading.CancellationTokenSource" /> che ha creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento da assegnare alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Combinazione bit per bit dei valori di enumerazione che controllano il comportamento della nuova attività di continuazione.I membri NotOn* e OnlyOn* non sono supportati.</param>
      <param name="scheduler">Oggetto usato per pianificare la nuova attività di continuazione.</param>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'attività creata.</typeparam>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.-oppure-Il valore dell'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="continuationOptions">Combinazione bit per bit dei valori di enumerazione che controllano il comportamento della nuova attività di continuazione.I membri NotOn* e OnlyOn* non sono supportati.</param>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'attività creata.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="continuationOptions" /> specifica un valore non valido. </exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]})">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationAction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento da assegnare alla nuova attività di continuazione.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-L'oggetto <see cref="T:System.Threading.CancellationTokenSource" /> che ha creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationAction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento da assegnare alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Combinazione bit per bit dei valori di enumerazione che controllano il comportamento della nuova attività di continuazione.I membri NotOn* e OnlyOn* non sono supportati.</param>
      <param name="scheduler">Oggetto usato per pianificare la nuova attività di continuazione.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationAction" /> è null.-oppure-Il valore dell'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="continuationOptions">Combinazione bit per bit dei valori di enumerazione che controllano il comportamento della nuova attività di continuazione.I membri NotOn* e OnlyOn* non sono supportati.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationAction" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="continuationOptions" /> specifica un valore non valido. </exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1})">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'attività creata.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento da assegnare alla nuova attività di continuazione.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'attività creata.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-Il <see cref="T:System.Threading.CancellationTokenSource" /> creato<paramref name=" cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento da assegnare alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Combinazione bit per bit dei valori di enumerazione che controllano il comportamento della nuova attività di continuazione.I membri NotOn* e OnlyOn* non sono supportati.</param>
      <param name="scheduler">Oggetto usato per pianificare la nuova attività di continuazione.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'attività creata.</typeparam>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.-oppure-Il valore dell'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="continuationOptions" /> specifica un valore non valido. </exception>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-L'oggetto <see cref="T:System.Threading.CancellationTokenSource" /> che ha creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un'attività di continuazione che viene avviata quando un set di attività specificate è stato completato.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="continuationOptions">Combinazione bit per bit dei valori di enumerazione che controllano il comportamento della nuova attività di continuazione.I membri NotOn* e OnlyOn* non sono supportati.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'attività creata.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="continuationOptions" /> specifica un valore non valido.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> è vuota o contiene un valore null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task})">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire al completamento di una delle attività nella matrice <paramref name="tasks" />.</param>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato. </exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null. -oppure-L'il <paramref name="continuationAction" /> argomento è null. </exception>
      <exception cref="T:System.ArgumentException">Il <paramref name="tasks" /> matrice contiene un null valore. -oppure-Il <paramref name="tasks" /> matrice è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire al completamento di una delle attività nella matrice <paramref name="tasks" />.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato. -oppure-<paramref name="cancellationToken" /> è già stato eliminato. </exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null. -oppure-Il valore dell'argomento <paramref name="continuationAction" /> è null. </exception>
      <exception cref="T:System.ArgumentException">Il <paramref name="tasks" /> matrice contiene un null valore. -oppure-Il <paramref name="tasks" /> matrice è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire al completamento di una delle attività nella matrice <paramref name="tasks" />.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Valore di <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione creato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione creato.</param>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationAction" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="scheduler" /> è Null.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire al completamento di una delle attività nella matrice <paramref name="tasks" />.</param>
      <param name="continuationOptions">Valore di <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione creato.</param>
      <exception cref="T:System.ObjectDisposedException">Eccezione generata quando uno degli elementi della matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationAction" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="continuationOptions" /> specifica un valore TaskContinuationOptions non valido.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0})">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eccezione generata quando uno degli elementi della matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationFunction" /> è Null.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eccezione generata quando uno degli elementi della matrice <paramref name="tasks" /> è stato eliminato.-oppure-L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationFunction" /> è Null.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Valore di <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione creato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione creato.</param>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationFunction" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="scheduler" /> è Null.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="continuationOptions">Valore di <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione creato.</param>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eccezione generata quando uno degli elementi della matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationFunction" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="continuationOptions" /> specifica un valore TaskContinuationOptions non valido.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}})">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire al completamento di una delle attività nella matrice <paramref name="tasks" />.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eccezione generata quando uno degli elementi della matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationAction" /> è Null.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire al completamento di una delle attività nella matrice <paramref name="tasks" />.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eccezione generata quando uno degli elementi della matrice <paramref name="tasks" /> è stato eliminato.-oppure-L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationAction" /> è Null.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire al completamento di una delle attività nella matrice <paramref name="tasks" />.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Valore di <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione creato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione creato.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationAction" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="scheduler" /> è Null.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationAction">Delegato dell'azione da eseguire al completamento di una delle attività nella matrice <paramref name="tasks" />.</param>
      <param name="continuationOptions">Valore di <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> di continuazione creato.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eccezione generata quando uno degli elementi della matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationAction" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="continuationOptions" /> specifica un valore TaskContinuationOptions non valido.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1})">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eccezione generata quando uno degli elementi della matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationFunction" /> è Null.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eccezione generata quando uno degli elementi della matrice <paramref name="tasks" /> è stato eliminato.-oppure-L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationFunction" /> è Null.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Valore di <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione creato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione creato.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationFunction" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="scheduler" /> è Null.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione che verrà avviato al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="continuationOptions">Valore di <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione creato.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <typeparam name="TResult">Tipo del risultato restituito dal delegato di <paramref name="continuationFunction" /> e associato all'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eccezione generata quando uno degli elementi della matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando la matrice <paramref name="tasks" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="continuationFunction" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="continuationOptions" /> specifica un valore TaskContinuationOptions non valido.</exception>
      <exception cref="T:System.ArgumentException">Eccezione generata quando la matrice <paramref name="tasks" /> contiene un valore Null.-oppure-Eccezione generata quando la matrice <paramref name="tasks" /> è vuota.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CreationOptions">
      <summary>Ottiene le opzioni di creazione attività predefinite per questa factory delle attività.</summary>
      <returns>Opzioni di creazione attività predefinite per questa factory delle attività.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <typeparam name="TArg1">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <typeparam name="TArg1">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <typeparam name="TArg1">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <typeparam name="TArg1">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg3">Terzo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del terzo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg3">Terzo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del terzo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg3">Terzo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del terzo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg3">Terzo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del terzo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="beginMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult})">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che esegue un'azione del metodo End al completamento di un oggetto <see cref="T:System.IAsyncResult" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> che rappresenta l'operazione asincrona.</returns>
      <param name="asyncResult">IAsyncResult il cui completamento deve attivare l'elaborazione di <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegato dell'azione che elabora l'oggetto <paramref name="asyncResult" /> completato.</param>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="asyncResult" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che esegue un'azione del metodo End al completamento di un oggetto <see cref="T:System.IAsyncResult" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> che rappresenta l'operazione asincrona.</returns>
      <param name="asyncResult">IAsyncResult il cui completamento deve attivare l'elaborazione di <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegato dell'azione che elabora l'oggetto <paramref name="asyncResult" /> completato.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="asyncResult" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task" /> che esegue un'azione del metodo End al completamento di un oggetto <see cref="T:System.IAsyncResult" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="asyncResult">IAsyncResult il cui completamento deve attivare l'elaborazione di <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegato dell'azione che elabora l'oggetto <paramref name="asyncResult" /> completato.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'attività che esegue il metodo End.</param>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="asyncResult" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="scheduler" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0})">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che esegue una funzione del metodo End al completamento di un oggetto <see cref="T:System.IAsyncResult" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta l'operazione asincrona.</returns>
      <param name="asyncResult">IAsyncResult il cui completamento deve attivare l'elaborazione di <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegato della funzione che elabora l'oggetto <paramref name="asyncResult" /> completato.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="asyncResult" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che esegue una funzione del metodo End al completamento di un oggetto <see cref="T:System.IAsyncResult" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta l'operazione asincrona.</returns>
      <param name="asyncResult">IAsyncResult il cui completamento deve attivare l'elaborazione di <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegato della funzione che elabora l'oggetto <paramref name="asyncResult" /> completato.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="asyncResult" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che esegue una funzione del metodo End al completamento di un oggetto <see cref="T:System.IAsyncResult" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta l'operazione asincrona.</returns>
      <param name="asyncResult">IAsyncResult il cui completamento deve attivare l'elaborazione di <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegato della funzione che elabora l'oggetto <paramref name="asyncResult" /> completato.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'attività che esegue il metodo End.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="asyncResult" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="endMethod" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="scheduler" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.Scheduler">
      <summary>Ottiene l'utilità di pianificazione predefinita per questa factory delle attività.</summary>
      <returns>Utilità di pianificazione predefinita per questa factory delle attività.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action)">
      <summary>Crea e avvia un'attività.</summary>
      <returns>Attività avviata.</returns>
      <param name="action">Delegato dell'azione da eseguire in modo asincrono.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="action" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> avviato.</returns>
      <param name="action">Delegato dell'azione da eseguire in modo asincrono.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato alla nuova attività.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="action" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> avviato.</returns>
      <param name="action">Delegato dell'azione da eseguire in modo asincrono.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato al nuovo oggetto <see cref="T:System.Threading.Tasks.Task" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="action" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="scheduler" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> avviato.</returns>
      <param name="action">Delegato dell'azione da eseguire in modo asincrono.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="action" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task" />. </summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> avviato. </returns>
      <param name="action">Delegato dell'azione da eseguire in modo asincrono. </param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="action" />. </param>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="action" /> è null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> avviato.</returns>
      <param name="action">Delegato dell'azione da eseguire in modo asincrono.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="action" />.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato al nuovo oggetto <see cref="T:System.Threading.Tasks.Task" />.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="action" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> avviato.</returns>
      <param name="action">Delegato dell'azione da eseguire in modo asincrono.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="action" />.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato alla nuova attività.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="action" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="scheduler" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task" /> avviato.</returns>
      <param name="action">Delegato dell'azione da eseguire in modo asincrono.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="action" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task" /> creato.</param>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="action" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0})">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> avviato.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />. </typeparam>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="function" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> avviato.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato al nuovo oggetto <see cref="T:System.Threading.Tasks.Task" />.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="function" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> avviato.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato alla nuova attività.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="function" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="scheduler" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> avviato.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="function" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> avviato.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="function" />.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="function" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> avviato.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="function" />.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato al nuovo oggetto <see cref="T:System.Threading.Tasks.Task" />.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="function" /> è Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> avviato.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="function" />.</param>
      <param name="cancellationToken">Oggetto <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> che verrà assegnato alla nuova attività.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">L'istanza <see cref="T:System.Threading.CancellationToken" /> fornita è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="function" /> è Null.-oppure-Eccezione generata quando l'argomento <paramref name="scheduler" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e avvia un oggetto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> avviato.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="function" />.</param>
      <param name="creationOptions">Valore di TaskCreationOptions che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <typeparam name="TResult">Tipo del risultato disponibile tramite <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Eccezione generata quando l'argomento <paramref name="function" /> è Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Eccezione generata quando l'argomento <paramref name="creationOptions" /> specifica un valore TaskCreationOptions non valido.Per ulteriori informazioni, vedere la sezione Osservazioni di <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory`1">
      <summary>Fornisce supporto per la creazione e la pianificazione di oggetti <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <typeparam name="TResult">Valore restituito degli oggetti <see cref="T:System.Threading.Tasks.Task`1" /> creati dai metodi di questa classe. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor">
      <summary>Inizializza un'istanza di <see cref="T:System.Threading.Tasks.TaskFactory`1" /> con la configurazione predefinita.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken)">
      <summary>Inizializza un'istanza di <see cref="T:System.Threading.Tasks.TaskFactory`1" /> con la configurazione predefinita.</summary>
      <param name="cancellationToken">Token di annullamento predefinito che verrà assegnato alle attività create da questo oggetto <see cref="T:System.Threading.Tasks.TaskFactory" />, a meno che durante la chiamata dei metodi factory non venga specificato in modo esplicito un altro token di annullamento.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Inizializza un'istanza di <see cref="T:System.Threading.Tasks.TaskFactory`1" /> con la configurazione specificata.</summary>
      <param name="cancellationToken">Token di annullamento predefinito che verrà assegnato alle attività create da questo oggetto <see cref="T:System.Threading.Tasks.TaskFactory" />, a meno che durante la chiamata dei metodi factory non venga specificato in modo esplicito un altro token di annullamento.</param>
      <param name="creationOptions">Opzioni predefinite da usare quando si creano attività con questo oggetto <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="continuationOptions">Opzioni predefinite da usare quando si creano attività di continuazione con questo oggetto <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="scheduler">Utilità di pianificazione predefinita da usare per pianificare le attività create con questo oggetto <see cref="T:System.Threading.Tasks.TaskFactory`1" />.Un valore Null indica che è necessario usare l'oggetto <see cref="P:System.Threading.Tasks.TaskScheduler.Current" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> o <paramref name="continuationOptions" /> specifica un valore non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Inizializza un'istanza di <see cref="T:System.Threading.Tasks.TaskFactory`1" /> con la configurazione specificata.</summary>
      <param name="creationOptions">Opzioni predefinite da usare quando si creano attività con questo oggetto <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="continuationOptions">Opzioni predefinite da usare quando si creano attività di continuazione con questo oggetto <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> o <paramref name="continuationOptions" /> specifica un valore non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Inizializza un'istanza di <see cref="T:System.Threading.Tasks.TaskFactory`1" /> con la configurazione specificata.</summary>
      <param name="scheduler">Utilità di pianificazione da usare per pianificare le attività create con questo oggetto <see cref="T:System.Threading.Tasks.TaskFactory`1" />.Un valore Null indica che è necessario usare l'oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> corrente.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CancellationToken">
      <summary>Ottiene il token di annullamento predefinito per questa factory delle attività.</summary>
      <returns>Token di annullamento predefinito per questa factory delle attività.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.ContinuationOptions">
      <summary>Ottiene il valore di enumerazione <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> per questa factory delle attività.</summary>
      <returns>Uno dei valori di enumerazione che specifica le opzioni di continuazione predefinite per questa factory delle attività.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0})">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di un set di attività fornite.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-L'elemento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null o è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di un set di attività fornite.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività di continuazione.</param>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-Il <see cref="T:System.Threading.CancellationTokenSource" /> creato<paramref name=" cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-<paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null o è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di un set di attività fornite.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività di continuazione creata.I valori NotOn* o OnlyOn* non sono validi.</param>
      <param name="scheduler">Utilità di pianificazione usata per pianificare l'attività di continuazione creata.</param>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.-oppure-Il valore dell'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null o è vuota.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> specifica un valore non valido.</exception>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-Il <see cref="T:System.Threading.CancellationTokenSource" /> creato<paramref name=" cancellationToken" /> è già stato eliminato.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di un set di attività fornite.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="continuationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività di continuazione creata.I valori NotOn* o OnlyOn* non sono validi.</param>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="continuationOptions" /> specifica un valore non valido.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null o è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0})">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di un set di attività fornite.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null o è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di un set di attività fornite.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività di continuazione.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-Il <see cref="T:System.Threading.CancellationTokenSource" /> creato<paramref name=" cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null o è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di un set di attività fornite.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività di continuazione creata.I valori NotOn* o OnlyOn* non sono validi.</param>
      <param name="scheduler">Utilità di pianificazione usata per pianificare l'attività di continuazione creata.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.-oppure-Il valore dell'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null o è vuota.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="continuationOptions" /> specifica un valore non valido.</exception>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-Il <see cref="T:System.Threading.CancellationTokenSource" /> creato<paramref name=" cancellationToken" /> è già stato eliminato.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di un set di attività fornite.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando tutte le attività nella matrice <paramref name="tasks" /> sono state completate.</param>
      <param name="continuationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività di continuazione creata.I valori NotOn* o OnlyOn* non sono validi.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="continuationOptions" /> specifica un valore non valido.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null o è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0})">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di una delle attività nel set fornito. </summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null o è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività di continuazione.</param>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-Il <see cref="T:System.Threading.CancellationTokenSource" /> creato<paramref name=" cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è Null.-oppure-L'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null.-oppure-Il <paramref name="tasks" /> matrice è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività di continuazione creata.I valori NotOn* o OnlyOn* non sono validi.</param>
      <param name="scheduler">Utilità di pianificazione usata per pianificare l'attività di continuazione creata.</param>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.-oppure-Il valore dell'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null.-oppure-Il <paramref name="tasks" /> matrice è vuota.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="continuationOptions" /> specifica un valore <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> non valido.</exception>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-Il <see cref="T:System.Threading.CancellationTokenSource" /> creato<paramref name=" cancellationToken" /> è già stato eliminato. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="continuationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività di continuazione creata.I valori NotOn* o OnlyOn* non sono validi.</param>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Nell'argomento <paramref name="continuationOptions" /> viene specificato un valore di enumerazione non valido.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null.-oppure-Il <paramref name="tasks" /> matrice è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0})">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null.-oppure-Il <paramref name="tasks" /> matrice è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuova attività di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività di continuazione.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-Il <see cref="T:System.Threading.CancellationTokenSource" /> creato<paramref name=" cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null.-oppure-Il <paramref name="tasks" /> matrice è vuota.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività di continuazione.</param>
      <param name="continuationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività di continuazione creata.I valori NotOn* o OnlyOn* non sono validi.</param>
      <param name="scheduler">Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> usato per pianificare l'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione creato.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.-oppure-L'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null.-oppure-Il <paramref name="tasks" /> matrice è vuota.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="continuationOptions" /> specifica un valore TaskContinuationOptions non valido.</exception>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.-oppure-Il <see cref="T:System.Threading.CancellationTokenSource" /> creato<paramref name=" cancellationToken" /> è già stato eliminato. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un'attività di continuazione che verrà avviata al completamento di una delle attività nel set fornito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.Tasks.Task`1" /> di continuazione.</returns>
      <param name="tasks">Matrice di attività da cui continuare al completamento di un'attività.</param>
      <param name="continuationFunction">Delegato della funzione da eseguire in modo asincrono quando una delle attività nella matrice <paramref name="tasks" /> viene completata.</param>
      <param name="continuationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività di continuazione creata.I valori NotOn* o OnlyOn* non sono validi.</param>
      <typeparam name="TAntecedentResult">Tipo del risultato degli oggetti <paramref name="tasks" /> precedenti.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno degli elementi nella matrice <paramref name="tasks" /> è stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">La matrice <paramref name="tasks" /> è null.-oppure-Il valore dell'argomento <paramref name="continuationFunction" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Nell'argomento <paramref name="continuationOptions" /> viene specificato un valore di enumerazione non valido.</exception>
      <exception cref="T:System.ArgumentException">La matrice <paramref name="tasks" /> contiene un valore null.-oppure-Il <paramref name="tasks" /> matrice è vuota.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CreationOptions">
      <summary>Ottiene il valore di enumerazione <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> per questa factory delle attività.</summary>
      <returns>Uno dei valori di enumerazione che specifica le opzioni di creazione predefinite per questa factory delle attività.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object)">
      <summary>Crea un'attività che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Attività creata che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="beginMethod" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un'attività che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività creata.</param>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="beginMethod" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="creationOptions" /> specifica un valore non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object)">
      <summary>Crea un'attività che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Attività creata che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <typeparam name="TArg1">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="beginMethod" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un'attività che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Attività creata che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività creata.</param>
      <typeparam name="TArg1">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="beginMethod" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Nel parametro <paramref name="creationOptions" /> è stato specificato un valore non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object)">
      <summary>Crea un'attività che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Attività creata che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="beginMethod" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un'attività che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Attività creata che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Oggetto che controlla il comportamento dell'oggetto <see cref="T:System.Threading.Tasks.Task`1" /> creato.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="beginMethod" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Nel parametro <paramref name="creationOptions" /> è stato specificato un valore non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object)">
      <summary>Crea un'attività che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Attività creata che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg3">Terzo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del terzo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="beginMethod" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un'attività che rappresenta una coppia di metodi Begin ed End conformi al modello di programmazione asincrona.</summary>
      <returns>Attività creata che rappresenta l'operazione asincrona.</returns>
      <param name="beginMethod">Delegato che inizia l'operazione asincrona.</param>
      <param name="endMethod">Delegato che termina l'operazione asincrona.</param>
      <param name="arg1">Primo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg2">Secondo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="arg3">Terzo argomento passato al delegato <paramref name="beginMethod" />.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="beginMethod" />.</param>
      <param name="creationOptions">Oggetto che controlla il comportamento dell'attività creata.</param>
      <typeparam name="TArg1">Tipo del secondo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del terzo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primo argomento passato al delegato <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="beginMethod" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Nel parametro <paramref name="creationOptions" /> è stato specificato un valore non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0})">
      <summary>Crea un'attività che esegue una funzione del metodo End al completamento di un oggetto <see cref="T:System.IAsyncResult" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> che rappresenta l'operazione asincrona.</returns>
      <param name="asyncResult">Oggetto <see cref="T:System.IAsyncResult" /> il cui completamento deve attivare l'elaborazione del metodo <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegato della funzione che elabora l'oggetto <paramref name="asyncResult" /> completato.</param>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="asyncResult" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un'attività che esegue una funzione del metodo End al completamento di un oggetto <see cref="T:System.IAsyncResult" /> specificato.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <param name="asyncResult">Oggetto <see cref="T:System.IAsyncResult" /> il cui completamento deve attivare l'elaborazione del metodo <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegato della funzione che elabora l'oggetto <paramref name="asyncResult" /> completato.</param>
      <param name="creationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività creata.</param>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="asyncResult" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'argomento <paramref name="creationOptions" /> specifica un valore non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un'attività che esegue una funzione del metodo End al completamento di un oggetto <see cref="T:System.IAsyncResult" /> specificato.</summary>
      <returns>Attività creata che rappresenta l'operazione asincrona.</returns>
      <param name="asyncResult">Oggetto <see cref="T:System.IAsyncResult" /> il cui completamento deve attivare l'elaborazione del metodo <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegato della funzione che elabora l'oggetto <paramref name="asyncResult" /> completato.</param>
      <param name="creationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività creata.</param>
      <param name="scheduler">Utilità di pianificazione usata per pianificare l'attività che esegue il metodo End.</param>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="asyncResult" /> è null.-oppure-Il valore dell'argomento <paramref name="endMethod" /> è null.-oppure-Il valore dell'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Nel parametro <paramref name="creationOptions" /> è stato specificato un valore non valido.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.Scheduler">
      <summary>Ottiene l'utilità di pianificazione per questa factory delle attività.</summary>
      <returns>Utilità di pianificazione per questa factory delle attività.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0})">
      <summary>Crea e avvia un'attività.</summary>
      <returns>Attività avviata.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite l'attività.</param>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="function" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken)">
      <summary>Crea e avvia un'attività.</summary>
      <returns>Attività avviata.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite l'attività.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività.</param>
      <exception cref="T:System.ObjectDisposedException">L'origine del token di annullamento che ha creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="function" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e avvia un'attività.</summary>
      <returns>Attività avviata.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite l'attività.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività.</param>
      <param name="creationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività creata.</param>
      <param name="scheduler">Utilità di pianificazione usata per pianificare l'attività creata.</param>
      <exception cref="T:System.ObjectDisposedException">L'origine del token di annullamento che ha creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="function" /> è null.-oppure-Il valore dell'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Nel parametro <paramref name="creationOptions" /> è stato specificato un valore non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e avvia un'attività.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.Task`1" /> avviato.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite l'attività.</param>
      <param name="creationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività creata.</param>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="function" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Nel parametro <paramref name="creationOptions" /> è stato specificato un valore non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object)">
      <summary>Crea e avvia un'attività.</summary>
      <returns>Attività avviata.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite l'attività.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="function" />.</param>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="function" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>Crea e avvia un'attività.</summary>
      <returns>Attività avviata.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite l'attività.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="function" />.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività.</param>
      <exception cref="T:System.ObjectDisposedException">L'origine del token di annullamento che ha creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="function" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e avvia un'attività.</summary>
      <returns>Attività avviata.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite l'attività.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="function" />.</param>
      <param name="cancellationToken">Token di annullamento che verrà assegnato alla nuova attività.</param>
      <param name="creationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività creata.</param>
      <param name="scheduler">Utilità di pianificazione usata per pianificare l'attività creata.</param>
      <exception cref="T:System.ObjectDisposedException">L'origine del token di annullamento che ha creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="function" /> è null.-oppure-Il valore dell'argomento <paramref name="scheduler" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Nel parametro <paramref name="creationOptions" /> è stato specificato un valore non valido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e avvia un'attività.</summary>
      <returns>Attività avviata.</returns>
      <param name="function">Delegato della funzione che restituisce il risultato futuro da rendere disponibile tramite l'attività.</param>
      <param name="state">Oggetto contenente i dati che devono essere usati dal delegato <paramref name="function" />.</param>
      <param name="creationOptions">Uno dei valori di enumerazione che controlla il comportamento dell'attività creata.</param>
      <exception cref="T:System.ArgumentNullException">Il valore dell'argomento <paramref name="function" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Nel parametro <paramref name="creationOptions" /> è stato specificato un valore non valido.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskScheduler">
      <summary>Rappresenta un oggetto che gestisce le operazioni di basso livello relative all'accodamento delle attività nei thread.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.#ctor">
      <summary>Inizializza <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Current">
      <summary>Ottiene l'oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> associato all'attività attualmente in esecuzione.</summary>
      <returns>Restituisce l'oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> associato all'attività attualmente in esecuzione.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Default">
      <summary>Ottiene l'istanza di <see cref="T:System.Threading.Tasks.TaskScheduler" /> predefinita fornita da .NET Framework.</summary>
      <returns>Restituisce l'istanza di <see cref="T:System.Threading.Tasks.TaskScheduler" /> predefinita.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext">
      <summary>Crea un oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> associato all'oggetto <see cref="T:System.Threading.SynchronizationContext" /> corrente.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" /> associato all'oggetto <see cref="T:System.Threading.SynchronizationContext" /> corrente, così come stabilito da <see cref="P:System.Threading.SynchronizationContext.Current" />.</returns>
      <exception cref="T:System.InvalidOperationException">Impossibile utilizzare SynchronizationContext corrente come TaskScheduler.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.GetScheduledTasks">
      <summary>Solo per il supporto del debugger, genera un oggetto enumerabile di istanze di <see cref="T:System.Threading.Tasks.Task" /> correntemente accodate all'utilità di pianificazione in attesa di esecuzione.</summary>
      <returns>Oggetto enumerabile che consente a un debugger l'attraversamento delle attività correntemente accodate all'utilità di pianificazione.</returns>
      <exception cref="T:System.NotSupportedException">Questa utilità di pianificazione in questo momento non è in grado di generare un elenco delle attività in coda.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Id">
      <summary>Ottiene l'ID univoco di questo oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <returns>Restituisce l'ID univoco di questo oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" />.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.MaximumConcurrencyLevel">
      <summary>Indica il livello di concorrenza massimo supportato da questo oggetto <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <returns>Restituisce un intero che rappresenta il livello di concorrenza massimo.L'utilità di pianificazione predefinita restituisce <see cref="F:System.Int32.MaxValue" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.QueueTask(System.Threading.Tasks.Task)">
      <summary>Accoda un oggetto <see cref="T:System.Threading.Tasks.Task" /> all'utilità di pianificazione. </summary>
      <param name="task">Oggetto <see cref="T:System.Threading.Tasks.Task" /> da accodare.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="task" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryDequeue(System.Threading.Tasks.Task)">
      <summary>Tenta di rimuovere dalla coda un oggetto <see cref="T:System.Threading.Tasks.Task" /> accodato in precedenza a questa utilità di pianificazione.</summary>
      <returns>Valore booleano che indica se l'argomento <paramref name="task" /> è stato rimosso correttamente dalla coda.</returns>
      <param name="task">Oggetto <see cref="T:System.Threading.Tasks.Task" /> da rimuovere dalla coda.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="task" /> è null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTask(System.Threading.Tasks.Task)">
      <summary>Tenta di eseguire l'oggetto <see cref="T:System.Threading.Tasks.Task" /> fornito in questa utilità di pianificazione.</summary>
      <returns>Valore booleano uguale a true se l'oggetto <paramref name="task" /> è stato eseguito correttamente, a false in caso contrario.In genere gli errori di esecuzione si verificano perché l'attività è già stata eseguita in precedenza oppure sta per essere eseguita da un altro thread.</returns>
      <param name="task">Oggetto <see cref="T:System.Threading.Tasks.Task" /> da eseguire.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="task" /> non è associato a questa utilità di pianificazione.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
      <summary>Determina se l'oggetto <see cref="T:System.Threading.Tasks.Task" /> fornito può essere eseguito in modo sincrono in questa chiamata e, in tal caso, lo esegue.</summary>
      <returns>Valore booleano che indica se l'attività è stata eseguita inline.</returns>
      <param name="task">Oggetto <see cref="T:System.Threading.Tasks.Task" /> da eseguire.</param>
      <param name="taskWasPreviouslyQueued">Boolean che indica se l'attività è stata accodata in precedenza.Se questo parametro è True, è possibile che l'attività sia stata accodata (pianificata) in precedenza. Se è False, è noto che l'attività non è stata accodata e questa chiamata viene effettuata per eseguire l'attività inline senza accodarla.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="task" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">L'oggetto <paramref name="task" /> è stato già eseguito.</exception>
    </member>
    <member name="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException">
      <summary>Si verifica quando un'eccezione non osservata di un'attività in cui si è verificato un errore sta per attivare i criteri di escalation delle eccezioni che, per impostazione predefinita, interromperebbero il processo.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskSchedulerException">
      <summary>Rappresenta un'eccezione utilizzata per comunicare un'operazione non valida eseguita da <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> con un messaggio fornito dal sistema in cui viene descritto l'errore.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> utilizzando il messaggio di errore predefinito e un riferimento all'eccezione interna che è la causa di questa eccezione.</summary>
      <param name="innerException">Eccezione causa dell'eccezione corrente.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> con un messaggio specifico in cui viene descritto l'errore.</summary>
      <param name="message">Messaggio in cui viene descritta l'eccezione.È necessario che il chiamante del costruttore assicuri che la stringa sia stata localizzata per le impostazioni cultura correnti del sistema.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa di questa eccezione.</summary>
      <param name="message">Messaggio in cui viene descritta l'eccezione.È necessario che il chiamante del costruttore assicuri che la stringa sia stata localizzata per le impostazioni cultura correnti del sistema.</param>
      <param name="innerException">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskStatus">
      <summary>Rappresenta la fase corrente del ciclo di vita di un oggetto <see cref="T:System.Threading.Tasks.Task" />.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Canceled">
      <summary>L'attività ha confermato l'annullamento generando un oggetto OperationCanceledException con il proprio oggetto CancellationToken mentre il token era in stato segnalato oppure l'oggetto CancellationToken dell'attività era già segnalato prima dell'inizio dell'esecuzione dell'attività.Per altre informazioni, vedere Annullamento delle attività.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Created">
      <summary>L'attività è stata inizializzata ma non ancora pianificata.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Faulted">
      <summary>L'attività è stata completata a causa di un'eccezione non gestita.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.RanToCompletion">
      <summary>L'esecuzione dell'attività è stata completata correttamente.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Running">
      <summary>L'attività è in esecuzione ma non è ancora stata completata.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForActivation">
      <summary>L'attività è in attesa di essere attivata e pianificata internamente dall'infrastruttura .NET Framework.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForChildrenToComplete">
      <summary>L'attività ha completato l'esecuzione ed è in attesa implicita del completamento delle attività figlio associate.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingToRun">
      <summary>L'esecuzione dell'attività è stata pianificata ma non è ancora iniziata.</summary>
    </member>
    <member name="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs">
      <summary>Fornisce i dati dell'evento generato quando l'eccezione di un oggetto <see cref="T:System.Threading.Tasks.Task" /> in cui si è verificato un errore non viene osservata.</summary>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.#ctor(System.AggregateException)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs" /> con l'eccezione non osservata.</summary>
      <param name="exception">Eccezione che non è stata osservata.</param>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception">
      <summary>Eccezione che non è stata osservata.</summary>
      <returns>Eccezione che non è stata osservata.</returns>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Observed">
      <summary>Ottiene un valore che indica se questa eccezione è stata contrassegnata come "osservata".</summary>
      <returns>true se l'eccezione è stata contrassegnata come "osservata"; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.SetObserved">
      <summary>Contrassegna l'oggetto <see cref="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception" /> come "osservato", impedendo in questo modo di attivare i criteri di escalation delle eccezioni che, per impostazione predefinita, terminano il processo.</summary>
    </member>
  </members>
</doc>