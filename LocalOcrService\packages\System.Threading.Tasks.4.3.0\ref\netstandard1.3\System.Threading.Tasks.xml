﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks</name>
  </assembly>
  <members>
    <member name="T:System.AggregateException">
      <summary>Represents one or more errors that occur during application execution.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.AggregateException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Initializes a new instance of the <see cref="T:System.AggregateException" /> class with references to the inner exceptions that are the cause of this exception.</summary>
      <param name="innerExceptions">The exceptions that are the cause of the current exception.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="innerExceptions" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">An element of <paramref name="innerExceptions" /> is null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Exception[])">
      <summary>Initializes a new instance of the <see cref="T:System.AggregateException" /> class with references to the inner exceptions that are the cause of this exception.</summary>
      <param name="innerExceptions">The exceptions that are the cause of the current exception.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="innerExceptions" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">An element of <paramref name="innerExceptions" /> is null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.AggregateException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Initializes a new instance of the <see cref="T:System.AggregateException" /> class with a specified error message and references to the inner exceptions that are the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="innerExceptions">The exceptions that are the cause of the current exception.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="innerExceptions" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">An element of <paramref name="innerExceptions" /> is null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.AggregateException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture. </param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="innerException" /> argument is null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception[])">
      <summary>Initializes a new instance of the <see cref="T:System.AggregateException" /> class with a specified error message and references to the inner exceptions that are the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="innerExceptions">The exceptions that are the cause of the current exception.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="innerExceptions" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">An element of <paramref name="innerExceptions" /> is null.</exception>
    </member>
    <member name="M:System.AggregateException.Flatten">
      <summary>Flattens an <see cref="T:System.AggregateException" /> instances into a single, new instance.</summary>
      <returns>A new, flattened <see cref="T:System.AggregateException" />.</returns>
    </member>
    <member name="M:System.AggregateException.GetBaseException">
      <summary>Returns the <see cref="T:System.AggregateException" /> that is the root cause of this exception.</summary>
      <returns>Returns the <see cref="T:System.AggregateException" /> that is the root cause of this exception.</returns>
    </member>
    <member name="M:System.AggregateException.Handle(System.Func{System.Exception,System.Boolean})">
      <summary>Invokes a handler on each <see cref="T:System.Exception" /> contained by this <see cref="T:System.AggregateException" />.</summary>
      <param name="predicate">The predicate to execute for each exception. The predicate accepts as an argument the <see cref="T:System.Exception" /> to be processed and returns a Boolean to indicate whether the exception was handled.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="predicate" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">An exception contained by this <see cref="T:System.AggregateException" /> was not handled.</exception>
    </member>
    <member name="P:System.AggregateException.InnerExceptions">
      <summary>Gets a read-only collection of the <see cref="T:System.Exception" /> instances that caused the current exception.</summary>
      <returns>Returns a read-only collection of the <see cref="T:System.Exception" /> instances that caused the current exception.</returns>
    </member>
    <member name="M:System.AggregateException.ToString">
      <summary>Creates and returns a string representation of the current <see cref="T:System.AggregateException" />.</summary>
      <returns>A string representation of the current exception.</returns>
    </member>
    <member name="T:System.OperationCanceledException">
      <summary>The exception that is thrown in a thread upon cancellation of an operation that the thread was executing.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.OperationCanceledException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.OperationCanceledException" /> class with a system-supplied error message.</summary>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.OperationCanceledException" /> class with a specified error message.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.OperationCanceledException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for the exception. </param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception. </param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception,System.Threading.CancellationToken)">
      <summary>Initializes a new instance of the <see cref="T:System.OperationCanceledException" /> class with a specified error message, a reference to the inner exception that is the cause of this exception, and a cancellation token.</summary>
      <param name="message">The error message that explains the reason for the exception. </param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception. </param>
      <param name="token">A cancellation token associated with the operation that was canceled.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Threading.CancellationToken)">
      <summary>Initializes a new instance of the <see cref="T:System.OperationCanceledException" /> class with a specified error message and a cancellation token.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="token">A cancellation token associated with the operation that was canceled.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.Threading.CancellationToken)">
      <summary>Initializes a new instance of the <see cref="T:System.OperationCanceledException" /> class with a cancellation token.</summary>
      <param name="token">A cancellation token associated with the operation that was canceled.</param>
    </member>
    <member name="P:System.OperationCanceledException.CancellationToken">
      <summary>Gets a token associated with the operation that was canceled.</summary>
      <returns>A token associated with the operation that was canceled, or a default token.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder">
      <summary>Represents a builder for asynchronous methods that return a task.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Schedules the state machine to proceed to the next action when the specified awaiter completes.</summary>
      <param name="awaiter">The awaiter.</param>
      <param name="stateMachine">The state machine.</param>
      <typeparam name="TAwaiter">The type of the awaiter.</typeparam>
      <typeparam name="TStateMachine">The type of the state machine.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Schedules the state machine to proceed to the next action when the specified awaiter completes. This method can be called from partially trusted code.</summary>
      <param name="awaiter">The awaiter.</param>
      <param name="stateMachine">The state machine.</param>
      <typeparam name="TAwaiter">The type of the awaiter.</typeparam>
      <typeparam name="TStateMachine">The type of the state machine.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Create">
      <summary>Creates an instance of the <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder" /> class.</summary>
      <returns>A new instance of the builder.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetException(System.Exception)">
      <summary>Marks the task as failed and binds the specified exception to the task.</summary>
      <param name="exception">The exception to bind to the task.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The task has already completed.-or-The builder is not initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult">
      <summary>Marks the task as successfully completed.</summary>
      <exception cref="T:System.InvalidOperationException">The task has already completed.-or-The builder is not initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Associates the builder with the specified state machine.</summary>
      <param name="stateMachine">The state machine instance to associate with the builder.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The state machine was previously set.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Start``1(``0@)">
      <summary>Begins running the builder with the associated state machine.</summary>
      <param name="stateMachine">The state machine instance, passed by reference.</param>
      <typeparam name="TStateMachine">The type of the state machine.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> is null.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Task">
      <summary>Gets the task for this builder.</summary>
      <returns>The task for this builder.</returns>
      <exception cref="T:System.InvalidOperationException">The builder is not initialized.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1">
      <summary>Represents a builder for asynchronous methods that returns a task and provides a parameter for the result.</summary>
      <typeparam name="TResult">The result to use to complete the task.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Schedules the state machine to proceed to the next action when the specified awaiter completes.</summary>
      <param name="awaiter">The awaiter.</param>
      <param name="stateMachine">The state machine.</param>
      <typeparam name="TAwaiter">The type of the awaiter.</typeparam>
      <typeparam name="TStateMachine">The type of the state machine.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Schedules the state machine to proceed to the next action when the specified awaiter completes. This method can be called from partially trusted code.</summary>
      <param name="awaiter">The awaiter.</param>
      <param name="stateMachine">The state machine.</param>
      <typeparam name="TAwaiter">The type of the awaiter.</typeparam>
      <typeparam name="TStateMachine">The type of the state machine.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Create">
      <summary>Creates an instance of the <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1" /> class.</summary>
      <returns>A new instance of the builder.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetException(System.Exception)">
      <summary>Marks the task as failed and binds the specified exception to the task.</summary>
      <param name="exception">The exception to bind to the task.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The task has already completed.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetResult(`0)">
      <summary>Marks the task as successfully completed.</summary>
      <param name="result">The result to use to complete the task.</param>
      <exception cref="T:System.InvalidOperationException">The task has already completed.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Associates the builder with the specified state machine.</summary>
      <param name="stateMachine">The state machine instance to associate with the builder.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The state machine was previously set.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Start``1(``0@)">
      <summary>Begins running the builder with the associated state machine.</summary>
      <param name="stateMachine">The state machine instance, passed by reference.</param>
      <typeparam name="TStateMachine">The type of the state machine.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> is null.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Task">
      <summary>Gets the task for this builder.</summary>
      <returns>The task for this builder.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder">
      <summary>Represents a builder for asynchronous methods that do not return a value.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Schedules the state machine to proceed to the next action when the specified awaiter completes.</summary>
      <param name="awaiter">The awaiter.</param>
      <param name="stateMachine">The state machine.</param>
      <typeparam name="TAwaiter">The type of the awaiter.</typeparam>
      <typeparam name="TStateMachine">The type of the state machine.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Schedules the state machine to proceed to the next action when the specified awaiter completes. This method can be called from partially trusted code.</summary>
      <param name="awaiter">The awaiter.</param>
      <param name="stateMachine">The state machine.</param>
      <typeparam name="TAwaiter">The type of the awaiter.</typeparam>
      <typeparam name="TStateMachine">The type of the state machine.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Create">
      <summary>Creates an instance of the <see cref="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder" /> class.</summary>
      <returns>A new instance of the builder.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetException(System.Exception)">
      <summary>Binds an exception to the method builder.</summary>
      <param name="exception">The exception to bind.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetResult">
      <summary>Marks the method builder as successfully completed.</summary>
      <exception cref="T:System.InvalidOperationException">The builder is not initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Associates the builder with the specified state machine.</summary>
      <param name="stateMachine">The state machine instance to associate with the builder.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The state machine was previously set.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start``1(``0@)">
      <summary>Begins running the builder with the associated state machine.</summary>
      <param name="stateMachine">The state machine instance, passed by reference.</param>
      <typeparam name="TStateMachine">The type of the state machine.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> is null.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable">
      <summary>Provides an awaitable object that enables configured awaits on a task.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.GetAwaiter">
      <summary>Returns an awaiter for this awaitable object.</summary>
      <returns>The awaiter.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1">
      <summary>Provides an awaitable object that enables configured awaits on a task.</summary>
      <typeparam name="TResult">The type of the result produced by this <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.GetAwaiter">
      <summary>Returns an awaiter for this awaitable object.</summary>
      <returns>The awaiter.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter">
      <summary>Provides an awaiter for an awaitable object(<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1" />).</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.GetResult">
      <summary>Ends the await on the completed task.</summary>
      <returns>The result of the completed task.</returns>
      <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task was canceled.</exception>
      <exception cref="T:System.Exception">The task completed in a faulted state.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.IsCompleted">
      <summary>Gets a value that specifies whether the task being awaited has been completed.</summary>
      <returns>true if the task being awaited has been completed; otherwise, false.</returns>
      <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>Schedules the continuation action for the task associated with this awaiter.</summary>
      <param name="continuation">The action to invoke when the await operation completes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuation" /> argument is null.</exception>
      <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Schedules the continuation action for the task associated with this awaiter. </summary>
      <param name="continuation">The action to invoke when the await operation completes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuation" /> argument is null.</exception>
      <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter">
      <summary>Provides an awaiter for an awaitable (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable" />) object.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.GetResult">
      <summary>Ends the await on the completed task.</summary>
      <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task was canceled.</exception>
      <exception cref="T:System.Exception">The task completed in a faulted state.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.IsCompleted">
      <summary>Gets a value that specifies whether the task being awaited is completed.</summary>
      <returns>true if the task being awaited is completed; otherwise, false.</returns>
      <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>Schedules the continuation action for the task associated with this awaiter.</summary>
      <param name="continuation">The action to invoke when the await operation completes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuation" /> argument is null.</exception>
      <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Schedules the continuation action for the task associated with this awaiter. </summary>
      <param name="continuation">The action to invoke when the await operation completes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuation" /> argument is null.</exception>
      <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.IAsyncStateMachine">
      <summary>Represents state machines that are generated for asynchronous methods. This type is intended for compiler use only.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.MoveNext">
      <summary>Moves the state machine to its next state.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Configures the state machine with a heap-allocated replica.</summary>
      <param name="stateMachine">The heap-allocated replica.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.ICriticalNotifyCompletion">
      <summary>Represents an awaiter that schedules continuations when an await operation completes.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ICriticalNotifyCompletion.UnsafeOnCompleted(System.Action)">
      <summary>Schedules the continuation action that's invoked when the instance completes.</summary>
      <param name="continuation">The action to invoke when the operation completes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuation" /> argument is null (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.INotifyCompletion">
      <summary>Represents an operation that schedules continuations when it completes.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.INotifyCompletion.OnCompleted(System.Action)">
      <summary>Schedules the continuation action that's invoked when the instance completes.</summary>
      <param name="continuation">The action to invoke when the operation completes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuation" /> argument is null (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter">
      <summary>Provides an object that waits for the completion of an asynchronous task. </summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.GetResult">
      <summary>Ends the wait for the completion of the asynchronous task.</summary>
      <exception cref="T:System.NullReferenceException">The <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> object was not properly initialized.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task was canceled.</exception>
      <exception cref="T:System.Exception">The task completed in a <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> state.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter.IsCompleted">
      <summary>Gets a value that indicates whether the asynchronous task has completed.</summary>
      <returns>true if the task has completed; otherwise, false.</returns>
      <exception cref="T:System.NullReferenceException">The <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> object was not properly initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.OnCompleted(System.Action)">
      <summary>Sets the action to perform when the <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> object stops waiting for the asynchronous task to complete.</summary>
      <param name="continuation">The action to perform when the wait operation completes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> is null.</exception>
      <exception cref="T:System.NullReferenceException">The <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> object was not properly initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Schedules the continuation action for the asynchronous task that is associated with this awaiter.</summary>
      <param name="continuation">The action to invoke when the await operation completes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The awaiter was not properly initialized.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter`1">
      <summary>Represents an object that waits for the completion of an asynchronous task and provides a parameter for the result.</summary>
      <typeparam name="TResult">The result for the task.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.GetResult">
      <summary>Ends the wait for the completion of the asynchronous task.</summary>
      <returns>The result of the completed task.</returns>
      <exception cref="T:System.NullReferenceException">The <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> object was not properly initialized.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task was canceled.</exception>
      <exception cref="T:System.Exception">The task completed in a <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> state.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter`1.IsCompleted">
      <summary>Gets a value that indicates whether the asynchronous task has completed.</summary>
      <returns>true if the task has completed; otherwise, false.</returns>
      <exception cref="T:System.NullReferenceException">The <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> object was not properly initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.OnCompleted(System.Action)">
      <summary>Sets the action to perform when the <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> object stops waiting for the asynchronous task to complete.</summary>
      <param name="continuation">The action to perform when the wait operation completes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> is null.</exception>
      <exception cref="T:System.NullReferenceException">The <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> object was not properly initialized.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.UnsafeOnCompleted(System.Action)">
      <summary>Schedules the continuation action for the asynchronous task associated with this awaiter.</summary>
      <param name="continuation">The action to invoke when the await operation completes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The awaiter was not properly initialized.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable">
      <summary>Provides the context for waiting when asynchronously switching into a target environment.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.GetAwaiter">
      <summary>Retrieves a <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> object  for this instance of the class.</summary>
      <returns>The object that is used to monitor the completion of an asynchronous operation.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter">
      <summary>Provides an awaiter for switching into a target environment.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.GetResult">
      <summary>Ends the await operation.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.IsCompleted">
      <summary>Gets a value that indicates whether a yield is not required.</summary>
      <returns>Always false, which indicates that a yield is always required for <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" />.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.OnCompleted(System.Action)">
      <summary>Sets the continuation to invoke.</summary>
      <param name="continuation">The action to invoke asynchronously.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> is null.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Posts the <paramref name="continuation" /> back to the current context.</summary>
      <param name="continuation">The action to invoke asynchronously.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuation" /> argument is null.</exception>
    </member>
    <member name="T:System.Threading.CancellationToken">
      <summary>Propagates notification that operations should be canceled.</summary>
    </member>
    <member name="M:System.Threading.CancellationToken.#ctor(System.Boolean)">
      <summary>Initializes the <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="canceled">The canceled state for the token.</param>
    </member>
    <member name="P:System.Threading.CancellationToken.CanBeCanceled">
      <summary>Gets whether this token is capable of being in the canceled state.</summary>
      <returns>true if this token is capable of being in the canceled state; otherwise false.</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Object)">
      <summary>Determines whether the current <see cref="T:System.Threading.CancellationToken" /> instance is equal to the specified <see cref="T:System.Object" />.</summary>
      <returns>True if <paramref name="other" /> is a <see cref="T:System.Threading.CancellationToken" /> and if the two instances are equal; otherwise, false. Two tokens are equal if they are associated with the same <see cref="T:System.Threading.CancellationTokenSource" /> or if they were both constructed from public CancellationToken constructors and their <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> values are equal.</returns>
      <param name="other">The other object to which to compare this instance.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Threading.CancellationToken)">
      <summary>Determines whether the current <see cref="T:System.Threading.CancellationToken" /> instance is equal to the specified token.</summary>
      <returns>True if the instances are equal; otherwise, false. Two tokens are equal if they are associated with the same <see cref="T:System.Threading.CancellationTokenSource" /> or if they were both constructed from public CancellationToken constructors and their <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> values are equal.</returns>
      <param name="other">The other <see cref="T:System.Threading.CancellationToken" /> to which to compare this instance.</param>
    </member>
    <member name="M:System.Threading.CancellationToken.GetHashCode">
      <summary>Serves as a hash function for a <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>A hash code for the current <see cref="T:System.Threading.CancellationToken" /> instance.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.IsCancellationRequested">
      <summary>Gets whether cancellation has been requested for this token.</summary>
      <returns>true if cancellation has been requested for this token; otherwise false.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.None">
      <summary>Returns an empty <see cref="T:System.Threading.CancellationToken" /> value.</summary>
      <returns>An empty cancellation token. </returns>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Equality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Determines whether two <see cref="T:System.Threading.CancellationToken" /> instances are equal.</summary>
      <returns>True if the instances are equal; otherwise, false.</returns>
      <param name="left">The first instance.</param>
      <param name="right">The second instance.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Inequality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Determines whether two <see cref="T:System.Threading.CancellationToken" /> instances are not equal.</summary>
      <returns>True if the instances are not equal; otherwise, false.</returns>
      <param name="left">The first instance.</param>
      <param name="right">The second instance.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action)">
      <summary>Registers a delegate that will be called when this <see cref="T:System.Threading.CancellationToken" /> is canceled.</summary>
      <returns>The <see cref="T:System.Threading.CancellationTokenRegistration" /> instance that can be used to deregister the callback.</returns>
      <param name="callback">The delegate to be executed when the <see cref="T:System.Threading.CancellationToken" /> is canceled.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action,System.Boolean)">
      <summary>Registers a delegate that will be called when this <see cref="T:System.Threading.CancellationToken" /> is canceled.</summary>
      <returns>The <see cref="T:System.Threading.CancellationTokenRegistration" /> instance that can be used to deregister the callback.</returns>
      <param name="callback">The delegate to be executed when the <see cref="T:System.Threading.CancellationToken" /> is canceled.</param>
      <param name="useSynchronizationContext">A Boolean value that indicates whether to capture the current <see cref="T:System.Threading.SynchronizationContext" /> and use it when invoking the <paramref name="callback" />.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object)">
      <summary>Registers a delegate that will be called when this <see cref="T:System.Threading.CancellationToken" /> is canceled.</summary>
      <returns>The <see cref="T:System.Threading.CancellationTokenRegistration" /> instance that can be used to deregister the callback.</returns>
      <param name="callback">The delegate to be executed when the <see cref="T:System.Threading.CancellationToken" /> is canceled.</param>
      <param name="state">The state to pass to the <paramref name="callback" /> when the delegate is invoked. This may be null.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object,System.Boolean)">
      <summary>Registers a delegate that will be called when this <see cref="T:System.Threading.CancellationToken" /> is canceled.</summary>
      <returns>The <see cref="T:System.Threading.CancellationTokenRegistration" /> instance that can be used to deregister the callback.</returns>
      <param name="callback">The delegate to be executed when the <see cref="T:System.Threading.CancellationToken" /> is canceled.</param>
      <param name="state">The state to pass to the <paramref name="callback" /> when the delegate is invoked. This may be null.</param>
      <param name="useSynchronizationContext">A Boolean value that indicates whether to capture the current <see cref="T:System.Threading.SynchronizationContext" /> and use it when invoking the <paramref name="callback" />.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.ThrowIfCancellationRequested">
      <summary>Throws a <see cref="T:System.OperationCanceledException" /> if this token has had cancellation requested.</summary>
      <exception cref="T:System.OperationCanceledException">The token has had cancellation requested.</exception>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.CancellationToken.WaitHandle">
      <summary>Gets a <see cref="T:System.Threading.WaitHandle" /> that is signaled when the token is canceled.</summary>
      <returns>A <see cref="T:System.Threading.WaitHandle" /> that is signaled when the token is canceled.</returns>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.CancellationTokenRegistration">
      <summary>Represents a callback delegate that has been registered with a <see cref="T:System.Threading.CancellationToken" />. </summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Threading.CancellationTokenRegistration" /> class.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Object)">
      <summary>Determines whether the current <see cref="T:System.Threading.CancellationTokenRegistration" /> instance is equal to the specified <see cref="T:System.Threading.CancellationTokenRegistration" />.</summary>
      <returns>True, if both this and <paramref name="obj" /> are equal. False, otherwise.Two <see cref="T:System.Threading.CancellationTokenRegistration" /> instances are equal if they both refer to the output of a single call to the same Register method of a <see cref="T:System.Threading.CancellationToken" />.</returns>
      <param name="obj">The other object to which to compare this instance.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Threading.CancellationTokenRegistration)">
      <summary>Determines whether the current <see cref="T:System.Threading.CancellationTokenRegistration" /> instance is equal to the specified <see cref="T:System.Threading.CancellationTokenRegistration" />.</summary>
      <returns>True, if both this and <paramref name="other" /> are equal. False, otherwise. Two <see cref="T:System.Threading.CancellationTokenRegistration" /> instances are equal if they both refer to the output of a single call to the same Register method of a <see cref="T:System.Threading.CancellationToken" />.</returns>
      <param name="other">The other <see cref="T:System.Threading.CancellationTokenRegistration" /> to which to compare this instance.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.GetHashCode">
      <summary>Serves as a hash function for a <see cref="T:System.Threading.CancellationTokenRegistration" />.</summary>
      <returns>A hash code for the current <see cref="T:System.Threading.CancellationTokenRegistration" /> instance.</returns>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Equality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>Determines whether two <see cref="T:System.Threading.CancellationTokenRegistration" /> instances are equal.</summary>
      <returns>True if the instances are equal; otherwise, false.</returns>
      <param name="left">The first instance.</param>
      <param name="right">The second instance.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Inequality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>Determines whether two <see cref="T:System.Threading.CancellationTokenRegistration" /> instances are not equal.</summary>
      <returns>True if the instances are not equal; otherwise, false.</returns>
      <param name="left">The first instance.</param>
      <param name="right">The second instance.</param>
    </member>
    <member name="T:System.Threading.CancellationTokenSource">
      <summary>Signals to a <see cref="T:System.Threading.CancellationToken" /> that it should be canceled.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.CancellationTokenSource" /> class.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.CancellationTokenSource" /> class that will be canceled after the specified delay in milliseconds.</summary>
      <param name="millisecondsDelay">The time interval in milliseconds to wait before canceling this <see cref="T:System.Threading.CancellationTokenSource" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsDelay" /> is less than -1. </exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.CancellationTokenSource" /> class that will be canceled after the specified time span.</summary>
      <param name="delay">The time interval to wait before canceling this <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" />.<see cref="P:System.TimeSpan.TotalMilliseconds" /> is less than -1 or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel">
      <summary>Communicates a request for cancellation.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel(System.Boolean)">
      <summary>Communicates a request for cancellation, and specifies whether remaining callbacks and cancelable operations should be processed.</summary>
      <param name="throwOnFirstException">true if exceptions should immediately propagate; otherwise, false.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.Int32)">
      <summary>Schedules a cancel operation on this <see cref="T:System.Threading.CancellationTokenSource" /> after the specified number of milliseconds.</summary>
      <param name="millisecondsDelay">The time span to wait before canceling this <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception thrown when <paramref name="millisecondsDelay" /> is less than -1.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.TimeSpan)">
      <summary>Schedules a cancel operation on this <see cref="T:System.Threading.CancellationTokenSource" /> after the specified time span.</summary>
      <param name="delay">The time span to wait before canceling this <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when <paramref name="delay" /> is less than -1 or greater than Int32.MaxValue.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Creates a <see cref="T:System.Threading.CancellationTokenSource" /> that will be in the canceled state when any of the source tokens are in the canceled state.</summary>
      <returns>A <see cref="T:System.Threading.CancellationTokenSource" /> that is linked to the source tokens.</returns>
      <param name="token1">The first cancellation token to observe.</param>
      <param name="token2">The second cancellation token to observe.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken[])">
      <summary>Creates a <see cref="T:System.Threading.CancellationTokenSource" /> that will be in the canceled state when any of the source tokens in the specified array are in the canceled state.</summary>
      <returns>A <see cref="T:System.Threading.CancellationTokenSource" /> that is linked to the source tokens.</returns>
      <param name="tokens">An array that contains the cancellation token instances to observe.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tokens" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tokens" /> is empty.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Threading.CancellationTokenSource" /> class.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Threading.CancellationTokenSource" /> class and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.IsCancellationRequested">
      <summary>Gets whether cancellation has been requested for this <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
      <returns>true if cancellation has been requested for this <see cref="T:System.Threading.CancellationTokenSource" />; otherwise, false.</returns>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.Token">
      <summary>Gets the <see cref="T:System.Threading.CancellationToken" /> associated with this <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
      <returns>The <see cref="T:System.Threading.CancellationToken" /> associated with this <see cref="T:System.Threading.CancellationTokenSource" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The token source has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair">
      <summary>Provides task schedulers that coordinate to execute tasks while ensuring that concurrent tasks may run concurrently and exclusive tasks never do.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> class.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> class that targets the specified scheduler.</summary>
      <param name="taskScheduler">The target scheduler on which this pair should execute.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> class that targets the specified scheduler with a maximum concurrency level.</summary>
      <param name="taskScheduler">The target scheduler on which this pair should execute.</param>
      <param name="maxConcurrencyLevel">The maximum number of tasks to run concurrently.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> class that targets the specified scheduler with a maximum concurrency level and a maximum number of scheduled tasks that may be processed as a unit.</summary>
      <param name="taskScheduler">The target scheduler on which this pair should execute.</param>
      <param name="maxConcurrencyLevel">The maximum number of tasks to run concurrently.</param>
      <param name="maxItemsPerTask">The maximum number of tasks to process for each underlying scheduled task used by the pair.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Complete">
      <summary>Informs the scheduler pair that it should not accept any more tasks.</summary>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Completion">
      <summary>Gets a <see cref="T:System.Threading.Tasks.Task" /> that will complete when the scheduler has completed processing.</summary>
      <returns>The asynchronous operation that will complete when the scheduler finishes processing.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ConcurrentScheduler">
      <summary>Gets a <see cref="T:System.Threading.Tasks.TaskScheduler" /> that can be used to schedule tasks to this pair that may run concurrently with other tasks on this pair.</summary>
      <returns>An object that can be used to schedule tasks concurrently.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ExclusiveScheduler">
      <summary>Gets a <see cref="T:System.Threading.Tasks.TaskScheduler" /> that can be used to schedule tasks to this pair that must run exclusively with regards to other tasks on this pair.</summary>
      <returns>An object that can be used to schedule tasks that do not run concurrently with other tasks.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task">
      <summary>Represents an asynchronous operation.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task" /> with the specified action.</summary>
      <param name="action">The delegate that represents the code to execute in the task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task" /> with the specified action and <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="action">The delegate that represents the code to execute in the task.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that the new  task will observe.</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task" /> with the specified action and creation options.</summary>
      <param name="action">The delegate that represents the code to execute in the task.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that the new task will observe.</param>
      <param name="creationOptions">The <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> used to customize the task's behavior.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task" /> with the specified action and creation options.</summary>
      <param name="action">The delegate that represents the code to execute in the task.</param>
      <param name="creationOptions">The <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> used to customize the task's behavior. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task" /> with the specified action and state.</summary>
      <param name="action">The delegate that represents the code to execute in the task.</param>
      <param name="state">An object representing data to be used by the action.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task" /> with the specified action, state, and options.</summary>
      <param name="action">The delegate that represents the code to execute in the task.</param>
      <param name="state">An object representing data to be used by the action.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that that the new task will observe.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task" /> with the specified action, state, and options.</summary>
      <param name="action">The delegate that represents the code to execute in the task.</param>
      <param name="state">An object representing data to be used by the action.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that that the new task will observe..</param>
      <param name="creationOptions">The <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> used to customize the task's behavior.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task" /> with the specified action, state, and options.</summary>
      <param name="action">The delegate that represents the code to execute in the task.</param>
      <param name="state">An object representing data to be used by the action.</param>
      <param name="creationOptions">The <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> used to customize the task's behavior.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.AsyncState">
      <summary>Gets the state object supplied when the <see cref="T:System.Threading.Tasks.Task" /> was created, or null if none was supplied.</summary>
      <returns>An <see cref="T:System.Object" /> that represents the state data that was passed in to the task when it was created.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CompletedTask">
      <summary>Gets a task that has already completed successfully. </summary>
      <returns>The successfully completed task. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.ConfigureAwait(System.Boolean)">
      <summary>Configures an awaiter used to await this <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>An object used to await this task.</returns>
      <param name="continueOnCapturedContext">true to attempt to marshal the continuation back to the original context captured; otherwise, false.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be passed the completed task as an argument.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Creates a continuation that receives a cancellation token and executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes when the target task competes according to the specified <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />. The continuation receives a cancellation token and uses a specified scheduler. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run according to the specified <paramref name="continuationOptions" />. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation that executes when the target task completes according to the specified <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run according to the specified <paramref name="continuationOptions" />. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task" /> completes. The continuation uses a specified scheduler. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. -or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object)">
      <summary>Creates a continuation that receives caller-supplied state information and executes when the target <see cref="T:System.Threading.Tasks.Task" /> completes. </summary>
      <returns>A new continuation task. </returns>
      <param name="continuationAction">An action to run when the task completes. When run, the delegate is passed the completed task and a caller-supplied state object as arguments. </param>
      <param name="state">An object representing data to be used by the continuation action. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Creates a continuation that receives caller-supplied state information and a cancellation token and that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation action.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that receives caller-supplied state information and a cancellation token and that executes when the target <see cref="T:System.Threading.Tasks.Task" /> completes. The continuation executes based on a set of specified conditions and uses a specified scheduler. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation action.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its  execution.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation that receives caller-supplied state information and executes when the target <see cref="T:System.Threading.Tasks.Task" /> completes. The continuation executes based on a set of specified conditions. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation action.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that receives caller-supplied state information and executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task" /> completes. The continuation uses a specified scheduler. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task" /> completes.  When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation action.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0})">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes and returns a value. </summary>
      <returns>A new continuation task. </returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task`1" />  completes. When run, the delegate will be passed the completed task as an argument.</param>
      <typeparam name="TResult"> The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task" /> completes and returns a value. The continuation receives a cancellation token. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <typeparam name="TResult"> The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes according to the specified continuation options and returns a value. The continuation is passed a cancellation token and uses a specified scheduler. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run according to the specified <paramref name="continuationOptions." /> When run, the delegate will be passed the completed task as an argument.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <typeparam name="TResult"> The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation that executes according to the specified continuation options and returns a value. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run according to the condition specified in <paramref name="continuationOptions" />. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TResult"> The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task" /> completes and returns a value. The continuation uses a specified scheduler. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <typeparam name="TResult"> The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object)">
      <summary>Creates a continuation that receives caller-supplied state information and executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task" /> completes and returns a value. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be passed the completed task and the caller-supplied state object as arguments. </param>
      <param name="state">An object representing data to be used by the continuation function.</param>
      <typeparam name="TResult">The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task" /> completes and returns a value. The continuation receives caller-supplied state information and a cancellation token. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation function.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <typeparam name="TResult">The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes based on the specified task continuation options when the target <see cref="T:System.Threading.Tasks.Task" /> completes and returns a value. The continuation receives caller-supplied state information and a cancellation token and uses the specified scheduler. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation function.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its  execution.</param>
      <typeparam name="TResult">The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation that executes based on the specified task continuation options when the target <see cref="T:System.Threading.Tasks.Task" /> completes. The continuation receives caller-supplied state information. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task" /> completes. When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation function.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TResult">The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task" /> completes. The continuation receives caller-supplied state information and uses a specified scheduler. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task" /> completes.  When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation function.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <typeparam name="TResult">The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.CreationOptions">
      <summary>Gets the <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> used to create this task.</summary>
      <returns>The <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> used to create this task.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CurrentId">
      <summary>Returns the unique ID of the currently executing <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>An integer that was assigned by the system to the currently-executing task.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32)">
      <summary>Creates a task that completes after a time delay. </summary>
      <returns>A task that represents the time delay. </returns>
      <param name="millisecondsDelay">The number of milliseconds to wait before completing the returned task, or -1 to wait indefinitely. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32,System.Threading.CancellationToken)">
      <summary>Creates a cancellable task that completes after a time delay. </summary>
      <returns>A task that represents the time delay. </returns>
      <param name="millisecondsDelay">The number of milliseconds to wait before completing the returned task, or -1 to wait indefinitely. </param>
      <param name="cancellationToken">The cancellation token that will be checked prior to completing the returned task. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled. </exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan)">
      <summary>Creates a task that completes after a specified time interval. </summary>
      <returns>A task that represents the time delay. </returns>
      <param name="delay">The time span to wait before completing the returned task, or TimeSpan.FromMilliseconds(-1) to wait indefinitely. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Creates a cancellable task that completes after a specified time interval. </summary>
      <returns>A task that represents the time delay. </returns>
      <param name="delay">The time span to wait before completing the returned task, or TimeSpan.FromMilliseconds(-1) to wait indefinitely. </param>
      <param name="cancellationToken">The cancellation token that will be checked prior to completing the returned task. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Exception">
      <summary>Gets the <see cref="T:System.AggregateException" /> that caused the <see cref="T:System.Threading.Tasks.Task" /> to end prematurely. If the <see cref="T:System.Threading.Tasks.Task" /> completed successfully or has not yet thrown any exceptions, this will return null.</summary>
      <returns>The <see cref="T:System.AggregateException" /> that caused the <see cref="T:System.Threading.Tasks.Task" /> to end prematurely.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Factory">
      <summary>Provides access to factory methods for creating and configuring <see cref="T:System.Threading.Tasks.Task" /> and <see cref="T:System.Threading.Tasks.Task`1" /> instances.</summary>
      <returns>A factory object that can create a variety of <see cref="T:System.Threading.Tasks.Task" /> and <see cref="T:System.Threading.Tasks.Task`1" /> objects. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled(System.Threading.CancellationToken)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that's completed due to cancellation with a specified cancellation token.</summary>
      <returns>The canceled task. </returns>
      <param name="cancellationToken">The cancellation token with which to complete the task. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled``1(System.Threading.CancellationToken)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that's completed due to cancellation with a specified cancellation token.</summary>
      <returns>The canceled task. </returns>
      <param name="cancellationToken">The cancellation token with which to complete the task. </param>
      <typeparam name="TResult">The type of the result returned by the task. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException``1(System.Exception)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that's completed with a specified exception. </summary>
      <returns>The faulted task. </returns>
      <param name="exception">The exception with which to complete the task. </param>
      <typeparam name="TResult">The type of the result returned by the task. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException(System.Exception)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that has completed with a specified exception. </summary>
      <returns>The faulted task. </returns>
      <param name="exception">The exception with which to complete the task. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromResult``1(``0)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that's completed successfully with the specified result.</summary>
      <returns>The successfully completed task.</returns>
      <param name="result">The result to store into the completed task. </param>
      <typeparam name="TResult">The type of the result returned by the task. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.GetAwaiter">
      <summary>Gets an awaiter used to await this <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>An awaiter instance.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Id">
      <summary>Gets a unique ID for this <see cref="T:System.Threading.Tasks.Task" /> instance.</summary>
      <returns>An integer that was assigned by the system to this task instance. </returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCanceled">
      <summary>Gets whether this <see cref="T:System.Threading.Tasks.Task" /> instance has completed execution due to being canceled.</summary>
      <returns>true if the task has completed due to being canceled; otherwise false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCompleted">
      <summary>Gets whether this <see cref="T:System.Threading.Tasks.Task" /> has completed.</summary>
      <returns>true if the task has completed; otherwise false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsFaulted">
      <summary>Gets whether the <see cref="T:System.Threading.Tasks.Task" /> completed due to an unhandled exception.</summary>
      <returns>true if the task has thrown an unhandled exception; otherwise false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action)">
      <summary>Queues the specified work to run on the ThreadPool and returns a task handle for that work.</summary>
      <returns>A task that represents the work queued to execute in the ThreadPool.</returns>
      <param name="action">The work to execute asynchronously</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action,System.Threading.CancellationToken)">
      <summary>Queues the specified work to run on the ThreadPool and returns a task handle for that work.</summary>
      <returns>A task that represents the work queued to execute in the ThreadPool.</returns>
      <param name="action">The work to execute asynchronously</param>
      <param name="cancellationToken">A cancellation token that should be used to cancel the work</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
      <summary>Queues the specified work to run on the ThreadPool and returns a proxy for the  Task(TResult) returned by <paramref name="function" />.</summary>
      <returns>A Task(TResult) that represents a proxy for the Task(TResult) returned by <paramref name="function" />.</returns>
      <param name="function">The work to execute asynchronously</param>
      <typeparam name="TResult">The type of the result returned by the proxy task.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>Queues the specified work to run on the ThreadPool and returns a proxy for the  Task(TResult) returned by <paramref name="function" />.</summary>
      <returns>A Task(TResult) that represents a proxy for the Task(TResult) returned by <paramref name="function" />.</returns>
      <param name="function">The work to execute asynchronously</param>
      <param name="cancellationToken">A cancellation token that should be used to cancel the work</param>
      <typeparam name="TResult">The type of the result returned by the proxy task.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task})">
      <summary>Queues the specified work to run on the ThreadPool and returns a proxy for the  task returned by <paramref name="function" />.</summary>
      <returns>A task that represents a proxy for the task returned by <paramref name="function" />.</returns>
      <param name="function">The work to execute asynchronously</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Queues the specified work to run on the ThreadPool and returns a proxy for the task returned by <paramref name="function" />.</summary>
      <returns>A task that represents a proxy for the task returned by <paramref name="function" />.</returns>
      <param name="function">The work to execute asynchronously. </param>
      <param name="cancellationToken">A cancellation token that should be used to cancel the work. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0})">
      <summary>Queues the specified work to run on the thread pool and returns a <see cref="T:System.Threading.Tasks.Task`1" /> object that represents that work. </summary>
      <returns>A task object that represents the work queued to execute in the thread pool. </returns>
      <param name="function">The work to execute asynchronously. </param>
      <typeparam name="TResult">The return type of the task. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>Queues the specified work to run on the thread pool and returns a Task(TResult) handle for that work.</summary>
      <returns>A Task(TResult) that represents the work queued to execute in the ThreadPool.</returns>
      <param name="function">The work to execute asynchronously</param>
      <param name="cancellationToken">A cancellation token that should be used to cancel the work</param>
      <typeparam name="TResult">The result type of the task.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously">
      <summary>Runs the <see cref="T:System.Threading.Tasks.Task" /> synchronously on the current <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started. It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously(System.Threading.Tasks.TaskScheduler)">
      <summary>Runs the <see cref="T:System.Threading.Tasks.Task" /> synchronously on the <see cref="T:System.Threading.Tasks.TaskScheduler" /> provided.</summary>
      <param name="scheduler">The scheduler on which to attempt to run this task inline.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started. It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start">
      <summary>Starts the <see cref="T:System.Threading.Tasks.Task" />, scheduling it for execution to the current <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started. It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start(System.Threading.Tasks.TaskScheduler)">
      <summary>Starts the <see cref="T:System.Threading.Tasks.Task" />, scheduling it for execution to the specified <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> with which to associate and execute this task.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started. It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Status">
      <summary>Gets the <see cref="T:System.Threading.Tasks.TaskStatus" /> of this task.</summary>
      <returns>The current <see cref="T:System.Threading.Tasks.TaskStatus" /> of this task instance.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#AsyncWaitHandle">
      <summary>Gets a <see cref="T:System.Threading.WaitHandle" /> that can be used to wait for the task to complete.</summary>
      <returns>A <see cref="T:System.Threading.WaitHandle" /> that can be used to wait for the task to complete.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#CompletedSynchronously">
      <summary>Gets an indication of whether the operation completed synchronously.</summary>
      <returns>true if the operation completed synchronously; otherwise, false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait">
      <summary>Waits for the <see cref="T:System.Threading.Tasks.Task" /> to complete execution.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object. -or-An exception was thrown during the execution of the task. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32)">
      <summary>Waits for the <see cref="T:System.Threading.Tasks.Task" /> to complete execution within a specified number of milliseconds.</summary>
      <returns>true if the <see cref="T:System.Threading.Tasks.Task" /> completed execution within the allotted time; otherwise, false.</returns>
      <param name="millisecondsTimeout">The number of milliseconds to wait, or <see cref="F:System.Threading.Timeout.Infinite" /> (-1) to wait indefinitely.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object. -or-An exception was thrown during the execution of the task. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Waits for the <see cref="T:System.Threading.Tasks.Task" /> to complete execution. The wait terminates if a timeout interval elapses or a cancellation token is canceled before the task completes. </summary>
      <returns>true if the <see cref="T:System.Threading.Tasks.Task" /> completed execution within the allotted time; otherwise, false.</returns>
      <param name="millisecondsTimeout">The number of milliseconds to wait, or <see cref="F:System.Threading.Timeout.Infinite" /> (-1) to wait indefinitely. </param>
      <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object. -or-An exception was thrown during the execution of the task. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Threading.CancellationToken)">
      <summary>Waits for the <see cref="T:System.Threading.Tasks.Task" /> to complete execution. The wait terminates if a cancellation token is canceled before the task completes. </summary>
      <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The task has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object. -or-An exception was thrown during the execution of the task. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.TimeSpan)">
      <summary>Waits for the <see cref="T:System.Threading.Tasks.Task" /> to complete execution within a specified time interval.</summary>
      <returns>true if the <see cref="T:System.Threading.Tasks.Task" /> completed execution within the allotted time; otherwise, false.</returns>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that represents the number of milliseconds to wait, or a <see cref="T:System.TimeSpan" /> that represents -1 milliseconds to wait indefinitely.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.AggregateException">The task was canceled. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object. -or-An exception was thrown during the execution of the task. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[])">
      <summary>Waits for all of the provided <see cref="T:System.Threading.Tasks.Task" /> objects to complete execution.</summary>
      <param name="tasks">An array of <see cref="T:System.Threading.Tasks.Task" /> instances on which to wait.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.-or-The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled. If a task was canceled, the <see cref="T:System.AggregateException" /> exception contains an <see cref="T:System.OperationCanceledException" /> exception in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32)">
      <summary>Waits for all of the provided <see cref="T:System.Threading.Tasks.Task" /> objects to complete execution within a specified number of milliseconds.</summary>
      <returns>true if all of the <see cref="T:System.Threading.Tasks.Task" /> instances completed execution within the allotted time; otherwise, false.</returns>
      <param name="tasks">An array of <see cref="T:System.Threading.Tasks.Task" /> instances on which to wait.</param>
      <param name="millisecondsTimeout">The number of milliseconds to wait, or <see cref="F:System.Threading.Timeout.Infinite" /> (-1) to wait indefinitely.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled. If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection. -or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>Waits for all of the provided <see cref="T:System.Threading.Tasks.Task" /> objects to complete execution within a specified number of milliseconds or until the wait is cancelled.</summary>
      <returns>true if all of the <see cref="T:System.Threading.Tasks.Task" /> instances completed execution within the allotted time; otherwise, false.</returns>
      <param name="tasks">An array of <see cref="T:System.Threading.Tasks.Task" /> instances on which to wait.</param>
      <param name="millisecondsTimeout">The number of milliseconds to wait, or <see cref="F:System.Threading.Timeout.Infinite" /> (-1) to wait indefinitely.</param>
      <param name="cancellationToken">A <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> to observe while waiting for the tasks to complete.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled. If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>Waits for all of the provided <see cref="T:System.Threading.Tasks.Task" /> objects to complete execution unless the wait is cancelled. </summary>
      <param name="tasks">An array of <see cref="T:System.Threading.Tasks.Task" /> instances on which to wait.</param>
      <param name="cancellationToken">A <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> to observe while waiting for the tasks to complete.</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled. If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>Waits for all of the provided cancellable <see cref="T:System.Threading.Tasks.Task" /> objects to complete execution within a specified time interval.</summary>
      <returns>true if all of the <see cref="T:System.Threading.Tasks.Task" /> instances completed execution within the allotted time; otherwise, false.</returns>
      <param name="tasks">An array of <see cref="T:System.Threading.Tasks.Task" /> instances on which to wait.</param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that represents the number of milliseconds to wait, or a <see cref="T:System.TimeSpan" /> that represents -1 milliseconds to wait indefinitely.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null. </exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled. If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[])">
      <summary>Waits for any of the provided <see cref="T:System.Threading.Tasks.Task" /> objects to complete execution.</summary>
      <returns>The index of the completed task in the <paramref name="tasks" /> array argument.</returns>
      <param name="tasks">An array of <see cref="T:System.Threading.Tasks.Task" /> instances on which to wait.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32)">
      <summary>Waits for any of the provided <see cref="T:System.Threading.Tasks.Task" /> objects to complete execution within a specified number of milliseconds.</summary>
      <returns>The index of the completed task in the <paramref name="tasks" /> array argument, or -1 if the timeout occurred.</returns>
      <param name="tasks">An array of <see cref="T:System.Threading.Tasks.Task" /> instances on which to wait.</param>
      <param name="millisecondsTimeout">The number of milliseconds to wait, or <see cref="F:System.Threading.Timeout.Infinite" /> (-1) to wait indefinitely.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>Waits for any of the provided <see cref="T:System.Threading.Tasks.Task" /> objects to complete execution within a specified number of milliseconds or until a cancellation token is cancelled.</summary>
      <returns>The index of the completed task in the <paramref name="tasks" /> array argument, or -1 if the timeout occurred.</returns>
      <param name="tasks">An array of <see cref="T:System.Threading.Tasks.Task" /> instances on which to wait. </param>
      <param name="millisecondsTimeout">The number of milliseconds to wait, or <see cref="F:System.Threading.Timeout.Infinite" /> (-1) to wait indefinitely. </param>
      <param name="cancellationToken">A <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> to observe while waiting for a task to complete. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>Waits for any of the provided <see cref="T:System.Threading.Tasks.Task" /> objects to complete execution unless the wait is cancelled.</summary>
      <returns>The index of the completed task in the <paramref name="tasks" /> array argument.</returns>
      <param name="tasks">An array of <see cref="T:System.Threading.Tasks.Task" /> instances on which to wait. </param>
      <param name="cancellationToken">A <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> to observe while waiting for a task to complete. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>Waits for any of the provided <see cref="T:System.Threading.Tasks.Task" /> objects to complete execution within a specified time interval.</summary>
      <returns>The index of the completed task in the <paramref name="tasks" /> array argument, or -1 if the timeout occurred.</returns>
      <param name="tasks">An array of <see cref="T:System.Threading.Tasks.Task" /> instances on which to wait.</param>
      <param name="timeout">A <see cref="T:System.TimeSpan" /> that represents the number of milliseconds to wait, or a <see cref="T:System.TimeSpan" /> that represents -1 milliseconds to wait indefinitely.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>Creates a task that will complete when all of the <see cref="T:System.Threading.Tasks.Task`1" /> objects in an enumerable collection have completed. </summary>
      <returns>A task that represents the completion of all of the supplied tasks. </returns>
      <param name="tasks">The tasks to wait on for completion. </param>
      <typeparam name="TResult">The type of the completed task. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>Creates a task that will complete when all of the <see cref="T:System.Threading.Tasks.Task" /> objects in an enumerable collection have completed.</summary>
      <returns>A task that represents the completion of all of the supplied tasks. </returns>
      <param name="tasks">The tasks to wait on for completion.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Threading.Tasks.Task[])">
      <summary>Creates a task that will complete when all of the <see cref="T:System.Threading.Tasks.Task" /> objects in an array have completed. </summary>
      <returns>A task that represents the completion of all of the supplied tasks.</returns>
      <param name="tasks">The tasks to wait on for completion.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Threading.Tasks.Task{``0}[])">
      <summary>Creates a task that will complete when all of the <see cref="T:System.Threading.Tasks.Task`1" /> objects in an array have completed. </summary>
      <returns>A task that represents the completion of all of the supplied tasks.</returns>
      <param name="tasks">The tasks to wait on for completion.</param>
      <typeparam name="TResult">The type of the completed task.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>Creates a task that will complete when any of the supplied tasks have completed.</summary>
      <returns>A task that represents the completion of one of the supplied tasks.  The return task's Result is the task that completed.</returns>
      <param name="tasks">The tasks to wait on for completion.</param>
      <typeparam name="TResult">The type of the completed task.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>Creates a task that will complete when any of the supplied tasks have completed.</summary>
      <returns>A task that represents the completion of one of the supplied tasks.  The return task's Result is the task that completed.</returns>
      <param name="tasks">The tasks to wait on for completion.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Threading.Tasks.Task[])">
      <summary>Creates a task that will complete when any of the supplied tasks have completed.</summary>
      <returns>A task that represents the completion of one of the supplied tasks.  The return task's Result is the task that completed.</returns>
      <param name="tasks">The tasks to wait on for completion.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Threading.Tasks.Task{``0}[])">
      <summary>Creates a task that will complete when any of the supplied tasks have completed.</summary>
      <returns>A task that represents the completion of one of the supplied tasks.  The return task's Result is the task that completed.</returns>
      <param name="tasks">The tasks to wait on for completion.</param>
      <typeparam name="TResult">The type of the completed task.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Yield">
      <summary>Creates an awaitable task that asynchronously yields back to the current context when awaited.</summary>
      <returns>A context that, when awaited, will asynchronously transition back into the current context at the time of the await. If the current <see cref="T:System.Threading.SynchronizationContext" /> is non-null, it is treated as the current context. Otherwise, the task scheduler that is associated with the currently executing task is treated as the current context. </returns>
    </member>
    <member name="T:System.Threading.Tasks.Task`1">
      <summary>Represents an asynchronous operation that can return a value.</summary>
      <typeparam name="TResult">The type of the result produced by this <see cref="T:System.Threading.Tasks.Task`1" />. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0})">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task`1" /> with the specified function.</summary>
      <param name="function">The delegate that represents the code to execute in the task. When the function has completed, the task's <see cref="P:System.Threading.Tasks.Task`1.Result" /> property will be set to return the result value of the function.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task`1" /> with the specified function.</summary>
      <param name="function">The delegate that represents the code to execute in the task. When the function has completed, the task's <see cref="P:System.Threading.Tasks.Task`1.Result" /> property will be set to return the result value of the function.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to be assigned to this task.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task`1" /> with the specified function and creation options.</summary>
      <param name="function">The delegate that represents the code to execute in the task. When the function has completed, the task's <see cref="P:System.Threading.Tasks.Task`1.Result" /> property will be set to return the result value of the function.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new task.</param>
      <param name="creationOptions">The <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> used to customize the task's behavior.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task`1" /> with the specified function and creation options.</summary>
      <param name="function">The delegate that represents the code to execute in the task. When the function has completed, the task's <see cref="P:System.Threading.Tasks.Task`1.Result" /> property will be set to return the result value of the function.</param>
      <param name="creationOptions">The <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> used to customize the task's behavior.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task`1" /> with the specified function and state.</summary>
      <param name="function">The delegate that represents the code to execute in the task. When the function has completed, the task's <see cref="P:System.Threading.Tasks.Task`1.Result" /> property will be set to return the result value of the function.</param>
      <param name="state">An object representing data to be used by the action.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task`1" /> with the specified action, state, and options.</summary>
      <param name="function">The delegate that represents the code to execute in the task. When the function has completed, the task's <see cref="P:System.Threading.Tasks.Task`1.Result" /> property will be set to return the result value of the function.</param>
      <param name="state">An object representing data to be used by the function.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to be assigned to the new task.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task`1" /> with the specified action, state, and options.</summary>
      <param name="function">The delegate that represents the code to execute in the task. When the function has completed, the task's <see cref="P:System.Threading.Tasks.Task`1.Result" /> property will be set to return the result value of the function.</param>
      <param name="state">An object representing data to be used by the function.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to be assigned to the new task.</param>
      <param name="creationOptions">The <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> used to customize the task's behavior.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initializes a new <see cref="T:System.Threading.Tasks.Task`1" /> with the specified action, state, and options.</summary>
      <param name="function">The delegate that represents the code to execute in the task. When the function has completed, the task's <see cref="P:System.Threading.Tasks.Task`1.Result" /> property will be set to return the result value of the function.</param>
      <param name="state">An object representing data to be used by the function.</param>
      <param name="creationOptions">The <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> used to customize the task's behavior.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ConfigureAwait(System.Boolean)">
      <summary>Configures an awaiter used to await this <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>An object used to await this task.</returns>
      <param name="continueOnCapturedContext">true to attempt to marshal the continuation back to the original context captured; otherwise, false.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}})">
      <summary>Creates a continuation that executes asynchronously when the target task completes. </summary>
      <returns>A new continuation task. </returns>
      <param name="continuationAction">An action to run when the antecedent <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be passed the completed task as an argument.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken)">
      <summary>Creates a cancelable continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation task. </returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate is passed the completed task as an argument. </param>
      <param name="cancellationToken">The cancellation token that is passed to the new continuation task. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes according the condition specified in <paramref name="continuationOptions" />.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run according the condition specified in <paramref name="continuationOptions" />. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation that executes according the condition specified in <paramref name="continuationOptions" />.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to according the condition specified in <paramref name="continuationOptions" />. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object)">
      <summary>Creates a continuation that that is passed state information and that executes when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes. </summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate is   passed the completed task and the caller-supplied state object as arguments. </param>
      <param name="state">An object representing data to be used by the continuation action. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Creates a continuation that executes when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation action.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation action.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as  well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its  execution.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation that executes when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation action.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such  as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">An action to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation action.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0})">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be passed the completed task as an argument.</param>
      <typeparam name="TNewResult"> The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken)">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new task.</param>
      <typeparam name="TNewResult"> The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes according the condition specified in <paramref name="continuationOptions" />.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run according the condition specified in <paramref name="continuationOptions" />.When run, the delegate will be passed as an argument this completed task.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new task.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <typeparam name="TNewResult"> The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation that executes according the condition specified in <paramref name="continuationOptions" />.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run according the condition specified in <paramref name="continuationOptions" />.When run, the delegate will be passed the completed task as an argument.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TNewResult"> The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes asynchronously when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be passed the completed task as an argument.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <typeparam name="TNewResult"> The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object)">
      <summary>Creates a continuation that executes when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation function.</param>
      <typeparam name="TNewResult">The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Creates a continuation that executes when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation function.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new task.</param>
      <typeparam name="TNewResult">The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation function.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new task.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <typeparam name="TNewResult">The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The  <paramref name="continuationOptions" />  argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation that executes when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be  passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation function.</param>
      <param name="continuationOptions">Options for when the continuation is scheduled and how it behaves. This includes criteria, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, as well as execution options, such as <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TNewResult">The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation that executes when the target <see cref="T:System.Threading.Tasks.Task`1" /> completes.</summary>
      <returns>A new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">A function to run when the <see cref="T:System.Threading.Tasks.Task`1" /> completes. When run, the delegate will be passed the completed task and the caller-supplied state object as arguments.</param>
      <param name="state">An object representing data to be used by the continuation function.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to associate with the continuation task and to use for its execution.</param>
      <typeparam name="TNewResult">The type of the result produced by the continuation.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Factory">
      <summary>Provides access to factory methods for creating and configuring <see cref="T:System.Threading.Tasks.Task`1" /> instances.</summary>
      <returns>A factory object that can create a variety of <see cref="T:System.Threading.Tasks.Task`1" /> objects.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.GetAwaiter">
      <summary>Gets an awaiter used to await this <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>An awaiter instance.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Result">
      <summary>Gets the result value of this <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>The result value of this <see cref="T:System.Threading.Tasks.Task`1" />, which is the same type as the task's type parameter.</returns>
      <exception cref="T:System.AggregateException">The task was canceled. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object. -or-An exception was thrown during the execution of the task. The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions. </exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskCanceledException">
      <summary>Represents an exception used to communicate task cancellation.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.TaskCanceledException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.TaskCanceledException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.TaskCanceledException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture. </param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception. </param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.Threading.Tasks.Task)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.TaskCanceledException" /> class with a reference to the <see cref="T:System.Threading.Tasks.Task" /> that has been canceled.</summary>
      <param name="task">A task that has been canceled.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskCanceledException.Task">
      <summary>Gets the task associated with this exception.</summary>
      <returns>A reference to the <see cref="T:System.Threading.Tasks.Task" /> that is associated with this exception.</returns>
    </member>
    <member name="T:System.Threading.Tasks.TaskCompletionSource`1">
      <summary>Represents the producer side of a <see cref="T:System.Threading.Tasks.Task`1" /> unbound to a delegate, providing access to the consumer side through the <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> property.</summary>
      <typeparam name="TResult">The type of the result value assocatied with this <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor">
      <summary>Creates a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> with the specified state.</summary>
      <param name="state">The state to use as the underlying <see cref="T:System.Threading.Tasks.Task`1" />'s AsyncState.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> with the specified state and options.</summary>
      <param name="state">The state to use as the underlying <see cref="T:System.Threading.Tasks.Task`1" />'s AsyncState.</param>
      <param name="creationOptions">The options to use when creating the underlying <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> represent options invalid for use with a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> with the specified options.</summary>
      <param name="creationOptions">The options to use when creating the underlying <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> represent options invalid for use with a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetCanceled">
      <summary>Transitions the underlying <see cref="T:System.Threading.Tasks.Task`1" /> into the <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> state.</summary>
      <exception cref="T:System.InvalidOperationException">The underlying <see cref="T:System.Threading.Tasks.Task`1" /> is already in one of the three final states: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />, or <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />, or if the underlying <see cref="T:System.Threading.Tasks.Task`1" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Transitions the underlying <see cref="T:System.Threading.Tasks.Task`1" /> into the <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> state.</summary>
      <param name="exceptions">The collection of exceptions to bind to this <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> was disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="exceptions" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">There are one or more null elements in <paramref name="exceptions" />.</exception>
      <exception cref="T:System.InvalidOperationException">The underlying <see cref="T:System.Threading.Tasks.Task`1" /> is already in one of the three final states: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />, or <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Exception)">
      <summary>Transitions the underlying <see cref="T:System.Threading.Tasks.Task`1" /> into the <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> state.</summary>
      <param name="exception">The exception to bind to this <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> was disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="exception" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The underlying <see cref="T:System.Threading.Tasks.Task`1" /> is already in one of the three final states: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />, or <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetResult(`0)">
      <summary>Transitions the underlying <see cref="T:System.Threading.Tasks.Task`1" /> into the <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> state.</summary>
      <param name="result">The result value to bind to this <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> was disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The underlying <see cref="T:System.Threading.Tasks.Task`1" /> is already in one of the three final states: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />, or <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskCompletionSource`1.Task">
      <summary>Gets the <see cref="T:System.Threading.Tasks.Task`1" /> created by this <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</summary>
      <returns>Returns the <see cref="T:System.Threading.Tasks.Task`1" /> created by this <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled">
      <summary>Attempts to transition the underlying <see cref="T:System.Threading.Tasks.Task`1" /> into the <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> state.</summary>
      <returns>True if the operation was successful; false if the operation was unsuccessful or the object has already been disposed.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled(System.Threading.CancellationToken)">
      <summary>Attempts to transition the underlying <see cref="T:System.Threading.Tasks.Task`1" /> into the <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> state and enables a cancellation token to be stored in the canceled task. </summary>
      <returns>true if the operation is successful; otherwise, false. </returns>
      <param name="cancellationToken">A cancellation token. </param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Attempts to transition the underlying <see cref="T:System.Threading.Tasks.Task`1" /> into the <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> state.</summary>
      <returns>True if the operation was successful; otherwise, false.</returns>
      <param name="exceptions">The collection of exceptions to bind to this <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> was disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="exceptions" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">There are one or more null elements in <paramref name="exceptions" />.-or-The <paramref name="exceptions" /> collection is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Exception)">
      <summary>Attempts to transition the underlying <see cref="T:System.Threading.Tasks.Task`1" /> into the <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> state.</summary>
      <returns>True if the operation was successful; otherwise, false.</returns>
      <param name="exception">The exception to bind to this <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> was disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="exception" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetResult(`0)">
      <summary>Attempts to transition the underlying <see cref="T:System.Threading.Tasks.Task`1" /> into the <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> state.</summary>
      <returns>True if the operation was successful; otherwise, false. </returns>
      <param name="result">The result value to bind to this <see cref="T:System.Threading.Tasks.Task`1" />.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskContinuationOptions">
      <summary>Specifies the behavior for a task that is created by using the <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)" /> or <see cref="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)" /> method.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent">
      <summary>Specifies that the continuation, if it is a child task, is attached to a parent in the task hierarchy. The continuation can be a child task only if its antecedent is also a child task. By default, a child task (that is, an inner task created by an outer task) executes independently of its parent. You can use the <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> option so that the parent and child tasks are synchronized. Note that if a parent task is configured with the <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" /> option, the <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> option in the child task has no effect, and the child task will execute as a detached child task. For more information, see Attached and Detached Child Tasks. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.DenyChildAttach">
      <summary>Specifies that any child task (that is, any nested inner task created by this continuation) that is created with the <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> option and attempts to execute as an attached child task will not be able to attach to the parent task and will execute instead as a detached child task. For more information, see Attached and Detached Child Tasks. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously">
      <summary>Specifies that the continuation task should be executed synchronously. With this option specified, the continuation runs on the same thread that causes the antecedent task to transition into its final state. If the antecedent is already complete when the continuation is created, the continuation will run on the thread that creates the continuation. If the antecedent's <see cref="T:System.Threading.CancellationTokenSource" /> is disposed in a finally block (Finally in Visual Basic), a continuation with this option will run in that finally block. Only very short-running continuations should be executed synchronously. Because the task executes synchronously, there is no need to call a method such as <see cref="M:System.Threading.Tasks.Task.Wait" /> to ensure that the calling thread waits for the task to complete. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.HideScheduler">
      <summary>Specifies that tasks created by the continuation by calling methods such as <see cref="M:System.Threading.Tasks.Task.Run(System.Action)" /> or <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})" /> see the default scheduler (<see cref="P:System.Threading.Tasks.TaskScheduler.Default" />) rather than the scheduler on which this continuation is running to be the current scheduler.  </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LazyCancellation">
      <summary>In the case of continuation cancellation, prevents completion of the continuation until the antecedent has completed.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LongRunning">
      <summary>Specifies that a continuation will be a long-running, course-grained operation. It provides a hint to the <see cref="T:System.Threading.Tasks.TaskScheduler" /> that oversubscription may be warranted. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.None">
      <summary>When no continuation options are specified, specifies that default behavior should be used when executing a continuation. The continuation runs asynchronously when the antecedent task completes, regardless of the antecedent's final <see cref="P:System.Threading.Tasks.Task.Status" /> property value. It the continuation is a child task, it is created as a detached nested task.  </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnCanceled">
      <summary>Specifies that the continuation task should not be scheduled if its antecedent was canceled. An antecedent is canceled if its <see cref="P:System.Threading.Tasks.Task.Status" /> property upon completion is <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />. This option is not valid for multi-task continuations.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnFaulted">
      <summary>Specifies that the continuation task should not be scheduled if its antecedent threw an unhandled exception. An antecedent throws an unhandled exception if its <see cref="P:System.Threading.Tasks.Task.Status" /> property upon completion is <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />. This option is not valid for multi-task continuations. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnRanToCompletion">
      <summary>Specifies that the continuation task should not be scheduled if its antecedent ran to completion. An antecedent runs to completion if its <see cref="P:System.Threading.Tasks.Task.Status" /> property upon completion is <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />. This option is not valid for multi-task continuations. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled">
      <summary>Specifies that the continuation should be scheduled only if its antecedent was canceled.  An antecedent is canceled if its <see cref="P:System.Threading.Tasks.Task.Status" /> property upon completion is <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />. This option is not valid for multi-task continuations.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted">
      <summary>Specifies that the continuation task should be scheduled only if its antecedent threw an unhandled exception. An antecedent throws an unhandled exception if its <see cref="P:System.Threading.Tasks.Task.Status" /> property upon completion is <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />. The <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted" />  option guarantees that the <see cref="P:System.Threading.Tasks.Task.Exception" /> property in the antecedent is not null. You can use that property to catch the exception and see which exception caused the task to fault. If you do not access the <see cref="P:System.Threading.Tasks.Task.Exception" /> property, the exception is unhandled. Also, if you attempt to access the <see cref="P:System.Threading.Tasks.Task`1.Result" /> property of a task that has been canceled or has faulted, a new exception is thrown. This option is not valid for multi-task continuations. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnRanToCompletion">
      <summary>Specifies that the continuation should be scheduled only if its antecedent ran to completion. An antecedent runs to completion if its <see cref="P:System.Threading.Tasks.Task.Status" /> property upon completion is <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />. This option is not valid for multi-task continuations.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.PreferFairness">
      <summary>A hint to a <see cref="T:System.Threading.Tasks.TaskScheduler" /> to schedule task in the order in which they were scheduled, so that tasks scheduled sooner are more likely to run sooner, and tasks scheduled later are more likely to run later. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.RunContinuationsAsynchronously">
      <summary>Specifies that the continuation task should be run asynchronously.  This option has precedence over <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />. </summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskCreationOptions">
      <summary>Specifies flags that control optional behavior for the creation and execution of tasks. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent">
      <summary>Specifies that a task is attached to a parent in the task hierarchy. By default, a child task (that is, an inner task created by an outer task) executes independently of its parent. You can use the <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> option so that the parent and child tasks are synchronized. Note that if a parent task is configured with the <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" /> option, the <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> option in the child task has no effect, and the child task will execute as a detached child task. For more information, see Attached and Detached Child Tasks. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach">
      <summary>Specifies that any child task that attempts to execute as an attached child task (that is, it is created with the <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> option) will not be able to attach to the parent task and will execute instead as a detached child task. For more information, see Attached and Detached Child Tasks. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.HideScheduler">
      <summary>Prevents the ambient scheduler from being seen as the current scheduler in the created task. This means that operations like StartNew or ContinueWith that are performed in the created task will see <see cref="P:System.Threading.Tasks.TaskScheduler.Default" /> as the current scheduler.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.LongRunning">
      <summary>Specifies that a task will be a long-running, coarse-grained operation involving fewer, larger components than fine-grained systems. It provides a hint to the <see cref="T:System.Threading.Tasks.TaskScheduler" /> that oversubscription may be warranted. Oversubscription lets you create more threads than the available number of hardware threads.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.None">
      <summary>Specifies that the default behavior should be used.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.PreferFairness">
      <summary>A hint to a <see cref="T:System.Threading.Tasks.TaskScheduler" /> to schedule a task in as fair a manner as possible, meaning that tasks scheduled sooner will be more likely to be run sooner, and tasks scheduled later will be more likely to be run later.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.RunContinuationsAsynchronously">
      <summary>Forces continuations added to the current task to be executed asynchronously. </summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskExtensions">
      <summary>Provides a set of static (Shared in Visual Basic) methods for working with specific kinds of <see cref="T:System.Threading.Tasks.Task" /> instances.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap``1(System.Threading.Tasks.Task{System.Threading.Tasks.Task{``0}})">
      <summary>Creates a proxy <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation of a Task&lt;Task&lt;T&gt;&gt; (C#) or Task (Of Task(Of T)) (Visual Basic).</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation of the provided Task&lt;Task&lt;T&gt;&gt; (C#) or Task (Of Task(Of T)) (Visual Basic).</returns>
      <param name="task">The Task&lt;Task&lt;T&gt;&gt; (C#) or Task (Of Task(Of T)) (Visual Basic) to unwrap.</param>
      <typeparam name="TResult">The type of the task's result.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown if the <paramref name="task" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap(System.Threading.Tasks.Task{System.Threading.Tasks.Task})">
      <summary>Creates a proxy <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation of a <see cref="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)" />.</summary>
      <returns>A Task that represents the asynchronous operation of the provided System.Threading.Tasks.Task(Of Task).</returns>
      <param name="task">The Task&lt;Task&gt; (C#) or Task (Of Task) (Visual Basic) to unwrap.</param>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown if the <paramref name="task" /> argument is null.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory">
      <summary>Provides support for creating and scheduling <see cref="T:System.Threading.Tasks.Task" /> objects. </summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor">
      <summary>Initializes a <see cref="T:System.Threading.Tasks.TaskFactory" /> instance with the default configuration.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken)">
      <summary>Initializes a <see cref="T:System.Threading.Tasks.TaskFactory" /> instance with the specified configuration.</summary>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to tasks created by this <see cref="T:System.Threading.Tasks.TaskFactory" /> unless another CancellationToken is explicitly specified while calling the factory methods.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Initializes a <see cref="T:System.Threading.Tasks.TaskFactory" /> instance with the specified configuration.</summary>
      <param name="cancellationToken">The default <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to tasks created by this <see cref="T:System.Threading.Tasks.TaskFactory" /> unless another CancellationToken is explicitly specified while calling the factory methods.</param>
      <param name="creationOptions">The default <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> to use when creating tasks with this TaskFactory.</param>
      <param name="continuationOptions">The default <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> to use when creating continuation tasks with this TaskFactory.</param>
      <param name="scheduler">The default <see cref="T:System.Threading.Tasks.TaskScheduler" /> to use to schedule any Tasks created with this TaskFactory. A null value indicates that TaskScheduler.Current should be used.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />. -or-The <paramref name="continuationOptions" /> argument specifies an invalid value.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Initializes a <see cref="T:System.Threading.Tasks.TaskFactory" /> instance with the specified configuration.</summary>
      <param name="creationOptions">The default <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> to use when creating tasks with this TaskFactory.</param>
      <param name="continuationOptions">The default <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> to use when creating continuation tasks with this TaskFactory.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />. -or-The <paramref name="continuationOptions" /> argument specifies an invalid value.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Initializes a <see cref="T:System.Threading.Tasks.TaskFactory" /> instance with the specified configuration.</summary>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> to use to schedule any tasks created with this TaskFactory. A null value indicates that the current TaskScheduler should be used.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CancellationToken">
      <summary>Gets the default cancellation token for this task factory.</summary>
      <returns>The default task cancellation token for this task factory.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.ContinuationOptions">
      <summary>Gets the default task continuation options for this task factory.</summary>
      <returns>The default task continuation options for this task factory.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]})">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed. </summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationAction">The action delegate to execute when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationAction">The action delegate to execute when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token to assign to the new continuation task.</param>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationAction">The action delegate to execute when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token to assign to the new continuation task.</param>
      <param name="continuationOptions">A bitwise combination of the enumeration values that control the behavior of the new continuation task.</param>
      <param name="scheduler">The object that is used to schedule the new continuation task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationAction">The action delegate to execute when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="continuationOptions">A bitwise combination of the enumeration values that control the behavior of the new continuation task. The NotOn* and OnlyOn* members are not supported. </param>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0})">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created task.</typeparam>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token to assign to the new continuation task.</param>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created task.</typeparam>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token to assign to the new continuation task.</param>
      <param name="continuationOptions">A bitwise combination of the enumeration values that control the behavior of the new continuation task. The NotOn* and OnlyOn* members are not supported.</param>
      <param name="scheduler">The object that is used to schedule the new continuation task.</param>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created task.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="continuationOptions">A bitwise combination of the enumeration values that control the behavior of the new continuation task. The NotOn* and OnlyOn* members are not supported. </param>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created task.</typeparam>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]})">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationAction">The action delegate to execute when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationAction">The action delegate to execute when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token to assign to the new continuation task.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationAction">The action delegate to execute when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token to assign to the new continuation task.</param>
      <param name="continuationOptions">A bitwise combination of the enumeration values that control the behavior of the new continuation task. The NotOn* and OnlyOn* members are not supported.</param>
      <param name="scheduler">The object that is used to schedule the new continuation task.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationAction">The action delegate to execute when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="continuationOptions">A bitwise combination of the enumeration values that control the behavior of the new continuation task. The NotOn* and OnlyOn* members are not supported. </param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1})">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created task.</typeparam>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token to assign to the new continuation task.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created task.</typeparam>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token to assign to the new continuation task.</param>
      <param name="continuationOptions">A bitwise combination of the enumeration values that control the behavior of the new continuation task. The NotOn* and OnlyOn* members are not supported. </param>
      <param name="scheduler">The object that is used to schedule the new continuation task.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created task.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value. </exception>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation task that starts when a set of specified tasks has completed.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="continuationOptions">A bitwise combination of the enumeration values that control the behavior of the new continuation task. The NotOn* and OnlyOn* members are not supported. </param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created task.</typeparam>
      <exception cref="T:System.ObjectDisposedException">An element in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array is empty or contains a null value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task})">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationAction">The action delegate to execute when one task in the <paramref name="tasks" /> array completes.</param>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null. -or-The The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value. -or-The <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationAction">The action delegate to execute when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed. -or-<paramref name="cancellationToken" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null. -or-The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value. -or-The <paramref name="tasks" /> array is empty .</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationAction">The action delegate to execute when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">The <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> value that controls the behavior of the created continuation <see cref="T:System.Threading.Tasks.Task" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the created continuation <see cref="T:System.Threading.Tasks.Task" />.</param>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationAction" /> argument is null.-or-The exception that is thrown when the <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationAction">The action delegate to execute when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="continuationOptions">The <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> value that controls the behavior of the created continuation <see cref="T:System.Threading.Tasks.Task" />.</param>
      <exception cref="T:System.ObjectDisposedException">The exception that is thrown when one of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="continuationOptions" /> argument specifies an invalid TaskContinuationOptions value.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0})">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task`1" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The exception that is thrown when one of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task`1" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The exception that is thrown when one of the elements in the <paramref name="tasks" /> array has been disposed.-or-The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task`1" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">The <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> value that controls the behavior of the created continuation <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the created continuation <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationFunction" /> argument is null.-or-The exception that is thrown when the <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task`1" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="continuationOptions">The <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> value that controls the behavior of the created continuation <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The exception that is thrown when one of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="continuationOptions" /> argument specifies an invalid TaskContinuationOptions value.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}})">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationAction">The action delegate to execute when one task in the <paramref name="tasks" /> array completes.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The exception that is thrown when one of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationAction">The action delegate to execute when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The exception that is thrown when one of the elements in the <paramref name="tasks" /> array has been disposed.-or-The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationAction">The action delegate to execute when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">The <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> value that controls the behavior of the created continuation <see cref="T:System.Threading.Tasks.Task" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the created continuation <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationAction" /> argument is null.-or-The exception that is thrown when the <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationAction">The action delegate to execute when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="continuationOptions">The <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> value that controls the behavior of the created continuation <see cref="T:System.Threading.Tasks.Task" />.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The exception that is thrown when one of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="continuationOptions" /> argument specifies an invalid TaskContinuationOptions value.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1})">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task`1" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The exception that is thrown when one of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task`1" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The exception that is thrown when one of the elements in the <paramref name="tasks" /> array has been disposed.-or-The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task`1" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">The <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> value that controls the behavior of the created continuation <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the created continuation <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationFunction" /> argument is null.-or-The exception that is thrown when the <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation <see cref="T:System.Threading.Tasks.Task`1" /> that will be started upon the completion of any Task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="continuationOptions">The <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> value that controls the behavior of the created continuation <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <typeparam name="TResult">The type of the result that is returned by the <paramref name="continuationFunction" /> delegate and associated with the created <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The exception that is thrown when one of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="tasks" /> array is null.-or-The exception that is thrown when the <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="continuationOptions" /> argument specifies an invalid TaskContinuationOptions value.</exception>
      <exception cref="T:System.ArgumentException">The exception that is thrown when the <paramref name="tasks" /> array contains a null value.-or-The exception that is thrown when the <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CreationOptions">
      <summary>Gets the default task creation options for this task factory.</summary>
      <returns>The default task creation options for this task factory.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task" />.</param>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <typeparam name="TArg1">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task" />.</param>
      <typeparam name="TArg1">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <typeparam name="TArg1">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TArg1">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task" />.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg3">The third argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the third argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg3">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg3">The third argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task" />.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the third argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg3">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg3">The third argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the third argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg3">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg3">The third argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the third argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg3">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="beginMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult})">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that executes an end method action when a specified <see cref="T:System.IAsyncResult" /> completes.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="asyncResult">The IAsyncResult whose completion should trigger the processing of the <paramref name="endMethod" />.</param>
      <param name="endMethod">The action delegate that processes the completed <paramref name="asyncResult" />.</param>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="asyncResult" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that executes an end method action when a specified <see cref="T:System.IAsyncResult" /> completes.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="asyncResult">The IAsyncResult whose completion should trigger the processing of the <paramref name="endMethod" />.</param>
      <param name="endMethod">The action delegate that processes the completed <paramref name="asyncResult" />.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task" />.</param>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="asyncResult" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task" /> that executes an end method action when a specified <see cref="T:System.IAsyncResult" /> completes.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
      <param name="asyncResult">The IAsyncResult whose completion should trigger the processing of the <paramref name="endMethod" />.</param>
      <param name="endMethod">The action delegate that processes the completed <paramref name="asyncResult" />.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the task that executes the end method.</param>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="asyncResult" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0})">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that executes an end method function when a specified <see cref="T:System.IAsyncResult" /> completes.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="asyncResult">The IAsyncResult whose completion should trigger the processing of the <paramref name="endMethod" />.</param>
      <param name="endMethod">The function delegate that processes the completed <paramref name="asyncResult" />.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="asyncResult" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that executes an end method function when a specified <see cref="T:System.IAsyncResult" /> completes.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="asyncResult">The IAsyncResult whose completion should trigger the processing of the <paramref name="endMethod" />.</param>
      <param name="endMethod">The function delegate that processes the completed <paramref name="asyncResult" />.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="asyncResult" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a <see cref="T:System.Threading.Tasks.Task`1" /> that executes an end method function when a specified <see cref="T:System.IAsyncResult" /> completes.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="asyncResult">The IAsyncResult whose completion should trigger the processing of the <paramref name="endMethod" />.</param>
      <param name="endMethod">The function delegate that processes the completed <paramref name="asyncResult" />.</param>
      <param name="creationOptions">The TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the task that executes the end method.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="asyncResult" /> argument is null.-or-The exception that is thrown when the <paramref name="endMethod" /> argument is null.-or-The exception that is thrown when the <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.Scheduler">
      <summary>Gets the default task scheduler for this task factory.</summary>
      <returns>The default task scheduler for this task factory.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action)">
      <summary>Creates and starts a task.</summary>
      <returns>The started task.</returns>
      <param name="action">The action delegate to execute asynchronously.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">The action delegate to execute asynchronously.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new task.</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">The action delegate to execute asynchronously.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new <see cref="T:System.Threading.Tasks.Task" /></param>
      <param name="creationOptions">A TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task" /></param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the created <see cref="T:System.Threading.Tasks.Task" />.</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="action" /> argument is null.-or-The exception that is thrown when the <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">The action delegate to execute asynchronously.</param>
      <param name="creationOptions">A TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task" /></param>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task" />. </summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task" />. </returns>
      <param name="action">The action delegate to execute asynchronously. </param>
      <param name="state">An object containing data to be used by the <paramref name="action" /> delegate. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">The action delegate to execute asynchronously.</param>
      <param name="state">An object containing data to be used by the <paramref name="action" /> delegate.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new <see cref="T:System.Threading.Tasks.Task" /></param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">The action delegate to execute asynchronously.</param>
      <param name="state">An object containing data to be used by the <paramref name="action" /> delegate.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new task.</param>
      <param name="creationOptions">A TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task" /></param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the created <see cref="T:System.Threading.Tasks.Task" />.</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="action" /> argument is null.-or-The exception that is thrown when the <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">The action delegate to execute asynchronously.</param>
      <param name="state">An object containing data to be used by the <paramref name="action" /> delegate.</param>
      <param name="creationOptions">A TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task" /></param>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0})">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">A function delegate that returns the future result to be available through the <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">A function delegate that returns the future result to be available through the <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new <see cref="T:System.Threading.Tasks.Task" /></param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">A function delegate that returns the future result to be available through the <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new task.</param>
      <param name="creationOptions">A TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="function" /> argument is null.-or-The exception that is thrown when the <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">A function delegate that returns the future result to be available through the <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="creationOptions">A TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="function" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">A function delegate that returns the future result to be available through the <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">An object containing data to be used by the <paramref name="function" /> delegate.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">A function delegate that returns the future result to be available through the <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">An object containing data to be used by the <paramref name="function" /> delegate.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new <see cref="T:System.Threading.Tasks.Task" /></param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">A function delegate that returns the future result to be available through the <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">An object containing data to be used by the <paramref name="function" /> delegate.</param>
      <param name="cancellationToken">The <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> that will be assigned to the new task.</param>
      <param name="creationOptions">A TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="function" /> argument is null.-or-The exception that is thrown when the <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates and starts a <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">A function delegate that returns the future result to be available through the <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">An object containing data to be used by the <paramref name="function" /> delegate.</param>
      <param name="creationOptions">A TaskCreationOptions value that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">The type of the result available through the <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The exception that is thrown when the <paramref name="function" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. The exception that is thrown when the <paramref name="creationOptions" /> argument specifies an invalid TaskCreationOptions value. For more information, see the Remarks for <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory`1">
      <summary>Provides support for creating and scheduling <see cref="T:System.Threading.Tasks.Task`1" /> objects.</summary>
      <typeparam name="TResult">The return value of the <see cref="T:System.Threading.Tasks.Task`1" /> objects that the methods of this class create. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor">
      <summary>Initializes a <see cref="T:System.Threading.Tasks.TaskFactory`1" /> instance with the default configuration.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken)">
      <summary>Initializes a <see cref="T:System.Threading.Tasks.TaskFactory`1" /> instance with the default configuration.</summary>
      <param name="cancellationToken">The default cancellation token that will be assigned to tasks created by this <see cref="T:System.Threading.Tasks.TaskFactory" /> unless another cancellation token is explicitly specified when calling the factory methods.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Initializes a <see cref="T:System.Threading.Tasks.TaskFactory`1" /> instance with the specified configuration.</summary>
      <param name="cancellationToken">The default cancellation token that will be assigned to tasks created by this <see cref="T:System.Threading.Tasks.TaskFactory" /> unless another cancellation token is explicitly specified when calling the factory methods.</param>
      <param name="creationOptions">The default options to use when creating tasks with this <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="continuationOptions">The default options to use when creating continuation tasks with this <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="scheduler">The default scheduler to use to schedule any tasks created with this <see cref="T:System.Threading.Tasks.TaskFactory`1" />. A null value indicates that <see cref="P:System.Threading.Tasks.TaskScheduler.Current" /> should be used.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> or <paramref name="continuationOptions" /> specifies an invalid value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Initializes a <see cref="T:System.Threading.Tasks.TaskFactory`1" /> instance with the specified configuration.</summary>
      <param name="creationOptions">The default options to use when creating tasks with this <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="continuationOptions">The default options to use when creating continuation tasks with this <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> or <paramref name="continuationOptions" /> specifies an invalid value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Initializes a <see cref="T:System.Threading.Tasks.TaskFactory`1" /> instance with the specified configuration.</summary>
      <param name="scheduler">The scheduler to use to schedule any tasks created with this <see cref="T:System.Threading.Tasks.TaskFactory`1" />. A null value indicates that the current <see cref="T:System.Threading.Tasks.TaskScheduler" /> should be used.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CancellationToken">
      <summary>Gets the default cancellation token for this task factory.</summary>
      <returns>The default cancellation token for this task factory.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.ContinuationOptions">
      <summary>Gets the <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> enumeration value for this task factory.</summary>
      <returns>One of the enumeration values that specifies the default continuation options for this task factory.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0})">
      <summary>Creates a continuation task that will be started upon the completion of a set of provided tasks.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value or is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken)">
      <summary>Creates a continuation task that will be started upon the completion of a set of provided tasks.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new continuation task.</param>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-<paramref name="continuationFunction" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value or is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation task that will be started upon the completion of a set of provided Tasks.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">One of the enumeration values that controls the behavior of the created continuation task. The NotOn* or OnlyOn* values are not valid.</param>
      <param name="scheduler">The scheduler that is used to schedule the created continuation task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value or is empty.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> specifies an invalid value.</exception>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation task that will be started upon the completion of a set of provided Tasks.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="continuationOptions">One of the enumeration values that controls the behavior of the created continuation task. The NotOn* or OnlyOn* values are not valid.</param>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value or is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0})">
      <summary>Creates a continuation task that will be started upon the completion of a set of provided tasks.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value or is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken)">
      <summary>Creates a continuation task that will be started upon the completion of a set of provided tasks.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new continuation task.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value or is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation task that will be started upon the completion of a set of provided tasks.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">One of the enumeration values that controls the behavior of the created continuation task. The NotOn* or OnlyOn* values are not valid.</param>
      <param name="scheduler">The scheduler that is used to schedule the created continuation task.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value or is empty.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value.</exception>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation task that will be started upon the completion of a set of provided tasks.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when all tasks in the <paramref name="tasks" /> array have completed.</param>
      <param name="continuationOptions">One of the enumeration values that controls the behavior of the created continuation task. The NotOn* or OnlyOn* values are not valid.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value or is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0})">
      <summary>Creates a continuation task that will be started upon the completion of any task in the provided set. </summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value or is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken)">
      <summary>Creates a continuation task that will be started upon the completion of any task in the provided set.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new continuation task.</param>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value.-or-The <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation task that will be started upon the completion of any task in the provided set.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">One of the enumeration values that controls the behavior of the created continuation task. The NotOn* or OnlyOn* values are not valid.</param>
      <param name="scheduler">The task scheduler that is used to schedule the created continuation task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value.-or-The <paramref name="tasks" /> array is empty.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> value.</exception>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation task that will be started upon the completion of any task in the provided set.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="continuationOptions">One of the enumeration values that controls the behavior of the created continuation task. The NotOn* or OnlyOn* values are not valid.</param>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid enumeration value.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value.-or-The <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0})">
      <summary>Creates a continuation task that will be started upon the completion of any task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value.-or-The <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken)">
      <summary>Creates a continuation task that will be started upon the completion of any task in the provided set.</summary>
      <returns>The new continuation task.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new continuation task.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value.-or-The <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a continuation task that will be started upon the completion of any task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new continuation task.</param>
      <param name="continuationOptions">One of the enumeration values that controls the behavior of the created continuation task. The NotOn* or OnlyOn* values are not valid.</param>
      <param name="scheduler">The <see cref="T:System.Threading.Tasks.TaskScheduler" /> that is used to schedule the created continuation <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value.-or-The <paramref name="tasks" /> array is empty.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid TaskContinuationOptions value.</exception>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Creates a continuation task that will be started upon the completion of any task in the provided set.</summary>
      <returns>The new continuation <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">The array of tasks from which to continue when one task completes.</param>
      <param name="continuationFunction">The function delegate to execute asynchronously when one task in the <paramref name="tasks" /> array completes.</param>
      <param name="continuationOptions">One of the enumeration values that controls the behavior of the created continuation task. The NotOn* or OnlyOn* values are not valid.</param>
      <typeparam name="TAntecedentResult">The type of the result of the antecedent <paramref name="tasks" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">One of the elements in the <paramref name="tasks" /> array has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> array is null.-or-The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid enumeration value.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contains a null value.-or-The <paramref name="tasks" /> array is empty.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CreationOptions">
      <summary>Gets the <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> enumeration value for this task factory.</summary>
      <returns>One of the enumeration values that specifies the default creation options for this task factory.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object)">
      <summary>Creates a task that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created task that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="beginMethod" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a task that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">One of the enumeration values that controls the behavior of the created task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="beginMethod" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object)">
      <summary>Creates a task that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created task that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <typeparam name="TArg1">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="beginMethod" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a task that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created task that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">One of the enumeration values that controls the behavior of the created task.</param>
      <typeparam name="TArg1">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="beginMethod" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> parameter specifies an invalid value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object)">
      <summary>Creates a task that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created task that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="beginMethod" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a task that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created task that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">An object that controls the behavior of the created <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="beginMethod" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> parameter specifies an invalid value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object)">
      <summary>Creates a task that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created task that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg3">The third argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the third argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg3">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="beginMethod" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a task that represents a pair of begin and end methods that conform to the Asynchronous Programming Model pattern.</summary>
      <returns>The created task that represents the asynchronous operation.</returns>
      <param name="beginMethod">The delegate that begins the asynchronous operation.</param>
      <param name="endMethod">The delegate that ends the asynchronous operation.</param>
      <param name="arg1">The first argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg2">The second argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="arg3">The third argument passed to the <paramref name="beginMethod" /> delegate.</param>
      <param name="state">An object containing data to be used by the <paramref name="beginMethod" /> delegate.</param>
      <param name="creationOptions">An object that controls the behavior of the created task.</param>
      <typeparam name="TArg1">The type of the second argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg2">The type of the third argument passed to <paramref name="beginMethod" /> delegate.</typeparam>
      <typeparam name="TArg3">The type of the first argument passed to the <paramref name="beginMethod" /> delegate.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="beginMethod" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> parameter specifies an invalid value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0})">
      <summary>Creates a task that executes an end method function when a specified <see cref="T:System.IAsyncResult" /> completes.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous operation.</returns>
      <param name="asyncResult">The <see cref="T:System.IAsyncResult" /> whose completion should trigger the processing of the <paramref name="endMethod" />.</param>
      <param name="endMethod">The function delegate that processes the completed <paramref name="asyncResult" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="asyncResult" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates a task that executes an end method function when a specified <see cref="T:System.IAsyncResult" /> completes.</summary>
      <returns>A task that represents the asynchronous operation.</returns>
      <param name="asyncResult">The <see cref="T:System.IAsyncResult" /> whose completion should trigger the processing of the <paramref name="endMethod" />.</param>
      <param name="endMethod">The function delegate that processes the completed <paramref name="asyncResult" />.</param>
      <param name="creationOptions">One of the enumeration values that controls the behavior of the created task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="asyncResult" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates a task that executes an end method function when a specified <see cref="T:System.IAsyncResult" /> completes.</summary>
      <returns>The created task that represents the asynchronous operation.</returns>
      <param name="asyncResult">The <see cref="T:System.IAsyncResult" /> whose completion should trigger the processing of the <paramref name="endMethod" />.</param>
      <param name="endMethod">The function delegate that processes the completed <paramref name="asyncResult" />.</param>
      <param name="creationOptions">One of the enumeration values that controls the behavior of the created task.</param>
      <param name="scheduler">The task scheduler that is used to schedule the task that executes the end method.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="asyncResult" /> argument is null.-or-The <paramref name="endMethod" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> parameter specifies an invalid value.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.Scheduler">
      <summary>Gets the task scheduler for this task factory.</summary>
      <returns>The task scheduler for this task factory.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0})">
      <summary>Creates and starts a task.</summary>
      <returns>The started task.</returns>
      <param name="function">A function delegate that returns the future result to be available through the task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken)">
      <summary>Creates and starts a task.</summary>
      <returns>The started task.</returns>
      <param name="function">A function delegate that returns the future result to be available through the task.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new task.</param>
      <exception cref="T:System.ObjectDisposedException">The cancellation token source that created<paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates and starts a task.</summary>
      <returns>The started task.</returns>
      <param name="function">A function delegate that returns the future result to be available through the task.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new task.</param>
      <param name="creationOptions">One of the enumeration values that controls the behavior of the created task.</param>
      <param name="scheduler">The task scheduler that is used to schedule the created task.</param>
      <exception cref="T:System.ObjectDisposedException">The cancellation token source that created<paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> parameter specifies an invalid value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates and starts a task.</summary>
      <returns>The started <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">A function delegate that returns the future result to be available through the task.</param>
      <param name="creationOptions">One of the enumeration values that controls the behavior of the created task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> parameter specifies an invalid value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object)">
      <summary>Creates and starts a task.</summary>
      <returns>The started task.</returns>
      <param name="function">A function delegate that returns the future result to be available through the task.</param>
      <param name="state">An object that contains data to be used by the <paramref name="function" /> delegate.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>Creates and starts a task.</summary>
      <returns>The started task.</returns>
      <param name="function">A function delegate that returns the future result to be available through the task.</param>
      <param name="state">An object that contains data to be used by the <paramref name="function" /> delegate.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new task.</param>
      <exception cref="T:System.ObjectDisposedException">The cancellation token source that created<paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Creates and starts a task.</summary>
      <returns>The started task.</returns>
      <param name="function">A function delegate that returns the future result to be available through the task.</param>
      <param name="state">An object that contains data to be used by the <paramref name="function" /> delegate.</param>
      <param name="cancellationToken">The cancellation token that will be assigned to the new task.</param>
      <param name="creationOptions">One of the enumeration values that controls the behavior of the created task.</param>
      <param name="scheduler">The task scheduler that is used to schedule the created task.</param>
      <exception cref="T:System.ObjectDisposedException">The cancellation token source that created<paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> parameter specifies an invalid value.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Creates and starts a task.</summary>
      <returns>The started task.</returns>
      <param name="function">A function delegate that returns the future result to be available through the task.</param>
      <param name="state">An object that contains data to be used by the <paramref name="function" /> delegate.</param>
      <param name="creationOptions">One of the enumeration values that controls the behavior of the created task.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> parameter specifies an invalid value.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskScheduler">
      <summary>Represents an object that handles the low-level work of queuing tasks onto threads.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.#ctor">
      <summary>Initializes the <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Current">
      <summary>Gets the <see cref="T:System.Threading.Tasks.TaskScheduler" /> associated with the currently executing task.</summary>
      <returns>Returns the <see cref="T:System.Threading.Tasks.TaskScheduler" /> associated with the currently executing task.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Default">
      <summary>Gets the default <see cref="T:System.Threading.Tasks.TaskScheduler" /> instance that is provided by the .NET Framework.</summary>
      <returns>Returns the default <see cref="T:System.Threading.Tasks.TaskScheduler" /> instance.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext">
      <summary>Creates a <see cref="T:System.Threading.Tasks.TaskScheduler" /> associated with the current <see cref="T:System.Threading.SynchronizationContext" />.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.TaskScheduler" /> associated with the current <see cref="T:System.Threading.SynchronizationContext" />, as determined by <see cref="P:System.Threading.SynchronizationContext.Current" />.</returns>
      <exception cref="T:System.InvalidOperationException">The current SynchronizationContext may not be used as a TaskScheduler.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.GetScheduledTasks">
      <summary>For debugger support only, generates an enumerable of <see cref="T:System.Threading.Tasks.Task" /> instances currently queued to the scheduler waiting to be executed.</summary>
      <returns>An enumerable that allows a debugger to traverse the tasks currently queued to this scheduler.</returns>
      <exception cref="T:System.NotSupportedException">This scheduler is unable to generate a list of queued tasks at this time.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Id">
      <summary>Gets the unique ID for this <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <returns>Returns the unique ID for this <see cref="T:System.Threading.Tasks.TaskScheduler" />.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.MaximumConcurrencyLevel">
      <summary>Indicates the maximum concurrency level this <see cref="T:System.Threading.Tasks.TaskScheduler" /> is able to support.</summary>
      <returns>Returns an integer that represents the maximum concurrency level. The default scheduler returns <see cref="F:System.Int32.MaxValue" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.QueueTask(System.Threading.Tasks.Task)">
      <summary>Queues a <see cref="T:System.Threading.Tasks.Task" /> to the scheduler. </summary>
      <param name="task">The <see cref="T:System.Threading.Tasks.Task" /> to be queued.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="task" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryDequeue(System.Threading.Tasks.Task)">
      <summary>Attempts to dequeue a <see cref="T:System.Threading.Tasks.Task" /> that was previously queued to this scheduler.</summary>
      <returns>A Boolean denoting whether the <paramref name="task" /> argument was successfully dequeued.</returns>
      <param name="task">The <see cref="T:System.Threading.Tasks.Task" /> to be dequeued.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="task" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTask(System.Threading.Tasks.Task)">
      <summary>Attempts to execute the provided <see cref="T:System.Threading.Tasks.Task" /> on this scheduler.</summary>
      <returns>A Boolean that is true if <paramref name="task" /> was successfully executed, false if it was not. A common reason for execution failure is that the task had previously been executed or is in the process of being executed by another thread.</returns>
      <param name="task">A <see cref="T:System.Threading.Tasks.Task" /> object to be executed.</param>
      <exception cref="T:System.InvalidOperationException">The <paramref name="task" /> is not associated with this scheduler.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
      <summary>Determines whether the provided <see cref="T:System.Threading.Tasks.Task" /> can be executed synchronously in this call, and if it can, executes it.</summary>
      <returns>A Boolean value indicating whether the task was executed inline.</returns>
      <param name="task">The <see cref="T:System.Threading.Tasks.Task" /> to be executed.</param>
      <param name="taskWasPreviouslyQueued">A Boolean denoting whether or not task has previously been queued. If this parameter is True, then the task may have been previously queued (scheduled); if False, then the task is known not to have been queued, and this call is being made in order to execute the task inline without queuing it.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="task" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <paramref name="task" /> was already executed.</exception>
    </member>
    <member name="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException">
      <summary>Occurs when a faulted task's unobserved exception is about to trigger exception escalation policy, which, by default, would terminate the process.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskSchedulerException">
      <summary>Represents an exception used to communicate an invalid operation by a <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> class using the default error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="innerException">The exception that is the cause of the current exception.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture. </param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception. </param>
    </member>
    <member name="T:System.Threading.Tasks.TaskStatus">
      <summary>Represents the current stage in the lifecycle of a <see cref="T:System.Threading.Tasks.Task" />.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Canceled">
      <summary>The task acknowledged cancellation by throwing an OperationCanceledException with its own CancellationToken while the token was in signaled state, or the task's CancellationToken was already signaled before the task started executing. For more information, see Task Cancellation.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Created">
      <summary>The task has been initialized but has not yet been scheduled.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Faulted">
      <summary>The task completed due to an unhandled exception.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.RanToCompletion">
      <summary>The task completed execution successfully.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Running">
      <summary>The task is running but has not yet completed.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForActivation">
      <summary>The task is waiting to be activated and scheduled internally by the .NET Framework infrastructure.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForChildrenToComplete">
      <summary>The task has finished executing and is implicitly waiting for attached child tasks to complete.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingToRun">
      <summary>The task has been scheduled for execution but has not yet begun executing.</summary>
    </member>
    <member name="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs">
      <summary>Provides data for the event that is raised when a faulted <see cref="T:System.Threading.Tasks.Task" />'s exception goes unobserved.</summary>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.#ctor(System.AggregateException)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs" /> class with the unobserved exception.</summary>
      <param name="exception">The Exception that has gone unobserved.</param>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception">
      <summary>The Exception that went unobserved.</summary>
      <returns>The Exception that went unobserved.</returns>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Observed">
      <summary>Gets whether this exception has been marked as "observed."</summary>
      <returns>true if this exception has been marked as "observed"; otherwise false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.SetObserved">
      <summary>Marks the <see cref="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception" /> as "observed," thus preventing it from triggering exception escalation policy which, by default, terminates the process.</summary>
    </member>
  </members>
</doc>