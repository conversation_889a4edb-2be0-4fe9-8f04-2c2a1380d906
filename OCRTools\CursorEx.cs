using OCRTools.Properties;
using System.Windows.Forms;

namespace OCRTools
{
    public static class CursorEx
    {
        public static Cursor Arrow = ImageHelp.SetCursor(Resources.normal);

        public static Cursor Cross = ImageHelp.SetCursor(Resources.Crosshair);

        public static Cursor EW = Cursors.SizeWE; // ImageHelp.SetCursor(Resources.Cursor_ew);

        public static Cursor Move = ImageHelp.SetCursor(Resources.move);

        public static Cursor NESW = Cursors.SizeNESW; // ImageHelp.SetCursor(Resources.Cursor_nesw);

        public static Cursor NS = Cursors.SizeNS; // ImageHelp.SetCursor(Resources.Cursor_ns);

        public static Cursor NWSE = Cursors.SizeNWSE; // ImageHelp.SetCursor(Resources.Cursor_nwse);

        public static Cursor None = ImageHelp.SetCursor(Resources.no);

        public static Cursor OpenHand = ImageHelp.SetCursor(Resources.openhand);

        public static Cursor CloseHand = ImageHelp.SetCursor(Resources.closedhand);
    }
}