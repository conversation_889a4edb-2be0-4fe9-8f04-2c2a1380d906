using System.Drawing;
using System.Drawing.Drawing2D;

namespace OCRTools
{
    internal class DrawGaus : DrawRectangle
    {
        public DrawGaus()
            : this(0, 0, 1, 1)
        {
        }

        public DrawGaus(int x, int y, int width, int height)
        {
            Rectangle = new Rectangle(x, y, width, height);
            Initialize();
        }

        public override DrawToolType NoteType => DrawToolType.Gaus;

        public override DrawObject Clone()
        {
            var drawGaus = new DrawGaus
            {
                Rectangle = Rectangle,
                BackgroundImageEx = BackgroundImageEx
            };
            FillDrawObjectFields(drawGaus);
            return drawGaus;
        }

        public override bool PointInObject(Point point)
        {
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            if (normalizedRectangle.Contains(point) && Selected) return true;
            if (normalizedRectangle.Contains(point) && IsAnyModifierPressed(KeyModifiers.Ctrl)) return true;
            return false;
        }

        public Bitmap GetRect(Image pic, Rectangle rect)
        {
            var destRect = new Rectangle(0, 0, rect.Width, rect.Height);
            var bitmap = new Bitmap(destRect.Width, destRect.Height);
            var graphics = Graphics.FromImage(bitmap);
            graphics.InterpolationMode = InterpolationMode.NearestNeighbor;
            graphics.PixelOffsetMode = PixelOffsetMode.Half;
            graphics.Clear(Color.FromArgb(0, 0, 0, 0));
            graphics.DrawImage(pic, destRect, rect, GraphicsUnit.Pixel);
            graphics.Dispose();
            return bitmap;
        }

        public override void Draw(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighQuality;
            var normalizedRectangle = GetNormalizedRectangle(Rectangle);
            if (!normalizedRectangle.IsLimt()) return;
            if (LastRect != normalizedRectangle)
            {
                var rect = GetRect(BackgroundImageEx, normalizedRectangle);
                if (IsCache)
                {
                    CurrentImage = rect;
                    LastRect = normalizedRectangle;
                    rect.GausBlur();
                    g.DrawImage(rect, normalizedRectangle);
                }
                else
                {
                    using (Brush brush = new SolidBrush(Color.FromArgb(50, Color.Black)))
                    {
                        g.FillRectangle(brush, normalizedRectangle);
                    }
                }
            }
            else
            {
                var rect = CurrentImage;
                if (rect.Width > 6 && rect.Height > 6) g.DrawImage(rect, normalizedRectangle);
            }
        }
    }
}