using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.Runtime.Serialization;
using System.Windows.Forms;

namespace OCRTools
{
    public abstract class DrawObject
    {
        private bool _selected;

        protected int TrackerSize = 10;

        protected DrawObject()
        {
            Id = GetHashCode();
        }

        public bool Selected
        {
            get => _selected;
            set => IsSelected = _selected = value;
        }

        public bool IsSelected { get; set; }

        public Color Color { get; set; }

        public Bitmap CurrentImage { get; set; }

        public Rectangle CurrentRect { get; set; }

        public Rectangle LastRect { get; set; }

        public virtual string LastText { get; set; }

        public virtual bool IsChange { get; set; }

        public virtual string Text { get; set; }

        public virtual Font Font { get; set; }

        public virtual bool IsCache { get; set; }

        public virtual bool IsChangeColor { get; set; }

        public virtual Rectangle Rectangle { get; set; }

        public virtual Rectangle RectangleT => Rectangle.GetNormalizedRectangle();

        public virtual Rectangle AddRectangle { get; set; }

        public virtual Point StartPoint { get; set; }

        public virtual Point EndPoint { get; set; }

        public virtual DrawToolType NoteType { get; set; }

        public int PenWidth { get; set; }

        public virtual int HandleCount => 0;

        public int Id { get; set; }

        public static Color LastUsedColor { get; set; } = Color.Black;

        public static int LastUsedPenWidth { get; set; } = 1;

        public static bool LastIsDot { get; set; }

        public static bool LastIsOutline { get; set; }

        public static float LastFontSize { get; set; } = 14f;


        public static string LastFontstyle { get; set; } = "微软雅黑";


        public float FontSize { get; set; }

        public string Fontstyle { get; set; }

        public bool IsDot { get; set; }

        public static bool LastIsArrowBoth { get; set; }

        public bool IsArrowBoth { get; set; }

        public bool IsMove { get; set; }

        public bool IsOutline { get; set; }

        public Bitmap BackgroundImageEx { get; set; }

        public virtual bool IsAnyModifierPressed(KeyModifiers modifiers)
        {
            var flag = false;
            if ((modifiers & KeyModifiers.Shift) != 0) flag |= (Control.ModifierKeys & Keys.Shift) != 0;
            if ((modifiers & KeyModifiers.Alt) != 0) flag |= (Control.ModifierKeys & Keys.Alt) != 0;
            if ((modifiers & KeyModifiers.Ctrl) != 0) flag |= (Control.ModifierKeys & Keys.Control) != 0;
            return flag;
        }

        public static Point SnapPositionToDegree(Point pos, Point pos2, float degree, float startDegree)
        {
            double num = MathHelpers.LookAtRadian(pos, pos2);
            var num2 = MathHelpers.DegreeToRadian(startDegree);
            var num3 = MathHelpers.DegreeToRadian(degree);
            var radian = (float)Math.Round((num + num2) / num3) * num3 - num2;
            var length = MathHelpers.Distance(pos, pos2);
            return (Point)(pos + MathHelpers.RadianToVector(radian, length));
        }

        public abstract DrawObject Clone();

        public virtual void Draw(Graphics g)
        {
        }

        public virtual Point GetHandle(int handleNumber)
        {
            return new Point(0, 0);
        }

        public virtual Rectangle GetHandleRectangle(int handleNumber)
        {
            var num = 4.DpiValue();
            var handle = GetHandle(handleNumber);
            return new Rectangle(handle.X - num, handle.Y - num, num * 2, num * 2);
        }

        public virtual Rectangle GetHandleRectangle2(int handleNumber)
        {
            var num = 3.DpiValue();
            var handle = GetHandle(handleNumber);
            return new Rectangle(handle.X - num, handle.Y - num, num * 2, num * 2);
        }

        public virtual void DrawTracker(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighQuality;
            if (!Selected) return;
            var x = GetHandle(1).X;
            var y = GetHandle(1).Y;
            if (GetHandleRectangle(3).X - GetHandleRectangle(1).X < 0) x = GetHandle(3).X;
            if (GetHandleRectangle(5).Y - GetHandleRectangle(1).Y < 0) y = GetHandle(5).Y;
            var value = GetHandleRectangle(3).X - GetHandleRectangle(1).X;
            var value2 = GetHandleRectangle(5).Y - GetHandleRectangle(1).Y;
            value = Math.Abs(value);
            value2 = Math.Abs(value2);
            if (value < 6 && value2 < 6) return;
            if (NoteType == DrawToolType.Polygon)
            {
                Brush brush = new SolidBrush(Color.White);
                var pen = new Pen(Color.Red, 2f);
                g.FillEllipse(brush, GetHandleRectangle(1));
                g.DrawEllipse(pen, GetHandleRectangle(1));
                g.FillEllipse(brush, GetHandleRectangle(HandleCount));
                g.DrawEllipse(pen, GetHandleRectangle(HandleCount));
                pen.Dispose();
                brush.Dispose();
            }
            else
            {
                if (NoteType == DrawToolType.Catch || NoteType == DrawToolType.MultiCatch) return;
                if (NoteType == DrawToolType.Step)
                {
                    g.SmoothingMode = SmoothingMode.HighSpeed;
                    var pen2 = new Pen(Color.Gray, 1f)
                    {
                        DashStyle = DashStyle.Custom,
                        DashPattern = new[]
                        {
                            5f,
                            5f
                        }
                    };
                    var rect = new Rectangle(x - 2, y - 2, value + 4, value2 + 4);
                    g.DrawRectangle(pen2, rect);
                    pen2.Dispose();
                }
                else if (NoteType == DrawToolType.Gaus || NoteType == DrawToolType.Mosaic ||
                         NoteType == DrawToolType.Text || NoteType == DrawToolType.Highlight ||
                         NoteType == DrawToolType.RectangleFill)
                {
                    var pen3 = new Pen(Color.Gray, 1f)
                    {
                        DashStyle = DashStyle.Custom,
                        DashPattern = new[]
                        {
                            5f,
                            5f
                        }
                    };
                    var rect2 = new Rectangle(x, y, value, value2);
                    g.DrawRectangle(pen3, rect2);
                    pen3.Dispose();
                    if (!StaticValue.IsShowText)
                        for (var i = 1; i <= HandleCount; i++)
                        {
                            Brush brush2 = new SolidBrush(Color.FromArgb(199, 199, 199));
                            var pen4 = new Pen(Color.Gray, 1f);
                            g.FillRectangle(brush2, GetHandleRectangle2(i));
                            g.DrawRectangle(pen4, GetHandleRectangle2(i));
                            pen4.Dispose();
                            brush2.Dispose();
                        }
                }
                else
                {
                    if (NoteType == DrawToolType.Ellipse)
                    {
                        var pen5 = new Pen(Color.Red, 1f);
                        var rect3 = new Rectangle(x, y, value, value2);
                        g.DrawRectangle(pen5, rect3);
                        pen5.Dispose();
                    }

                    for (var j = 1; j <= HandleCount; j++)
                    {
                        Brush brush3 = new SolidBrush(Color.White);
                        var pen6 = new Pen(Color.Red, 2f);
                        g.FillEllipse(brush3, GetHandleRectangle(j));
                        g.DrawEllipse(pen6, GetHandleRectangle(j));
                        pen6.Dispose();
                        brush3.Dispose();
                    }
                }
            }
        }

        public abstract Rectangle GetBoundingBox();

        public virtual int HitTest(Point point)
        {
            return -1;
        }

        public virtual int HitCatch(Point point)
        {
            return -1;
        }

        public virtual bool PointInObject(Point point)
        {
            return false;
        }

        public virtual Cursor GetHandleCursor(int handleNumber)
        {
            return CursorEx.Cross;
        }

        public virtual bool IntersectsWith(Rectangle rectangle)
        {
            return false;
        }

        public virtual void Move(int deltaX, int deltaY)
        {
        }

        public static int SignMultiplier(int value)
        {
            if (value < 0) return -1;
            return 1;
        }

        public static Rectangle MakeSquare(Rectangle rectangle)
        {
            var num = Math.Min(Math.Abs(rectangle.Width), Math.Abs(rectangle.Height));
            rectangle.Height = num * SignMultiplier(rectangle.Height);
            rectangle.Width = num * SignMultiplier(rectangle.Width);
            return rectangle;
        }

        public virtual void MoveHandleTo(Point point, int handleNumber)
        {
        }

        public virtual void MoveHandleTo(Point point, int handleNumber, bool shiftPressed)
        {
        }

        public virtual void Dump()
        {
        }

        public virtual void Normalize()
        {
        }

        public virtual void SaveToStream(SerializationInfo info, int orderNumber)
        {
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Color", orderNumber), Color.ToArgb());
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "PenWidth", orderNumber), PenWidth);
        }

        public virtual void LoadFromStream(SerializationInfo info, int orderNumber)
        {
            var @int = info.GetInt32(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Color", orderNumber));
            Color = Color.FromArgb(@int);
            PenWidth = info.GetInt32(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "PenWidth", orderNumber));
            Id = GetHashCode();
        }

        protected void Initialize()
        {
            Color = LastUsedColor;
            PenWidth = LastUsedPenWidth;
            IsDot = LastIsDot;
            IsOutline = LastIsOutline;
            IsArrowBoth = LastIsArrowBoth;
            Fontstyle = LastFontstyle;
            FontSize = LastFontSize;
        }

        protected void FillDrawObjectFields(DrawObject drawObject)
        {
            drawObject._selected = _selected;
            drawObject.Color = Color;
            drawObject.PenWidth = PenWidth;
            drawObject.IsDot = IsDot;
            drawObject.Id = Id;
        }
    }
}