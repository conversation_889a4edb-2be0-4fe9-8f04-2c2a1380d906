using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.Runtime.Serialization;
using System.Windows.Forms;

namespace OCRTools
{
    internal class DrawRectangle : DrawObject
    {
        public Rectangle rectangle;

        public DrawRectangle()
            : this(0, 0, 1, 1)
        {
        }

        public DrawRectangle(int x, int y, int width, int height)
        {
            rectangle.X = x;
            rectangle.Y = y;
            rectangle.Width = width;
            rectangle.Height = height;
            Initialize();
        }

        public override Rectangle Rectangle
        {
            get => rectangle;
            set => rectangle = value;
        }

        public override int HandleCount => 8;

        public override DrawToolType NoteType => DrawToolType.Rectangle;

        public override Rectangle GetBoundingBox()
        {
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            if (IsSelected)
            {
                var num = 15;
                normalizedRectangle.Inflate(num, num);
            }

            return normalizedRectangle;
        }

        public override DrawObject Clone()
        {
            var drawRectangle = new DrawRectangle
            {
                rectangle = rectangle
            };
            FillDrawObjectFields(drawRectangle);
            return drawRectangle;
        }

        public override void Draw(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighSpeed;
            var normalizedRectangle = GetNormalizedRectangle(Rectangle);
            if (normalizedRectangle.IsLimt())
            {
                var pen = new Pen(Color.White)
                {
                    Color = Color,
                    Width = PenWidth.DpiValue()
                };
                if (IsDot)
                    pen.DashPattern = new[]
                    {
                        3f,
                        3f
                    };
                g.DrawRectangle(pen, normalizedRectangle);
                pen.Dispose();
            }
        }

        protected void SetRectangle(int x, int y, int width, int height)
        {
            rectangle.X = x;
            rectangle.Y = y;
            rectangle.Width = width;
            rectangle.Height = height;
        }

        public override Point GetHandle(int handleNumber)
        {
            var num = rectangle.X + rectangle.Width / 2;
            var num2 = rectangle.Y + rectangle.Height / 2;
            var x = rectangle.X;
            var y = rectangle.Y;
            switch (handleNumber)
            {
                case 1:
                    x = rectangle.X;
                    y = rectangle.Y;
                    break;
                case 2:
                    x = num;
                    y = rectangle.Y;
                    break;
                case 3:
                    x = rectangle.Right;
                    y = rectangle.Y;
                    break;
                case 4:
                    x = rectangle.Right;
                    y = num2;
                    break;
                case 5:
                    x = rectangle.Right;
                    y = rectangle.Bottom;
                    break;
                case 6:
                    x = num;
                    y = rectangle.Bottom;
                    break;
                case 7:
                    x = rectangle.X;
                    y = rectangle.Bottom;
                    break;
                case 8:
                    x = rectangle.X;
                    y = num2;
                    break;
            }

            return new Point(x, y);
        }

        public override int HitTest(Point point)
        {
            if (Selected)
                for (var i = 1; i <= HandleCount; i++)
                    if (GetHandleRectangle(i).Contains(point))
                        return i;
            if (PointInObject(point)) return 0;
            return -1;
        }

        public override bool PointInObject(Point point)
        {
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            var rectangle = new Rectangle(normalizedRectangle.X - 5, normalizedRectangle.Y - 5,
                normalizedRectangle.Width + 10, normalizedRectangle.Height + 10);
            var rectangle2 = new Rectangle(normalizedRectangle.X + 5, normalizedRectangle.Y + 5,
                normalizedRectangle.Width - 10, normalizedRectangle.Height - 10);
            if (rectangle.Contains(point) && !rectangle2.Contains(point)) return true;
            return false;
        }

        public override void MoveHandleTo(Point point, int handleNumber, bool shiftPressed)
        {
            var num = Rectangle.Left;
            var num2 = Rectangle.Top;
            var num3 = Rectangle.Right;
            var num4 = Rectangle.Bottom;
            switch (handleNumber)
            {
                case 1:
                    num = point.X;
                    num2 = point.Y;
                    break;
                case 2:
                    num2 = point.Y;
                    break;
                case 3:
                    num3 = point.X;
                    num2 = point.Y;
                    break;
                case 4:
                    num3 = point.X;
                    break;
                case 5:
                    num3 = point.X;
                    num4 = point.Y;
                    break;
                case 6:
                    num4 = point.Y;
                    break;
                case 7:
                    num = point.X;
                    num4 = point.Y;
                    break;
                case 8:
                    num = point.X;
                    break;
            }

            rectangle.X = num;
            rectangle.Y = num2;
            rectangle.Width = num3 - num;
            rectangle.Height = num4 - num2;
            if (shiftPressed)
                switch (handleNumber)
                {
                    case 1:
                        rectangle = MakeSquare(rectangle);
                        rectangle.X = num3 - rectangle.Width;
                        rectangle.Y = num4 - rectangle.Height;
                        break;
                    case 5:
                        rectangle = MakeSquare(rectangle);
                        break;
                    case 7:
                        rectangle = MakeSquare(rectangle);
                        rectangle.X = num3 - rectangle.Width;
                        rectangle.Y = num2;
                        break;
                    case 3:
                        rectangle = MakeSquare(rectangle);
                        rectangle.X = num;
                        rectangle.Y = num4 - rectangle.Height;
                        break;
                    case 2:
                    case 6:
                        rectangle.Width = SignMultiplier(rectangle.Width) * Math.Abs(rectangle.Height);
                        break;
                    case 4:
                    case 8:
                        rectangle.Height = SignMultiplier(rectangle.Height) * Math.Abs(rectangle.Width);
                        break;
                }
        }

        public override Cursor GetHandleCursor(int handleNumber)
        {
            switch (handleNumber)
            {
                case 1:
                    return CursorEx.NWSE;
                case 2:
                    return CursorEx.NS;
                case 3:
                    return CursorEx.NESW;
                case 4:
                    return CursorEx.EW;
                case 5:
                    return CursorEx.NWSE;
                case 6:
                    return CursorEx.NS;
                case 7:
                    return CursorEx.NESW;
                case 8:
                    return CursorEx.EW;
                default:
                    return CursorEx.Cross;
            }
        }

        public override void MoveHandleTo(Point point, int handleNumber)
        {
            var num = Rectangle.Left;
            var num2 = Rectangle.Top;
            var num3 = Rectangle.Right;
            var num4 = Rectangle.Bottom;
            switch (handleNumber)
            {
                case 1:
                    num = point.X;
                    num2 = point.Y;
                    break;
                case 2:
                    num2 = point.Y;
                    break;
                case 3:
                    num3 = point.X;
                    num2 = point.Y;
                    break;
                case 4:
                    num3 = point.X;
                    break;
                case 5:
                    num3 = point.X;
                    num4 = point.Y;
                    break;
                case 6:
                    num4 = point.Y;
                    break;
                case 7:
                    num = point.X;
                    num4 = point.Y;
                    break;
                case 8:
                    num = point.X;
                    break;
            }

            SetRectangle(num, num2, num3 - num, num4 - num2);
        }

        public override bool IntersectsWith(Rectangle rectangle)
        {
            return Rectangle.IntersectsWith(rectangle);
        }

        public override void Move(int deltaX, int deltaY)
        {
            rectangle.X += deltaX;
            rectangle.Y += deltaY;
        }

        public override void Normalize()
        {
            rectangle = GetNormalizedRectangle(rectangle);
        }

        public override void SaveToStream(SerializationInfo info, int orderNumber)
        {
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Rect", orderNumber), rectangle);
            base.SaveToStream(info, orderNumber);
        }

        public override void LoadFromStream(SerializationInfo info, int orderNumber)
        {
            rectangle = (Rectangle)info.GetValue(
                string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Rect", orderNumber), typeof(Rectangle));
            base.LoadFromStream(info, orderNumber);
        }

        public static Rectangle GetNormalizedRectangle(int x1, int y1, int x2, int y2)
        {
            if (x2 < x1)
            {
                var num = x2;
                x2 = x1;
                x1 = num;
            }

            if (y2 < y1)
            {
                var num2 = y2;
                y2 = y1;
                y1 = num2;
            }

            return new Rectangle(x1, y1, x2 - x1, y2 - y1);
        }

        public static Rectangle GetNormalizedRectangle(Rectangle r)
        {
            return GetNormalizedRectangle(r.X, r.Y, r.X + r.Width, r.Y + r.Height);
        }
    }
}