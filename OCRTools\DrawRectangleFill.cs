using System.Drawing;
using System.Drawing.Drawing2D;

namespace OCRTools
{
    internal class DrawRectangleFill : DrawRectangle
    {
        public DrawRectangleFill()
            : this(0, 0, 1, 1)
        {
        }

        public DrawRectangleFill(int x, int y, int width, int height)
        {
            Rectangle = new Rectangle(x, y, width, height);
            Initialize();
        }

        public override DrawToolType NoteType => DrawToolType.RectangleFill;

        public override DrawObject Clone()
        {
            var drawRectangleFill = new DrawRectangleFill
            {
                Rectangle = Rectangle
            };
            FillDrawObjectFields(drawRectangleFill);
            return drawRectangleFill;
        }

        public override bool PointInObject(Point point)
        {
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            if (normalizedRectangle.Contains(point) && Selected) return true;
            if (normalizedRectangle.Contains(point) && IsAnyModifierPressed(KeyModifiers.Ctrl)) return true;
            return false;
        }

        public override void Draw(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighQuality;
            var normalizedRectangle = GetNormalizedRectangle(Rectangle);
            if (normalizedRectangle.IsLimt())
            {
                if (Color == CustomColor.gray) Color = Color.White;
                using (Brush brush = new SolidBrush(Color))
                {
                    g.FillRectangle(brush, normalizedRectangle);
                }
            }
        }
    }
}