using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class DskinPaste : Form
    {
        private bool leftFlag;

        private Point mouseOff;

        private IContainer components = null;

        public DskinPaste()
        {
            InitializeComponent();
            base.LostFocus += delegate
            {
                leftFlag = false;
            };
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                mouseOff = new Point(-e.X, -e.Y);
                leftFlag = true;
            }
            base.OnMouseDown(e);
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            if (leftFlag)
            {
                Point mousePosition = Control.MousePosition;
                mousePosition.Offset(mouseOff.X, mouseOff.Y);
                base.Location = mousePosition;
            }
            base.OnMouseMove(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            if (leftFlag)
            {
                leftFlag = false;
            }
            base.OnMouseUp(e);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            SuspendLayout();
            base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
            base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            base.ClientSize = new System.Drawing.Size(234, 159);
            base.Name = "DskinPaste";
            base.ShowIcon = false;
            base.ShowInTaskbar = false;
            base.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            Text = "";
            ResumeLayout(false);
        }
    }
}
