using System;
using System.Drawing;

namespace OCRTools
{
    public static class Effect
    {
        private static void BoxBlurHorizontal(UnsafeBitmap unsafeBitmap, int range)
        {
            var width = unsafeBitmap.Width;
            var height = unsafeBitmap.Height;
            var num = range / 2;
            var array = new ColorBgra[width];
            for (var i = 0; i < height; i++)
            {
                var num2 = 0;
                var num3 = 0;
                var num4 = 0;
                var num5 = 0;
                var num6 = 0;
                for (var j = -num; j < width; j++)
                {
                    var num7 = j - num - 1;
                    if (num7 >= 0)
                    {
                        var pixel = unsafeBitmap.GetPixel(num7, i);
                        if (pixel.Bgra != 0)
                        {
                            num3 -= pixel.Red;
                            num4 -= pixel.Green;
                            num5 -= pixel.Blue;
                            num6 -= pixel.Alpha;
                        }

                        num2--;
                    }

                    var num8 = j + num;
                    if (num8 < width)
                    {
                        var pixel2 = unsafeBitmap.GetPixel(num8, i);
                        if (pixel2.Bgra != 0)
                        {
                            num3 += pixel2.Red;
                            num4 += pixel2.Green;
                            num5 += pixel2.Blue;
                            num6 += pixel2.Alpha;
                        }

                        num2++;
                    }

                    if (j >= 0)
                        array[j] = new ColorBgra((byte)(num5 / num2), (byte)(num4 / num2), (byte)(num3 / num2),
                            (byte)(num6 / num2));
                }

                for (var k = 0; k < width; k++) unsafeBitmap.SetPixel(k, i, array[k]);
            }
        }

        private static void BoxBlurVertical(UnsafeBitmap unsafeBitmap, int range)
        {
            var width = unsafeBitmap.Width;
            var height = unsafeBitmap.Height;
            var num = range / 2;
            var array = new ColorBgra[height];
            for (var i = 0; i < width; i++)
            {
                var num2 = 0;
                var num3 = 0;
                var num4 = 0;
                var num5 = 0;
                var num6 = 0;
                for (var j = -num; j < height; j++)
                {
                    var num7 = j - num - 1;
                    if (num7 >= 0)
                    {
                        var pixel = unsafeBitmap.GetPixel(i, num7);
                        if (pixel.Bgra != 0)
                        {
                            num3 -= pixel.Red;
                            num4 -= pixel.Green;
                            num5 -= pixel.Blue;
                            num6 -= pixel.Alpha;
                        }

                        num2--;
                    }

                    var num8 = j + num;
                    if (num8 < height)
                    {
                        var pixel2 = unsafeBitmap.GetPixel(i, num8);
                        if (pixel2.Bgra != 0)
                        {
                            num3 += pixel2.Red;
                            num4 += pixel2.Green;
                            num5 += pixel2.Blue;
                            num6 += pixel2.Alpha;
                        }

                        num2++;
                    }

                    if (j >= 0)
                        array[j] = new ColorBgra((byte)(num5 / num2), (byte)(num4 / num2), (byte)(num3 / num2),
                            (byte)(num6 / num2));
                }

                for (var k = 0; k < height; k++) unsafeBitmap.SetPixel(i, k, array[k]);
            }
        }

        public static void GausBlur(this Bitmap bmp, int range = 15)
        {
            if (range > 1)
            {
                if (range.IsEvenNumber()) range++;
                using (var unsafeBitmap = new UnsafeBitmap(bmp, true))
                {
                    BoxBlurHorizontal(unsafeBitmap, range);
                    BoxBlurVertical(unsafeBitmap, range);
                    BoxBlurHorizontal(unsafeBitmap, range);
                    BoxBlurVertical(unsafeBitmap, range);
                }
            }
        }

        public static void HighlightImage(this Bitmap bmp, Color highlightColor)
        {
            var rectangle = new Rectangle(0, 0, bmp.Width, bmp.Height);
            using (var unsafeBitmap = new UnsafeBitmap(bmp, true))
            {
                for (var i = rectangle.Y; i < rectangle.Height; i++)
                    for (var j = rectangle.X; j < rectangle.Width; j++)
                    {
                        var pixel = unsafeBitmap.GetPixel(j, i);
                        pixel.Red = Math.Min(pixel.Red, highlightColor.R);
                        pixel.Green = Math.Min(pixel.Green, highlightColor.G);
                        pixel.Blue = Math.Min(pixel.Blue, highlightColor.B);
                        pixel.Alpha = 80;
                        unsafeBitmap.SetPixel(j, i, pixel);
                    }
            }
        }

        public static void Pixelate(this Bitmap bmp, int pixelSize = 15)
        {
            if (pixelSize > 1)
                using (var unsafeBitmap = new UnsafeBitmap(bmp, true))
                {
                    for (var i = 0; i < unsafeBitmap.Height; i += pixelSize)
                        for (var j = 0; j < unsafeBitmap.Width; j += pixelSize)
                        {
                            var num = Math.Min(j + pixelSize, unsafeBitmap.Width);
                            var num2 = Math.Min(i + pixelSize, unsafeBitmap.Height);
                            var num3 = (num - j) * (num2 - i);
                            var num4 = 0;
                            var num5 = 0;
                            var num6 = 0;
                            var num7 = 0;
                            for (var k = i; k < num2; k++)
                                for (var l = j; l < num; l++)
                                {
                                    var pixel = unsafeBitmap.GetPixel(l, k);
                                    num4 += pixel.Red;
                                    num5 += pixel.Green;
                                    num6 += pixel.Blue;
                                    num7 += pixel.Alpha;
                                }

                            var color = new ColorBgra((byte)(num6 / num3), (byte)(num5 / num3), (byte)(num4 / num3),
                                (byte)(num7 / num3));
                            for (var m = i; m < num2; m++)
                                for (var n = j; n < num; n++)
                                    unsafeBitmap.SetPixel(n, m, color);
                        }
                }
        }
    }
}