using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    internal class FmEdit : Form
    {
        private DrawArea FmEditDrawArea;

        private IContainer components = null;

        private PictureBox Rectangle1;

        private PictureBox Rectangle7;

        private PictureBox Rectangle6;

        private PictureBox Rectangle5;

        private PictureBox Rectangle4;

        private PictureBox Rectangle3;

        private PictureBox Rectangle2;

        private ListButton fontstyleButton;

        private ListButton fontsizeButton;

        private PictureBox Outline;

        private void Rectangle_Selected(PictureBox sender)
        {
            InitImageEditRectangleTool();
            sender.Image = ImageHelp.EditRectangleTool(DrawObject.LastUsedColor, ischeck: true);
        }

        public FmEdit(DrawArea DrawArea)
        {
            base.Opacity = 0.0;
            FmEditDrawArea = DrawArea;
            InitializeComponent();
            DrawArea.MouseDown += delegate
            {
                DrawObject selected = DrawArea.GetSelected(DrawArea);
                Color[] array = new Color[7]
                {
                    CustomColor.red,
                    CustomColor.yellow,
                    CustomColor.blue,
                    CustomColor.Green,
                    CustomColor.yellow,
                    CustomColor.black,
                    CustomColor.gray
                };
                if (selected != null)
                {
                    DrawObject.LastUsedColor = selected.Color;
                    DrawObject.LastFontstyle = selected.Fontstyle;
                    DrawObject.LastFontSize = selected.FontSize;
                    DrawObject.LastIsOutline = selected.IsOutline;
                    Outline.Image = ImageHelp.CreateTextDotTool(DrawObject.LastIsOutline);
                    fontstyleButton.Text = DrawObject.LastFontstyle;
                    fontsizeButton.Text = DrawObject.LastFontSize.ToString() + "pt";
                    if (selected.Color == array[0])
                    {
                        Rectangle_Selected(Rectangle1);
                    }
                    else if (selected.Color == array[1])
                    {
                        Rectangle_Selected(Rectangle2);
                    }
                    else if (selected.Color == array[2])
                    {
                        Rectangle_Selected(Rectangle3);
                    }
                    else if (selected.Color == array[3])
                    {
                        Rectangle_Selected(Rectangle4);
                    }
                    else if (selected.Color == array[4])
                    {
                        Rectangle_Selected(Rectangle5);
                    }
                    else if (selected.Color == array[5])
                    {
                        Rectangle_Selected(Rectangle6);
                    }
                    else if (selected.Color == array[6])
                    {
                        Rectangle_Selected(Rectangle7);
                    }
                }
            };
            fontstyleButton.TextChanged += delegate
            {
                switch (fontstyleButton.Text)
                {
                    case "宋体":
                        DrawObject.LastFontstyle = "宋体";
                        break;
                    case "黑体":
                        DrawObject.LastFontstyle = "黑体";
                        break;
                    case "楷体":
                        DrawObject.LastFontstyle = "楷体";
                        break;
                    case "幼圆":
                        DrawObject.LastFontstyle = "幼圆";
                        break;
                    case "微软雅黑":
                        DrawObject.LastFontstyle = "微软雅黑";
                        break;
                }
                RefreshDraw();
            };
            fontsizeButton.TextChanged += delegate
            {
                switch (fontsizeButton.Text)
                {
                    case "12pt":
                        DrawObject.LastFontSize = 12f;
                        break;
                    case "14pt":
                        DrawObject.LastFontSize = 14f;
                        break;
                    case "16pt":
                        DrawObject.LastFontSize = 16f;
                        break;
                    case "18pt":
                        DrawObject.LastFontSize = 18f;
                        break;
                    case "20pt":
                        DrawObject.LastFontSize = 20f;
                        break;
                    case "22pt":
                        DrawObject.LastFontSize = 22f;
                        break;
                    case "24pt":
                        DrawObject.LastFontSize = 24f;
                        break;
                }
                RefreshDraw();
            };
        }

        private void RefreshDraw()
        {
            if (FmEditDrawArea.GraphicsList.ShowPropertiesDialog(FmEditDrawArea))
            {
                DrawObject selected = FmEditDrawArea.GetSelected(FmEditDrawArea);
                if (selected != null)
                {
                    selected.IsChange = true;
                }
                FmEditDrawArea.Refresh();
            }
        }

        private void Form2_Load(object sender, EventArgs e)
        {
            DrawObject.LastIsOutline = true;
            fontstyleButton.ListItems = new string[5]
            {
                "宋体",
                "黑体",
                "楷体",
                "幼圆",
                "微软雅黑"
            };
            fontstyleButton.Islist = true;
            fontsizeButton.ListItems = new string[7]
            {
                "12pt",
                "14pt",
                "16pt",
                "18pt",
                "20pt",
                "22pt",
                "24pt"
            };
            fontsizeButton.Islist = true;
            Outline.Image = ImageHelp.CreateTextDotTool();
            foreach (Control control in base.Controls)
            {
                if (control is PictureBox)
                {
                    PictureBox pictureBox = (PictureBox)control;
                    switch (pictureBox.Name)
                    {
                        case "Rectangle1":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.red);
                            break;
                        case "Rectangle2":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.yellow);
                            break;
                        case "Rectangle3":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.blue);
                            break;
                        case "Rectangle4":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.Green);
                            break;
                        case "Rectangle5":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.purple);
                            break;
                        case "Rectangle6":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.black, ischeck: true);
                            break;
                        case "Rectangle7":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.gray);
                            break;
                    }
                }
            }
        }

        private void InitImageEditRectangleTool()
        {
            foreach (Control control in base.Controls)
            {
                if (control is PictureBox)
                {
                    PictureBox pictureBox = (PictureBox)control;
                    switch (control.Name)
                    {
                        case "Rectangle1":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.red);
                            break;
                        case "Rectangle2":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.yellow);
                            break;
                        case "Rectangle3":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.blue);
                            break;
                        case "Rectangle4":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.Green);
                            break;
                        case "Rectangle5":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.purple);
                            break;
                        case "Rectangle6":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.black);
                            break;
                        case "Rectangle7":
                            pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.gray);
                            break;
                    }
                }
            }
        }

        private void Tools_MouseDown(object sender, MouseEventArgs e)
        {
            PictureBox pictureBox = (PictureBox)sender;
            switch (pictureBox.Name)
            {
                case "Rectangle1":
                    InitImageEditRectangleTool();
                    pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.red, ischeck: true);
                    DrawObject.LastUsedColor = CustomColor.red;
                    break;
                case "Rectangle2":
                    InitImageEditRectangleTool();
                    pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.yellow, ischeck: true);
                    DrawObject.LastUsedColor = CustomColor.yellow;
                    break;
                case "Rectangle3":
                    InitImageEditRectangleTool();
                    pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.blue, ischeck: true);
                    DrawObject.LastUsedColor = CustomColor.blue;
                    break;
                case "Rectangle4":
                    InitImageEditRectangleTool();
                    pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.Green, ischeck: true);
                    DrawObject.LastUsedColor = CustomColor.Green;
                    break;
                case "Rectangle5":
                    InitImageEditRectangleTool();
                    pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.purple, ischeck: true);
                    DrawObject.LastUsedColor = CustomColor.purple;
                    break;
                case "Rectangle6":
                    InitImageEditRectangleTool();
                    pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.black, ischeck: true);
                    DrawObject.LastUsedColor = CustomColor.black;
                    break;
                case "Rectangle7":
                    InitImageEditRectangleTool();
                    pictureBox.Image = ImageHelp.EditRectangleTool(CustomColor.gray, ischeck: true);
                    DrawObject.LastUsedColor = CustomColor.gray;
                    break;
            }
            RefreshDraw();
        }

        private void Outline_MouseDown(object sender, MouseEventArgs e)
        {
            PictureBox pictureBox = (PictureBox)sender;
            pictureBox.Image = ImageHelp.CreateTextDotTool(!DrawObject.LastIsOutline);
            DrawObject.LastIsOutline = !DrawObject.LastIsOutline;
            RefreshDraw();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            Rectangle7 = new System.Windows.Forms.PictureBox();
            Rectangle6 = new System.Windows.Forms.PictureBox();
            Rectangle5 = new System.Windows.Forms.PictureBox();
            Rectangle4 = new System.Windows.Forms.PictureBox();
            Rectangle3 = new System.Windows.Forms.PictureBox();
            Rectangle2 = new System.Windows.Forms.PictureBox();
            Rectangle1 = new System.Windows.Forms.PictureBox();
            fontstyleButton = new OCRTools.ListButton();
            fontsizeButton = new OCRTools.ListButton();
            Outline = new System.Windows.Forms.PictureBox();
            ((System.ComponentModel.ISupportInitialize)Rectangle7).BeginInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle6).BeginInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle5).BeginInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle4).BeginInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle3).BeginInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle2).BeginInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)Outline).BeginInit();
            SuspendLayout();
            Rectangle7.BackColor = System.Drawing.Color.White;
            Rectangle7.Dock = System.Windows.Forms.DockStyle.Right;
            Rectangle7.Location = new System.Drawing.Point(353, 1);
            Rectangle7.Name = "Rectangle7";
            Rectangle7.Size = new System.Drawing.Size(30, 28);
            Rectangle7.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            Rectangle7.TabIndex = 6;
            Rectangle7.TabStop = false;
            Rectangle7.MouseDown += new System.Windows.Forms.MouseEventHandler(Tools_MouseDown);
            Rectangle6.BackColor = System.Drawing.Color.White;
            Rectangle6.Dock = System.Windows.Forms.DockStyle.Right;
            Rectangle6.Location = new System.Drawing.Point(323, 1);
            Rectangle6.Name = "Rectangle6";
            Rectangle6.Size = new System.Drawing.Size(30, 28);
            Rectangle6.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            Rectangle6.TabIndex = 5;
            Rectangle6.TabStop = false;
            Rectangle6.MouseDown += new System.Windows.Forms.MouseEventHandler(Tools_MouseDown);
            Rectangle5.BackColor = System.Drawing.Color.White;
            Rectangle5.Dock = System.Windows.Forms.DockStyle.Right;
            Rectangle5.Location = new System.Drawing.Point(293, 1);
            Rectangle5.Name = "Rectangle5";
            Rectangle5.Size = new System.Drawing.Size(30, 28);
            Rectangle5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            Rectangle5.TabIndex = 4;
            Rectangle5.TabStop = false;
            Rectangle5.MouseDown += new System.Windows.Forms.MouseEventHandler(Tools_MouseDown);
            Rectangle4.BackColor = System.Drawing.Color.White;
            Rectangle4.Dock = System.Windows.Forms.DockStyle.Right;
            Rectangle4.Location = new System.Drawing.Point(263, 1);
            Rectangle4.Name = "Rectangle4";
            Rectangle4.Size = new System.Drawing.Size(30, 28);
            Rectangle4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            Rectangle4.TabIndex = 3;
            Rectangle4.TabStop = false;
            Rectangle4.MouseDown += new System.Windows.Forms.MouseEventHandler(Tools_MouseDown);
            Rectangle3.BackColor = System.Drawing.Color.White;
            Rectangle3.Dock = System.Windows.Forms.DockStyle.Right;
            Rectangle3.Location = new System.Drawing.Point(233, 1);
            Rectangle3.Name = "Rectangle3";
            Rectangle3.Size = new System.Drawing.Size(30, 28);
            Rectangle3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            Rectangle3.TabIndex = 2;
            Rectangle3.TabStop = false;
            Rectangle3.MouseDown += new System.Windows.Forms.MouseEventHandler(Tools_MouseDown);
            Rectangle2.BackColor = System.Drawing.Color.White;
            Rectangle2.Dock = System.Windows.Forms.DockStyle.Right;
            Rectangle2.Location = new System.Drawing.Point(203, 1);
            Rectangle2.Name = "Rectangle2";
            Rectangle2.Size = new System.Drawing.Size(30, 28);
            Rectangle2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            Rectangle2.TabIndex = 1;
            Rectangle2.TabStop = false;
            Rectangle2.MouseDown += new System.Windows.Forms.MouseEventHandler(Tools_MouseDown);
            Rectangle1.BackColor = System.Drawing.Color.White;
            Rectangle1.Dock = System.Windows.Forms.DockStyle.Right;
            Rectangle1.Location = new System.Drawing.Point(173, 1);
            Rectangle1.Name = "Rectangle1";
            Rectangle1.Size = new System.Drawing.Size(30, 28);
            Rectangle1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            Rectangle1.TabIndex = 0;
            Rectangle1.TabStop = false;
            Rectangle1.MouseDown += new System.Windows.Forms.MouseEventHandler(Tools_MouseDown);
            fontstyleButton.BackColor = System.Drawing.Color.White;
            fontstyleButton.Dock = System.Windows.Forms.DockStyle.Fill;
            fontstyleButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            fontstyleButton.Font = new System.Drawing.Font("微软雅黑", 9f);
            fontstyleButton.IsBorder = false;
            fontstyleButton.ListItems = null;
            fontstyleButton.Location = new System.Drawing.Point(1, 1);
            fontstyleButton.Name = "fontstyleButton";
            fontstyleButton.Size = new System.Drawing.Size(70, 28);
            fontstyleButton.TabIndex = 0;
            fontstyleButton.Text = "微软雅黑";
            fontstyleButton.UseVisualStyleBackColor = false;
            fontsizeButton.BackColor = System.Drawing.Color.White;
            fontsizeButton.Dock = System.Windows.Forms.DockStyle.Right;
            fontsizeButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            fontsizeButton.Font = new System.Drawing.Font("微软雅黑", 9f);
            fontsizeButton.IsBorder = false;
            fontsizeButton.ListItems = null;
            fontsizeButton.Location = new System.Drawing.Point(71, 1);
            fontsizeButton.Name = "fontsizeButton";
            fontsizeButton.Size = new System.Drawing.Size(72, 28);
            fontsizeButton.TabIndex = 1;
            fontsizeButton.Text = "14pt";
            fontsizeButton.UseVisualStyleBackColor = false;
            Outline.BackColor = System.Drawing.Color.White;
            Outline.Dock = System.Windows.Forms.DockStyle.Right;
            Outline.Location = new System.Drawing.Point(143, 1);
            Outline.Name = "Outline";
            Outline.Size = new System.Drawing.Size(30, 28);
            Outline.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            Outline.TabIndex = 2;
            Outline.TabStop = false;
            Outline.MouseDown += new System.Windows.Forms.MouseEventHandler(Outline_MouseDown);
            base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
            base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(28, 160, 224);
            base.ClientSize = new System.Drawing.Size(384, 30);
            base.Controls.Add(fontstyleButton);
            base.Controls.Add(fontsizeButton);
            base.Controls.Add(Outline);
            base.Controls.Add(Rectangle1);
            base.Controls.Add(Rectangle2);
            base.Controls.Add(Rectangle3);
            base.Controls.Add(Rectangle4);
            base.Controls.Add(Rectangle5);
            base.Controls.Add(Rectangle6);
            base.Controls.Add(Rectangle7);
            base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            MaximumSize = new System.Drawing.Size(384, 30);
            MinimumSize = new System.Drawing.Size(384, 30);
            base.Name = "FmEdit";
            base.Padding = new System.Windows.Forms.Padding(1);
            base.ShowIcon = false;
            base.ShowInTaskbar = false;
            base.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            Text = "Form2";
            base.TopMost = true;
            base.Load += new System.EventHandler(Form2_Load);
            ((System.ComponentModel.ISupportInitialize)Rectangle7).EndInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle6).EndInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle5).EndInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle4).EndInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle3).EndInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle2).EndInit();
            ((System.ComponentModel.ISupportInitialize)Rectangle1).EndInit();
            ((System.ComponentModel.ISupportInitialize)Outline).EndInit();
            ResumeLayout(false);
        }
    }
}
