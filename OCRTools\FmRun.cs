using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.Windows.Forms;

namespace OCRTools
{
    public class FmRun : SilverFm
    {
        public enum KeyModifiers
        {
            None = 0,
            Alt = 1,
            Control = 2,
            Shift = 4,
            Windows = 8
        }

        private const int WM_HOTKEY = 786;

        private IContainer components = null;

        private ListButton button1;

        private ListButton button4;

        private ListButton button2;

        private ListButton button3;

        private ListButton button5;

        private ListButton _listButton1;

        private ListButton _listButton2;

        private Control control1;

        private Control control2;

        private Control control3;

        private Control control4;

        private Control control5;

        private Control control6;

        public FmRun()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            Catch();
        }

        private void QuickCatch()
        {
            if (!StaticValue.isCatchScreen)
            {
                DrawArea drawArea = new DrawArea
                {
                    IsShowCross = false,
                    IsEdit = false
                };
                drawArea.Prepare();
                drawArea.Status = "截图";
                drawArea.ShowDialog();
            }
        }

        private void QuickCatchCross()
        {
            if (!StaticValue.isCatchScreen)
            {
                StaticValue.current_ToolType = DrawToolType.Catch;
                DrawArea drawArea = new DrawArea
                {
                    IsShowCross = true,
                    IsEdit = false
                };
                drawArea.Prepare();
                drawArea.Status = "截图";
                drawArea.ShowDialog();
            }
        }

        private void QuickGif()
        {
            if (!StaticValue.isCatchScreen)
            {
                StaticValue.current_ToolType = DrawToolType.Catch;
                DrawArea drawArea = new DrawArea
                {
                    IsShowCross = false,
                    IsEdit = false
                };
                drawArea.Prepare();
                drawArea.Status = "录制";
                drawArea.GifPrepare();
                drawArea.ShowDialog();
            }
        }

        private void Catch()
        {
            if (!StaticValue.isCatchScreen)
            {
                StaticValue.current_ToolType = DrawToolType.Catch;
                DrawArea drawArea = new DrawArea
                {
                    IsEdit = true
                };
                drawArea.Prepare();
                drawArea.Status = "截图";
                drawArea.ShowDialog();
            }
        }

        private void MultiCatch()
        {
            if (!StaticValue.isCatchScreen)
            {
                StaticValue.current_ToolType = DrawToolType.MultiCatch;
                DrawArea drawArea = new DrawArea
                {
                    IsEdit = true,
                    IsMulti = true
                };
                drawArea.Prepare();
                drawArea.Status = "截图";
                drawArea.ShowDialog();
            }
        }

        public static void DrawRoundRect(GraphicsPath gp, int x, int y, int width, int height, int radius)
        {
            gp.AddArc(x, y, radius, radius, 180f, 90f);
            gp.AddArc(width - radius, y, radius, radius, 270f, 90f);
            gp.AddArc(width - radius, height - radius, radius, radius, 0f, 90f);
            gp.AddArc(x, height - radius, radius, radius, 90f, 90f);
            gp.CloseAllFigures();
        }

        private void Form2_Load(object sender, EventArgs e)
        {
        }

        private void ProcessHotkey(Message m)
        {
            string text = m.WParam.ToString();
            string a = text;
            if (!(a == "101"))
            {
                if (a == "102")
                {
                    Paste();
                }
            }
            else
            {
                Catch();
            }
        }

        protected override void WndProc(ref Message m)
        {
            int msg = m.Msg;
            if (msg == 786)
            {
                ProcessHotkey(m);
            }
            base.WndProc(ref m);
        }

        public bool RGB(string str, out Color color)
        {
            if (str == "" || str == null)
            {
                color = Color.FromArgb(0, 0, 0);
                return false;
            }
            string text = str.Trim();
            str = str.Replace("，", ",").Replace("#", "");
            color = Color.FromArgb(0, 0, 0);
            string[] array = str.Split(',');
            if (array.Length == 3)
            {
                try
                {
                    int red = Convert.ToInt32(array[0]);
                    int green = Convert.ToInt32(array[1]);
                    int blue = Convert.ToInt32(array[2]);
                    color = Color.FromArgb(red, green, blue);
                    return true;
                }
                catch
                {
                }
            }
            else
            {
                try
                {
                    if (str.Length == 6 && text.Substring(0, 1) == "#")
                    {
                        int red = int.Parse(str.Substring(0, 2), NumberStyles.AllowHexSpecifier);
                        int green = int.Parse(str.Substring(2, 2), NumberStyles.AllowHexSpecifier);
                        int blue = int.Parse(str.Substring(4, 2), NumberStyles.AllowHexSpecifier);
                        color = Color.FromArgb(red, green, blue);
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            }
            return false;
        }

        public static Bitmap CreatColorPanel(Color color)
        {
            Bitmap bitmap = new Bitmap(150.DPIValue(), 60.DPIValue());
            using (Graphics graphics = Graphics.FromImage(bitmap))
            {
                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    using (SolidBrush brush2 = new SolidBrush(color))
                    {
                        StringFormat stringFormat = new StringFormat
                        {
                            LineAlignment = StringAlignment.Center,
                            Alignment = StringAlignment.Near
                        };
                        Font font = new Font("微软雅黑", 10.5f, FontStyle.Regular);
                        Rectangle rect = new Rectangle(0, 0, bitmap.Width, bitmap.Height);
                        rect.SizeOffset(2, 2);
                        rect.Offset(-1, -1);
                        graphics.FillRectangle(brush, new Rectangle(-1, -1, rect.Width + 2, rect.Height + 2));
                        Rectangle rect2 = new Rectangle(-1, -1, rect.Width + 2, rect.Height / 6);
                        graphics.FillRectangle(brush2, rect2);
                        graphics.DrawString(layoutRectangle: new Rectangle(0, rect.Height / 4, rect.Width, rect.Height / 3), s: "  RGB:   " + GetInfoText_color(color), font: font, brush: new SolidBrush(Color.Black), format: stringFormat);
                        graphics.DrawString(layoutRectangle: new Rectangle(0, rect.Height / 3 + rect.Height / 4, rect.Width, rect.Height / 3), s: "  HEX:   " + GetInfoText_HEXcolor(color), font: font, brush: new SolidBrush(Color.Black), format: stringFormat);
                    }
                }
            }
            return bitmap;
        }

        private static string GetInfoText_HEXcolor(Color currentColor)
        {
            return "#" + ColorToHex(currentColor).ToUpper();
        }

        public static string ColorToHex(Color color)
        {
            return $"{color.R:X2}{color.G:X2}{color.B:X2}";
        }

        private static string GetInfoText_color(Color currentColor)
        {
            return string.Concat(new object[5]
            {
                currentColor.R.ToString(),
                " , ",
                currentColor.G.ToString(),
                " , ",
                currentColor.B.ToString()
            });
        }

        private void button4_Click(object sender, EventArgs e)
        {
            Paste();
        }

        private void Paste()
        {
            string text = "";
            IDataObject dataObject = Clipboard.GetDataObject();
            if (dataObject.GetDataPresent(DataFormats.Bitmap))
            {
                Bitmap img = (Bitmap)dataObject.GetData(DataFormats.Bitmap);
                this.ViewImage(img);
                return;
            }
            if (dataObject.GetDataPresent(DataFormats.Text))
            {
                text = (string)dataObject.GetData(DataFormats.Text);
            }
            if (RGB(text, out Color color))
            {
                ColorValue.color = color;
                ColorValue.colorRgb = GetInfoText_color(color);
                ColorValue.colorHex = GetInfoText_HEXcolor(color);
                //FmPastColor fmPastColor = new FmPastColor(CreatColorPanel(color));
                //fmPastColor.Show();
            }
        }

        private void FmRun_FormClosing(object sender, FormClosingEventArgs e)
        {
            HotKey.UnregisterHotKey(base.Handle, 102);
            HotKey.UnregisterHotKey(base.Handle, 101);
        }

        private void button2_Click(object sender, EventArgs e)
        {
            QuickCatch();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            QuickCatchCross();
        }

        private void button5_Click(object sender, EventArgs e)
        {
            MultiCatch();
        }

        private void customButton1_Click(object sender, EventArgs e)
        {
            QuickGif();
        }

        private void customButton2_Click(object sender, EventArgs e)
        {
            Close();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            button1 = new OCRTools.ListButton();
            button4 = new OCRTools.ListButton();
            button2 = new OCRTools.ListButton();
            button3 = new OCRTools.ListButton();
            button5 = new OCRTools.ListButton();
            _listButton1 = new OCRTools.ListButton();
            _listButton2 = new OCRTools.ListButton();
            control1 = new System.Windows.Forms.Control();
            control2 = new System.Windows.Forms.Control();
            control3 = new System.Windows.Forms.Control();
            control4 = new System.Windows.Forms.Control();
            control5 = new System.Windows.Forms.Control();
            control6 = new System.Windows.Forms.Control();
            SuspendLayout();
            button1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            button1.IsBorder = true;
            button1.ListItems = null;
            button1.Location = new System.Drawing.Point(40, 25);
            button1.Margin = new System.Windows.Forms.Padding(5);
            button1.Name = "button1";
            button1.Size = new System.Drawing.Size(103, 42);
            button1.TabIndex = 0;
            button1.Text = "开始截图";
            button1.UseVisualStyleBackColor = false;
            button1.Click += new System.EventHandler(button1_Click);
            button4.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            button4.IsBorder = true;
            button4.ListItems = null;
            button4.Location = new System.Drawing.Point(40, 332);
            button4.Margin = new System.Windows.Forms.Padding(5);
            button4.Name = "button4";
            button4.Size = new System.Drawing.Size(103, 42);
            button4.TabIndex = 4;
            button4.Text = "贴图";
            button4.UseVisualStyleBackColor = false;
            button4.Click += new System.EventHandler(button4_Click);
            button2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            button2.IsBorder = true;
            button2.ListItems = null;
            button2.Location = new System.Drawing.Point(40, 101);
            button2.Margin = new System.Windows.Forms.Padding(5);
            button2.Name = "button2";
            button2.Size = new System.Drawing.Size(103, 42);
            button2.TabIndex = 5;
            button2.Text = "快速截图";
            button2.UseVisualStyleBackColor = false;
            button2.Click += new System.EventHandler(button2_Click);
            button3.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            button3.IsBorder = true;
            button3.ListItems = null;
            button3.Location = new System.Drawing.Point(40, 176);
            button3.Margin = new System.Windows.Forms.Padding(5);
            button3.Name = "button3";
            button3.Size = new System.Drawing.Size(103, 42);
            button3.TabIndex = 6;
            button3.Text = "十字线";
            button3.UseVisualStyleBackColor = false;
            button3.Click += new System.EventHandler(button3_Click);
            button5.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            button5.IsBorder = true;
            button5.ListItems = null;
            button5.Location = new System.Drawing.Point(40, 410);
            button5.Margin = new System.Windows.Forms.Padding(5);
            button5.Name = "button5";
            button5.Size = new System.Drawing.Size(103, 42);
            button5.TabIndex = 7;
            button5.Text = "多区域截图";
            button5.UseVisualStyleBackColor = false;
            button5.Click += new System.EventHandler(button5_Click);
            _listButton1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            _listButton1.IsBorder = true;
            _listButton1.ListItems = null;
            _listButton1.Location = new System.Drawing.Point(40, 252);
            _listButton1.Margin = new System.Windows.Forms.Padding(5);
            _listButton1.Name = "_listButton1";
            _listButton1.Size = new System.Drawing.Size(103, 42);
            _listButton1.TabIndex = 8;
            _listButton1.Text = "录制Gif";
            _listButton1.UseVisualStyleBackColor = false;
            _listButton1.Click += new System.EventHandler(customButton1_Click);
            _listButton2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            _listButton2.IsBorder = true;
            _listButton2.ListItems = null;
            _listButton2.Location = new System.Drawing.Point(40, 488);
            _listButton2.Margin = new System.Windows.Forms.Padding(5);
            _listButton2.Name = "_listButton2";
            _listButton2.Size = new System.Drawing.Size(103, 42);
            _listButton2.TabIndex = 9;
            _listButton2.Text = "退出";
            _listButton2.UseVisualStyleBackColor = false;
            _listButton2.Click += new System.EventHandler(customButton2_Click);
            control1.Location = new System.Drawing.Point(0, 0);
            control1.Name = "control1";
            control1.Size = new System.Drawing.Size(0, 0);
            control1.TabIndex = 0;
            control1.Text = "control1";
            control2.Location = new System.Drawing.Point(0, 0);
            control2.Name = "control2";
            control2.Size = new System.Drawing.Size(0, 0);
            control2.TabIndex = 0;
            control2.Text = "control2";
            control3.Location = new System.Drawing.Point(0, 0);
            control3.Name = "control3";
            control3.Size = new System.Drawing.Size(0, 0);
            control3.TabIndex = 0;
            control3.Text = "control3";
            control4.Location = new System.Drawing.Point(0, 0);
            control4.Name = "control4";
            control4.Size = new System.Drawing.Size(0, 0);
            control4.TabIndex = 0;
            control4.Text = "control4";
            control5.Location = new System.Drawing.Point(0, 0);
            control5.Name = "control5";
            control5.Size = new System.Drawing.Size(0, 0);
            control5.TabIndex = 0;
            control5.Text = "control5";
            control6.Location = new System.Drawing.Point(0, 0);
            control6.Name = "control6";
            control6.Size = new System.Drawing.Size(0, 0);
            control6.TabIndex = 0;
            control6.Text = "control6";
            base.AutoScaleDimensions = new System.Drawing.SizeF(8f, 19f);
            base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.White;
            base.ClientSize = new System.Drawing.Size(178, 568);
            base.Controls.Add(_listButton2);
            base.Controls.Add(_listButton1);
            base.Controls.Add(button5);
            base.Controls.Add(button3);
            base.Controls.Add(button2);
            base.Controls.Add(button4);
            base.Controls.Add(button1);
            base.Margin = new System.Windows.Forms.Padding(5, 8, 5, 8);
            base.MaximizeBox = false;
            base.MinimizeBox = false;
            base.Name = "FmRun";
            base.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            Text = "截图";
            base.TopMost = true;
            base.FormClosing += new System.Windows.Forms.FormClosingEventHandler(FmRun_FormClosing);
            base.Load += new System.EventHandler(Form2_Load);
            ResumeLayout(false);
        }
    }
}
