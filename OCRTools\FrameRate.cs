using System.Diagnostics;

namespace OCRTools
{
    public class FrameRate
    {
        private static Stopwatch stopwatch = new Stopwatch();

        private static int interval = 15;

        private static bool started = true;

        public static void Start(int interval)
        {
            stopwatch = new Stopwatch();
            FrameRate.interval = interval;
        }

        public static int GetMilliseconds()
        {
            if (started)
            {
                started = false;
                stopwatch.Start();
                return interval;
            }
            int result = (int)stopwatch.ElapsedMilliseconds;
            stopwatch.Restart();
            return result;
        }

        public static void Stop()
        {
            stopwatch.Stop();
            started = true;
        }
    }
}
