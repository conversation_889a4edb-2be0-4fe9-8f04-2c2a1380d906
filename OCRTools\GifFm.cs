using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class GifFm : Form
    {
        public static class StatusColors
        {
            public static Color recoder = Color.FromArgb(237, 83, 80);

            public static Color normal = Color.FromArgb(28, 160, 224);

            public static Color loading = Color.FromArgb(26, 173, 25);
        }

        private GifTool tool;

        public Color statusColor;

        private const int Guying_HTLEFT = 10;

        private const int Guying_HTRIGHT = 11;

        private const int Guying_HTTOP = 12;

        private const int Guying_HTTOPLEFT = 13;

        private const int Guying_HTTOPRIGHT = 14;

        private const int Guying_HTBOTTOM = 15;

        private const int Guying_HTBOTTOMLEFT = 16;

        private const int Guying_HTBOTTOMRIGHT = 17;

        private IContainer components = null;

        public PictureBox pictureBox1;

        private ContextMenuStrip contextMenuStrip1;

        private ToolStripMenuItem 刷新ToolStripMenuItem;

        private ToolStripMenuItem 删除ToolStripMenuItem;

        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams createParams = base.CreateParams;
                createParams.ExStyle |= 33554432;
                return createParams;
            }
        }

        public Color StatusColor
        {
            get => statusColor;
            set
            {
                statusColor = value;
                BackColor = value;
            }
        }

        public GifFm(Rectangle rectangle)
        {
            InitializeComponent();
            contextMenuStrip1.Renderer = new ComboxbtnRenderer(fontCenter: true);
            contextMenuStrip1.ShowImageMargin = false;
            base.TopMost = true;
            base.Shown += delegate
            {
                base.Bounds = new Rectangle(rectangle.X - 2, rectangle.Y - 2, rectangle.Width + 4, rectangle.Height + 4);
                tool = new GifTool(this);
                tool.Show(this);
            };
            StatusColor = StatusColors.normal;
        }

        protected override void WndProc(ref Message m)
        {
            int msg = m.Msg;
            if (msg == 132)
            {
                base.WndProc(ref m);
                if (!(StatusColor == StatusColors.normal))
                {
                    return;
                }
                Point p = new Point((int)m.LParam & 0xFFFF, ((int)m.LParam >> 16) & 0xFFFF);
                p = PointToClient(p);
                if (p.X <= 5)
                {
                    if (p.Y <= 5)
                    {
                        m.Result = (IntPtr)13;
                    }
                    else if (p.Y >= base.ClientSize.Height - 5)
                    {
                        m.Result = (IntPtr)16;
                    }
                    else
                    {
                        m.Result = (IntPtr)10;
                    }
                }
                else if (p.X >= base.ClientSize.Width - 5)
                {
                    if (p.Y <= 5)
                    {
                        m.Result = (IntPtr)14;
                    }
                    else if (p.Y >= base.ClientSize.Height - 5)
                    {
                        m.Result = (IntPtr)17;
                    }
                    else
                    {
                        m.Result = (IntPtr)11;
                    }
                }
                else if (p.Y <= 5)
                {
                    m.Result = (IntPtr)12;
                }
                else if (p.Y >= base.ClientSize.Height - 5)
                {
                    m.Result = (IntPtr)15;
                }
            }
            else
            {
                base.WndProc(ref m);
            }
        }

        private void GifFm_SizeChanged(object sender, EventArgs e)
        {
            if (tool != null)
            {
                tool.Location = new Point(base.Left, base.Top + base.Height + 2.DPIValue());
                tool.ReadTool();
            }
        }

        private void 刷新ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (tool != null)
            {
                tool.FmRefresh();
            }
        }

        private void pictureBox1_MouseUp(object sender, MouseEventArgs e)
        {
        }

        private void GifFm_MouseUp(object sender, MouseEventArgs e)
        {
        }

        private void 删除ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (tool != null)
            {
                tool.Fmdelete();
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            pictureBox1 = new System.Windows.Forms.PictureBox();
            contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(components);
            刷新ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            删除ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            contextMenuStrip1.SuspendLayout();
            SuspendLayout();
            pictureBox1.BackColor = System.Drawing.Color.Green;
            pictureBox1.ContextMenuStrip = contextMenuStrip1;
            pictureBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            pictureBox1.Location = new System.Drawing.Point(2, 2);
            pictureBox1.Margin = new System.Windows.Forms.Padding(0);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new System.Drawing.Size(470, 205);
            pictureBox1.TabIndex = 2;
            pictureBox1.TabStop = false;
            pictureBox1.MouseUp += new System.Windows.Forms.MouseEventHandler(pictureBox1_MouseUp);
            contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[2]
            {
                刷新ToolStripMenuItem,
                删除ToolStripMenuItem
            });
            contextMenuStrip1.Name = "contextMenuStrip1";
            contextMenuStrip1.Size = new System.Drawing.Size(101, 48);
            刷新ToolStripMenuItem.Name = "刷新ToolStripMenuItem";
            刷新ToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            刷新ToolStripMenuItem.Text = "刷新";
            刷新ToolStripMenuItem.Click += new System.EventHandler(刷新ToolStripMenuItem_Click);
            删除ToolStripMenuItem.Name = "删除ToolStripMenuItem";
            删除ToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            删除ToolStripMenuItem.Text = "删除";
            删除ToolStripMenuItem.Click += new System.EventHandler(删除ToolStripMenuItem_Click);
            base.AutoScaleDimensions = new System.Drawing.SizeF(7f, 17f);
            base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(0, 128, 1);
            base.ClientSize = new System.Drawing.Size(474, 209);
            base.ControlBox = false;
            base.Controls.Add(pictureBox1);
            Font = new System.Drawing.Font("微软雅黑", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            base.Margin = new System.Windows.Forms.Padding(0);
            base.MaximizeBox = false;
            base.MinimizeBox = false;
            MinimumSize = new System.Drawing.Size(200, 60);
            base.Name = "GifFm";
            base.Padding = new System.Windows.Forms.Padding(2);
            base.ShowIcon = false;
            base.ShowInTaskbar = false;
            base.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            base.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            base.TopMost = true;
            base.TransparencyKey = System.Drawing.Color.Green;
            base.SizeChanged += new System.EventHandler(GifFm_SizeChanged);
            base.MouseUp += new System.Windows.Forms.MouseEventHandler(GifFm_MouseUp);
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            contextMenuStrip1.ResumeLayout(false);
            ResumeLayout(false);
        }
    }
}
