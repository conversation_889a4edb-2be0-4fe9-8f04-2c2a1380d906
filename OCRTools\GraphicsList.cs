using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Security.Permissions;

namespace OCRTools
{
    [Serializable]
    internal class GraphicsList : ISerializable
    {
        public delegate void ValueChangeHandler(object sender, EventArgs e);

        public List<DrawObject> graphicsList;

        public GraphicsList()
        {
            graphicsList = new List<DrawObject>();
        }

        protected GraphicsList(SerializationInfo info, StreamingContext context)
        {
            graphicsList = new List<DrawObject>();
            var @int = info.GetInt32("Count");
            for (var i = 0; i < @int; i++)
            {
                var @string = info.GetString(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Type", i));
                var drawObject = (DrawObject)Assembly.GetExecutingAssembly().CreateInstance(@string);
                drawObject.LoadFromStream(info, i);
                graphicsList.Add(drawObject);
            }
        }

        public IEnumerable<DrawObject> SelectedObjects => from o in graphicsList
                                                          where o.Selected
                                                          select o;

        public int Count => graphicsList.Count;

        public DrawObject this[int index]
        {
            get
            {
                if (index < 0 || index >= graphicsList.Count) return null;
                return graphicsList[index];
            }
        }

        public int SelectionCount
        {
            get
            {
                var num = 0;
                foreach (var unused in Selection) num++;

                return num;
            }
        }

        public IEnumerable<DrawObject> Selection
        {
            get
            {
                foreach (var o in graphicsList)
                    if (o.Selected)
                        yield return o;
            }
        }

        [SecurityPermission(SecurityAction.Demand, SerializationFormatter = true)]
        public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            info.AddValue("Count", graphicsList.Count);
            var num = 0;
            foreach (var graphics in graphicsList)
            {
                info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Type", num),
                    graphics.GetType().FullName);
                graphics.SaveToStream(info, num);
                num++;
            }
        }

        public event ValueChangeHandler ValueChanged;

        public void Change()
        {
            if (ValueChanged != null) ValueChanged(null, EventArgs.Empty);
        }

        public void Draw(Graphics g)
        {
            var count = graphicsList.Count;
            for (var num = count - 1; num >= 0; num--)
            {
                var drawObject = graphicsList[num];
                if (drawObject.NoteType == DrawToolType.Mosaic || drawObject.NoteType == DrawToolType.Gaus ||
                    drawObject.NoteType == DrawToolType.RectangleFill)
                {
                    drawObject.Draw(g);
                    if (drawObject.Selected) drawObject.DrawTracker(g);
                }
            }

            for (var num2 = count - 1; num2 >= 0; num2--)
            {
                var drawObject = graphicsList[num2];
                if (drawObject.NoteType != DrawToolType.Mosaic && drawObject.NoteType != DrawToolType.Gaus &&
                    drawObject.NoteType != DrawToolType.RectangleFill && drawObject.NoteType != 0 &&
                    drawObject.NoteType != DrawToolType.Step)
                {
                    drawObject.Draw(g);
                    if (drawObject.Selected) drawObject.DrawTracker(g);
                }
            }

            for (var num3 = count - 1; num3 >= 0; num3--)
            {
                var drawObject = graphicsList[num3];
                if (drawObject.NoteType == DrawToolType.Step)
                {
                    drawObject.Draw(g);
                    if (drawObject.Selected) drawObject.DrawTracker(g);
                }
            }

            for (var num4 = count - 1; num4 >= 0; num4--)
            {
                var drawObject = graphicsList[num4];
                if (drawObject.NoteType == DrawToolType.Catch)
                {
                    drawObject.Draw(g);
                    if (drawObject.Selected) drawObject.DrawTracker(g);
                    break;
                }
            }

            Change();
        }

        public void OutDraw(Graphics g)
        {
            var count = graphicsList.Count;
            for (var num = count - 1; num >= 0; num--)
            {
                var drawObject = graphicsList[num];
                if (drawObject.NoteType == DrawToolType.Mosaic || drawObject.NoteType == DrawToolType.Gaus ||
                    drawObject.NoteType == DrawToolType.RectangleFill)
                {
                    drawObject.Draw(g);
                    if (drawObject.Selected) drawObject.DrawTracker(g);
                }
            }

            for (var num2 = count - 1; num2 >= 0; num2--)
            {
                var drawObject = graphicsList[num2];
                if (drawObject.NoteType != DrawToolType.Mosaic && drawObject.NoteType != DrawToolType.Gaus &&
                    drawObject.NoteType != DrawToolType.RectangleFill && drawObject.NoteType != 0 &&
                    drawObject.NoteType != DrawToolType.Step)
                {
                    drawObject.Draw(g);
                    if (drawObject.Selected) drawObject.DrawTracker(g);
                }
            }

            for (var num3 = count - 1; num3 >= 0; num3--)
            {
                var drawObject = graphicsList[num3];
                if (drawObject.NoteType == DrawToolType.Step)
                {
                    drawObject.Draw(g);
                    if (drawObject.Selected) drawObject.DrawTracker(g);
                }
            }

            Change();
        }

        public void Dump()
        {
            foreach (var graphics in graphicsList) graphics.Dump();
        }

        public bool Clear()
        {
            var result = graphicsList.Count > 0;
            graphicsList.Clear();
            return result;
        }

        public void Add(DrawObject obj)
        {
            graphicsList.Insert(0, obj);
        }

        public void Insert(int index, DrawObject obj)
        {
            if (index >= 0 && index < graphicsList.Count) graphicsList.Insert(index, obj);
        }

        public void Replace(int index, DrawObject obj)
        {
            if (index >= 0 && index < graphicsList.Count)
            {
                graphicsList.RemoveAt(index);
                graphicsList.Insert(index, obj);
            }
        }

        public void RemoveAt(int index)
        {
            graphicsList.RemoveAt(index);
        }

        public void DeleteLastAddedObject()
        {
            if (graphicsList.Count > 0 && graphicsList.Count > 1) graphicsList.RemoveAt(0);
        }

        public void SelectInRectangle(Rectangle rectangle)
        {
            UnselectAll();
            foreach (var graphics in graphicsList)
                if (graphics.IntersectsWith(rectangle))
                    graphics.Selected = true;
        }

        public void UnselectAll()
        {
            foreach (var graphics in graphicsList) graphics.Selected = false;
        }

        public void SelectObject(DrawObject drawObject)
        {
            foreach (var graphics in graphicsList)
                if (drawObject.Id == graphics.Id)
                {
                    drawObject.Selected = true;
                    break;
                }
        }

        public void SelectAll()
        {
            foreach (var graphics in graphicsList)
                if (graphics.NoteType != 0)
                    graphics.Selected = true;
        }

        public bool DeleteSelection()
        {
            var result = false;
            var count = graphicsList.Count;
            for (var num = count - 1; num >= 0; num--)
                if (graphicsList[num].Selected && graphicsList[num].NoteType != 0)
                {
                    graphicsList.RemoveAt(num);
                    result = true;
                }

            return result;
        }

        public bool DeleteSelectionall()
        {
            var count = graphicsList.Count;
            for (var num = count - 1; num >= 0; num--) graphicsList.RemoveAt(num);
            return false;
        }

        public List<DrawObject> BufferReverse()
        {
            var count = graphicsList.Count;
            for (var i = 0; i < count / 2; i++)
            {
                var value = graphicsList[i];
                graphicsList[i] = graphicsList[count - i - 1];
                graphicsList[count - i - 1] = value;
            }

            return graphicsList;
        }

        public bool MoveSelectionToFront()
        {
            var list = new List<DrawObject>();
            var count = graphicsList.Count;
            for (var num = count - 1; num >= 0; num--)
                if (graphicsList[num].Selected)
                {
                    list.Add(graphicsList[num]);
                    graphicsList.RemoveAt(num);
                    break;
                }

            count = list.Count;
            for (var num = 0; num < count; num++) graphicsList.Insert(0, list[num]);
            return count > 0;
        }

        public bool MoveSelectionToFront(DrawCatch drawCatch)
        {
            var list = new List<DrawObject>();
            var count = graphicsList.Count;
            for (var num = count - 1; num >= 0; num--)
                if (graphicsList[num] == drawCatch)
                {
                    list.Add(graphicsList[num]);
                    graphicsList.RemoveAt(num);
                }

            count = list.Count;
            for (var num = 0; num < count; num++) graphicsList.Insert(0, list[num]);
            return count > 0;
        }

        public bool MoveSelectionToBack(DrawObject drawObject)
        {
            var list = new List<DrawObject>();
            var count = graphicsList.Count;
            for (var num = count - 1; num >= 0; num--)
                if (graphicsList[num] == drawObject)
                {
                    list.Add(graphicsList[num]);
                    graphicsList.RemoveAt(num);
                }

            count = list.Count;
            for (var num = count - 1; num >= 0; num--) graphicsList.Add(list[num]);
            return count > 0;
        }

        public bool MoveSelectionToBack(DrawCatch drawCatch)
        {
            var list = new List<DrawObject>();
            var count = graphicsList.Count;
            for (var num = count - 1; num >= 0; num--)
                if (graphicsList[num] == drawCatch)
                {
                    list.Add(graphicsList[num]);
                    graphicsList.RemoveAt(num);
                }

            count = list.Count;
            for (var num = count - 1; num >= 0; num--) graphicsList.Add(list[num]);
            return count > 0;
        }

        private GraphicsProperties GetProperties()
        {
            var graphicsProperties = new GraphicsProperties();
            var flag = true;
            var num = 0;
            var num2 = 1;
            var flag2 = true;
            var flag3 = true;
            foreach (var item in Selection)
                if (flag)
                {
                    num = item.Color.ToArgb();
                    num2 = item.PenWidth;
                    flag = false;
                }
                else
                {
                    if (item.Color.ToArgb() != num) flag2 = false;
                    if (item.PenWidth != num2) flag3 = false;
                }

            if (flag2) graphicsProperties.Color = Color.FromArgb(num);
            if (flag3) graphicsProperties.PenWidth = num2;
            return graphicsProperties;
        }

        private bool ApplyProperties()
        {
            var result = false;
            foreach (var graphics in graphicsList)
                if (graphics.Selected)
                {
                    if (graphics.Color != DrawObject.LastUsedColor)
                    {
                        graphics.Color = DrawObject.LastUsedColor;
                        result = true;
                    }

                    if (graphics.PenWidth != DrawObject.LastUsedPenWidth)
                    {
                        graphics.PenWidth = DrawObject.LastUsedPenWidth;
                        result = true;
                    }

                    if (graphics.IsDot != DrawObject.LastIsDot)
                    {
                        graphics.IsDot = DrawObject.LastIsDot;
                        result = true;
                    }

                    if (graphics.IsOutline != DrawObject.LastIsOutline)
                    {
                        graphics.IsOutline = DrawObject.LastIsOutline;
                        result = true;
                    }

                    if (graphics.IsArrowBoth != DrawObject.LastIsArrowBoth)
                    {
                        graphics.IsArrowBoth = DrawObject.LastIsArrowBoth;
                        result = true;
                    }

                    if (graphics.FontSize != DrawObject.LastFontSize)
                    {
                        graphics.FontSize = DrawObject.LastFontSize;
                        result = true;
                    }

                    if (graphics.Fontstyle != DrawObject.LastFontstyle)
                    {
                        graphics.Fontstyle = DrawObject.LastFontstyle;
                        result = true;
                    }
                }

            return result;
        }

        public bool ShowPropertiesDialog(DrawArea parent)
        {
            if (SelectionCount < 1) return false;
            var commandChangeState = new CommandChangeState(this);
            if (ApplyProperties())
            {
                commandChangeState.NewState(this);
                parent.AddCommandToHistory(commandChangeState);
            }

            return true;
        }
    }
}