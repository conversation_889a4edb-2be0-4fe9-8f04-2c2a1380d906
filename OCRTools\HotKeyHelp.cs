using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    public static class HotKeyHelp
    {
        public static string GetStringByKey(KeyEventArgs e)
        {
            if (e.KeyValue == 16)
            {
                return "Shift + ";
            }
            if (e.KeyValue == 17)
            {
                return "Ctrl + ";
            }
            if (e.KeyValue == 18)
            {
                return "Alt + ";
            }
            StringBuilder stringBuilder = new StringBuilder();
            if (e.Modifiers != 0)
            {
                if (e.Control)
                {
                    stringBuilder.Append("Ctrl + ");
                }
                if (e.Alt)
                {
                    stringBuilder.Append("Alt + ");
                }
                if (e.Shift)
                {
                    stringBuilder.Append("Shift + ");
                }
            }
            if (e.KeyValue >= 48 && e.KeyValue <= 57)
            {
                stringBuilder.Append(e.KeyCode.ToString());
            }
            else
            {
                stringBuilder.Append(e.KeyCode);
            }
            return stringBuilder.ToString();
        }

        public static string GetSingleStrByKey(KeyEventArgs e)
        {
            if (e.KeyValue == 16)
            {
                return "Shift";
            }
            if (e.KeyValue == 17)
            {
                return "Ctrl";
            }
            if (e.KeyValue == 18)
            {
                return "Alt";
            }
            return e.KeyCode.ToString();
        }

        public static KeyEventArgs GetKeyByString(string strKey)
        {
            Keys keys = Keys.None;
            string[] array = strKey.Split('+');
            if (array.Length != 0)
            {
                string[] array2 = array;
                foreach (string text in array2)
                {
                    int result;
                    if (text.Trim().ToUpper() == "CTRL")
                    {
                        keys |= Keys.Control;
                    }
                    else if (text.Trim().ToUpper() == "SHIFT")
                    {
                        keys |= Keys.Shift;
                    }
                    else if (text.Trim().ToUpper() == "ALT")
                    {
                        keys |= Keys.Alt;
                    }
                    else if (int.TryParse(text, out result))
                    {
                        KeysConverter keysConverter = new KeysConverter();
                        Keys keys2 = (Keys)keysConverter.ConvertFromString("D" + text);
                        keys |= keys2;
                    }
                    else
                    {
                        KeysConverter keysConverter2 = new KeysConverter();
                        Keys keys3 = (Keys)keysConverter2.ConvertFromString(text);
                        keys |= keys3;
                    }
                }
            }
            return new KeyEventArgs(keys);
        }
    }
}
