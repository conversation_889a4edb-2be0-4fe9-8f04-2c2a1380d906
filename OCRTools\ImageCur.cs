using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.InteropServices;

namespace OCRTools
{
    public class ImageCur
    {
        private class CurHead
        {
            private byte[] m_Retain = new byte[2];

            private byte[] m_Type = new byte[2]
            {
                2,
                0
            };

            private byte[] m_ImageCount = new byte[2];

            private byte m_ImageWidth;

            private byte m_ImageHeight;

            private byte m_ColorCount;

            private byte[] m_NotUser = new byte[5];

            private byte[] m_ImageLength = new byte[4];

            private byte[] m_ImageRVA = new byte[4];

            public byte ImageHeight
            {
                get => m_ImageHeight;
                set => m_ImageHeight = value;
            }

            public ushort ImageCount
            {
                get => BitConverter.ToUInt16(m_ImageCount, 0);
                set => m_ImageCount = BitConverter.GetBytes(value);
            }

            public byte ImageWidth
            {
                get => m_ImageWidth;
                set => m_ImageWidth = value;
            }

            public byte ColorCount
            {
                get => m_ColorCount;
                set => m_ColorCount = value;
            }

            public uint ImageLength
            {
                get => BitConverter.ToUInt32(m_ImageLength, 0);
                set => m_ImageLength = BitConverter.GetBytes(value);
            }

            public uint ImageRVA
            {
                get => BitConverter.ToUInt32(m_ImageRVA, 0);
                set => m_ImageRVA = BitConverter.GetBytes(value);
            }

            public CurHead()
            {
            }

            public CurHead(byte[] p_Data)
            {
                ImageCount = BitConverter.ToUInt16(p_Data, 4);
                ImageHeight = p_Data[7];
                ImageWidth = p_Data[6];
                ColorCount = p_Data[8];
                ImageLength = BitConverter.ToUInt32(p_Data, 14);
                ImageRVA = BitConverter.ToUInt32(p_Data, 18);
            }

            public byte[] GetByte()
            {
                return new byte[22]
                {
                    m_Retain[0],
                    m_Retain[1],
                    m_Retain[0],
                    m_Retain[1],
                    m_ImageCount[0],
                    m_ImageCount[1],
                    m_ImageWidth,
                    m_ImageHeight,
                    m_ColorCount,
                    0,
                    0,
                    0,
                    0,
                    0,
                    m_ImageLength[0],
                    m_ImageLength[1],
                    m_ImageLength[2],
                    m_ImageLength[3],
                    m_ImageRVA[0],
                    m_ImageRVA[1],
                    m_ImageRVA[2],
                    m_ImageRVA[3]
                };
            }

            public byte[] GetImageByte()
            {
                return new byte[16]
                {
                    m_ImageWidth,
                    m_ImageHeight,
                    m_ColorCount,
                    0,
                    0,
                    0,
                    0,
                    0,
                    m_ImageLength[0],
                    m_ImageLength[1],
                    m_ImageLength[2],
                    m_ImageLength[3],
                    m_ImageRVA[0],
                    m_ImageRVA[1],
                    m_ImageRVA[2],
                    m_ImageRVA[3]
                };
            }
        }

        private CurHead m_CurHead;

        private Bitmap m_CurImage;

        private IList<Image> m_CurList = new List<Image>();

        public IList<Image> CurImage
        {
            get => m_CurList;
            set => m_CurList = value;
        }

        public ImageCur(string p_FileFullName)
        {
            byte[] p_Bytes = File.ReadAllBytes(p_FileFullName);
            ImageCurBytes(p_Bytes);
        }

        public ImageCur(byte[] p_FileBytes)
        {
            ImageCurBytes(p_FileBytes);
        }

        private void ImageCurBytes(byte[] p_Bytes)
        {
            m_CurHead = new CurHead(p_Bytes);
            for (int i = 0; i != m_CurHead.ImageCount; i++)
            {
                m_CurHead.ImageWidth = (byte)BitConverter.ToInt16(p_Bytes, (int)(m_CurHead.ImageRVA + 4));
                m_CurHead.ImageHeight = (byte)(BitConverter.ToInt16(p_Bytes, (int)(m_CurHead.ImageRVA + 8)) / 2);
                short piex = BitConverter.ToInt16(p_Bytes, (int)(m_CurHead.ImageRVA + 14));
                LoadImgae(piex, p_Bytes, (int)(m_CurHead.ImageRVA + 40));
                m_CurList.Add(m_CurImage);
                byte[] array = new byte[4];
                array[3] = p_Bytes[(i + 1) * 16 + 18 + 3];
                array[2] = p_Bytes[(i + 1) * 16 + 18 + 2];
                array[1] = p_Bytes[(i + 1) * 16 + 18 + 1];
                array[0] = p_Bytes[(i + 1) * 16 + 18];
                m_CurHead.ImageRVA = BitConverter.ToUInt32(array, 0);
            }
        }

        public ImageCur()
        {
        }

        private void Load32(byte[] p_FileBytes, byte[] p_NewData, int p_ReadIndex, int p_Width, int p_Height, int p_MashIndex, BitmapData p_NewBitmapData)
        {
            for (int num = p_NewBitmapData.Height - 1; num != -1; num--)
            {
                int num2 = num * p_NewBitmapData.Stride;
                for (int i = 0; i != p_NewBitmapData.Width; i++)
                {
                    p_NewData[num2 + i * 4] = p_FileBytes[p_ReadIndex];
                    p_NewData[num2 + i * 4 + 1] = p_FileBytes[p_ReadIndex + 1];
                    p_NewData[num2 + i * 4 + 2] = p_FileBytes[p_ReadIndex + 2];
                    p_NewData[num2 + i * 4 + 3] = p_FileBytes[p_ReadIndex + 3];
                    p_ReadIndex += 4;
                }
            }
        }

        private void Load24(byte[] p_FileBytes, byte[] p_NewData, int p_ReadIndex, int p_Width, int p_Height, int p_MashIndex, BitmapData p_NewBitmapData)
        {
            int num = p_Width / 8;
            if (p_Width % 8 != 0)
            {
                num++;
            }
            if (num % 4 != 0)
            {
                num += num % 4;
            }
            byte[] array = new byte[num * p_Height];
            Array.Copy(p_FileBytes, p_MashIndex, array, 0, array.Length);
            for (int i = 0; i != array.Length; i++)
            {
                array[i] = ReverseByte(array[i]);
            }
            BitArray bitArray = new BitArray(array);
            for (int num2 = p_NewBitmapData.Height - 1; num2 != -1; num2--)
            {
                p_MashIndex = (p_Height - 1 - num2) * (num * 8);
                int num3 = num2 * p_NewBitmapData.Stride;
                for (int j = 0; j != p_NewBitmapData.Width; j++)
                {
                    p_NewData[num3 + j * 4] = p_FileBytes[p_ReadIndex];
                    p_NewData[num3 + j * 4 + 1] = p_FileBytes[p_ReadIndex + 1];
                    p_NewData[num3 + j * 4 + 2] = p_FileBytes[p_ReadIndex + 2];
                    p_NewData[num3 + j * 4 + 3] = byte.MaxValue;
                    if (bitArray[p_MashIndex])
                    {
                        p_NewData[num3 + j * 4 + 3] = 0;
                    }
                    p_ReadIndex += 3;
                    p_MashIndex++;
                }
            }
        }

        private void Load8(byte[] p_FileBytes, byte[] p_NewData, int p_ReadIndex, int p_Width, int p_Height, int p_MashIndex, BitmapData p_NewBitmapData)
        {
            Hashtable hashtable = new Hashtable();
            for (int i = 0; i != 256; i++)
            {
                hashtable.Add(i.ToString(), Color.FromArgb(p_FileBytes[p_ReadIndex + 3], p_FileBytes[p_ReadIndex + 2], p_FileBytes[p_ReadIndex + 1], p_FileBytes[p_ReadIndex]));
                p_ReadIndex += 4;
            }
            p_MashIndex = p_Width * p_Height + p_ReadIndex;
            int num = p_Width / 8;
            if (p_Width % 8 != 0)
            {
                num++;
            }
            if (num % 4 != 0)
            {
                num += num % 4;
            }
            byte[] array = new byte[num * p_Height];
            Array.Copy(p_FileBytes, p_MashIndex, array, 0, array.Length);
            for (int j = 0; j != array.Length; j++)
            {
                array[j] = ReverseByte(array[j]);
            }
            BitArray bitArray = new BitArray(array);
            for (int num2 = p_NewBitmapData.Height - 1; num2 != -1; num2--)
            {
                p_MashIndex = (p_Height - 1 - num2) * (num * 8);
                int num3 = num2 * p_NewBitmapData.Stride;
                for (int k = 0; k != p_NewBitmapData.Width; k++)
                {
                    byte b = p_FileBytes[p_ReadIndex];
                    Color color = (Color)hashtable[b.ToString()];
                    p_NewData[num3 + k * 4] = color.B;
                    p_NewData[num3 + k * 4 + 1] = color.G;
                    p_NewData[num3 + k * 4 + 2] = color.R;
                    p_NewData[num3 + k * 4 + 3] = byte.MaxValue;
                    if (bitArray[p_MashIndex])
                    {
                        p_NewData[num3 + k * 4 + 3] = 0;
                    }
                    p_ReadIndex++;
                    p_MashIndex++;
                }
            }
        }

        private void Load4(byte[] p_FileBytes, byte[] p_NewData, int p_ReadIndex, int p_Width, int p_Height, int p_MashIndex, BitmapData p_NewBitmapData)
        {
            Hashtable hashtable = new Hashtable();
            for (int i = 0; i != 16; i++)
            {
                hashtable.Add(i.ToString(), Color.FromArgb(p_FileBytes[p_ReadIndex + 3], p_FileBytes[p_ReadIndex + 2], p_FileBytes[p_ReadIndex + 1], p_FileBytes[p_ReadIndex]));
                p_ReadIndex += 4;
            }
            p_MashIndex = p_Width * p_Height / 2 + p_ReadIndex;
            int num = p_Width / 8;
            if (p_Width % 8 != 0)
            {
                num++;
            }
            if (num % 4 != 0)
            {
                num += num % 4;
            }
            byte[] array = new byte[num * p_Height];
            Array.Copy(p_FileBytes, p_MashIndex, array, 0, array.Length);
            for (int j = 0; j != array.Length; j++)
            {
                array[j] = ReverseByte(array[j]);
            }
            BitArray bitArray = new BitArray(array);
            bool flag = true;
            for (int num2 = p_NewBitmapData.Height - 1; num2 != -1; num2--)
            {
                p_MashIndex = (p_Height - 1 - num2) * (num * 8);
                int num3 = num2 * p_NewBitmapData.Stride;
                for (int k = 0; k != p_NewBitmapData.Width; k++)
                {
                    byte b = p_FileBytes[p_ReadIndex];
                    if (flag)
                    {
                        b = (byte)((b & 0xF0) >> 4);
                        flag = false;
                    }
                    else
                    {
                        b = (byte)(b & 0xF);
                        flag = true;
                        p_ReadIndex++;
                    }
                    Color color = (Color)hashtable[b.ToString()];
                    p_NewData[num3 + k * 4] = color.B;
                    p_NewData[num3 + k * 4 + 1] = color.G;
                    p_NewData[num3 + k * 4 + 2] = color.R;
                    p_NewData[num3 + k * 4 + 3] = byte.MaxValue;
                    if (bitArray[p_MashIndex])
                    {
                        p_NewData[num3 + k * 4 + 3] = 0;
                    }
                    p_MashIndex++;
                }
            }
        }

        private void Load1(byte[] p_FileBytes, byte[] p_NewData, int p_ReadIndex, int p_Width, int p_Height, int p_MashIndex, BitmapData p_NewBitmapData)
        {
            Hashtable hashtable = new Hashtable();
            for (int i = 0; i != 2; i++)
            {
                hashtable.Add(i.ToString(), Color.FromArgb(p_FileBytes[p_ReadIndex + 3], p_FileBytes[p_ReadIndex + 2], p_FileBytes[p_ReadIndex + 1], p_FileBytes[p_ReadIndex]));
                p_ReadIndex += 4;
            }
            p_MashIndex = p_Width * p_Height / 8 + p_ReadIndex;
            int num = p_Width / 8;
            if (p_Width % 8 != 0)
            {
                num++;
            }
            if (num % 4 != 0)
            {
                num += num % 4;
            }
            byte[] array = new byte[num * p_Height];
            Array.Copy(p_FileBytes, p_MashIndex, array, 0, array.Length);
            for (int j = 0; j != array.Length; j++)
            {
                array[j] = ReverseByte(array[j]);
            }
            BitArray bitArray = new BitArray(array);
            int num2 = 7;
            for (int num3 = p_NewBitmapData.Height - 1; num3 != -1; num3--)
            {
                p_MashIndex = (p_Height - 1 - num3) * (num * 8);
                int num4 = num3 * p_NewBitmapData.Stride;
                for (int k = 0; k != p_NewBitmapData.Width; k++)
                {
                    byte b = p_FileBytes[p_ReadIndex];
                    BitArray bitArray2 = new BitArray(new byte[1]
                    {
                        b
                    });
                    b = (byte)(bitArray2[num2] ? 1 : 0);
                    if (num2 == 0)
                    {
                        p_ReadIndex++;
                        num2 = 7;
                    }
                    else
                    {
                        num2--;
                    }
                    Color color = (Color)hashtable[b.ToString()];
                    p_NewData[num4 + k * 4] = color.B;
                    p_NewData[num4 + k * 4 + 1] = color.G;
                    p_NewData[num4 + k * 4 + 2] = color.R;
                    p_NewData[num4 + k * 4 + 3] = byte.MaxValue;
                    if (bitArray[p_MashIndex])
                    {
                        p_NewData[num4 + k * 4 + 3] = 0;
                    }
                    p_MashIndex++;
                }
            }
        }

        private void LoadImgae(short m_Piex, byte[] p_FileBytes, int _StarIndex)
        {
            int imageWidth = m_CurHead.ImageWidth;
            int imageHeight = m_CurHead.ImageHeight;
            m_CurImage = new Bitmap(imageWidth, imageHeight, PixelFormat.Format32bppArgb);
            BitmapData bitmapData = m_CurImage.LockBits(new Rectangle(0, 0, imageWidth, imageHeight), ImageLockMode.ReadWrite, PixelFormat.Format32bppArgb);
            byte[] array = new byte[bitmapData.Stride * bitmapData.Height];
            switch (m_Piex)
            {
                case 1:
                    {
                        int p_MashIndex = imageWidth * imageHeight / 8 + _StarIndex;
                        Load1(p_FileBytes, array, _StarIndex, imageWidth, imageHeight, p_MashIndex, bitmapData);
                        break;
                    }
                case 4:
                    {
                        int p_MashIndex = imageWidth * imageHeight / 2 + _StarIndex;
                        Load4(p_FileBytes, array, _StarIndex, imageWidth, imageHeight, p_MashIndex, bitmapData);
                        break;
                    }
                case 8:
                    {
                        int p_MashIndex = imageWidth * imageHeight + _StarIndex;
                        Load8(p_FileBytes, array, _StarIndex, imageWidth, imageHeight, p_MashIndex, bitmapData);
                        break;
                    }
                case 24:
                    {
                        int p_MashIndex = imageWidth * imageHeight * 3 + _StarIndex;
                        Load24(p_FileBytes, array, _StarIndex, imageWidth, imageHeight, p_MashIndex, bitmapData);
                        break;
                    }
                case 32:
                    {
                        int p_MashIndex = imageWidth * imageHeight * 4 + _StarIndex;
                        Load32(p_FileBytes, array, _StarIndex, imageWidth, imageHeight, p_MashIndex, bitmapData);
                        break;
                    }
                default:
                    throw new Exception("不支持的格式");
            }
            Marshal.Copy(array, 0, bitmapData.Scan0, array.Length);
            m_CurImage.UnlockBits(bitmapData);
        }

        public byte[] StreamToBytes(Stream stream)
        {
            byte[] array = new byte[stream.Length];
            stream.Read(array, 0, array.Length);
            stream.Seek(0L, SeekOrigin.Begin);
            return array;
        }

        public void SaveImage(string p_FileName, bool Argb24)
        {
            if (m_CurList.Count == 0)
            {
                return;
            }
            FileStream fileStream = new FileStream(p_FileName, FileMode.Create, FileAccess.Write);
            m_CurHead = new CurHead();
            fileStream.Write(new byte[4]
            {
                0,
                0,
                2,
                0
            }, 0, 4);
            fileStream.Write(BitConverter.GetBytes((ushort)m_CurList.Count), 0, 2);
            List<byte[]> list = new List<byte[]>();
            m_CurHead.ImageRVA = (uint)(m_CurList.Count * 16 + 6);
            for (int i = 0; i != m_CurList.Count; i++)
            {
                if (m_CurList[i].Width > 255 || m_CurList[i].Height > 255)
                {
                    fileStream.Close();
                    throw new Exception("图形文件过大！");
                }
                byte[] array2 = new byte[10];
                byte[] array = (!Argb24) ? GetImageBytes32(i) : GetImageBytes24(i);
                m_CurHead.ImageHeight = (byte)CurImage[i].Height;
                m_CurHead.ImageWidth = (byte)CurImage[i].Width;
                m_CurHead.ImageRVA += m_CurHead.ImageLength;
                m_CurHead.ImageLength = (uint)array.Length;
                list.Add(array);
                fileStream.Write(m_CurHead.GetImageByte(), 0, 16);
            }
            for (int j = 0; j != list.Count; j++)
            {
                byte[] bytes = BitConverter.GetBytes((uint)(m_CurList[j].Height * 2));
                list[j][8] = bytes[0];
                list[j][9] = bytes[1];
                list[j][10] = bytes[2];
                list[j][11] = bytes[3];
                fileStream.Write(list[j], 0, list[j].Length);
            }
            fileStream.Close();
        }

        public byte[] SaveImage2(bool Argb24 = true)
        {
            if (m_CurList.Count == 0)
            {
                return null;
            }
            Stream stream = new MemoryStream();
            m_CurHead = new CurHead();
            stream.Write(new byte[4]
            {
                0,
                0,
                2,
                0
            }, 0, 4);
            stream.Write(BitConverter.GetBytes((ushort)m_CurList.Count), 0, 2);
            List<byte[]> list = new List<byte[]>();
            m_CurHead.ImageRVA = (uint)(m_CurList.Count * 16 + 6);
            for (int i = 0; i != m_CurList.Count; i++)
            {
                if (m_CurList[i].Width > 255 || m_CurList[i].Height > 255)
                {
                    stream.Close();
                    throw new Exception("图形文件过大！");
                }
                byte[] array2 = new byte[10];
                byte[] array = (!Argb24) ? GetImageBytes32(i) : GetImageBytes24(i);
                m_CurHead.ImageHeight = (byte)CurImage[i].Height;
                m_CurHead.ImageWidth = (byte)CurImage[i].Width;
                m_CurHead.ImageRVA += m_CurHead.ImageLength;
                m_CurHead.ImageLength = (uint)array.Length;
                list.Add(array);
                stream.Write(m_CurHead.GetImageByte(), 0, 16);
            }
            for (int j = 0; j != list.Count; j++)
            {
                byte[] bytes = BitConverter.GetBytes((uint)(m_CurList[j].Height * 2));
                list[j][8] = bytes[0];
                list[j][9] = bytes[1];
                list[j][10] = bytes[2];
                list[j][11] = bytes[3];
                stream.Write(list[j], 0, list[j].Length);
            }
            return StreamToBytes(stream);
        }

        public MemoryStream SaveImage(bool Argb24)
        {
            if (m_CurList.Count == 0)
            {
                throw new Exception("无图形可保存");
            }
            MemoryStream memoryStream = new MemoryStream();
            m_CurHead = new CurHead();
            memoryStream.Write(new byte[4]
            {
                0,
                0,
                2,
                0
            }, 0, 4);
            memoryStream.Write(BitConverter.GetBytes((ushort)m_CurList.Count), 0, 2);
            List<byte[]> list = new List<byte[]>();
            m_CurHead.ImageRVA = (uint)(m_CurList.Count * 16 + 6);
            for (int i = 0; i != m_CurList.Count; i++)
            {
                if (m_CurList[i].Width > 255 || m_CurList[i].Height > 255)
                {
                    memoryStream.Close();
                    throw new Exception("图形文件过大！");
                }
                byte[] array2 = new byte[10];
                byte[] array = (!Argb24) ? GetImageBytes32(i) : GetImageBytes24(i);
                m_CurHead.ImageHeight = (byte)CurImage[i].Height;
                m_CurHead.ImageWidth = (byte)CurImage[i].Width;
                m_CurHead.ImageRVA += m_CurHead.ImageLength;
                m_CurHead.ImageLength = (uint)array.Length;
                list.Add(array);
                memoryStream.Write(m_CurHead.GetImageByte(), 0, 16);
            }
            for (int j = 0; j != list.Count; j++)
            {
                byte[] bytes = BitConverter.GetBytes((uint)(m_CurList[j].Height * 2));
                list[j][8] = bytes[0];
                list[j][9] = bytes[1];
                list[j][10] = bytes[2];
                list[j][11] = bytes[3];
                memoryStream.Write(list[j], 0, list[j].Length);
            }
            return memoryStream;
        }

        private byte[] GetImageBytes24(int p_ImageIndex)
        {
            int width = m_CurList[p_ImageIndex].Width;
            int height = m_CurList[p_ImageIndex].Height;
            Bitmap bitmap = new Bitmap(width, height, PixelFormat.Format32bppArgb);
            Graphics graphics = Graphics.FromImage(bitmap);
            graphics.DrawImage(m_CurList[p_ImageIndex], 0, 0, 32, 32);
            graphics.Dispose();
            BitmapData bitmapData = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);
            byte[] array = new byte[width * height * 3];
            byte[] array2 = new byte[width * height * 4];
            Marshal.Copy(bitmapData.Scan0, array2, 0, array2.Length);
            bitmap.UnlockBits(bitmapData);
            int num = bitmap.Width / 8;
            if (num % 4 != 0)
            {
                num += num % 4;
            }
            byte[] array3 = new byte[num * height];
            int num2 = 0;
            int num3 = 0;
            for (int num4 = height - 1; num4 != -1; num4--)
            {
                int num5 = num3 * num;
                int num6 = num3 * width * 3;
                int num7 = num4 * bitmapData.Stride;
                for (int i = 0; i != width; i++)
                {
                    array[num6] = array2[num7];
                    array[num6 + 1] = array2[num7 + 1];
                    array[num6 + 2] = array2[num7 + 2];
                    if (array2[num7 + 3] == byte.MaxValue)
                    {
                        array3[num5] = (byte)((array3[num5] << 1) & 0xFE);
                    }
                    else
                    {
                        array3[num5] = (byte)((array3[num5] << 1) | 1);
                    }
                    num7 += 4;
                    num6 += 3;
                    num2++;
                    if (num2 % 8 == 0)
                    {
                        num5++;
                        num2 = 0;
                    }
                }
                num3++;
            }
            MemoryStream memoryStream = new MemoryStream();
            Bitmap bitmap2 = new Bitmap(10, 1, PixelFormat.Format24bppRgb);
            bitmap2.Save(memoryStream, ImageFormat.Bmp);
            memoryStream.Position = 14L;
            byte[] array4 = new byte[40];
            memoryStream.Read(array4, 0, 40);
            memoryStream.Dispose();
            MemoryStream memoryStream2 = new MemoryStream();
            byte[] bytes = BitConverter.GetBytes(width);
            byte[] bytes2 = BitConverter.GetBytes(height * 2);
            array4[4] = bytes[0];
            array4[5] = bytes[1];
            array4[6] = bytes[2];
            array4[7] = bytes[3];
            array4[8] = bytes2[0];
            array4[9] = bytes2[1];
            array4[10] = bytes2[2];
            array4[11] = bytes2[3];
            memoryStream2.Write(array4, 0, 40);
            memoryStream2.Write(array, 0, array.Length);
            memoryStream2.Write(array3, 0, array3.Length);
            return memoryStream2.ToArray();
        }

        private byte[] GetImageBytes32(int p_ImageIndex)
        {
            MemoryStream memoryStream = new MemoryStream();
            if (m_CurList[p_ImageIndex].PixelFormat != PixelFormat.Format32bppArgb)
            {
                Bitmap bitmap = new Bitmap(m_CurList[p_ImageIndex].Width, m_CurList[p_ImageIndex].Height, PixelFormat.Format32bppArgb);
                Graphics graphics = Graphics.FromImage(bitmap);
                graphics.RotateTransform(90f);
                graphics.Dispose();
                bitmap.Save(memoryStream, ImageFormat.Bmp);
            }
            else
            {
                m_CurList[p_ImageIndex].Save(memoryStream, ImageFormat.Bmp);
            }
            memoryStream.ToArray();
            byte[] array = new byte[memoryStream.Length - 14];
            memoryStream.Position = 14L;
            memoryStream.Read(array, 0, array.Length);
            return array;
        }

        public static byte ReverseByte(byte p_Byte)
        {
            byte b = 0;
            b = (byte)(b | (byte)((p_Byte & 1) << 7));
            b = (byte)(b | (byte)(((p_Byte >> 1) & 1) << 6));
            b = (byte)(b | (byte)(((p_Byte >> 2) & 1) << 5));
            b = (byte)(b | (byte)(((p_Byte >> 3) & 1) << 4));
            b = (byte)(b | (byte)(((p_Byte >> 4) & 1) << 3));
            b = (byte)(b | (byte)(((p_Byte >> 5) & 1) << 2));
            b = (byte)(b | (byte)(((p_Byte >> 6) & 1) << 1));
            return (byte)(b | (byte)((p_Byte >> 7) & 1));
        }
    }
}
