using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    public static class MagnifierHelp
    {
        public static Rectangle GetActiveScreenBounds0Based()
        {
            return AutoSelectApi.ScreenToClient(GetActiveScreenBounds());
        }

        public static Rectangle GetActiveScreenBounds()
        {
            return Screen.FromPoint(GetCursorPosition()).Bounds;
        }

        public static Point GetCursorPosition()
        {
            if (GetCursorPos(out var lpPoint)) return (Point)lpPoint;
            return Point.Empty;
        }

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool GetCursorPos(out CursorData.POINT lpPoint);
    }
}