using System;

namespace OCRTools
{
    public static class MathHelpers
    {
        public static float Lerp(float value1, float value2, float amount)
        {
            return value1 + (value2 - value1) * amount;
        }

        public static Vector Lerp(Vector pos1, Vector pos2, float amount)
        {
            float x = Lerp(pos1.X, pos2.X, amount);
            float y = Lerp(pos1.Y, pos2.Y, amount);
            return new Vector(x, y);
        }

        public static T Clamp<T>(T num, T min, T max) where T : IComparable<T>
        {
            if (num.CompareTo(min) <= 0) return min;
            if (num.CompareTo(max) >= 0) return max;
            return num;
        }

        public static float DegreeToRadian(float degree)
        {
            return degree * 0.01745329f;
        }

        public static Vector RadianToVector(float radian)
        {
            return new Vector((float)Math.Cos(radian), (float)Math.Sin(radian));
        }

        public static Vector RadianToVector(float radian, float length)
        {
            return RadianToVector(radian) * length;
        }

        public static float LookAtRadian(Vector pos1, Vector pos2)
        {
            return (float)Math.Atan2(pos2.Y - pos1.Y, pos2.X - pos1.X);
        }

        public static float Distance(Vector pos1, Vector pos2)
        {
            return (float)Math.Sqrt(Math.Pow(pos2.X - pos1.X, 2.0) + Math.Pow(pos2.Y - pos1.Y, 2.0));
        }

        public static float LookAtDegree(Vector pos1, Vector pos2)
        {
            return RadianToDegree(LookAtRadian(pos1, pos2));
        }

        public const float RadianPI = 57.29578f; // 180.0 / Math.PI
        public const float DegreePI = 0.01745329f; // Math.PI / 180.0
        public const float TwoPI = 6.28319f; // Math.PI * 2

        public static float RadianToDegree(float radian)
        {
            return radian * RadianPI;
        }
    }
}