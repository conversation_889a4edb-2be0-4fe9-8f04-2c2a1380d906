using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices;
using System.Security.Permissions;
using System.Windows.Forms;

namespace OCRTools
{
    public class Native
    {
        //internal struct Rect
        //{
        //    public int Left;

        //    public int Top;

        //    public int Right;

        //    public int Bottom;
        //}

        internal struct CursorInfo
        {
            public int cbSize;

            public int flags;

            public IntPtr hCursor;

            public PointW ptScreenPos;
        }

        internal struct PointW
        {
            public int X;

            public int Y;
        }

        internal struct Iconinfo
        {
            public bool fIcon;

            public int xHotspot;

            public int yHotspot;

            public IntPtr hbmMask;

            public IntPtr hbmColor;
        }

        internal const int CursorShowing = 1;

        public const int VK_RBUTTON = 2;

        public const int VK_LBUTTON = 1;

        [DllImport("gdi32.dll")]
        internal static extern bool PatBlt(IntPtr hdc, int nXLeft, int nYLeft, int nWidth, int nHeight, uint dwRop);

        [DllImport("gdi32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        internal static extern bool DeleteObject([In] IntPtr hObject);

        [DllImport("user32.dll")]
        internal static extern IntPtr GetDesktopWindow();

        [DllImport("user32.dll")]
        internal static extern IntPtr GetWindowDC(IntPtr ptr);

        [DllImport("gdi32.dll")]
        internal static extern IntPtr CreateCompatibleBitmap(IntPtr hdc, int nWidth, int nHeight);

        [DllImport("user32.dll")]
        internal static extern bool ReleaseDC(IntPtr hWnd, IntPtr hDc);

        [DllImport("gdi32.dll", SetLastError = true)]
        internal static extern IntPtr CreateCompatibleDC([In] IntPtr hdc);

        [DllImport("gdi32.dll")]
        internal static extern IntPtr SelectObject([In] IntPtr hdc, [In] IntPtr hgdiobj);

        [DllImport("user32.dll")]
        public static extern bool PrintWindow(IntPtr hwnd, IntPtr hdcBlt, uint nFlags);

        [DllImport("gdi32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        internal static extern bool BitBlt([In] IntPtr hdc, int nXDest, int nYDest, int nWidth, int nHeight, [In] IntPtr hdcSrc, int nXSrc, int nYSrc, CopyPixelOperation dwRop);

        [DllImport("user32.dll")]
        internal static extern bool GetCursorInfo(out CursorInfo pci);

        [DllImport("user32.dll")]
        internal static extern IntPtr CopyIcon(IntPtr hIcon);

        [DllImport("user32.dll", SetLastError = true)]
        internal static extern bool DestroyIcon(IntPtr hIcon);

        [DllImport("user32.dll")]
        internal static extern bool GetIconInfo(IntPtr hIcon, out Iconinfo piconinfo);

        [DllImport("user32.dll", SetLastError = true)]
        internal static extern bool DrawIconEx(IntPtr hdc, int xLeft, int yTop, IntPtr hIcon, int cxWidth, int cyHeight, int istepIfAniCur, IntPtr hbrFlickerFreeDraw, int diFlags);

        [DllImport("gdi32.dll")]
        internal static extern bool DeleteDC([In] IntPtr hdc);

        public static Bitmap CaptureWithCursor(Size size, int positionX, int positionY)
        {
            int cursorPosX;
            int cursorPosY;
            return CaptureWithCursor(size.Width, size.Height, positionX, positionY, out cursorPosX, out cursorPosY);
        }

        public static Bitmap Capture(Size size, int positionX, int positionY)
        {
            return CaptureWithCursor(size.Width, size.Height, positionX, positionY);
        }

        public static Bitmap CaptureWithCursor(int width, int height, int positionX, int positionY)
        {
            IntPtr desktopWindow = GetDesktopWindow();
            IntPtr windowDC = GetWindowDC(desktopWindow);
            IntPtr hdc = CreateCompatibleDC(windowDC);
            IntPtr intPtr = CreateCompatibleBitmap(windowDC, width, height);
            IntPtr intPtr2 = SelectObject(hdc, intPtr);
            try
            {
                new UIPermission(UIPermissionWindow.AllWindows).Demand();
                BitBlt(hdc, 0, 0, width, height, windowDC, positionX, positionY, (CopyPixelOperation)1087111200);
                Bitmap bitmap = Image.FromHbitmap(intPtr);
                if (bitmap == null)
                {
                    return null;
                }
                return bitmap;
            }
            catch (Exception)
            {
            }
            finally
            {
                SelectObject(hdc, intPtr2);
                DeleteObject(intPtr);
                DeleteObject(intPtr2);
                DeleteDC(hdc);
                ReleaseDC(desktopWindow, windowDC);
            }
            return null;
        }

        public static Bitmap GetWindow(IntPtr hWnd)
        {
            IntPtr windowDC = GetWindowDC(hWnd);
            Control control = Control.FromHandle(hWnd);
            IntPtr intPtr = CreateCompatibleBitmap(windowDC, control.Width, control.Height);
            IntPtr intPtr2 = CreateCompatibleDC(windowDC);
            SelectObject(intPtr2, intPtr);
            PrintWindow(hWnd, intPtr2, 0u);
            Bitmap result = Image.FromHbitmap(intPtr);
            DeleteDC(windowDC);
            DeleteDC(intPtr2);
            return result;
        }

        public static Bitmap captureControl(Control control)
        {
            IntPtr windowDC = GetWindowDC(control.Handle);
            IntPtr hdc = CreateCompatibleDC(windowDC);
            IntPtr intPtr = CreateCompatibleBitmap(windowDC, control.Width, control.Height);
            IntPtr hgdiobj = SelectObject(hdc, intPtr);
            if (BitBlt(hdc, 0, 0, control.Width, control.Height, windowDC, 0, 0, (CopyPixelOperation)1087111200))
            {
                Bitmap result = Image.FromHbitmap(intPtr);
                SelectObject(hdc, hgdiobj);
                DeleteObject(intPtr);
                DeleteDC(hdc);
                ReleaseDC(control.Handle, windowDC);
                return result;
            }
            return null;
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, ExactSpelling = true)]
        public static extern short GetAsyncKeyState(int vkey);

        public static Bitmap CaptureWithCursor(int width, int height, int positionX, int positionY, out int cursorPosX, out int cursorPosY)
        {
            IntPtr desktopWindow = GetDesktopWindow();
            IntPtr windowDC = GetWindowDC(desktopWindow);
            IntPtr hdc = CreateCompatibleDC(windowDC);
            IntPtr intPtr = CreateCompatibleBitmap(windowDC, width, height);
            IntPtr intPtr2 = SelectObject(hdc, intPtr);
            cursorPosX = (cursorPosY = -1);
            try
            {
                new UIPermission(UIPermissionWindow.AllWindows).Demand();
                BitBlt(hdc, 0, 0, width, height, windowDC, positionX, positionY, (CopyPixelOperation)1087111200);
                Iconinfo iconinfo = default(Iconinfo);
                CursorInfo cursorInfo = default(CursorInfo);
                Point point = new Point(0, 0);
                try
                {
                    CursorInfo pci = default(CursorInfo);
                    pci.cbSize = Marshal.SizeOf(pci);
                    if (GetCursorInfo(out pci))
                    {
                        if (pci.flags == 1)
                        {
                            IntPtr intPtr3 = CopyIcon(pci.hCursor);
                            if (intPtr3 != IntPtr.Zero)
                            {
                                if (GetIconInfo(intPtr3, out Iconinfo piconinfo))
                                {
                                    cursorPosX = pci.ptScreenPos.X - positionX;
                                    cursorPosY = pci.ptScreenPos.Y - positionY;
                                    point = new Point(cursorPosX, cursorPosY);
                                    if (cursorPosX > 0 && cursorPosY > 0)
                                    {
                                        iconinfo = piconinfo;
                                        cursorInfo = pci;
                                        if (GetAsyncKeyState(1) == 0 && GetAsyncKeyState(2) == 0)
                                        {
                                            DrawIconEx(hdc, cursorPosX - piconinfo.xHotspot, cursorPosY - piconinfo.yHotspot, pci.hCursor, 0, 0, 0, IntPtr.Zero, 3);
                                        }
                                    }
                                }
                                DeleteObject(piconinfo.hbmColor);
                                DeleteObject(piconinfo.hbmMask);
                            }
                            DestroyIcon(intPtr3);
                        }
                        DeleteObject(pci.hCursor);
                    }
                }
                catch (Exception)
                {
                }
                Bitmap bitmap = Image.FromHbitmap(intPtr);
                if (bitmap == null)
                {
                    return null;
                }
                if (GetAsyncKeyState(1) != 0 || GetAsyncKeyState(2) != 0)
                {
                    using (Graphics graphics = Graphics.FromImage(bitmap))
                    {
                        using (Pen pen = new Pen(Color.FromArgb(120, 120, 120), 1f))
                        {
                            using (Pen pen2 = new Pen(Color.White, 1f))
                            {
                                graphics.SmoothingMode = SmoothingMode.HighQuality;
                                graphics.DrawEllipse(pen, point.X - 10.DPIValue(), point.Y - 10.DPIValue(), 20.DPIValue(), 20.DPIValue());
                                graphics.DrawEllipse(pen2, point.X - 9.DPIValue(), point.Y - 9.DPIValue(), 18.DPIValue(), 18.DPIValue());
                                IntPtr hdc2 = graphics.GetHdc();
                                if (cursorPosX > 0 && cursorPosY > 0)
                                {
                                    DrawIconEx(hdc2, point.X - iconinfo.xHotspot, point.Y - iconinfo.yHotspot, cursorInfo.hCursor, 0, 0, 0, IntPtr.Zero, 3);
                                    graphics.ReleaseHdc(hdc2);
                                }
                                DeleteObject(iconinfo.hbmColor);
                                DeleteObject(iconinfo.hbmMask);
                                DeleteObject(cursorInfo.hCursor);
                            }
                        }
                    }
                }
                return bitmap;
            }
            catch (Exception)
            {
            }
            finally
            {
                SelectObject(hdc, intPtr2);
                DeleteObject(intPtr);
                DeleteObject(intPtr2);
                DeleteDC(hdc);
                ReleaseDC(desktopWindow, windowDC);
            }
            return null;
        }
    }
}
