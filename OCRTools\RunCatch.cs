using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Globalization;
using System.IO;
using System.Windows.Forms;

namespace OCRTools
{
    public class RunCatch
    {
        public string OCRresult;

        private string temppaste = "";

        public string TempPath
        {
            get;
            set;
        }

        public string ModeGif
        {
            get;
            set;
        }

        public RunCatch(Color color, IntPtr intPtr, IntPtr intPtr2)
        {
            StaticValue.MainintPtr = intPtr;
            StaticValue.MainintPtrback = intPtr2;
            StaticValue.ThemeColor = color;
        }

        public void init()
        {
            StaticValue.TempPath = TempPath;
        }

        public Rectangle QuickRectCatch(bool getcolor = true)
        {
            if (!StaticValue.isCatchScreen)
            {
                using (DrawArea drawArea = new DrawArea())
                {
                    drawArea.IsShowCross = false;
                    drawArea.IsEdit = false;
                    drawArea.Prepare();
                    drawArea.IsRGB = getcolor;
                    drawArea.isShowZoom = false;
                    drawArea.ActiveTool = DrawToolType.QuickCatch;
                    StaticValue.current_ToolType = DrawToolType.QuickCatch;
                    drawArea.Status = "文字识别";
                    Rectangle result = default(Rectangle);
                    if (drawArea.ShowDialog() == DialogResult.OK)
                    {
                        StaticValue.isCatchScreen = false;
                        if (drawArea.Status == "文字识别")
                        {
                            StaticValue.Catch_Rectangle.Location = drawArea.PointToScreen(StaticValue.Catch_Rectangle.Location);
                            return StaticValue.Catch_Rectangle.SizeOffset(1, 1);
                        }
                        return result;
                    }
                }
            }
            return default(Rectangle);
        }

        public Image QuickCatch(bool getcolor = true)
        {
            if (!StaticValue.isCatchScreen)
            {
                DrawArea @catch = new DrawArea();
                try
                {
                    @catch.UnshowZoom = false;
                    @catch.IsShowCross = false;
                    @catch.IsEdit = false;
                    @catch.Prepare();
                    @catch.IsRGB = getcolor;
                    @catch.ActiveTool = DrawToolType.QuickCatch;
                    StaticValue.current_ToolType = DrawToolType.QuickCatch;
                    @catch.Status = "文字识别";
                    Image result = null;
                    if (@catch.ShowDialog() == DialogResult.OK)
                    {
                        StaticValue.isCatchScreen = false;
                        if (@catch.Status == "文字识别")
                        {
                            result = @catch.GetResultImage();
                        }
                    }
                }
                finally
                {
                    if (@catch != null)
                    {
                        ((IDisposable)@catch).Dispose();
                    }
                }
            }
            return null;
        }

        public Image OCRCatch(bool getcolor = true, bool iscross = false)
        {
            if (!StaticValue.isCatchScreen)
            {
                DrawArea @catch = new DrawArea();
                try
                {
                    @catch.IsShowCross = iscross;
                    @catch.IsEdit = false;
                    @catch.UnshowZoom = iscross;
                    @catch.Prepare();
                    @catch.IsRGB = getcolor;
                    @catch.ActiveTool = DrawToolType.QuickCatch;
                    StaticValue.current_ToolType = DrawToolType.QuickCatch;
                    @catch.Status = "OCR增强";
                    Image result = null;
                    if (@catch.ShowDialog() == DialogResult.OK)
                    {
                        StaticValue.isCatchScreen = false;
                        if (@catch.Status == "文字识别")
                        {
                            result = @catch.GetResultImage();
                        }
                        @catch.Close();
                        @catch.Dispose();
                        return result;
                    }
                }
                finally
                {
                    if (@catch != null)
                    {
                        ((IDisposable)@catch).Dispose();
                    }
                }
            }
            return null;
        }

        public Image Catch(bool getcolor, out string flag)
        {
            flag = "";
            if (!StaticValue.isCatchScreen)
            {
                using (DrawArea drawArea = new DrawArea())
                {
                    drawArea.IsShowCross = true;
                    drawArea.IsEdit = true;
                    drawArea.Prepare();
                    drawArea.IsRGB = getcolor;
                    drawArea.ActiveTool = DrawToolType.Catch;
                    StaticValue.current_ToolType = DrawToolType.Catch;
                    drawArea.Status = "截图";
                    Image image = null;
                    if (drawArea.ShowDialog() == DialogResult.OK)
                    {
                        OCRresult = drawArea.OCRresult;
                        StaticValue.isCatchScreen = false;
                        image = drawArea.GetResultImage();
                        if (drawArea.IsAnyModifierPressed(KeyModifiers.Ctrl))
                        {
                            Image image2 = image;
                            image = RenderHelper.ImageAddshadow(image2);
                        }
                        if (drawArea.Status == "上传")
                        {
                            flag = "上传";
                        }
                    }
                    drawArea.Close();
                    drawArea.Dispose();
                    return image;
                }
            }
            return null;
        }

        public Image QuickCatchCross(bool getcolor = true)
        {
            if (!StaticValue.isCatchScreen)
            {
                DrawArea @catch = new DrawArea();
                try
                {
                    @catch.IsShowCross = true;
                    @catch.UnshowZoom = true;
                    @catch.IsEdit = false;
                    @catch.Prepare();
                    @catch.IsRGB = getcolor;
                    StaticValue.current_ToolType = DrawToolType.QuickCatch;
                    @catch.Status = "文字识别";
                    Image result = null;
                    if (@catch.ShowDialog() == DialogResult.OK)
                    {
                        StaticValue.isCatchScreen = false;
                        if (@catch.Status == "文字识别")
                        {
                            result = @catch.GetResultImage();
                        }
                        @catch.Close();
                        @catch.Dispose();
                        return result;
                    }
                }
                finally
                {
                    if (@catch != null)
                    {
                        ((IDisposable)@catch).Dispose();
                    }
                }
            }
            return null;
        }

        public void QuickGif()
        {
            if (!StaticValue.isCatchScreen)
            {
                StaticValue.current_ToolType = DrawToolType.Catch;
                using (DrawArea drawArea = new DrawArea())
                {
                    drawArea.IsShowCross = false;
                    drawArea.IsEdit = false;
                    drawArea.Prepare();
                    drawArea.Status = "录制";
                    drawArea.GifPrepare();
                    if (drawArea.ShowDialog() == DialogResult.OK)
                    {
                        StaticValue.isCatchScreen = false;
                    }
                    drawArea.Close();
                    drawArea.Dispose();
                }
            }
        }

        public void MultiCatch()
        {
            if (!StaticValue.isCatchScreen)
            {
                StaticValue.current_ToolType = DrawToolType.MultiCatch;
                using (DrawArea drawArea = new DrawArea())
                {
                    drawArea.IsEdit = true;
                    drawArea.IsMulti = true;
                    drawArea.Prepare();
                    drawArea.Status = "截图";
                    if (drawArea.ShowDialog() == DialogResult.OK)
                    {
                        StaticValue.isCatchScreen = false;
                    }
                    drawArea.Close();
                    drawArea.Dispose();
                }
            }
        }

        public List<Image> MultiCatchImages()
        {
            if (!StaticValue.isCatchScreen)
            {
                StaticValue.current_ToolType = DrawToolType.MultiCatch;
                using (DrawArea drawArea = new DrawArea())
                {
                    drawArea.IsEdit = true;
                    drawArea.IsMulti = true;
                    drawArea.Prepare();
                    drawArea.Status = "截图";
                    if (drawArea.ShowDialog() == DialogResult.OK)
                    {
                        StaticValue.isCatchScreen = false;
                        return drawArea.GetImages();
                    }
                    drawArea.Close();
                    drawArea.Dispose();
                }
            }
            return null;
        }

        public bool RGB(string str, out Color color)
        {
            if (str == "" || str == null)
            {
                color = Color.FromArgb(0, 0, 0);
                return false;
            }
            string text = str.Trim();
            str = str.Replace("，", ",").Replace("#", "");
            color = Color.FromArgb(0, 0, 0);
            string[] array = str.Split(',');
            if (array.Length == 3)
            {
                try
                {
                    int red = Convert.ToInt32(array[0]);
                    int green = Convert.ToInt32(array[1]);
                    int blue = Convert.ToInt32(array[2]);
                    color = Color.FromArgb(red, green, blue);
                    return true;
                }
                catch
                {
                }
            }
            else
            {
                try
                {
                    if (str.Length == 6 && text.Substring(0, 1) == "#")
                    {
                        int red = int.Parse(str.Substring(0, 2), NumberStyles.AllowHexSpecifier);
                        int green = int.Parse(str.Substring(2, 2), NumberStyles.AllowHexSpecifier);
                        int blue = int.Parse(str.Substring(4, 2), NumberStyles.AllowHexSpecifier);
                        color = Color.FromArgb(red, green, blue);
                        return true;
                    }
                    if (str.Length == 8 && text.Substring(0, 1) == "#")
                    {
                        int alpha = int.Parse(str.Substring(0, 2), NumberStyles.AllowHexSpecifier);
                        int red = int.Parse(str.Substring(2, 2), NumberStyles.AllowHexSpecifier);
                        int green = int.Parse(str.Substring(4, 2), NumberStyles.AllowHexSpecifier);
                        int blue = int.Parse(str.Substring(6, 2), NumberStyles.AllowHexSpecifier);
                        color = Color.FromArgb(alpha, red, green, blue);
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            }
            return false;
        }

        public static Bitmap CreatColorPanel(Color color)
        {
            Bitmap bitmap = new Bitmap(150.DPIValue(), 60.DPIValue());
            using (Graphics graphics = Graphics.FromImage(bitmap))
            {
                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    using (SolidBrush brush2 = new SolidBrush(color))
                    {
                        StringFormat stringFormat = new StringFormat
                        {
                            LineAlignment = StringAlignment.Center,
                            Alignment = StringAlignment.Near
                        };
                        Font font = new Font("微软雅黑", 9f, FontStyle.Regular);
                        Rectangle rect = new Rectangle(0, 0, bitmap.Width, bitmap.Height);
                        rect.SizeOffset(2, 2);
                        rect.Offset(-1, -1);
                        graphics.FillRectangle(brush, new Rectangle(-1, -1, rect.Width + 2, rect.Height + 2));
                        Rectangle rect2 = new Rectangle(-1, -1, rect.Width + 2, rect.Height / 6);
                        graphics.FillRectangle(brush2, rect2);
                        graphics.DrawString(layoutRectangle: new Rectangle(0, rect.Height / 4, rect.Width, rect.Height / 3), s: "  RGB:   " + GetInfoText_color(color), font: font, brush: new SolidBrush(Color.Black), format: stringFormat);
                        graphics.DrawString(layoutRectangle: new Rectangle(0, rect.Height / 3 + rect.Height / 4, rect.Width, rect.Height / 3), s: "  HEX:   " + GetInfoText_HEXcolor(color), font: font, brush: new SolidBrush(Color.Black), format: stringFormat);
                    }
                }
            }
            return bitmap;
        }

        public static string GetInfoText_HEXcolor(Color currentColor)
        {
            return "#" + ColorToHex(currentColor).ToUpper();
        }

        public static string ColorToHex(Color color)
        {
            return $"{color.R:X2}{color.G:X2}{color.B:X2}";
        }

        public static string GetInfoText_color(Color currentColor)
        {
            return string.Concat(new object[5]
            {
                currentColor.R.ToString(),
                " , ",
                currentColor.G.ToString(),
                " , ",
                currentColor.B.ToString()
            });
        }

        public static Image ReadImageFile(string path)
        {
            string extension = Path.GetExtension(path);
            string text = StaticValue.TempPath + DateTime.Now.ToString("yyyyMMddHHmmss") + extension;
            File.Copy(path, text, overwrite: true);
            return Image.FromFile(text);
        }

        public static string ImageCompareString(Image firstImage)
        {
            MemoryStream memoryStream = new MemoryStream();
            firstImage.Save(memoryStream, ImageFormat.Png);
            return Convert.ToBase64String(memoryStream.ToArray());
        }

        //public void Paste()
        //{
        //    IDataObject dataObject = Clipboard.GetDataObject();
        //    if (Clipboard.ContainsFileDropList())
        //    {
        //        StringCollection fileDropList = Clipboard.GetFileDropList();
        //        string extension = Path.GetExtension(fileDropList[0].ToLower());
        //        if (extension.Contains("png") || extension.Contains("jpeg") || extension.Contains("jpg") || extension.Contains("bmp") || extension.Contains("gif"))
        //        {
        //            try
        //            {
        //                Image img = ReadImageFile(fileDropList[0]);
        //                var fmScreenPaste = new FrmPasteScreen(img);
        //                fmScreenPaste.Show();
        //            }
        //            catch
        //            {
        //            }
        //            return;
        //        }
        //    }
        //    if (dataObject.GetDataPresent(DataFormats.Bitmap))
        //    {
        //        Bitmap img2 = (Bitmap)dataObject.GetData(DataFormats.Bitmap);
        //        this.ViewImage(img2);
        //    }
        //    else if (dataObject.GetDataPresent(DataFormats.Text))
        //    {
        //        string text = (string)dataObject.GetData(DataFormats.Text);
        //        if (RGB(text, out Color color))
        //        {
        //            ColorValue.color = color;
        //            ColorValue.colorRgb = GetInfoText_color(color);
        //            ColorValue.colorHex = GetInfoText_HEXcolor(color);
        //            FmPastColor fmPastColor = new FmPastColor(CreatColorPanel(color));
        //            fmPastColor.Show();
        //        }
        //    }
        //}
    }
}
