using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class SilverFm : Form
    {
        private bool leftFlag;

        private Point mouseOff;

        private IContainer components = null;

        public SilverFm()
        {
            InitializeComponent();
            base.AutoScaleMode = AutoScaleMode.Font;
            Font = new Font("微软雅黑", 10f);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                mouseOff = new Point(-e.X, -e.Y);
                leftFlag = true;
            }
            base.OnMouseDown(e);
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            if (leftFlag)
            {
                Point mousePosition = Control.MousePosition;
                mousePosition.Offset(mouseOff.X, mouseOff.Y);
                base.Location = mousePosition;
            }
            base.OnMouseMove(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            if (leftFlag)
            {
                leftFlag = false;
            }
            base.OnMouseUp(e);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            OnPaintBackground(e);
            Graphics graphics = e.Graphics;
            using (Pen pen = new Pen(Color.Silver))
            {
                using (SolidBrush brush = new SolidBrush(BackColor))
                {
                    graphics.FillRectangle(brush, base.ClientRectangle);
                    graphics.DrawRectangle(pen, base.ClientRectangle.SizeOffset(-1, -1));
                }
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            SuspendLayout();
            BackColor = System.Drawing.Color.White;
            base.ClientSize = new System.Drawing.Size(341, 147);
            base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            base.Name = "SilverFm";
            base.ShowIcon = false;
            base.ShowInTaskbar = false;
            Text = "SilverFm";
            ResumeLayout(false);
        }
    }
}
