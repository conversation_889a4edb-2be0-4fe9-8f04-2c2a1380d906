using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    internal class TextboxEx : TextBox
    {
        private UpDownButtonEx _upDownButtonExT;

        public TextboxEx()
        {
            Text = 1.ToString();
        }

        public void Init(UpDownButtonEx upDownButtonEx)
        {
            _upDownButtonExT = upDownButtonEx;
            Text = 1.ToString();
        }

        [DllImport("user32.dll")]
        private static extern int ReleaseDC(IntPtr hwnd, IntPtr hDc);

        [DllImport("user32.dll")]
        private static extern IntPtr GetWindowDC(IntPtr hWnd);

        protected override void OnTextChanged(EventArgs e)
        {
            const int num = 100;
            if (!string.IsNullOrEmpty(Text))
            {
                if (int.Parse(Text) > num) Text = (num - 1).ToString();
                if (int.Parse(Text) < 1) Text = 1.ToString();
                if (_upDownButtonExT != null)
                {
                    _upDownButtonExT.Text = Text;
                    _upDownButtonExT.Change();
                }
            }
        }

        protected override void OnKeyPress(KeyPressEventArgs e)
        {
            if (e.KeyChar != '\b' && !char.IsDigit(e.KeyChar)) e.Handled = true;
        }

        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            if (m.Msg != 15 && m.Msg != 307) return;
            var windowDc = GetWindowDC(m.HWnd);
            if (windowDc.ToInt32() != 0)
            {
                if (BorderStyle == BorderStyle.FixedSingle)
                {
                    var pen = new Pen(Color.Silver, 1f);
                    var graphics = Graphics.FromHdc(windowDc);
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawRectangle(pen, 0, 0, Width - 1, Height - 1);
                    pen.Dispose();
                }

                m.Result = IntPtr.Zero;
                ReleaseDC(m.HWnd, windowDc);
            }
        }
    }
}