using System.Windows.Forms;

namespace OCRTools
{
    internal abstract class Tool
    {
        public virtual void OnMouseDown(Draw<PERSON><PERSON> drawArea, Mouse<PERSON>ventArgs e)
        {
        }

        public virtual void OnMouseMove(<PERSON><PERSON><PERSON> drawArea, MouseEventArgs e)
        {
        }

        public virtual void MouseDoubleClick(Draw<PERSON><PERSON> drawArea, MouseEventArgs e)
        {
        }

        public virtual void OnMouseUp(Draw<PERSON><PERSON> drawArea, MouseEventArgs e)
        {
        }
    }
}