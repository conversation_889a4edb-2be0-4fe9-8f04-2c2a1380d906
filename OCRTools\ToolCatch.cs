using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolCatch : ToolObject
    {
        private DrawCatch _drawCatch;

        private bool _isdraw;

        private Point _point;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            _point = e.Location;
            if (drawArea.Cursor != CursorEx.Cross && _drawCatch != null)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = DrawToolType.Pointer;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                drawArea.Refresh();
                _drawCatch = new DrawCatch(e.X, e.Y, 1, 1);
                drawArea.Catch = _drawCatch;
                NewObject(drawArea, _drawCatch);
                _drawCatch.ChangeRect(e.X, e.Y, 1, 1);
                _isdraw = true;
            }
        }

        protected void NewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.graphicsList.Insert(0, o);
            drawArea.Capture = true;
            o.Selected = true;
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            var num = Math.Abs(e.X - _point.X);
            var num2 = Math.Abs(e.Y - _point.Y);
            if (e.Button == MouseButtons.Left && _drawCatch != null)
            {
                _drawCatch.IsSelected = true;
                if (num > 10 && num2 > 10 && _isdraw)
                {
                    drawArea.isAutoDraw = false;
                    using (new AutomaticCanvasRefresher(drawArea, _drawCatch.GetAddBound))
                    {
                        var obj = _drawCatch;
                        using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                        {
                            _drawCatch.MoveHandleTo(e.Location, 5);
                        }
                    }
                }
            }
        }

        public override void MouseDoubleClick(DrawArea drawArea, MouseEventArgs e)
        {
            drawArea.Copy();
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawCatch != null)
            {
                _isdraw = false;
                drawArea.isAutoDraw = false;
                drawArea.isShowZoom = false;
                drawArea.IsShowCross = false;
                StaticValue.CurrentRectangle = _drawCatch.Rectangle;
                if (Math.Abs(e.X - _point.X) < 10 && Math.Abs(e.Y - _point.Y) < 10)
                {
                    _drawCatch = new DrawCatch(drawArea.AutoRect.SizeOffset(-1));
                    StaticValue.CurrentRectangle = drawArea.AutoRect;
                    drawArea.GraphicsList.RemoveAt(0);
                    drawArea.GraphicsList.Add(_drawCatch);
                }

                _drawCatch.Normalize();
                drawArea.AddCommandToHistory(new CommandAdd(_drawCatch));
                drawArea.Refresh();
                _drawCatch.IsCatchMove = true;
                drawArea.ShowTool(_drawCatch);
                drawArea.ActiveTool = DrawToolType.Rectangle;
            }
        }
    }
}