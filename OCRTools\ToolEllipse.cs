using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolEllipse : ToolRectangle
    {
        private DrawObject _drawEllipse;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                _drawEllipse = new DrawEllipse(e.X, e.Y, 1, 1);
                AddNewObject(drawArea, _drawEllipse);
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (_drawEllipse == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                _drawEllipse.IsSelected = true;
                var drawObject = _drawEllipse;
                using (new AutomaticCanvasRefresher(drawArea, drawObject.GetBoundingBox))
                {
                    _drawEllipse.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawEllipse != null)
            {
                StaticValue.CurrentRectangle = _drawEllipse.Rectangle;
                if (!_drawEllipse.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var drawObject = _drawEllipse;
                using (new AutomaticCanvasRefresher(drawArea, drawObject.GetBoundingBox))
                {
                    _drawEllipse.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(_drawEllipse));
                }
            }
        }
    }
}