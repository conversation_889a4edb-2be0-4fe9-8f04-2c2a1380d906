using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolLine : ToolObject
    {
        private DrawLine _drawLine;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                _drawLine = new DrawLine(e.X, e.Y, e.X + 1, e.Y + 1);
                AddNewObject(drawArea, _drawLine);
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (_drawLine == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                _drawLine.IsSelected = true;
                var obj = _drawLine;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawLine.MoveHandleTo(e.Location, 2, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawLine != null)
            {
                StaticValue.CurrentRectangle = _drawLine.Rectangle;
                if (!_drawLine.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = _drawLine;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawLine.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(_drawLine));
                }
            }
        }
    }
}