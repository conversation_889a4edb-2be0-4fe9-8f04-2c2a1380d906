using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolMultiCatch : ToolObject
    {
        private DrawMultiCatch _drawRectangle;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = DrawToolType.MultiCatch;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else if (drawArea.GraphicsList.Count < 99)
            {
                _drawRectangle = new DrawMultiCatch(e.X, e.Y, 1, 1);
                AddNewObject(drawArea, _drawRectangle);
                var num = drawArea.NumberArry[drawArea.GraphicsList.Count - 1];
                _drawRectangle.Text = num.ToString();
            }
        }

        protected new void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            o.Selected = true;
            drawArea.Refresh();
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (_drawRectangle == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                _drawRectangle.IsSelected = true;
                var drawMultiCatch = _drawRectangle;
                using (new AutomaticCanvasRefresher(drawArea, drawMultiCatch.GetBoundingBox))
                {
                    _drawRectangle.MoveHandleTo(e.Location, 5);
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawRectangle != null)
            {
                StaticValue.CurrentRectangle = _drawRectangle.Rectangle;
                if (!_drawRectangle.Rectangle.IsLimt())
                {
                    _drawRectangle.Selected = false;
                    drawArea.GraphicsList.RemoveAt(0);
                }
                else
                {
                    var drawMultiCatch = _drawRectangle;
                    using (new AutomaticCanvasRefresher(drawArea, drawMultiCatch.GetBoundingBox))
                    {
                        _drawRectangle.Normalize();
                        drawArea.AddCommandToHistory(new CommandAdd(_drawRectangle));
                    }

                    drawArea.ShowMultiTool(_drawRectangle);
                }

                drawArea.ActiveTool = DrawToolType.MultiCatch;
                StaticValue.CurrentToolType = DrawToolType.MultiCatch;
            }
        }
    }
}