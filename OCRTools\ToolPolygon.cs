using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolPolygon : ToolObject
    {
        private int _lastX;

        private int _lastY;

        private DrawPolygon _newPolygon;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                _newPolygon = new DrawPolygon(e.X, e.Y, e.X + 1, e.Y + 1);
                AddNewObject(drawArea, _newPolygon);
                _lastX = e.X;
                _lastY = e.Y;
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (_newPolygon == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                _newPolygon.IsSelected = true;
                var drawPolygon = _newPolygon;
                using (new AutomaticCanvasRefresher(drawArea, drawPolygon.GetBoundingBox))
                {
                    var point = new Point(e.X, e.Y);
                    var num = (e.X - _lastX) * (e.X - _lastX) + (e.Y - _lastY) * (e.Y - _lastY);
                    if (num < 5)
                    {
                        _newPolygon.MoveHandleTo(point, _newPolygon.HandleCount);
                    }
                    else
                    {
                        _newPolygon.AddPoint(point);
                        _lastX = e.X;
                        _lastY = e.Y;
                    }
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_newPolygon != null)
            {
                var array = _newPolygon.pointArray.ToArray();
                if (array.Length <= 2)
                {
                    drawArea.GraphicsList.RemoveAt(0);
                }
                else
                {
                    _newPolygon.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(_newPolygon));
                }

                drawArea.Refresh();
            }
        }
    }
}