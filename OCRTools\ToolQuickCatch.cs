using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolQuickCatch : ToolObject
    {
        private DrawQuickCatch _drawCatch;

        private bool _isfill = true;

        private Point _point;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            _point = e.Location;
            _drawCatch = new DrawQuickCatch(e.X, e.Y, 1, 1);
            drawArea.Catch = _drawCatch;
            NewObject(drawArea, _drawCatch);
            _drawCatch.ChangeRect(e.X, e.Y, 1, 1);
            drawArea.isAutoDraw = false;
            if (drawArea.IsShowCross)
            {
                drawArea.Cursor = CursorEx.Cross;
                drawArea.IsShowCross = false;
            }
        }

        protected void NewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.graphicsList.Insert(0, o);
            drawArea.Capture = true;
            o.Selected = true;
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            var num = Math.Abs(e.X - _point.X);
            var num2 = Math.Abs(e.Y - _point.Y);
            if (e.Button != MouseButtons.Left) return;
            if (_drawCatch == null)
            {
                drawArea.ActiveTool = DrawToolType.Text;
                return;
            }

            _drawCatch.IsSelected = true;
            if (num > 10 && num2 > 10)
            {
                if (_isfill)
                {
                    _drawCatch.MoveHandleTo(e.Location, 5);
                    drawArea.Refresh();
                    _isfill = false;
                }
                else
                {
                    using (new AutomaticCanvasRefresher(drawArea, _drawCatch.GetAddBound))
                    {
                        var drawQuickCatch = _drawCatch;
                        using (new AutomaticCanvasRefresher(drawArea, drawQuickCatch.GetBoundingBox))
                        {
                            _drawCatch.MoveHandleTo(e.Location, 5);
                        }
                    }
                }
            }
            else if (!_isfill)
            {
                using (new AutomaticCanvasRefresher(drawArea, _drawCatch.GetAddBound))
                {
                    var drawQuickCatch2 = _drawCatch;
                    using (new AutomaticCanvasRefresher(drawArea, drawQuickCatch2.GetBoundingBox))
                    {
                        _drawCatch.MoveHandleTo(e.Location, 5);
                    }
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            drawArea.isShowZoom = false;
            if (_drawCatch != null)
            {
                StaticValue.CurrentRectangle = _drawCatch.Rectangle;
                if (Math.Abs(e.X - _point.X) < 10 && Math.Abs(e.Y - _point.Y) < 10)
                {
                    _drawCatch = new DrawQuickCatch(drawArea.AutoRect.SizeOffset(-1));
                    StaticValue.CurrentRectangle = drawArea.AutoRect.SizeOffset(-1);
                    drawArea.GraphicsList.RemoveAt(0);
                    drawArea.GraphicsList.Add(_drawCatch);
                    StaticValue.catchRectangle = StaticValue.CurrentRectangle;
                }

                _drawCatch.Normalize();
                drawArea.AddCommandToHistory(new CommandAdd(_drawCatch));
                StaticValue.CurrentToolType = DrawToolType.QuickCatch;
                drawArea.Closing();
            }
        }
    }
}