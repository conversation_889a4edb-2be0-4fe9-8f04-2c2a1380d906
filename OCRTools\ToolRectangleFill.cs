using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolRectangleFill : ToolObject
    {
        private DrawRectangleFill _drawRectangleFill;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                _drawRectangleFill = new DrawRectangleFill(e.X, e.Y, 1, 1)
                {
                    BackgroundImageEx = drawArea.BackgroundImageEx
                };
                AddNewObject(drawArea, _drawRectangleFill);
            }
        }

        protected new void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            o.Selected = true;
            drawArea.Refresh();
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (_drawRectangleFill == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                _drawRectangleFill.IsSelected = true;
                var obj = _drawRectangleFill;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawRectangleFill.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawRectangleFill != null)
            {
                StaticValue.CurrentRectangle = _drawRectangleFill.Rectangle;
                _drawRectangleFill.IsCache = true;
                if (!_drawRectangleFill.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = _drawRectangleFill;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawRectangleFill.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(_drawRectangleFill));
                }
            }
        }
    }
}