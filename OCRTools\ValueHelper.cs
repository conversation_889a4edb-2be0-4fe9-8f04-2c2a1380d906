using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    internal static class ValueHelper
    {
        public const int MK_LBUTTON = 1;

        [DllImport("user32.dll")]
        public static extern int SendMessage(IntPtr hWnd, int message, IntPtr wParam, IntPtr lParam);

        public static void SendMsg(this Control MControl, Point point)
        {
            Point p = default(Point);
            p.X = MControl.PointToScreen(point).X - 1;
            p.Y = MControl.PointToScreen(point).Y - 1;
            p = MControl.Parent.PointToClient(p);
            SendMessage(MControl.Parent.Handle, 513, (IntPtr)1, (IntPtr)(p.Y * 65536 + p.X));
        }
    }
}
