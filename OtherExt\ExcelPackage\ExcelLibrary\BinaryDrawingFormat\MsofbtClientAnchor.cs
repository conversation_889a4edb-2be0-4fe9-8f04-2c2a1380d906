using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtClientAnchor : EscherRecord
	{
		public ushort Flag;

		public ushort Col1;

		public ushort DX1;

		public ushort Row1;

		public ushort DY1;

		public ushort Col2;

		public ushort DX2;

		public ushort Row2;

		public ushort DY2;

		public byte[] ExtraData;

		public MsofbtClientAnchor(EscherRecord record)
			: base(record)
		{
		}

		public MsofbtClientAnchor()
		{
			Type = 61456;
		}

		public override void Decode()
		{
			MemoryStream memoryStream = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(memoryStream);
			Flag = binaryReader.ReadUInt16();
			Col1 = binaryReader.ReadUInt16();
			DX1 = binaryReader.ReadUInt16();
			Row1 = binaryReader.ReadUInt16();
			DY1 = binaryReader.ReadUInt16();
			Col2 = binaryReader.ReadUInt16();
			DX2 = binaryReader.ReadUInt16();
			Row2 = binaryReader.ReadUInt16();
			DY2 = binaryReader.ReadUInt16();
			ExtraData = binaryReader.ReadBytes((int)(memoryStream.Length - memoryStream.Position));
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(Flag);
			binaryWriter.Write(Col1);
			binaryWriter.Write(DX1);
			binaryWriter.Write(Row1);
			binaryWriter.Write(DY1);
			binaryWriter.Write(Col2);
			binaryWriter.Write(DX2);
			binaryWriter.Write(Row2);
			binaryWriter.Write(DY2);
			binaryWriter.Write(ExtraData);
			Data = memoryStream.ToArray();
			Size = (uint)Data.Length;
			base.Encode();
		}
	}
}
