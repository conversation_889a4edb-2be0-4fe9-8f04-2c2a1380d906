using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtContainer : EscherRecord
	{
		public List<EscherRecord> EscherRecords = new List<EscherRecord>();

		public override void Decode()
		{
			MemoryStream memoryStream = new MemoryStream(Data);
			EscherRecords.Clear();
			while (memoryStream.Position < memoryStream.Length)
			{
				EscherRecord escherRecord = EscherRecord.Read(memoryStream);
				escherRecord.Decode();
				EscherRecords.Add(escherRecord);
			}
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter writer = new BinaryWriter(memoryStream);
			foreach (EscherRecord escherRecord in EscherRecords)
			{
				escherRecord.Encode();
				escherRecord.Write(writer);
			}
			Data = memoryStream.ToArray();
			Size = (uint)Data.Length;
		}

		public TRecord FindChild<TRecord>() where TRecord : EscherRecord
		{
			foreach (EscherRecord escherRecord in EscherRecords)
			{
				if (escherRecord is TRecord)
				{
					return escherRecord as TRecord;
				}
			}
			return null;
		}

		public List<TRecord> FindChildren<TRecord>() where TRecord : EscherRecord
		{
			List<TRecord> list = new List<TRecord>();
			foreach (EscherRecord escherRecord in EscherRecords)
			{
				if (escherRecord is TRecord)
				{
					list.Add(escherRecord as TRecord);
				}
			}
			return list;
		}

		public MsofbtContainer()
		{
		}

		public MsofbtContainer(EscherRecord record)
			: base(record)
		{
		}
	}
}
