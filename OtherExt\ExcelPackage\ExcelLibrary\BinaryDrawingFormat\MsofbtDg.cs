using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtDg : EscherRecord
	{
		public int NumShapes;

		public int LastShapeID;

		public MsofbtDg(EscherRecord record)
			: base(record)
		{
		}

		public MsofbtDg()
		{
			Type = 61448;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			NumShapes = binaryReader.ReadInt32();
			LastShapeID = binaryReader.ReadInt32();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(NumShapes);
			binaryWriter.Write(LastShapeID);
			Data = memoryStream.ToArray();
			Size = (uint)Data.Length;
			base.Encode();
		}
	}
}
