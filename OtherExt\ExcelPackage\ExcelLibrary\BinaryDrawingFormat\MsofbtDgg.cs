using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtDgg : EscherRecord
	{
		public int MaxShapeID;

		public int NumIDClusters;

		public int NumSavedShapes;

		public int NumSavedDrawings;

		public List<long> IDClusters;

		public Dictionary<int, int> GroupIdClusters = new Dictionary<int, int>();

		public MsofbtDgg(EscherRecord record)
			: base(record)
		{
		}

		public MsofbtDgg()
		{
			Type = 61446;
			IDClusters = new List<long>();
		}

		public void decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			MaxShapeID = binaryReader.ReadInt32();
			NumIDClusters = binaryReader.ReadInt32();
			NumSavedShapes = binaryReader.ReadInt32();
			NumSavedDrawings = binaryReader.ReadInt32();
			int numIDClusters = NumIDClusters;
			IDClusters = new List<long>(numIDClusters);
			for (int i = 0; i < numIDClusters; i++)
			{
				IDClusters.Add(binaryReader.ReadInt64());
			}
		}

		public void encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(MaxShapeID);
			binaryWriter.Write(NumIDClusters);
			binaryWriter.Write(NumSavedShapes);
			binaryWriter.Write(NumSavedDrawings);
			foreach (long iDCluster in IDClusters)
			{
				binaryWriter.Write(iDCluster);
			}
			Data = memoryStream.ToArray();
			Size = (uint)Data.Length;
			base.Encode();
		}

		public override void Decode()
		{
			MemoryStream memoryStream = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(memoryStream);
			MaxShapeID = binaryReader.ReadInt32();
			NumIDClusters = binaryReader.ReadInt32();
			NumSavedShapes = binaryReader.ReadInt32();
			NumSavedDrawings = binaryReader.ReadInt32();
			IDClusters = new List<long>();
			while (memoryStream.Position < memoryStream.Length)
			{
				int key = binaryReader.ReadInt32();
				int value = binaryReader.ReadInt32();
				GroupIdClusters[key] = value;
			}
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(MaxShapeID);
			binaryWriter.Write(GetNumIDClusters());
			binaryWriter.Write(NumSavedShapes);
			binaryWriter.Write(NumSavedDrawings);
			List<int> list = new List<int>(GroupIdClusters.Keys);
			list.Sort();
			foreach (int item in list)
			{
				binaryWriter.Write(item);
				binaryWriter.Write(GroupIdClusters[item]);
			}
			Data = memoryStream.ToArray();
			Size = (uint)Data.Length;
			base.Encode();
		}

		public int GetNumIDClusters()
		{
			return GroupIdClusters.Count + 1;
		}
	}
}
