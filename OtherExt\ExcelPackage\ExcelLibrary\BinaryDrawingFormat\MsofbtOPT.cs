using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtOPT : EscherRecord
	{
		public List<ShapeProperty> Properties = new List<ShapeProperty>();

		public MsofbtOPT(EscherRecord record)
			: base(record)
		{
		}

		public MsofbtOPT()
		{
			Type = 61451;
		}

		public void Add(PropertyIDs propertyID, uint propertyValue)
		{
			ShapeProperty shapeProperty = new ShapeProperty();
			shapeProperty.PropertyID = propertyID;
			shapeProperty.PropertyValue = propertyValue;
			shapeProperty.IsBlipID = propertyID == PropertyIDs.BlipId;
			Properties.Add(shapeProperty);
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			Properties.Clear();
			for (int i = 0; i < base.Instance; i++)
			{
				Properties.Add(ShapeProperty.Decode(binaryReader));
			}
			foreach (ShapeProperty property in Properties)
			{
				if (property.IsComplex)
				{
					int propertyValue = (int)property.PropertyValue;
					property.ComplexData = binaryReader.ReadBytes(propertyValue);
				}
			}
		}

		public override void Encode()
		{
			base.Instance = (ushort)Properties.Count;
			base.Version = 3;
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			Properties.Sort((ShapeProperty p1, ShapeProperty p2) => p1.PropertyID - p2.PropertyID);
			foreach (ShapeProperty property in Properties)
			{
				property.Encode(binaryWriter);
			}
			foreach (ShapeProperty property2 in Properties)
			{
				if (property2.IsComplex)
				{
					binaryWriter.Write(property2.ComplexData);
				}
			}
			Data = memoryStream.ToArray();
			Size = (uint)Data.Length;
			base.Encode();
		}
	}
}
