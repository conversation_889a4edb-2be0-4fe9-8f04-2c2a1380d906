using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtSp : EscherRecord
	{
		public int ShapeId;

		public int Flags;

        public MsofbtSp(EscherRecord record)
			: base(record)
		{
		}

		public MsofbtSp()
		{
			Type = 61450;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			ShapeId = binaryReader.ReadInt32();
			Flags = binaryReader.ReadInt32();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(ShapeId);
			binaryWriter.Write(Flags);
			Data = memoryStream.ToArray();
			Size = (uint)Data.Length;
			base.Encode();
		}
	}
}
