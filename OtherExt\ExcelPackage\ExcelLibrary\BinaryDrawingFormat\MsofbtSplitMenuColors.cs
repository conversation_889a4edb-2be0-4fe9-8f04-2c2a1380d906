using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtSplitMenuColors : EscherRecord
	{
		public int Color1;

		public int Color2;

		public int Color3;

		public int Color4;

		public MsofbtSplitMenuColors(EscherRecord record)
			: base(record)
		{
		}

		public MsofbtSplitMenuColors()
		{
			Type = 61726;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			Color1 = binaryReader.ReadInt32();
			Color2 = binaryReader.ReadInt32();
			Color3 = binaryReader.ReadInt32();
			Color4 = binaryReader.ReadInt32();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(Color1);
			binaryWriter.Write(Color2);
			binaryWriter.Write(Color3);
			binaryWriter.Write(Color4);
			Data = memoryStream.ToArray();
			Size = (uint)Data.Length;
			base.Encode();
		}
	}
}
