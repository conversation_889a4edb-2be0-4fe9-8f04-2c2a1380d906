using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class ShapeProperty
	{
		public const int Size = 6;

		public PropertyIDs PropertyID;

		public bool IsBlipID;

		public bool IsComplex;

		public uint PropertyValue;

		public byte[] ComplexData;

		public static ShapeProperty Decode(BinaryReader reader)
		{
			ShapeProperty shapeProperty = new ShapeProperty();
			ushort num = reader.ReadUInt16();
			shapeProperty.PropertyID = (PropertyIDs)(num & 0x3FFFu);
			shapeProperty.IsBlipID = (num & 0x4000) == 16384;
			shapeProperty.IsComplex = (num & 0x8000) == 32768;
			shapeProperty.PropertyValue = reader.ReadUInt32();
			return shapeProperty;
		}

		public void Encode(BinaryWriter writer)
		{
			ushort num = (ushort)(PropertyID & (PropertyIDs)16383);
			if (IsBlipID)
			{
				num = (ushort)(num | 0x4000u);
			}
			if (IsComplex)
			{
				num = (ushort)(num | 0x8000u);
			}
			writer.Write(num);
			writer.Write(PropertyValue);
		}
	}
}
