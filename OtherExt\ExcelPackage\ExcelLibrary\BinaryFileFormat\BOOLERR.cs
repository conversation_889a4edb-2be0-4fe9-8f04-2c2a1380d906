using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class BOOLERR : CellValue
	{
		public byte Value;

		public byte ValueType;

		public BOOLERR(Record record)
			: base(record)
		{
		}

		public BOOLERR()
		{
			Type = 517;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			RowIndex = binaryReader.ReadUInt16();
			ColIndex = binaryReader.ReadUInt16();
			XFIndex = binaryReader.ReadUInt16();
			Value = binaryReader.ReadByte();
			ValueType = binaryReader.ReadByte();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(RowIndex);
			binaryWriter.Write(ColIndex);
			binaryWriter.Write(XFIndex);
			binaryWriter.Write(Value);
			binaryWriter.Write(ValueType);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}

		public object GetValue()
		{
			if (ValueType == 0)
			{
				return Value == 1;
			}
			return ErrorCode.ErrorCodes[Value];
		}
	}
}
