using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class CALCCOUNT : Record
	{
		public ushort Value;

		public CALCCOUNT(Record record)
			: base(record)
		{
		}

		public CALCCOUNT()
		{
			Type = 12;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			Value = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(Value);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
