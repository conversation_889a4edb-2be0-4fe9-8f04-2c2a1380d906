using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class CODEPAGE : Record
	{
		public ushort CodePageIdentifier;

		public CODEPAGE(Record record)
			: base(record)
		{
		}

		public CODEPAGE()
		{
			Type = 66;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			CodePageIdentifier = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(CodePageIdentifier);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
