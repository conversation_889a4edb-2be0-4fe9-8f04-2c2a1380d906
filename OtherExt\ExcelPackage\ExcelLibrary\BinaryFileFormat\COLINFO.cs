using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class COLINFO : Record
	{
		public ushort FirstColIndex;

		public ushort LastColIndex;

		public ushort Width;

		public ushort XFIndex;

		public ushort OptionFlags;

		public ushort NotUsed;

		public COLINFO(Record record)
			: base(record)
		{
		}

		public COLINFO()
		{
			Type = 125;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			FirstColIndex = binaryReader.ReadUInt16();
			LastColIndex = binaryReader.ReadUInt16();
			Width = binaryReader.ReadUInt16();
			XFIndex = binaryReader.ReadUInt16();
			OptionFlags = binaryReader.ReadUInt16();
			NotUsed = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(FirstColIndex);
			binaryWriter.Write(LastColIndex);
			binaryWriter.Write(Width);
			binaryWriter.Write(XFIndex);
			binaryWriter.Write(OptionFlags);
			binaryWriter.Write(NotUsed);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
