using System.Collections.Generic;
using ExcelLibrary.SpreadSheet;

namespace ExcelLibrary.BinaryFileFormat
{
	public class CellFormatCollection
	{
		private Dictionary<ushort, CellFormat> lookupTable;

		public CellFormat this[ushort formatIndex]
		{
			get
			{
				if (lookupTable.ContainsKey(formatIndex))
				{
					return lookupTable[formatIndex];
				}
				throw new KeyNotFoundException("Unable to find specific cell format");
			}
		}

		public CellFormatCollection()
		{
			lookupTable = new Dictionary<ushort, CellFormat>();
			lookupTable.Add(0, new CellFormat(CellFormatType.General, "General"));
			lookupTable.Add(1, new CellFormat(CellFormatType.Number, "0"));
			lookupTable.Add(2, new CellFormat(CellFormatType.Number, "0.00"));
			lookupTable.Add(3, new CellFormat(CellFormatType.Number, "#,##0"));
			lookupTable.Add(4, new CellFormat(CellFormatType.Number, "#,##0.00"));
			lookupTable.Add(5, new CellFormat(CellFormatType.Currency, "($#,##0_);($#,##0)"));
			lookupTable.Add(6, new CellFormat(CellFormatType.Currency, "($#,##0_);[Red]($#,##0)"));
			lookupTable.Add(7, new CellFormat(CellFormatType.Currency, "($#,##0.00);($#,##0.00)"));
			lookupTable.Add(8, new CellFormat(CellFormatType.Currency, "($#,##0.00_);[Red]($#,##0.00)"));
			lookupTable.Add(9, new CellFormat(CellFormatType.Percentage, "0%"));
			lookupTable.Add(10, new CellFormat(CellFormatType.Percentage, "0.00%"));
			lookupTable.Add(11, new CellFormat(CellFormatType.Scientific, "0.00E+00"));
			lookupTable.Add(12, new CellFormat(CellFormatType.Fraction, "# ?/?"));
			lookupTable.Add(13, new CellFormat(CellFormatType.Fraction, "# ??/??"));
			lookupTable.Add(14, new CellFormat(CellFormatType.Date, "m/d/yy"));
			lookupTable.Add(15, new CellFormat(CellFormatType.Date, "d-mmm-yy"));
			lookupTable.Add(16, new CellFormat(CellFormatType.Date, "d-mmm"));
			lookupTable.Add(17, new CellFormat(CellFormatType.Date, "mmm-yy"));
			lookupTable.Add(18, new CellFormat(CellFormatType.Time, "h:mm AM/PM"));
			lookupTable.Add(19, new CellFormat(CellFormatType.Time, "h:mm:ss AM/PM"));
			lookupTable.Add(20, new CellFormat(CellFormatType.Time, "h:mm"));
			lookupTable.Add(21, new CellFormat(CellFormatType.Time, "h:mm:ss"));
			lookupTable.Add(22, new CellFormat(CellFormatType.DateTime, "m/d/yy h:mm"));
			lookupTable.Add(37, new CellFormat(CellFormatType.Accounting, "(#,##0_);(#,##0)"));
			lookupTable.Add(38, new CellFormat(CellFormatType.Accounting, "(#,##0_);[Red](#,##0)"));
			lookupTable.Add(39, new CellFormat(CellFormatType.Accounting, "(#,##0.00_);(#,##0.00)"));
			lookupTable.Add(40, new CellFormat(CellFormatType.Accounting, "(#,##0.00_);[Red](#,##0.00)"));
			lookupTable.Add(41, new CellFormat(CellFormatType.Currency, "_(*#,##0_);_(*(#,##0);_(* \"-\"_);_(@_)"));
			lookupTable.Add(42, new CellFormat(CellFormatType.Currency, "_($*#,##0_);_($*(#,##0);_($* \"-\"_);_(@_)"));
			lookupTable.Add(43, new CellFormat(CellFormatType.Currency, "_(*#,##0.00_);_(*(#,##0.00);_(*\"-\"??_);_(@_)"));
			lookupTable.Add(44, new CellFormat(CellFormatType.Currency, "_($*#,##0.00_);_($*(#,##0.00);_($*\"-\"??_);_(@_)"));
			lookupTable.Add(45, new CellFormat(CellFormatType.Time, "mm:ss"));
			lookupTable.Add(46, new CellFormat(CellFormatType.Time, "[h]:mm:ss"));
			lookupTable.Add(47, new CellFormat(CellFormatType.Time, "mm:ss.0"));
			lookupTable.Add(48, new CellFormat(CellFormatType.Scientific, "##0.0E+0"));
			lookupTable.Add(49, new CellFormat(CellFormatType.Text, "@"));
		}

		public void Add(FORMAT record)
		{
			if (record != null)
			{
				if (lookupTable.ContainsKey(record.FormatIndex))
				{
					CellFormat cellFormat = lookupTable[record.FormatIndex];
					lookupTable[record.FormatIndex] = new CellFormat(cellFormat.FormatType, record.FormatString);
				}
				else
				{
					lookupTable.Add(record.FormatIndex, new CellFormat(CellFormatType.Custom, record.FormatString));
				}
			}
		}

		public ushort GetFormatIndex(string formatString)
		{
			foreach (KeyValuePair<ushort, CellFormat> item in lookupTable)
			{
				if (formatString == item.Value.FormatString)
				{
					return item.Key;
				}
			}
			return ushort.MaxValue;
		}
	}
}
