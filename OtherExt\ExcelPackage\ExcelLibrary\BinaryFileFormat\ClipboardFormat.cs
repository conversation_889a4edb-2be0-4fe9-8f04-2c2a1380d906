using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class ClipboardFormat : SubRecord
	{
		public ushort Reserved;

		public ClipboardFormat(SubRecord record)
			: base(record)
		{
		}

		public ClipboardFormat()
		{
			Type = 7;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			Reserved = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(Reserved);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
