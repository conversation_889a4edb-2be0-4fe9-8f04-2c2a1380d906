using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class DBCELL : Record
	{
		public uint FirstRowOffset;

		public List<ushort> FirstCellOffsets;

		public DBCELL(Record record)
			: base(record)
		{
		}

		public DBCELL()
		{
			Type = 215;
			FirstCellOffsets = new List<ushort>();
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			FirstRowOffset = binaryReader.ReadUInt32();
			int num = (Size - 4) / 2;
			FirstCellOffsets = new List<ushort>(num);
			for (int i = 0; i < num; i++)
			{
				FirstCellOffsets.Add(binaryReader.ReadUInt16());
			}
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(FirstRowOffset);
			foreach (ushort firstCellOffset in FirstCellOffsets)
			{
				binaryWriter.Write(firstCellOffset);
			}
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
