using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class DEFCOLWIDTH : Record
	{
		public ushort Value;

		public DEFCOLWIDTH(Record record)
			: base(record)
		{
		}

		public DEFCOLWIDTH()
		{
			Type = 85;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			Value = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(Value);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
