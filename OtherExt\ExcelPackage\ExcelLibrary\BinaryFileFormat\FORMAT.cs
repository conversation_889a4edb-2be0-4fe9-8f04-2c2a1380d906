using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class FORMAT : Record
	{
		public ushort FormatIndex;

		public string FormatString;

		public FORMAT(Record record)
			: base(record)
		{
		}

		public FORMAT()
		{
			Type = 1054;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			FormatIndex = binaryReader.ReadUInt16();
			FormatString = ReadString(binaryReader, 16);
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(FormatIndex);
			Record.WriteString(binaryWriter, FormatString, 16);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
