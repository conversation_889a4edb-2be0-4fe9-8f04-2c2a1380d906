using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class MULBLANK : Record
	{
		public ushort RowIndex;

		public ushort FirstColIndex;

		public List<ushort> XFIndice;

		public short LastColIndex;

		public MULBLANK(Record record)
			: base(record)
		{
		}

		public MULBLANK()
		{
			Type = 190;
			XFIndice = new List<ushort>();
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			RowIndex = binaryReader.ReadUInt16();
			FirstColIndex = binaryReader.ReadUInt16();
			int num = (Size - 6) / 2;
			XFIndice = new List<ushort>(num);
			for (int i = 0; i < num; i++)
			{
				XFIndice.Add(binaryReader.ReadUInt16());
			}
			LastColIndex = binaryReader.ReadInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(RowIndex);
			binaryWriter.Write(FirstColIndex);
			foreach (ushort xFIndie in XFIndice)
			{
				binaryWriter.Write(xFIndie);
			}
			binaryWriter.Write(LastColIndex);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
