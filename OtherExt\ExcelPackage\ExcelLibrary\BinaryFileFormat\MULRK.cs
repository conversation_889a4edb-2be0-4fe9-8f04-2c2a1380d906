using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class MULRK : Record
	{
		public List<uint> RKList;

		public List<ushort> XFList;

		public ushort RowIndex;

		public ushort FirstColIndex;

		public List<uint> XFRKList;

		public short LastColIndex;

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			RowIndex = binaryReader.ReadUInt16();
			FirstColIndex = binaryReader.ReadUInt16();
			int num = (Size - 6) / 6;
			RKList = new List<uint>(num);
			XFList = new List<ushort>(num);
			for (int i = 0; i < num; i++)
			{
				ushort item = binaryReader.ReadUInt16();
				uint item2 = binaryReader.ReadUInt32();
				XFList.Add(item);
				RKList.Add(item2);
			}
			LastColIndex = binaryReader.ReadInt16();
		}

		public MULRK(Record record)
			: base(record)
		{
		}

		public MULRK()
		{
			Type = 189;
			XFRKList = new List<uint>();
		}

		public void decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			RowIndex = binaryReader.ReadUInt16();
			FirstColIndex = binaryReader.ReadUInt16();
			int num = (Size - 6) / 6;
			XFRKList = new List<uint>(num);
			for (int i = 0; i < num; i++)
			{
				XFRKList.Add(binaryReader.ReadUInt32());
			}
			LastColIndex = binaryReader.ReadInt16();
		}

		public void encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(RowIndex);
			binaryWriter.Write(FirstColIndex);
			foreach (uint xFRK in XFRKList)
			{
				binaryWriter.Write(xFRK);
			}
			binaryWriter.Write(LastColIndex);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
