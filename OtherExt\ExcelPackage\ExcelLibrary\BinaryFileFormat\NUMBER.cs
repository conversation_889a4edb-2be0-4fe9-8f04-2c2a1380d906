using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class NUMBER : CellValue
	{
		public double Value;

		public NUMBER(Record record)
			: base(record)
		{
		}

		public NUMBER()
		{
			Type = 515;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			RowIndex = binaryReader.ReadUInt16();
			ColIndex = binaryReader.ReadUInt16();
			XFIndex = binaryReader.ReadUInt16();
			Value = binaryReader.ReadDouble();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(RowIndex);
			binaryWriter.Write(ColIndex);
			binaryWriter.Write(XFIndex);
			binaryWriter.Write(Value);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
