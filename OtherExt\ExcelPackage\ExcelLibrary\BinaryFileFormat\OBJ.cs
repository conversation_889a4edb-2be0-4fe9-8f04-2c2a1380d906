using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class OBJ : Record
	{
		public List<SubRecord> SubRecords = new List<SubRecord>();

		public override void Decode()
		{
			MemoryStream memoryStream = new MemoryStream(Data);
			SubRecords.Clear();
			while (memoryStream.Position < Size)
			{
				SubRecord subRecord = SubRecord.Read(memoryStream);
				subRecord.Decode();
				SubRecords.Add(subRecord);
			}
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter writer = new BinaryWriter(memoryStream);
			foreach (SubRecord subRecord in SubRecords)
			{
				subRecord.Encode();
				subRecord.Write(writer);
			}
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
		}

		public OBJ(Record record)
			: base(record)
		{
		}

		public OBJ()
		{
			Type = 93;
		}
	}
}
