using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class PALETTE : Record
	{
		public short NumColors;

		public List<int> Colors;

		public PALETTE(Record record)
			: base(record)
		{
		}

		public PALETTE()
		{
			Type = 146;
			Colors = new List<int>();
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			NumColors = binaryReader.ReadInt16();
			int numColors = NumColors;
			Colors = new List<int>(numColors);
			for (int i = 0; i < numColors; i++)
			{
				Colors.Add(binaryReader.ReadInt32());
			}
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(NumColors);
			foreach (int color in Colors)
			{
				binaryWriter.Write(color);
			}
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
