using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class RSTRING : Record
	{
		public uint FormattingRuns;

		public RSTRING(Record record)
			: base(record)
		{
		}

		public RSTRING()
		{
			Type = 214;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			FormattingRuns = binaryReader.ReadUInt32();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(FormattingRuns);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
