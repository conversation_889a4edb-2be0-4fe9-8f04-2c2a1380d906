using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class STRING : Record
	{
		public string Value;

		public STRING(Record record)
			: base(record)
		{
		}

		public STRING()
		{
			Type = 519;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader reader = new BinaryReader(input);
			Value = ReadString(reader, 16);
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter writer = new BinaryWriter(memoryStream);
			Record.WriteString(writer, Value, 16);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
