using System;
using System.Collections.Generic;
using ExcelLibrary.SpreadSheet;
using QiHe.CodeLib;

namespace ExcelLibrary.BinaryFileFormat
{
	public class SharedResource
	{
		public SST SharedStringTable;

		public DateTime BaseDate;

		public ColorPalette ColorPalette = new ColorPalette();

		public List<FORMAT> FormatRecords = new List<FORMAT>();

		public List<XF> ExtendedFormats = new List<XF>();

		public CellFormatCollection CellFormats = new CellFormatCollection();

		public List<FONT> Fonts = new List<FONT>();

		private Dictionary<string, int> NumberFormatXFIndice = new Dictionary<string, int>();

		private ushort MaxNumberFormatIndex;

		public SharedResource()
		{
		}

		public SharedResource(bool newbook)
		{
			FONT fONT = new FONT
			{
				Height = 200,
				OptionFlags = 0,
				ColorIndex = 32767,
				Weight = 400,
				Escapement = 0,
				Underline = 0,
				CharacterSet = 1,
				Name = "Arial"
			};
			for (ushort num = 0; num < 21; num = (ushort)(num + 1))
			{
				XF item = new XF
				{
					Attributes = 252,
					CellProtection = 65524,
					PatternColorIndex = 64,
					PatternBackgroundColorIndex = 130,
					FontIndex = 0,
					FormatIndex = num
				};
				ExtendedFormats.Add(item);
			}
			MaxNumberFormatIndex = 163;
			GetXFIndex(CellFormat.General);
			SharedStringTable = new SST();
		}

		public string GetStringFromSST(int index)
		{
			if (SharedStringTable != null)
			{
				return SharedStringTable.StringList[index];
			}
			return null;
		}

		public int GetSSTIndex(string text)
		{
			SharedStringTable.TotalOccurance++;
			int num = SharedStringTable.StringList.IndexOf(text);
			if (num == -1)
			{
				SharedStringTable.StringList.Add(text);
				return SharedStringTable.StringList.Count - 1;
			}
			return num;
		}

		public double EncodeDateTime(DateTime value)
		{
			return (value - BaseDate).TotalDays;
		}

		internal int GetXFIndex(CellFormat cellFormat)
		{
			string formatString = cellFormat.FormatString;
			if (NumberFormatXFIndice.ContainsKey(formatString))
			{
				return NumberFormatXFIndice[formatString];
			}
			ushort num = CellFormats.GetFormatIndex(formatString);
			if (num == ushort.MaxValue)
			{
				num = MaxNumberFormatIndex++;
			}
			FORMAT fORMAT = new FORMAT();
			fORMAT.FormatIndex = num;
			fORMAT.FormatString = formatString;
			FormatRecords.Add(fORMAT);
			XF xF = new XF();
			xF.Attributes = 252;
			xF.CellProtection = 0;
			xF.PatternColorIndex = 64;
			xF.PatternBackgroundColorIndex = 130;
			xF.FontIndex = 0;
			xF.FormatIndex = num;
			ExtendedFormats.Add(xF);
			int num2 = ExtendedFormats.Count - 1;
			NumberFormatXFIndice.Add(formatString, num2);
			return num2;
		}
	}
}
