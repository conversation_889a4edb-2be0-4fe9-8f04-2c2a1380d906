using System;
using System.IO;
using System.Text;

namespace ExcelLibrary.BinaryFileFormat
{
	public class StringDecoder
	{
		private Record record;

		private BinaryReader reader;

		private int ContinuedIndex = -1;

		public StringDecoder(Record record, BinaryReader reader)
		{
			this.record = record;
			this.reader = reader;
		}

		public string ReadString(int lengthbits)
		{
			if (reader.BaseStream.Position == reader.BaseStream.Length)
			{
				if (ContinuedIndex >= record.ContinuedRecords.Count - 1)
				{
					return null;
				}
				SwitchToContinuedRecord();
			}
			int num = ((lengthbits == 8) ? reader.ReadByte() : reader.ReadUInt16());
			byte b = reader.ReadByte();
			bool compressed = (b & 1) == 0;
			bool flag = (b & 4) == 4;
			bool flag2 = (b & 8) == 8;
			int num2 = 0;
			int num3 = 0;
			if (flag2)
			{
				num2 = reader.ReadUInt16();
			}
			if (flag)
			{
				num3 = reader.ReadInt32();
			}
			string text = ReadString(num, compressed);
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append(text);
			if (text.Length < num)
			{
				SwitchToContinuedRecord();
				stringBuilder.Append(ReadContinuedString(num - text.Length));
			}
			ReadBytes(4 * num2 + num3);
			return stringBuilder.ToString();
		}

		private string ReadString(int stringlength, bool compressed)
		{
			byte[] array2;
			if (compressed)
			{
				byte[] array = reader.ReadBytes(stringlength);
				array2 = new byte[array.Length * 2];
				for (int i = 0; i < array.Length; i++)
				{
					array2[i * 2] = array[i];
					array2[i * 2 + 1] = 0;
				}
			}
			else
			{
				array2 = reader.ReadBytes(stringlength * 2);
			}
			return Encoding.Unicode.GetString(array2);
		}

		private string ReadContinuedString(int stringlength)
		{
			if (reader.BaseStream.Position == reader.BaseStream.Length)
			{
				return null;
			}
			byte b = reader.ReadByte();
			bool compressed = (b & 1) == 0;
			string text = ReadString(stringlength, compressed);
			if (text.Length < stringlength)
			{
				SwitchToContinuedRecord();
				StringBuilder stringBuilder = new StringBuilder();
				stringBuilder.Append(text);
				stringBuilder.Append(ReadContinuedString(stringlength - text.Length));
				return stringBuilder.ToString();
			}
			return text;
		}

		private byte[] ReadBytes(int count)
		{
			byte[] array = reader.ReadBytes(count);
			int num = array.Length;
			if (num < count)
			{
				SwitchToContinuedRecord();
				byte[] array2 = new byte[count];
				byte[] array3 = ReadBytes(count - num);
				array.CopyTo(array2, 0);
				array3.CopyTo(array2, num);
				return array2;
			}
			return array;
		}

		private void SwitchToContinuedRecord()
		{
			ContinuedIndex++;
			MemoryStream input = new MemoryStream(record.ContinuedRecords[ContinuedIndex].Data);
			reader = new BinaryReader(input);
		}

		public string ReadString(int lengthbits, out RichTextFormat rtf)
		{
			if (reader.BaseStream.Position == reader.BaseStream.Length)
			{
				if (ContinuedIndex >= record.ContinuedRecords.Count - 1)
				{
					rtf = null;
					return null;
				}
				SwitchToContinuedRecord();
			}
			int num = ((lengthbits == 8) ? reader.ReadByte() : reader.ReadUInt16());
			byte b = reader.ReadByte();
			bool compressed = (b & 1) == 0;
			bool flag = (b & 4) == 4;
			bool flag2 = (b & 8) == 8;
			int num2 = 0;
			int num3 = 0;
			if (flag2)
			{
				num2 = reader.ReadUInt16();
			}
			if (flag)
			{
				num3 = reader.ReadInt32();
			}
			string text = ReadString(num, compressed);
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append(text);
			if (text.Length < num)
			{
				SwitchToContinuedRecord();
				stringBuilder.Append(ReadContinuedString(num - text.Length));
			}
			byte[] richTextBytes = ReadBytes(4 * num2 + num3);
			rtf = DecodeRichTextFormatting(richTextBytes, num2);
			return stringBuilder.ToString();
		}

		private RichTextFormat DecodeRichTextFormatting(byte[] richTextBytes, int runs)
		{
			RichTextFormat richTextFormat = new RichTextFormat(runs);
			for (int i = 0; i < runs; i++)
			{
				richTextFormat.CharIndexes.Add(BitConverter.ToUInt16(richTextBytes, i * 4));
				richTextFormat.FontIndexes.Add(BitConverter.ToUInt16(richTextBytes, i * 4 + 2));
			}
			return richTextFormat;
		}
	}
}
