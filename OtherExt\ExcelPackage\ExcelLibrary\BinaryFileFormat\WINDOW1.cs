using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class WINDOW1 : Record
	{
		public ushort HorizontalPosition;

		public ushort VerticalPosition;

		public ushort WindowWidth;

		public ushort WindowHeight;

		public ushort OptionFlags;

		public ushort ActiveWorksheet;

		public ushort FirstVisibleTab;

		public ushort SelecteWorksheets;

		public ushort TabBarWidth;

		public WINDOW1(Record record)
			: base(record)
		{
		}

		public WINDOW1()
		{
			Type = 61;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			HorizontalPosition = binaryReader.ReadUInt16();
			VerticalPosition = binaryReader.ReadUInt16();
			WindowWidth = binaryReader.ReadUInt16();
			WindowHeight = binaryReader.ReadUInt16();
			OptionFlags = binaryReader.ReadUInt16();
			ActiveWorksheet = binaryReader.ReadUInt16();
			FirstVisibleTab = binaryReader.ReadUInt16();
			SelecteWorksheets = binaryReader.ReadUInt16();
			TabBarWidth = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(HorizontalPosition);
			binaryWriter.Write(VerticalPosition);
			binaryWriter.Write(WindowWidth);
			binaryWriter.Write(WindowHeight);
			binaryWriter.Write(OptionFlags);
			binaryWriter.Write(ActiveWorksheet);
			binaryWriter.Write(FirstVisibleTab);
			binaryWriter.Write(SelecteWorksheets);
			binaryWriter.Write(TabBarWidth);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
