using System;
using System.Collections.Generic;
using ExcelLibrary.BinaryDrawingFormat;
using ExcelLibrary.SpreadSheet;
using QiHe.CodeLib;

namespace ExcelLibrary.BinaryFileFormat
{
	public class WorkSheetEncoder
	{
		public static List<Record> Encode(Worksheet worksheet, SharedResource sharedResource)
		{
			List<Record> list = new List<Record>();
			BOF bOF = new BOF();
			bOF.BIFFversion = 1536;
			bOF.StreamType = 16;
			bOF.BuildID = 3515;
			bOF.BuildYear = 1996;
			bOF.RequiredExcelVersion = 6u;
			list.Add(bOF);
			foreach (KeyValuePair<Pair<ushort, ushort>, ushort> item2 in worksheet.Cells.ColumnWidth)
			{
				COLINFO cOLINFO = new COLINFO();
				cOLINFO.FirstColIndex = item2.Key.Left;
				cOLINFO.LastColIndex = item2.Key.Right;
				cOLINFO.Width = item2.Value;
				list.Add(cOLINFO);
			}
			DIMENSIONS dIMENSIONS = new DIMENSIONS();
			if (worksheet.Cells.Rows.Count > 0)
			{
				dIMENSIONS.FirstRow = worksheet.Cells.FirstRowIndex;
				dIMENSIONS.FirstColumn = (short)worksheet.Cells.FirstColIndex;
				dIMENSIONS.LastRow = worksheet.Cells.LastRowIndex + 1;
				dIMENSIONS.LastColumn = (short)(worksheet.Cells.LastColIndex + 1);
			}
			list.Add(dIMENSIONS);
			List<Record> list2 = new List<Record>(32);
			List<Record> list3 = new List<Record>();
			for (int i = dIMENSIONS.FirstRow; i < dIMENSIONS.LastRow; i++)
			{
				if (!worksheet.Cells.Rows.ContainsKey(i))
				{
					continue;
				}
				Row row = worksheet.Cells.Rows[i];
				ROW rOW = new ROW();
				rOW.RowIndex = (ushort)i;
				rOW.FirstColIndex = (ushort)row.FirstColIndex;
				rOW.LastColIndex = (ushort)(row.LastColIndex + 1);
				rOW.RowHeight = row.Height;
				rOW.Flags = 983296u;
				list2.Add(rOW);
				for (int j = row.FirstColIndex; j <= row.LastColIndex; j++)
				{
					Cell cell = row.GetCell(j);
					if (cell != Cell.EmptyCell && cell.Value != null)
					{
                        try
                        {
                            CellValue cellValue = EncodeCell(cell, sharedResource);
                            cellValue.RowIndex = (ushort)i;
                            cellValue.ColIndex = (ushort)j;
                            cellValue.XFIndex = (ushort)sharedResource.GetXFIndex(cell.Format);
                            list3.Add(cellValue);
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(e);
                            throw;
                        }
					}
				}
				if (list2.Count == 32)
				{
					list.AddRange(list2);
					list.AddRange(list3);
					list2.Clear();
					list3.Clear();
				}
			}
			if (list2.Count > 0)
			{
				list.AddRange(list2);
				list.AddRange(list3);
			}
			EOF item = new EOF();
			list.Add(item);
			return list;
		}

		private static CellValue EncodeCell(Cell cell, SharedResource sharedResource)
		{
			object value = cell.Value;
			if (value is int || value is short || value is uint || value is byte)
			{
				RK rK = new RK();
				rK.Value = (uint)(Convert.ToInt32(value) << 2) | 2u;
				return rK;
			}
			if (value is decimal)
			{
				if (Math.Abs((decimal)value) <= 5368709.11m)
				{
					RK rK2 = new RK();
					rK2.Value = (uint)((int)((decimal)value * 100m) << 2) | 3u;
					return rK2;
				}
				NUMBER nUMBER = new NUMBER();
				nUMBER.Value = (double)(decimal)value;
				return nUMBER;
			}
			if (value is double)
			{
				NUMBER nUMBER2 = new NUMBER();
				nUMBER2.Value = (double)value;
				return nUMBER2;
			}
			if (value is string)
			{
				LABELSST lABELSST = new LABELSST();
				lABELSST.SSTIndex = sharedResource.GetSSTIndex((string)value);
				return lABELSST;
			}
			if (value is DateTime)
			{
				NUMBER nUMBER3 = new NUMBER();
				nUMBER3.Value = sharedResource.EncodeDateTime((DateTime)value);
				return nUMBER3;
			}
			if (value is bool)
			{
				BOOLERR bOOLERR = new BOOLERR();
				bOOLERR.ValueType = 0;
				bOOLERR.Value = Convert.ToByte((bool)value);
				return bOOLERR;
			}
			if (value is ErrorCode)
			{
				BOOLERR bOOLERR2 = new BOOLERR();
				bOOLERR2.ValueType = 1;
				bOOLERR2.Value = ((ErrorCode)value).Code;
				return bOOLERR2;
			}
			throw new Exception("Invalid cell value.");
		}

	}
}
