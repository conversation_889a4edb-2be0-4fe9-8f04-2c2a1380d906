using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using ExcelLibrary.SpreadSheet;

namespace ExcelLibrary.BinaryFileFormat
{
	public class WorkbookDecoder
	{
		public static Workbook Decode(Stream stream)
		{
			Workbook workbook = new Workbook();
			SharedResource sharedResource;
			List<BOUNDSHEET> list = DecodeRecords(workbook.Records = ReadRecords(stream, out workbook.DrawingGroup), out sharedResource);
			foreach (BOUNDSHEET item in list)
			{
				stream.Position = item.StreamPosition;
				Worksheet worksheet = WorksheetDecoder.Decode(workbook, stream, sharedResource);
				worksheet.Book = workbook;
				worksheet.Name = item.SheetName;
				worksheet.SheetType = (SheetType)item.SheetType;
				workbook.Worksheets.Add(worksheet);
			}
			return workbook;
		}

		private static List<Record> ReadRecords(Stream stream, out MSODRAWINGGROUP drawingGroup)
		{
			List<Record> list = new List<Record>();
			drawingGroup = null;
			Record record = Record.Read(stream);
			record.Decode();
			Record record2 = record;
			if (record is BOF && ((BOF)record).StreamType == 5)
			{
				while (record.Type != 10)
				{
					if (record.Type == 60)
					{
						record2.ContinuedRecords.Add(record);
					}
					else
					{
						ushort type = record.Type;
						if (type == 235)
						{
							if (drawingGroup == null)
							{
								drawingGroup = record as MSODRAWINGGROUP;
								list.Add(record);
							}
							else
							{
								drawingGroup.ContinuedRecords.Add(record);
							}
						}
						else
						{
							list.Add(record);
						}
						record2 = record;
					}
					record = Record.Read(stream);
				}
				list.Add(record);
				return list;
			}
			throw new Exception("Invalid Workbook.");
		}

		private static List<BOUNDSHEET> DecodeRecords(List<Record> records, out SharedResource sharedResource)
		{
			sharedResource = new SharedResource();
			List<BOUNDSHEET> list = new List<BOUNDSHEET>();
			foreach (Record record in records)
			{
				record.Decode();
				switch (record.Type)
				{
				case 133:
					list.Add(record as BOUNDSHEET);
					break;
				case 224:
					sharedResource.ExtendedFormats.Add(record as XF);
					break;
				case 1054:
					sharedResource.CellFormats.Add(record as FORMAT);
					break;
				case 252:
					sharedResource.SharedStringTable = record as SST;
					break;
				case 34:
				{
					DATEMODE dATEMODE = record as DATEMODE;
					switch (dATEMODE.Mode)
					{
					case 0:
						sharedResource.BaseDate = DateTime.Parse("1899-12-31");
						break;
					case 1:
						sharedResource.BaseDate = DateTime.Parse("1904-01-01");
						break;
					}
					break;
				}
				case 146:
				{
					PALETTE pALETTE = record as PALETTE;
					int num = 8;
					foreach (int color in pALETTE.Colors)
					{
						sharedResource.ColorPalette[num] = Color.FromArgb(color);
						num++;
					}
					break;
				}
				case 49:
				{
					FONT item = record as FONT;
					sharedResource.Fonts.Add(item);
					break;
				}
				}
			}
			return list;
		}
	}
}
