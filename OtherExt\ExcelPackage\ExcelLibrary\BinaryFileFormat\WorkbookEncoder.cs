using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using ExcelLibrary.BinaryDrawingFormat;
using ExcelLibrary.SpreadSheet;

namespace ExcelLibrary.BinaryFileFormat
{
	public class WorkbookEncoder
	{
		public static void Encode(Workbook workbook, Stream stream)
		{
			List<Record> list = EncodeWorkbook(workbook);
			BinaryWriter binaryWriter = new BinaryWriter(stream);
			foreach (Record item in list)
			{
				item.Write(binaryWriter);
			}
			binaryWriter.Close();
		}

		private static List<Record> EncodeWorkbook(Workbook workbook)
		{
			SharedResource sharedResource = new SharedResource(newbook: true);
			List<Record> list = new List<Record>();
			BOF bOF = new BOF();
			bOF.BIFFversion = 1536;
			bOF.StreamType = 5;
			bOF.BuildID = 3515;
			bOF.BuildYear = 1996;
			bOF.RequiredExcelVersion = 6u;
			list.Add(bOF);
			CODEPAGE cODEPAGE = new CODEPAGE();
			cODEPAGE.CodePageIdentifier = (ushort)Encoding.Unicode.CodePage;
			list.Add(cODEPAGE);
			WINDOW1 wINDOW = new WINDOW1();
			wINDOW.WindowWidth = 16384;
			wINDOW.WindowHeight = 8192;
			wINDOW.SelecteWorksheets = 1;
			wINDOW.TabBarWidth = 600;
			wINDOW.OptionFlags = 56;
			list.Add(wINDOW);
			DATEMODE dATEMODE = new DATEMODE();
			dATEMODE.Mode = 1;
			sharedResource.BaseDate = DateTime.Parse("1904-01-01");
			list.Add(dATEMODE);
			List<List<Record>> list2 = new List<List<Record>>();
			foreach (Worksheet worksheet in workbook.Worksheets)
			{
				List<Record> list3 = WorkSheetEncoder.Encode(worksheet, sharedResource);
				Record.EncodeRecords(list3);
				list2.Add(list3);
			}
			list.AddRange(sharedResource.FormatRecords.ToArray());
			list.AddRange(sharedResource.ExtendedFormats.ToArray());
			List<BOUNDSHEET> list4 = new List<BOUNDSHEET>();
			foreach (Worksheet worksheet2 in workbook.Worksheets)
			{
				BOUNDSHEET bOUNDSHEET = new BOUNDSHEET();
				bOUNDSHEET.Visibility = 0;
				bOUNDSHEET.SheetType = 0;
				bOUNDSHEET.SheetName = worksheet2.Name;
				bOUNDSHEET.StreamPosition = 0u;
				list4.Add(bOUNDSHEET);
				list.Add(bOUNDSHEET);
			}
			Record.EncodeRecords(list);
			int sstOffset = Record.CountDataLength(list);
			list.Add(sharedResource.SharedStringTable);
			list.Add(CreateEXTSST(sharedResource.SharedStringTable, sstOffset));
			EOF item = new EOF();
			list.Add(item);
			Record.EncodeRecords(list);
			int num = Record.CountDataLength(list);
			for (int i = 0; i < workbook.Worksheets.Count; i++)
			{
				list4[i].StreamPosition = (uint)num;
				list4[i].Encode();
				int num2 = Record.CountDataLength(list2[i]);
				num += num2;
			}
			List<Record> list5 = new List<Record>();
			list5.AddRange(list);
			foreach (List<Record> item2 in list2)
			{
				list5.AddRange(item2);
			}
			return list5;
		}

		private static EXTSST CreateEXTSST(SST sst, int sstOffset)
		{
			EXTSST eXTSST = new EXTSST();
			eXTSST.NumStrings = 8;
			int num = 0;
			int num2 = sstOffset + 12;
			int num3 = 12;
			foreach (string @string in sst.StringList)
			{
				int stringDataLength = Record.GetStringDataLength(@string);
				if (num3 + stringDataLength > 8228)
				{
					num2 += 4;
					num3 = 4;
				}
				if (num == 0)
				{
					StringOffset stringOffset = new StringOffset();
					stringOffset.AbsolutePosition = (uint)num2;
					stringOffset.RelativePosition = (ushort)num3;
					eXTSST.Offsets.Add(stringOffset);
				}
				num++;
				if (num == eXTSST.NumStrings)
				{
					num = 0;
				}
				num2 += stringDataLength;
				num3 += stringDataLength;
			}
			return eXTSST;
		}
	}
}
