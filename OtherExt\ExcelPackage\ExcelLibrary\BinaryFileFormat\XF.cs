using System;
using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class XF : Record
	{
		public ushort FontIndex;

		public ushort FormatIndex;

		public ushort CellProtection;

		public byte Alignment;

		public byte Rotation;

		public byte Indent;

		public byte Attributes;

		public uint LineStyle;

		public uint LineColor;

		public ushort Background;

		public int PatternColorIndex
		{
			get
			{
				return Background & 0x7F;
			}
			set
			{
				if (value > 127)
				{
					throw new ArgumentOutOfRangeException("PatternColorIndex");
				}
				Background = (ushort)((Background & 0x3F80u) | (uint)value);
			}
		}

		public int PatternBackgroundColorIndex
		{
			get
			{
				return (Background & 0x3F80) >> 6;
			}
			set
			{
				Background = (ushort)((Background & 0x7Fu) | (uint)(value << 6));
			}
		}

		public XF(Record record)
			: base(record)
		{
		}

		public XF()
		{
			Type = 224;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			FontIndex = binaryReader.ReadUInt16();
			FormatIndex = binaryReader.ReadUInt16();
			CellProtection = binaryReader.ReadUInt16();
			Alignment = binaryReader.ReadByte();
			Rotation = binaryReader.ReadByte();
			Indent = binaryReader.ReadByte();
			Attributes = binaryReader.ReadByte();
			LineStyle = binaryReader.ReadUInt32();
			LineColor = binaryReader.ReadUInt32();
			Background = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(FontIndex);
			binaryWriter.Write(FormatIndex);
			binaryWriter.Write(CellProtection);
			binaryWriter.Write(Alignment);
			binaryWriter.Write(Rotation);
			binaryWriter.Write(Indent);
			binaryWriter.Write(Attributes);
			binaryWriter.Write(LineStyle);
			binaryWriter.Write(LineColor);
			binaryWriter.Write(Background);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
