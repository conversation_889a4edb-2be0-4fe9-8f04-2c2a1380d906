using System;
using System.Collections.Generic;

namespace ExcelLibrary.CompoundDocumentFormat
{
	public class DirectoryEntry : IComparable<DirectoryEntry>
	{
		public char[] NameBuffer;

		public ushort NameDataSize;

		public byte EntryType;

		public byte NodeColor;

		public int LeftChildDID;

		public int RightChildDID;

		public int MembersTreeNodeDID;

		public Guid UniqueIdentifier;

		public int UserFlags;

		public DateTime CreationTime;

		public DateTime LastModificationTime;

		public int FirstSectorID;

		public int StreamLength;

		public int UnUsed;

		public int ID = -1;

		public CompoundDocument Document;

		public DirectoryEntry Parent;

		public Dictionary<string, DirectoryEntry> Members = new Dictionary<string, DirectoryEntry>();

		private byte[] data;

		private string name;

		public byte[] Data
		{
			get
			{
				if (data == null)
				{
					data = Document.GetStreamData(this);
				}
				return data;
			}
			set
			{
				data = value;
			}
		}

		public string Name
		{
			get
			{
				if (name == null)
				{
					int num = (int)NameDataSize / 2 - 1;
					if (num == 1)
					{
						name = string.Empty;
					}
					else
					{
						name = new string(NameBuffer, 0, num);
					}
				}
				return name;
			}
			set
			{
				if (value.Length > 31)
				{
					throw new Exception("Directory Entry Name exceeds 31 chars.");
				}
				value.ToCharArray().CopyTo(NameBuffer, 0);
				NameBuffer[value.Length] = '\0';
				NameDataSize = (ushort)((value.Length + 1) * 2);
				name = value;
			}
		}

		internal DirectoryEntry()
		{
		}

		public DirectoryEntry(string name)
		{
			NameBuffer = new char[32];
			Name = name;
			CreationTime = DateTime.Now;
			LastModificationTime = CreationTime;
			LeftChildDID = -1;
			RightChildDID = -1;
			MembersTreeNodeDID = -1;
			FirstSectorID = -2;
		}

		public DirectoryEntry(CompoundDocument document, string name)
			: this(name)
		{
			Document = document;
		}

		public void AddChild(DirectoryEntry entry)
		{
			if (entry.Parent != null)
			{
				throw new ArgumentException("DirectoryEntry already has a parent.");
			}
			entry.Parent = this;
			Members.Add(entry.Name, entry);
		}

		public override string ToString()
		{
			return Name;
		}

		public int CompareTo(DirectoryEntry other)
		{
			return CompareString(Name, other.Name);
		}

		private static int CompareString(string strA, string strB)
		{
			if (strA != null && strB != null)
			{
				if (strA.Length < strB.Length)
				{
					return -1;
				}
				if (strA.Length > strB.Length)
				{
					return 1;
				}
			}
			return string.Compare(strA, strB, StringComparison.OrdinalIgnoreCase);
		}
	}
}
