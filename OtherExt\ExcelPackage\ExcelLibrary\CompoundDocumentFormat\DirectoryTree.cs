using System;
using QiHe.CodeLib;

namespace ExcelLibrary.CompoundDocumentFormat
{
	public class DirectoryTree
	{
		public static void Build(DirectoryEntry rootEntry)
		{
			if (rootEntry.Members.Count > 0)
			{
				rootEntry.MembersTreeNodeDID = BuildStorageEntry(rootEntry);
			}
		}

		private static int BuildStorageEntry(DirectoryEntry storageEntry)
		{
			RedBlackTree<DirectoryEntry> redBlackTree = new RedBlackTree<DirectoryEntry>();
			foreach (DirectoryEntry value in storageEntry.Members.Values)
			{
				redBlackTree.Add(value);
			}
			foreach (RedBlackTreeNode<DirectoryEntry> item in redBlackTree.InorderTreeWalk(redBlackTree.Root))
			{
				DirectoryEntry data = item.Data;
				data.NodeColor = GetNodeColor(item.Color);
				data.LeftChildDID = GetNodeID(item.Left);
				data.RightChildDID = GetNodeID(item.Right);
				if (data.Members.Count > 0)
				{
					data.EntryType = 1;
					data.MembersTreeNodeDID = BuildStorageEntry(data);
				}
				else
				{
					data.EntryType = 2;
					data.MembersTreeNodeDID = -1;
				}
			}
			return redBlackTree.Root.Data.ID;
		}

		private static int GetNodeID(RedBlackTreeNode<DirectoryEntry> node)
		{
			if (node == RedBlackTreeNode<DirectoryEntry>.Nil)
			{
				return -1;
			}
			return node.Data.ID;
		}

		private static byte GetNodeColor(QiHe.CodeLib.NodeColor nodeColor)
		{
			switch (nodeColor)
			{
			case QiHe.CodeLib.NodeColor.Black:
				return 1;
			case QiHe.CodeLib.NodeColor.Red:
				return 0;
			default:
				throw new ArgumentException();
			}
		}
	}
}
