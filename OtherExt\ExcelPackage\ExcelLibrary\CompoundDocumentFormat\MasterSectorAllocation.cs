using System;
using System.Collections.Generic;

namespace ExcelLibrary.CompoundDocumentFormat
{
	public class MasterSectorAllocation
	{
		private CompoundDocument Document;

		private int NumberOfSecIDs;

		private int CurrentMSATSector;

		private int SecIDCapacity;

		private List<int> MasterSectorAllocationTable;

		public MasterSectorAllocation(CompoundDocument document)
		{
			Document = document;
			NumberOfSecIDs = document.Header.NumberOfSATSectors;
			CurrentMSATSector = document.Header.FirstSectorIDofMasterSectorAllocationTable;
			SecIDCapacity = document.SectorSize / 4 - 1;
			InitializeMasterSectorAllocationTable();
		}

		private void InitializeMasterSectorAllocationTable()
		{
			MasterSectorAllocationTable = new List<int>(NumberOfSecIDs);
			SelectSIDs(Document.Header.MasterSectorAllocationTable);
			int num = Document.Header.FirstSectorIDofMasterSectorAllocationTable;
			while (num != -2)
			{
				CurrentMSATSector = num;
				int[] array = Document.ReadSectorDataAsIntegers(num);
				SelectSIDs(array);
				num = array[array.Length - 1];
			}
		}

		private void SelectSIDs(int[] SIDs)
		{
			foreach (int item in SIDs)
			{
				if (MasterSectorAllocationTable.Count < NumberOfSecIDs)
				{
					MasterSectorAllocationTable.Add(item);
					continue;
				}
				break;
			}
		}

		public int GetSATSectorID(int SATSectorIndex)
		{
			if (SATSectorIndex < NumberOfSecIDs)
			{
				return MasterSectorAllocationTable[SATSectorIndex];
			}
			if (SATSectorIndex == NumberOfSecIDs)
			{
				return AllocateSATSector();
			}
			throw new ArgumentOutOfRangeException("SATSectorIndex");
		}

		public int AllocateSATSector()
		{
			int[] array = new int[SecIDCapacity + 1];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = -1;
			}
			int num = Document.AllocateNewSector(array);
			MasterSectorAllocationTable.Add(num);
			NumberOfSecIDs++;
			int num2 = NumberOfSecIDs - 1;
			if (NumberOfSecIDs <= 109)
			{
				Document.Header.MasterSectorAllocationTable[num2] = num;
				Document.Write(76 + num2 * 4, num);
			}
			else
			{
				if (CurrentMSATSector == -2)
				{
					CurrentMSATSector = AllocateMSATSector();
					Document.Header.FirstSectorIDofMasterSectorAllocationTable = CurrentMSATSector;
				}
				int num3 = (num2 - 109) % SecIDCapacity;
				Document.WriteInSector(CurrentMSATSector, num3 * 4, num);
				if (num3 == SecIDCapacity - 1)
				{
					int num4 = AllocateMSATSector();
					Document.WriteInSector(CurrentMSATSector, SecIDCapacity * 4, num4);
					CurrentMSATSector = num4;
				}
			}
			Document.SectorAllocation.LinkSectorID(num, -3);
			Document.Header.NumberOfSATSectors++;
			return num;
		}

		public int AllocateMSATSector()
		{
			int[] array = new int[SecIDCapacity + 1];
			for (int i = 0; i < SecIDCapacity; i++)
			{
				array[i] = -1;
			}
			array[SecIDCapacity] = -2;
			int num = Document.AllocateNewSector(array);
			Document.SectorAllocation.LinkSectorID(num, -4);
			Document.Header.NumberOfMasterSectors++;
			return num;
		}
	}
}
