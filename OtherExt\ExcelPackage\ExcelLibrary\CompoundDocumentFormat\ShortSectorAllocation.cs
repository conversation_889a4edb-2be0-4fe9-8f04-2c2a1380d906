using System;
using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.CompoundDocumentFormat
{
	public class ShortSectorAllocation
	{
		private CompoundDocument Document;

		private List<int> ShortSectorAllocationTable;

		public ShortSectorAllocation(CompoundDocument document)
		{
			Document = document;
			ShortSectorAllocationTable = document.GetStreamDataAsIntegers(document.Header.FirstSectorIDofShortSectorAllocationTable);
			while (ShortSectorAllocationTable.Count > 0 && ShortSectorAllocationTable[ShortSectorAllocationTable.Count - 1] == -1)
			{
				ShortSectorAllocationTable.RemoveAt(ShortSectorAllocationTable.Count - 1);
			}
		}

		public int AllocateSector()
		{
			int count = ShortSectorAllocationTable.Count;
			LinkSectorID(count, -2);
			Document.AllocateNewShortSector();
			Document.Header.NumberOfShortSectors++;
			return count;
		}

		public int AllocateSectorAfter(int sectorID)
		{
			int num = AllocateSector();
			LinkSectorID(sectorID, num);
			return num;
		}

		public void LinkSectorID(int sectorID, int newSectorID)
		{
			if (sectorID < ShortSectorAllocationTable.Count)
			{
				ShortSectorAllocationTable[sectorID] = newSectorID;
				return;
			}
			if (sectorID == ShortSectorAllocationTable.Count)
			{
				ShortSectorAllocationTable.Add(newSectorID);
				return;
			}
			throw new ArgumentOutOfRangeException("sectorID");
		}

		public int GetNextSectorID(int sectorID)
		{
			if (sectorID < ShortSectorAllocationTable.Count)
			{
				return ShortSectorAllocationTable[sectorID];
			}
			return -2;
		}

		public List<int> GetSIDChain(int StartSID)
		{
			List<int> list = new List<int>();
			for (int num = StartSID; num != -2; num = GetNextSectorID(num))
			{
				list.Add(num);
			}
			return list;
		}

		public void Save()
		{
			if (ShortSectorAllocationTable.Count <= 0)
			{
				return;
			}
			if (Document.Header.FirstSectorIDofShortSectorAllocationTable == -2)
			{
				int num = Document.SectorSize / 4;
				int[] array = new int[num];
				for (int i = 0; i < array.Length; i++)
				{
					array[i] = -1;
				}
				Document.Header.FirstSectorIDofShortSectorAllocationTable = Document.AllocateDataSector();
				Document.WriteInSector(Document.Header.FirstSectorIDofShortSectorAllocationTable, 0, array);
			}
			MemoryStream memoryStream = new MemoryStream(ShortSectorAllocationTable.Count * 4);
			CompoundDocument.WriteArrayOfInt32(new BinaryWriter(memoryStream), ShortSectorAllocationTable.ToArray());
			Document.WriteStreamData(Document.Header.FirstSectorIDofShortSectorAllocationTable, memoryStream.ToArray());
		}
	}
}
