using System;
using ExcelLibrary.BinaryFileFormat;

namespace ExcelLibrary.SpreadSheet
{
	public class Cell
	{
		private object _value;

		private CellFormat _format;

		private CellStyle _style;

		internal SharedResource SharedResource;

		public static readonly Cell EmptyCell = new Cell(null);

		public bool IsEmpty => this == EmptyCell;

		public object Value
		{
			get
			{
				return _value;
			}
			set
			{
				if (IsEmpty)
				{
					throw new Exception("Can not set value to an empty cell.");
				}
				_value = value;
			}
		}

		public string StringValue
		{
			get
			{
				if (_value == null)
				{
					return string.Empty;
				}
				return _value.ToString();
			}
		}

		public DateTime DateTimeValue
		{
			get
			{
				if (_value is double)
				{
					double num = (double)_value;
					if (SharedResource.BaseDate == DateTime.Parse("1899-12-31") && num > 59.0)
					{
						num -= 1.0;
					}
					return SharedResource.BaseDate.AddDays(num);
				}
				if (_value is string)
				{
					return DateTime.Parse((string)_value);
				}
				if (_value is DateTime)
				{
					return (DateTime)_value;
				}
				throw new Exception("Invalid DateTime Cell.");
			}
			set
			{
				_value = value;
			}
		}

		public string FormatString
		{
			get
			{
				return _format.FormatString;
			}
			set
			{
				_format.FormatString = value;
			}
		}

		public CellFormat Format
		{
			get
			{
				return _format;
			}
			set
			{
				_format = value;
			}
		}

		public CellStyle Style
		{
			get
			{
				return _style;
			}
			set
			{
				_style = value;
			}
		}

		public Cell(object value)
		{
			_value = value;
			_format = CellFormat.General;
		}

		public Cell(object value, string formatString)
		{
			_value = value;
			_format = new CellFormat(CellFormatType.General, formatString);
		}

		public Cell(object value, CellFormat format)
		{
			_value = value;
			_format = format;
		}

		public override string ToString()
		{
			return StringValue;
		}

		public FONT GetFontForCharacter(ushort charIndex)
		{
			return WorksheetDecoder.getFontForCharacter(this, charIndex);
		}
	}
}
