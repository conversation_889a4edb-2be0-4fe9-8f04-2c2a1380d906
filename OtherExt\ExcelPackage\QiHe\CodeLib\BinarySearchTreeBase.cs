using System;
using System.Collections.Generic;

namespace QiHe.CodeLib
{
	public class BinarySearchTreeBase<TItem, TTreeNode> where TItem : IComparable<TItem> where TTreeNode : BinaryTreeNodeBase<TItem, TTreeNode>, new()
	{
		public TTreeNode Root;

        public virtual TTreeNode Nil => null;

		public void Add(TItem item)
		{
			Insert(new TTreeNode
			{
				Data = item
			});
		}

		public IEnumerable<TTreeNode> InorderTreeWalk(TTreeNode node)
		{
			if (node == Nil)
			{
				yield break;
			}
			foreach (TTreeNode item in InorderTreeWalk(node.Left))
			{
				yield return item;
			}
			yield return node;
			foreach (TTreeNode item2 in InorderTreeWalk(node.Right))
			{
				yield return item2;
			}
		}

		public TTreeNode Search(TTreeNode node, TItem item)
		{
			if (node == Nil || node.Data.CompareTo(item) == 0)
			{
				return node;
			}
			if (item.CompareTo(node.Data) < 0)
			{
				return Search(node.Left, item);
			}
			return Search(node.Right, item);
		}

		public TTreeNode Minimum(TTreeNode node)
		{
			if (node == Nil)
			{
				return Nil;
			}
			TTreeNode val = node;
			while (val.Left != Nil)
			{
				val = val.Left;
			}
			return val;
		}

		protected TTreeNode Successor(TTreeNode node)
		{
			if (node.Right != Nil)
			{
				return Minimum(node.Right);
			}
			TTreeNode parent = node.Parent;
			while (parent != Nil && node == parent.Right)
			{
				node = parent;
				parent = parent.Parent;
			}
			return parent;
		}

		protected void RotateLeft(TTreeNode x)
		{
			TTreeNode right = x.Right;
			x.Right = right.Left;
			if (right.Left != Nil)
			{
				right.Left.Parent = x;
			}
			if (right != Nil)
			{
				right.Parent = x.Parent;
			}
			if (x.Parent != Nil)
			{
				if (x.IsLeftChild)
				{
					x.Parent.Left = right;
				}
				else
				{
					x.Parent.Right = right;
				}
			}
			else
			{
				Root = right;
			}
			right.Left = x;
			if (x != Nil)
			{
				x.Parent = right;
			}
		}

		protected void RotateRight(TTreeNode x)
		{
			TTreeNode left = x.Left;
			x.Left = left.Right;
			if (left.Right != Nil)
			{
				left.Right.Parent = x;
			}
			if (left != Nil)
			{
				left.Parent = x.Parent;
			}
			if (x.Parent != Nil)
			{
				if (x.IsRightChild)
				{
					x.Parent.Right = left;
				}
				else
				{
					x.Parent.Left = left;
				}
			}
			else
			{
				Root = left;
			}
			left.Right = x;
			if (x != Nil)
			{
				x.Parent = left;
			}
		}

		public virtual void Insert(TTreeNode node)
		{
			TTreeNode val = Nil;
			for (TTreeNode val2 = Root; val2 != Nil; val2 = ((node.Data.CompareTo(val2.Data) >= 0) ? val2.Right : val2.Left))
			{
				val = val2;
			}
			node.Parent = val;
			if (val == Nil)
			{
				Root = node;
			}
			else if (node.Data.CompareTo(val.Data) < 0)
			{
				val.Left = node;
			}
			else
			{
				val.Right = node;
			}
        }

		public virtual TTreeNode Delete(TTreeNode node)
		{
			TTreeNode val = ((node.Left != Nil && node.Right != Nil) ? Successor(node) : node);
			TTreeNode val2 = ((val.Left == Nil) ? val.Right : val.Left);
			if (val2 != Nil)
			{
				val2.Parent = val.Parent;
			}
			TTreeNode parent = val.Parent;
			if (parent == Nil)
			{
				Root = val2;
			}
			else if (val.IsLeftChild)
			{
				parent.Left = val2;
			}
			else
			{
				parent.Right = val2;
			}
			if (val != node)
			{
				node.Data = val.Data;
			}

            return val;
		}
	}
}
