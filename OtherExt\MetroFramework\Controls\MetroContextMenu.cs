﻿using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using OCRTools;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    public class MetroContextMenu : ContextMenuStrip, IMetroControl
    {
        private MetroColorStyle _metroStyle;

        private MetroStyleManager _metroStyleManager;

        private MetroThemeStyle _metroTheme;

        public MetroContextMenu(IContainer container)
        {
            container?.Add(this);
        }

        [DefaultValue(MetroCommonStyle.DefaultStyle)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || _metroStyle != 0) return _metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroCommonStyle.DefaultStyle;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [DefaultValue(MetroThemeStyle.Light)]
        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || _metroTheme != 0) return _metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return _metroTheme;
            }
            set => _metroTheme = value;
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get => _metroStyleManager;
            set
            {
                _metroStyleManager = value;
                Settheme();
            }
        }

        private void Settheme()
        {
            BackColor = MetroPaint.BackColor.Form(Theme);
            ForeColor = MetroPaint.ForeColor.Button.Normal(Theme);
            Renderer = new MetroCtxRenderer(Theme, Style);
        }

        private class MetroCtxRenderer : ToolStripProfessionalRenderer
        {
            private readonly MetroThemeStyle _theme;

            public MetroCtxRenderer(MetroThemeStyle theme, MetroColorStyle style)
                : base(new ContextColors(theme, style))
            {
                _theme = theme;
            }

            protected override void OnRenderItemText(ToolStripItemTextRenderEventArgs e)
            {
                e.TextColor = MetroPaint.ForeColor.Button.Normal(_theme);
                base.OnRenderItemText(e);
            }

            protected override void OnRenderArrow(ToolStripArrowRenderEventArgs e)
            {
                e.ArrowColor = CommonSetting.夜间模式 ? Color.White : Color.Black;
                base.OnRenderArrow(e);
            }
        }

        private class ContextColors : ProfessionalColorTable
        {
            private readonly MetroColorStyle _style;
            private readonly MetroThemeStyle _theme;

            public ContextColors(MetroThemeStyle theme, MetroColorStyle style)
            {
                _theme = theme;
                _style = style;
            }

            public override Color MenuItemSelected => MetroPaint.GetStyleColor(_style);

            public override Color MenuBorder => MetroPaint.BackColor.Form(_theme);

            public override Color ToolStripBorder => MetroPaint.GetStyleColor(_style);

            public override Color MenuItemBorder => MetroPaint.GetStyleColor(_style);

            public override Color ToolStripDropDownBackground => MetroPaint.BackColor.Form(_theme);

            public override Color ImageMarginGradientBegin => MetroPaint.BackColor.Form(_theme);

            public override Color ImageMarginGradientMiddle => MetroPaint.BackColor.Form(_theme);

            public override Color ImageMarginGradientEnd => MetroPaint.BackColor.Form(_theme);
        }
    }
}