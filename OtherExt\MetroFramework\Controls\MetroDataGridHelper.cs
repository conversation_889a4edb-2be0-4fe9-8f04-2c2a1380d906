﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    public class MetroDataGridHelper
    {
        private MetroScrollBar _scrollbar;

        private DataGridView _grid;

        private int _ignoreScrollbarChange;

        private bool _ishorizontal;

        private HScrollBar hScrollbar;

        private VScrollBar vScrollbar;

        public MetroDataGridHelper(MetroScrollBar scrollbar, DataGridView grid)
        {
            new MetroDataGridHelper(scrollbar, grid, vertical: true);
        }

        public MetroDataGridHelper(MetroScrollBar scrollbar, DataGridView grid, bool vertical)
        {
            _scrollbar = scrollbar;
            _scrollbar.UseBarColor = true;
            _grid = grid;
            _ishorizontal = !vertical;
            foreach (object control in _grid.Controls)
            {
                if (control.GetType() == typeof(VScrollBar))
                {
                    vScrollbar = (VScrollBar)control;
                }
                if (control.GetType() == typeof(HScrollBar))
                {
                    hScrollbar = (HScrollBar)control;
                }
            }
            _grid.RowsAdded += _grid_RowsAdded;
            _grid.UserDeletedRow += _grid_UserDeletedRow;
            _grid.Scroll += _grid_Scroll;
            _grid.Resize += _grid_Resize;
            _scrollbar.Scroll += _scrollbar_Scroll;
            _scrollbar.ScrollbarSize = 17;
            UpdateScrollbar();
        }

        private void _grid_Scroll(object sender, ScrollEventArgs e)
        {
            UpdateScrollbar();
        }

        private void _grid_UserDeletedRow(object sender, DataGridViewRowEventArgs e)
        {
            UpdateScrollbar();
        }

        private void _grid_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
        {
            UpdateScrollbar();
        }

        private void _scrollbar_Scroll(object sender, ScrollEventArgs e)
        {
            if (_ignoreScrollbarChange <= 0)
            {
                if (_ishorizontal)
                {
                    try
                    {
                        hScrollbar.Value = _scrollbar.Value;
                        _grid.HorizontalScrollingOffset = _scrollbar.Value;
                    }
                    catch
                    {
                    }
                }
                else if (_scrollbar.Value >= 0 && _scrollbar.Value < _grid.Rows.Count)
                {
                    _grid.FirstDisplayedScrollingRowIndex = ((_scrollbar.Value + ((_scrollbar.Value != 1) ? 1 : (-1)) >= _grid.Rows.Count) ? (_grid.Rows.Count - 1) : (_scrollbar.Value + ((_scrollbar.Value != 1) ? 1 : (-1))));
                }
                else
                {
                    _grid.FirstDisplayedScrollingRowIndex = _scrollbar.Value - 1;
                }
                _grid.Invalidate();
            }
        }

        private void BeginIgnoreScrollbarChangeEvents()
        {
            _ignoreScrollbarChange++;
        }

        private void EndIgnoreScrollbarChangeEvents()
        {
            if (_ignoreScrollbarChange > 0)
            {
                _ignoreScrollbarChange--;
            }
        }

        public void UpdateScrollbar()
        {
            if (_grid != null)
            {
                try
                {
                    BeginIgnoreScrollbarChangeEvents();
                    if (_ishorizontal)
                    {
                        VisibleFlexGridCols();
                        _scrollbar.Maximum = hScrollbar.Maximum;
                        _scrollbar.Minimum = hScrollbar.Minimum;
                        _scrollbar.SmallChange = hScrollbar.SmallChange;
                        _scrollbar.LargeChange = hScrollbar.LargeChange;
                        _scrollbar.Location = new Point(0, _grid.Height - _scrollbar.ScrollbarSize);
                        _scrollbar.Width = _grid.Width - (vScrollbar.Visible ? _scrollbar.ScrollbarSize : 0);
                        _scrollbar.BringToFront();
                        _scrollbar.Visible = hScrollbar.Visible;
                        _scrollbar.Value = ((hScrollbar.Value == 0) ? 1 : hScrollbar.Value);
                    }
                    else
                    {
                        int num = VisibleFlexGridRows();
                        _scrollbar.Maximum = _grid.RowCount;
                        _scrollbar.Minimum = 1;
                        _scrollbar.SmallChange = 1;
                        _scrollbar.LargeChange = Math.Max(1, num - 1);
                        _scrollbar.Value = _grid.FirstDisplayedScrollingRowIndex;
                        if (_grid.RowCount > 0 && _grid.Rows[_grid.RowCount - 1].Cells[0].Displayed)
                        {
                            _scrollbar.Value = _grid.RowCount;
                        }
                        _scrollbar.Location = new Point(_grid.Width - _scrollbar.ScrollbarSize, 0);
                        _scrollbar.Height = _grid.Height - (hScrollbar.Visible ? _scrollbar.ScrollbarSize : 0);
                        _scrollbar.BringToFront();
                        _scrollbar.Visible = vScrollbar.Visible;
                    }
                }
                finally
                {
                    EndIgnoreScrollbarChangeEvents();
                }
            }
        }

        private int VisibleFlexGridRows()
        {
            return _grid.DisplayedRowCount(includePartialRow: true);
        }

        private int VisibleFlexGridCols()
        {
            return _grid.DisplayedColumnCount(includePartialColumns: true);
        }

        public bool VisibleVerticalScroll()
        {
            bool result = false;
            if (_grid.DisplayedRowCount(includePartialRow: true) < _grid.RowCount + (_grid.RowHeadersVisible ? 1 : 0))
            {
                result = true;
            }
            return result;
        }

        public bool VisibleHorizontalScroll()
        {
            bool result = false;
            if (_grid.DisplayedColumnCount(includePartialColumns: true) < _grid.ColumnCount + (_grid.ColumnHeadersVisible ? 1 : 0))
            {
                result = true;
            }
            return result;
        }

        private void _grid_Resize(object sender, EventArgs e)
        {
            UpdateScrollbar();
        }

        private void _grid_AfterDataRefresh(object sender, ListChangedEventArgs e)
        {
            UpdateScrollbar();
        }
    }
}
