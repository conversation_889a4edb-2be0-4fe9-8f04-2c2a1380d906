﻿using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    [ToolboxBitmap(typeof(DateTimePicker))]
    public class MetroDateTime : DateTimePicker, IMetroControl
    {
        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private MetroStyleManager metroStyleManager;

        private bool useCustomBackColor;

        private bool useCustomForeColor;

        private bool useStyleColors;

        private bool displayFocusRectangle;
        private bool isHovered;

        private bool isPressed;

        private bool isFocused;

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public MetroColorStyle Style
        {
            get
            {
                if (base.DesignMode || metroStyle != 0)
                {
                    return metroStyle;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                if (StyleManager == null)
                {
                    return MetroColorStyle.Blue;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (base.DesignMode || metroTheme != 0)
                {
                    return metroTheme;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                if (StyleManager == null)
                {
                    return MetroThemeStyle.Light;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomBackColor
        {
            get
            {
                return useCustomBackColor;
            }
            set
            {
                useCustomBackColor = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomForeColor
        {
            get
            {
                return useCustomForeColor;
            }
            set
            {
                useCustomForeColor = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseStyleColors
        {
            get
            {
                return useStyleColors;
            }
            set
            {
                useStyleColors = value;
            }
        }

        [DefaultValue(true)]
        [Browsable(false)]
        [Category("Metro Behaviour")]
        public bool UseSelectable
        {
            get
            {
                return GetStyle(ControlStyles.Selectable);
            }
            set
            {
                SetStyle(ControlStyles.Selectable, value);
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool DisplayFocus
        {
            get
            {
                return displayFocusRectangle;
            }
            set
            {
                displayFocusRectangle = value;
            }
        }

        [DefaultValue(MetroDateTimeSize.Medium)]
        [Category("Metro Appearance")]
        public MetroDateTimeSize FontSize { get; set; } = MetroDateTimeSize.Medium;

        [Category("Metro Appearance")]
        [DefaultValue(MetroDateTimeWeight.Regular)]
        public MetroDateTimeWeight FontWeight { get; set; } = MetroDateTimeWeight.Regular;

        [Browsable(false)]
        [DefaultValue(false)]
        public new bool ShowUpDown
        {
            get
            {
                return base.ShowUpDown;
            }
            set
            {
                base.ShowUpDown = false;
            }
        }

        [Browsable(false)]
        public override Font Font
        {
            get
            {
                return base.Font;
            }
            set
            {
                base.Font = value;
            }
        }

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
            {
                this.CustomPaintBackground(this, e);
            }
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
            {
                this.CustomPaint(this, e);
            }
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
            {
                this.CustomPaintForeground(this, e);
            }
        }

        public MetroDateTime()
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.OptimizedDoubleBuffer, value: true);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                Color color = BackColor;
                if (!useCustomBackColor)
                {
                    color = MetroPaint.BackColor.Form(Theme);
                }
                if (color.A == byte.MaxValue && BackgroundImage == null)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint))
                {
                    OnPaintBackground(e);
                }
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            MinimumSize = new Size(0, GetPreferredSize(Size.Empty).Height);
            Color color;
            Color color2;
            if (isHovered && !isPressed && base.Enabled)
            {
                color = MetroPaint.ForeColor.ComboBox.Hover(Theme);
                color2 = MetroPaint.GetStyleColor(Style);
            }
            else if (isHovered && isPressed && base.Enabled)
            {
                color = MetroPaint.ForeColor.ComboBox.Press(Theme);
                color2 = MetroPaint.GetStyleColor(Style);
            }
            else if (!base.Enabled)
            {
                color = MetroPaint.ForeColor.ComboBox.Disabled(Theme);
                color2 = MetroPaint.BorderColor.ComboBox.Disabled(Theme);
            }
            else
            {
                color = MetroPaint.ForeColor.ComboBox.Normal(Theme);
                color2 = MetroPaint.BorderColor.ComboBox.Normal(Theme);
            }
            using (Pen pen = new Pen(color2))
            {
                Rectangle rect = new Rectangle(0, 0, base.Width - 1, base.Height - 1);
                e.Graphics.DrawRectangle(pen, rect);
            }
            using (SolidBrush brush = new SolidBrush(color))
            {
                e.Graphics.FillPolygon(brush, new Point[3]
                {
                new Point(base.Width - 20, base.Height / 2 - 2),
                new Point(base.Width - 9, base.Height / 2 - 2),
                new Point(base.Width - 15, base.Height / 2 + 4)
                });
            }
            int num = 0;
            if (base.ShowCheckBox)
            {
                num = 15;
                using (Pen pen2 = new Pen(color2))
                {
                    Rectangle rect2 = new Rectangle(3, base.Height / 2 - 6, 12, 12);
                    e.Graphics.DrawRectangle(pen2, rect2);
                }
                if (base.Checked)
                {
                    Color styleColor = MetroPaint.GetStyleColor(Style);
                    using (SolidBrush brush2 = new SolidBrush(styleColor))
                    {
                        Rectangle rect3 = new Rectangle(5, base.Height / 2 - 4, 9, 9);
                        e.Graphics.FillRectangle(brush2, rect3);
                    }
                }
                else
                {
                    color = MetroPaint.ForeColor.ComboBox.Disabled(Theme);
                }
            }
            TextRenderer.DrawText(bounds: new Rectangle(2 + num, 2, base.Width - 20, base.Height - 4), dc: e.Graphics, text: Text, font: MetroFonts.DateTime(FontSize, FontWeight), foreColor: color, flags: TextFormatFlags.VerticalCenter);
            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, color, e.Graphics));
            if (displayFocusRectangle && isFocused)
            {
                ControlPaint.DrawFocusRectangle(e.Graphics, base.ClientRectangle);
            }
        }

        protected override void OnValueChanged(EventArgs eventargs)
        {
            base.OnValueChanged(eventargs);
            Invalidate();
        }

        protected override void OnGotFocus(EventArgs e)
        {
            isFocused = true;
            isHovered = true;
            Invalidate();
            base.OnGotFocus(e);
        }

        protected override void OnLostFocus(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLostFocus(e);
        }

        protected override void OnEnter(EventArgs e)
        {
            isFocused = true;
            isHovered = true;
            Invalidate();
            base.OnEnter(e);
        }

        protected override void OnLeave(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLeave(e);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Space)
            {
                isHovered = true;
                isPressed = true;
                Invalidate();
            }
            base.OnKeyDown(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            Invalidate();
            base.OnKeyUp(e);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isPressed = true;
                Invalidate();
            }
            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            if (!isFocused)
            {
                isHovered = false;
            }
            Invalidate();
            base.OnMouseLeave(e);
        }

        public override Size GetPreferredSize(Size proposedSize)
        {
            base.GetPreferredSize(proposedSize);
            using (Graphics dc = CreateGraphics())
            {
                string text = (Text.Length > 0) ? Text : "MeasureText";
                proposedSize = new Size(int.MaxValue, int.MaxValue);
                Size result = TextRenderer.MeasureText(dc, text, MetroFonts.DateTime(FontSize, FontWeight), proposedSize, TextFormatFlags.VerticalCenter | TextFormatFlags.LeftAndRightPadding);
                result.Height += 10;
                return result;
            }
        }

        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
        }
    }
    public enum MetroDateTimeSize
    {
        Small,
        Medium,
        Tall
    }
    public enum MetroDateTimeWeight
    {
        Light,
        Regular,
        Bold
    }

}
