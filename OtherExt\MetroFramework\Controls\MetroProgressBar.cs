﻿/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    [Designer(typeof(Design.MetroProgressBarDesigner), typeof(System.Windows.Forms.Design.ParentControlDesigner))]
    [ToolboxBitmap(typeof(ProgressBar))]
    public class MetroProgressBar : ProgressBar, IMetroControl
    {
        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private MetroStyleManager metroStyleManager;

        private bool useCustomBackColor;

        private bool useCustomForeColor;

        private bool useStyleColors = true;

        private MetroProgressBarSize metroLabelSize = MetroProgressBarSize.Medium;

        private MetroProgressBarWeight metroLabelWeight;

        private ContentAlignment textAlign = ContentAlignment.MiddleRight;

        private bool hideProgressText = true;

        private ProgressBarStyle progressBarStyle = ProgressBarStyle.Continuous;

        private int marqueeX;

        private Timer marqueeTimer;

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public new MetroColorStyle Style
        {
            get
            {
                if (base.DesignMode || metroStyle != 0)
                {
                    return metroStyle;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                if (StyleManager == null)
                {
                    return MetroColorStyle.Blue;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (base.DesignMode || metroTheme != 0)
                {
                    return metroTheme;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                if (StyleManager == null)
                {
                    return MetroThemeStyle.Light;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor
        {
            get
            {
                return useCustomBackColor;
            }
            set
            {
                useCustomBackColor = value;
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomForeColor
        {
            get
            {
                return useCustomForeColor;
            }
            set
            {
                useCustomForeColor = value;
            }
        }

        [Category("Metro Appearance")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        [DefaultValue(true)]
        public bool UseStyleColors
        {
            get
            {
                return useStyleColors;
            }
            set
            {
                useStyleColors = value;
            }
        }

        [Browsable(false)]
        [Category("Metro Behaviour")]
        [DefaultValue(false)]
        public bool UseSelectable
        {
            get
            {
                return GetStyle(ControlStyles.Selectable);
            }
            set
            {
                SetStyle(ControlStyles.Selectable, value);
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroProgressBarSize.Medium)]
        public MetroProgressBarSize FontSize
        {
            get
            {
                return metroLabelSize;
            }
            set
            {
                metroLabelSize = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroProgressBarWeight.Light)]
        public MetroProgressBarWeight FontWeight
        {
            get
            {
                return metroLabelWeight;
            }
            set
            {
                metroLabelWeight = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(ContentAlignment.MiddleRight)]
        public ContentAlignment TextAlign
        {
            get
            {
                return textAlign;
            }
            set
            {
                textAlign = value;
            }
        }

        [DefaultValue(true)]
        [Category("Metro Appearance")]
        public bool HideProgressText
        {
            get
            {
                return hideProgressText;
            }
            set
            {
                hideProgressText = value;
            }
        }

        [DefaultValue(ProgressBarStyle.Continuous)]
        [Category("Metro Appearance")]
        public ProgressBarStyle ProgressBarStyle
        {
            get
            {
                return progressBarStyle;
            }
            set
            {
                progressBarStyle = value;
            }
        }

        public new int Value
        {
            get
            {
                return base.Value;
            }
            set
            {
                if (value <= base.Maximum)
                {
                    base.Value = value;
                    Invalidate();
                }
            }
        }

        [Browsable(false)]
        public double ProgressTotalPercent => (1.0 - (base.Maximum - Value) / (double)base.Maximum) * 100.0;

        [Browsable(false)]
        public double ProgressTotalValue => 1.0 - (base.Maximum - Value) / (double)base.Maximum;

        [Browsable(false)]
        public string ProgressPercentText => $"{Math.Round(ProgressTotalPercent)}%";

        private double ProgressBarWidth => Value / (double)base.Maximum * ClientRectangle.Width;

        private int ProgressBarMarqueeWidth => base.ClientRectangle.Width / 3;

        private bool marqueeTimerEnabled
        {
            get
            {
                if (marqueeTimer != null)
                {
                    return marqueeTimer.Enabled;
                }
                return false;
            }
        }

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
            {
                this.CustomPaintBackground(this, e);
            }
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
            {
                this.CustomPaint(this, e);
            }
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
            {
                this.CustomPaintForeground(this, e);
            }
        }

        public MetroProgressBar()
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.OptimizedDoubleBuffer, value: true);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                Color color = BackColor;
                if (!useCustomBackColor)
                {
                    color = (base.Enabled ? MetroPaint.BackColor.ProgressBar.Bar.Normal(Theme) : MetroPaint.BackColor.ProgressBar.Bar.Disabled(Theme));
                }
                if (color.A == byte.MaxValue)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint))
                {
                    OnPaintBackground(e);
                }
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            if (progressBarStyle == ProgressBarStyle.Continuous)
            {
                if (!base.DesignMode)
                {
                    StopTimer();
                }
                DrawProgressContinuous(e.Graphics);
            }
            else if (progressBarStyle == ProgressBarStyle.Blocks)
            {
                if (!base.DesignMode)
                {
                    StopTimer();
                }
                DrawProgressContinuous(e.Graphics);
            }
            else if (progressBarStyle == ProgressBarStyle.Marquee)
            {
                if (!base.DesignMode && base.Enabled)
                {
                    StartTimer();
                }
                if (!base.Enabled)
                {
                    StopTimer();
                }
                if (Value == base.Maximum)
                {
                    StopTimer();
                    DrawProgressContinuous(e.Graphics);
                }
                else
                {
                    DrawProgressMarquee(e.Graphics);
                }
            }
            DrawProgressText(e.Graphics);
            using (Pen pen = new Pen(MetroPaint.BorderColor.ProgressBar.Normal(Theme)))
            {
                Rectangle rect = new Rectangle(0, 0, base.Width - 1, base.Height - 1);
                e.Graphics.DrawRectangle(pen, rect);
            }
            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
        }

        private void DrawProgressContinuous(Graphics graphics)
        {
            graphics.FillRectangle(MetroPaint.GetStyleBrush(Style), 0, 0, (int)ProgressBarWidth, base.ClientRectangle.Height);
        }

        private void DrawProgressMarquee(Graphics graphics)
        {
            graphics.FillRectangle(MetroPaint.GetStyleBrush(Style), marqueeX, 0, ProgressBarMarqueeWidth, base.ClientRectangle.Height);
        }

        private void DrawProgressText(Graphics graphics)
        {
            if (!HideProgressText)
            {
                TextRenderer.DrawText(foreColor: base.Enabled ? MetroPaint.ForeColor.ProgressBar.Normal(Theme) : MetroPaint.ForeColor.ProgressBar.Disabled(Theme), dc: graphics, text: ProgressPercentText, font: MetroFonts.ProgressBar(metroLabelSize, metroLabelWeight), bounds: base.ClientRectangle, flags: MetroPaint.GetTextFormatFlags(TextAlign));
            }
        }

        public override Size GetPreferredSize(Size proposedSize)
        {
            base.GetPreferredSize(proposedSize);
            using (Graphics dc = CreateGraphics())
            {
                proposedSize = new Size(int.MaxValue, int.MaxValue);
                return TextRenderer.MeasureText(dc, ProgressPercentText, MetroFonts.ProgressBar(metroLabelSize, metroLabelWeight), proposedSize, MetroPaint.GetTextFormatFlags(TextAlign));
            }
        }

        private void StartTimer()
        {
            if (!marqueeTimerEnabled)
            {
                if (marqueeTimer == null)
                {
                    marqueeTimer = new Timer
                    {
                        Interval = 10
                    };
                    marqueeTimer.Tick += marqueeTimer_Tick;
                }
                marqueeX = -ProgressBarMarqueeWidth;
                marqueeTimer.Stop();
                marqueeTimer.Start();
                marqueeTimer.Enabled = true;
                Invalidate();
            }
        }

        private void StopTimer()
        {
            if (marqueeTimer != null)
            {
                marqueeTimer.Stop();
                Invalidate();
            }
        }

        private void marqueeTimer_Tick(object sender, EventArgs e)
        {
            marqueeX++;
            if (marqueeX > base.ClientRectangle.Width)
            {
                marqueeX = -ProgressBarMarqueeWidth;
            }
            Invalidate();
        }
    }
}
