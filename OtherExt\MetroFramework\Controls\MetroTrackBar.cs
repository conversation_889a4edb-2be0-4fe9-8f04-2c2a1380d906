﻿/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    [DefaultEvent("Scroll")]
    [ToolboxBitmap(typeof(TrackBar))]
    public class MetroTrackBar : Control, IMetroControl
    {
        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private MetroStyleManager metroStyleManager;

        private bool useCustomBackColor;

        private bool useCustomForeColor;

        private bool useStyleColors;

        private bool displayFocusRectangle;

        private int trackerValue = 50;

        private int barMinimum;

        private int barMaximum = 100;

        private int smallChange = 1;

        private int largeChange = 5;

        private int mouseWheelBarPartitions = 10;

        private bool isHovered;

        private bool isPressed;

        private bool isFocused;

        [DefaultValue(MetroColorStyle.Blue)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (base.DesignMode || metroStyle != 0)
                {
                    return metroStyle;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                if (StyleManager == null)
                {
                    return MetroColorStyle.Blue;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (base.DesignMode || metroTheme != 0)
                {
                    return metroTheme;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                if (StyleManager == null)
                {
                    return MetroThemeStyle.Light;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomBackColor
        {
            get
            {
                return useCustomBackColor;
            }
            set
            {
                useCustomBackColor = value;
            }
        }

        [DefaultValue(false)]
        [Browsable(false)]
        [Category("Metro Appearance")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool UseCustomForeColor
        {
            get
            {
                return useCustomForeColor;
            }
            set
            {
                useCustomForeColor = value;
            }
        }

        [Browsable(false)]
        [Category("Metro Appearance")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [DefaultValue(false)]
        public bool UseStyleColors
        {
            get
            {
                return useStyleColors;
            }
            set
            {
                useStyleColors = value;
            }
        }

        [Category("Metro Behaviour")]
        [Browsable(false)]
        [DefaultValue(true)]
        public bool UseSelectable
        {
            get
            {
                return GetStyle(ControlStyles.Selectable);
            }
            set
            {
                SetStyle(ControlStyles.Selectable, value);
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool DisplayFocus
        {
            get
            {
                return displayFocusRectangle;
            }
            set
            {
                displayFocusRectangle = value;
            }
        }

        [DefaultValue(50)]
        public int Value
        {
            get
            {
                return trackerValue;
            }
            set
            {
                if ((value >= barMinimum) & (value <= barMaximum))
                {
                    trackerValue = value;
                    OnValueChanged();
                    Invalidate();
                    return;
                }
                throw new ArgumentOutOfRangeException("Value is outside appropriate range (min, max)");
            }
        }

        [DefaultValue(0)]
        public int Minimum
        {
            get
            {
                return barMinimum;
            }
            set
            {
                if (value < barMaximum)
                {
                    barMinimum = value;
                    if (trackerValue < barMinimum)
                    {
                        trackerValue = barMinimum;
                        if (this.ValueChanged != null)
                        {
                            this.ValueChanged(this, new EventArgs());
                        }
                    }
                    Invalidate();
                    return;
                }
                throw new ArgumentOutOfRangeException("Minimal value is greather than maximal one");
            }
        }

        [DefaultValue(100)]
        public int Maximum
        {
            get
            {
                return barMaximum;
            }
            set
            {
                if (value > barMinimum)
                {
                    barMaximum = value;
                    if (trackerValue > barMaximum)
                    {
                        trackerValue = barMaximum;
                        if (this.ValueChanged != null)
                        {
                            this.ValueChanged(this, new EventArgs());
                        }
                    }
                    Invalidate();
                    return;
                }
                throw new ArgumentOutOfRangeException("Maximal value is lower than minimal one");
            }
        }

        [DefaultValue(1)]
        public int SmallChange
        {
            get
            {
                return smallChange;
            }
            set
            {
                smallChange = value;
            }
        }

        [DefaultValue(5)]
        public int LargeChange
        {
            get
            {
                return largeChange;
            }
            set
            {
                largeChange = value;
            }
        }

        [DefaultValue(10)]
        public int MouseWheelBarPartitions
        {
            get
            {
                return mouseWheelBarPartitions;
            }
            set
            {
                if (value > 0)
                {
                    mouseWheelBarPartitions = value;
                    return;
                }
                throw new ArgumentOutOfRangeException("MouseWheelBarPartitions has to be greather than zero");
            }
        }

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        public event EventHandler ValueChanged;

        public event ScrollEventHandler Scroll;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
            {
                this.CustomPaintBackground(this, e);
            }
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
            {
                this.CustomPaint(this, e);
            }
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
            {
                this.CustomPaintForeground(this, e);
            }
        }

        private void OnValueChanged()
        {
            if (this.ValueChanged != null)
            {
                this.ValueChanged(this, EventArgs.Empty);
            }
        }

        private void OnScroll(ScrollEventType scrollType, int newValue)
        {
            if (this.Scroll != null)
            {
                this.Scroll(this, new ScrollEventArgs(scrollType, newValue));
            }
        }

        public MetroTrackBar(int min, int max, int value)
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.Selectable | ControlStyles.UserMouse | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
            BackColor = Color.Transparent;
            Minimum = min;
            Maximum = max;
            Value = value;
        }

        public MetroTrackBar()
            : this(0, 100, 50)
        {
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                Color color = BackColor;
                if (!useCustomBackColor)
                {
                    color = MetroPaint.BackColor.Form(Theme);
                }
                if (color.A == byte.MaxValue)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint))
                {
                    OnPaintBackground(e);
                }
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            Color thumbColor;
            Color barColor;
            if (isHovered && !isPressed && base.Enabled)
            {
                thumbColor = MetroPaint.BackColor.TrackBar.Thumb.Hover(Theme);
                barColor = MetroPaint.BackColor.TrackBar.Bar.Hover(Theme);
            }
            else if (isHovered && isPressed && base.Enabled)
            {
                thumbColor = MetroPaint.BackColor.TrackBar.Thumb.Press(Theme);
                barColor = MetroPaint.BackColor.TrackBar.Bar.Press(Theme);
            }
            else if (!base.Enabled)
            {
                thumbColor = MetroPaint.BackColor.TrackBar.Thumb.Disabled(Theme);
                barColor = MetroPaint.BackColor.TrackBar.Bar.Disabled(Theme);
            }
            else
            {
                thumbColor = MetroPaint.BackColor.TrackBar.Thumb.Normal(Theme);
                barColor = MetroPaint.BackColor.TrackBar.Bar.Normal(Theme);
            }
            DrawTrackBar(e.Graphics, thumbColor, barColor);
            if (displayFocusRectangle && isFocused)
            {
                ControlPaint.DrawFocusRectangle(e.Graphics, base.ClientRectangle);
            }
        }

        private void DrawTrackBar(Graphics g, Color thumbColor, Color barColor)
        {
            int num = (trackerValue - barMinimum) * (base.Width - 6) / (barMaximum - barMinimum);
            using (SolidBrush brush = new SolidBrush(thumbColor))
            {
                Rectangle rect = new Rectangle(0, base.Height / 2 - 2, num, 4);
                g.FillRectangle(brush, rect);
                Rectangle rect2 = new Rectangle(num, base.Height / 2 - 8, 6, 16);
                g.FillRectangle(brush, rect2);
            }
            using (SolidBrush brush2 = new SolidBrush(barColor))
            {
                Rectangle rect3 = new Rectangle(num + 7, base.Height / 2 - 2, base.Width - num + 7, 4);
                g.FillRectangle(brush2, rect3);
            }
        }

        protected override void OnGotFocus(EventArgs e)
        {
            isFocused = true;
            Invalidate();
            base.OnGotFocus(e);
        }

        protected override void OnLostFocus(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLostFocus(e);
        }

        protected override void OnEnter(EventArgs e)
        {
            isFocused = true;
            Invalidate();
            base.OnEnter(e);
        }

        protected override void OnLeave(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLeave(e);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            isHovered = true;
            isPressed = true;
            Invalidate();
            base.OnKeyDown(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnKeyUp(e);
            switch (e.KeyCode)
            {
                case Keys.Left:
                case Keys.Down:
                    SetProperValue(Value - smallChange);
                    OnScroll(ScrollEventType.SmallDecrement, Value);
                    break;
                case Keys.Up:
                case Keys.Right:
                    SetProperValue(Value + smallChange);
                    OnScroll(ScrollEventType.SmallIncrement, Value);
                    break;
                case Keys.Home:
                    Value = barMinimum;
                    break;
                case Keys.End:
                    Value = barMaximum;
                    break;
                case Keys.Next:
                    SetProperValue(Value - largeChange);
                    OnScroll(ScrollEventType.LargeDecrement, Value);
                    break;
                case Keys.Prior:
                    SetProperValue(Value + largeChange);
                    OnScroll(ScrollEventType.LargeIncrement, Value);
                    break;
            }
            if (Value == barMinimum)
            {
                OnScroll(ScrollEventType.First, Value);
            }
            if (Value == barMaximum)
            {
                OnScroll(ScrollEventType.Last, Value);
            }
            Point point = PointToClient(Cursor.Position);
            OnMouseMove(new MouseEventArgs(MouseButtons.None, 0, point.X, point.Y, 0));
        }

        protected override bool ProcessDialogKey(Keys keyData)
        {
            if ((keyData == Keys.Tab) | (Control.ModifierKeys == Keys.Shift))
            {
                return base.ProcessDialogKey(keyData);
            }
            OnKeyDown(new KeyEventArgs(keyData));
            return true;
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isPressed = true;
                Invalidate();
            }
            base.OnMouseDown(e);
            if (e.Button == MouseButtons.Left)
            {
                base.Capture = true;
                OnScroll(ScrollEventType.ThumbTrack, trackerValue);
                OnValueChanged();
                OnMouseMove(e);
            }
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);
            if (base.Capture & (e.Button == MouseButtons.Left))
            {
                ScrollEventType scrollType = ScrollEventType.ThumbPosition;
                int x = e.Location.X;
                float num = (barMaximum - barMinimum) / (float)(base.ClientSize.Width - 3);
                trackerValue = (int)(x * num + barMinimum);
                if (trackerValue <= barMinimum)
                {
                    trackerValue = barMinimum;
                    scrollType = ScrollEventType.First;
                }
                else if (trackerValue >= barMaximum)
                {
                    trackerValue = barMaximum;
                    scrollType = ScrollEventType.Last;
                }
                OnScroll(scrollType, trackerValue);
                OnValueChanged();
                Invalidate();
            }
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            isHovered = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnMouseWheel(MouseEventArgs e)
        {
            base.OnMouseWheel(e);
            int num = e.Delta / 120 * (barMaximum - barMinimum) / mouseWheelBarPartitions;
            SetProperValue(Value + num);
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        private void SetProperValue(int val)
        {
            if (val < barMinimum)
            {
                Value = barMinimum;
            }
            else if (val > barMaximum)
            {
                Value = barMaximum;
            }
            else
            {
                Value = val;
            }
        }
    }
}
