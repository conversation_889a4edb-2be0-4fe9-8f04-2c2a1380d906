﻿using MetroFramework.Components;
using MetroFramework.Interfaces;
using OCRTools;
using System;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Windows.Forms;

namespace MetroFramework.Design
{
    public class MetroStyleManagerDesigner : ComponentDesigner
    {
        private IComponentChangeService _componentChangeService;

        private IDesignerHost _designerHost;
        private DesignerVerbCollection _designerVerbs;

        public override DesignerVerbCollection Verbs
        {
            get
            {
                if (_designerVerbs != null) return _designerVerbs;
                _designerVerbs = new DesignerVerbCollection
                {
                    new DesignerVerb("Reset Styles to Default", OnResetStyles)
                };
                return _designerVerbs;
            }
        }

        public IDesignerHost DesignerHost
        {
            get
            {
                if (_designerHost != null) return _designerHost;
                _designerHost = (IDesignerHost)GetService(typeof(IDesignerHost));
                return _designerHost;
            }
        }

        public IComponentChangeService ComponentChangeService
        {
            get
            {
                if (_componentChangeService != null) return _componentChangeService;
                _componentChangeService = (IComponentChangeService)GetService(typeof(IComponentChangeService));
                return _componentChangeService;
            }
        }

        private void OnResetStyles(object sender, EventArgs args)
        {
            var metroStyleManager = Component as MetroStyleManager;
            if (metroStyleManager != null && metroStyleManager.Owner == null)
                MessageBox.Show("StyleManager needs the Owner property assigned to before it can reset styles.",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
            else
                ResetStyles(metroStyleManager, metroStyleManager?.Owner);
        }

        private void ResetStyles(MetroStyleManager styleManager, Control control)
        {
            var metroForm = control as IMetroForm;
            if (metroForm == null || ReferenceEquals(styleManager, metroForm.StyleManager))
            {
                if (control is IMetroControl)
                {
                    ResetProperty(control, "Style", MetroCommonStyle.DefaultStyle);
                    ResetProperty(control, "Theme", MetroThemeStyle.Light);
                }
                else if (control is IMetroComponent)
                {
                    ResetProperty(control, "Style", MetroCommonStyle.DefaultStyle);
                    ResetProperty(control, "Theme", MetroThemeStyle.Light);
                }

                if (control.ContextMenuStrip != null) ResetStyles(styleManager, control.ContextMenuStrip);
                if (control is TabControl tabControl)
                    foreach (TabPage tabPage in tabControl.TabPages)
                        ResetStyles(styleManager, tabPage);
                foreach (Control control2 in control.Controls)
                    ResetStyles(styleManager, control2);
            }
        }

        private void ResetProperty(Control control, string name, object newValue)
        {
            var propertyDescriptor = TypeDescriptor.GetProperties(control)[name];
            if (propertyDescriptor != null)
            {
                var value = propertyDescriptor.GetValue(control);
                if (!newValue.Equals(value))
                {
                    ComponentChangeService.OnComponentChanging(control, propertyDescriptor);
                    propertyDescriptor.SetValue(control, newValue);
                    ComponentChangeService.OnComponentChanged(control, propertyDescriptor, value, newValue);
                }
            }
        }
    }
}