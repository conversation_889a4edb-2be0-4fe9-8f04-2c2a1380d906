﻿using System;
using System.Drawing;

namespace MetroFramework.Drawing
{
    public class MetroImage
    {
        public static Image ResizeImage(Image imgToResize, Rectangle maxOffset)
        {
            var width = imgToResize.Width;
            var height = imgToResize.Height;
            var num = maxOffset.Width / (float)width;
            var num2 = maxOffset.Height / (float)height;
            var num3 = num2 < num ? num2 : num;
            var thumbWidth = (int)(width * num3);
            var thumbHeight = (int)(height * num3);
            return imgToResize.GetThumbnailImage(thumbWidth, thumbHeight, null, IntPtr.Zero);
        }
    }
}