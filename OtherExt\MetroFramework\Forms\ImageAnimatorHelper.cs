using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection;
using System.Windows.Forms;

namespace OCRTools.OtherExt.MetroFramework.Forms
{
    internal class ImageAnimatorHelper
    {
        // 正在处理动画的对象集合
        private static HashSet<object> _animatingObjects = new HashSet<object>();

        /// <summary>
        /// 处理鼠标移动时的图片动画效果
        /// </summary>
        public static void HandleMouseMoveAnimation(object sender, EventArgs e)
        {
            if (sender == null)
                return;

            // 检查对象是否已在处理中，如果是则直接返回
            lock (_animatingObjects)
            {
                if (_animatingObjects.Contains(sender))
                    return;
            }

            Enum.TryParse<MouseMoveActionEnum>(CommonSetting.图片动效, out var effectType);
            if (effectType == MouseMoveActionEnum.无)
                return;

            try
            {
                var imgProperty = "Image";
                var baseImage = GetSenderImage(sender, imgProperty);
                if (baseImage == null)
                {
                    imgProperty = "BackgroundImage";
                    baseImage = GetSenderImage(sender, imgProperty);
                }
                if (baseImage == null)
                {
                    return;
                }

                // 启动动画
                CustomImageAnimator.StartAnimation(sender, imgProperty, baseImage, effectType);
            }
            catch { }
        }

        /// <summary>
        /// 获取对象的图片属性
        /// </summary>
        private static Image GetSenderImage(object sender, string imgProperty)
        {
            return (Image)sender?.GetType().GetProperty(imgProperty)?.GetValue(sender);
        }

        /// <summary>
        /// 鼠标移动动画效果类型
        /// </summary>
        [Obfuscation]
        public enum MouseMoveActionEnum
        {
            无 = 0,
            跳动 = 1,
            抖动 = 2,
            旋转 = 3,
        }

        /// <summary>
        /// 动画帧类型
        /// </summary>
        private enum FrameType
        {
            Scale,      // 缩放
            Rotate,     // 旋转
        }

        /// <summary>
        /// 动画帧 - 表示动画的一个状态
        /// </summary>
        private class AnimationFrame
        {
            // 帧类型
            public FrameType Type { get; set; }

            // 缩放因子（用于Scale类型）
            public float ScaleFactor { get; set; } = 1.0f;

            // 旋转角度（用于Rotate类型）
            public float Angle { get; set; } = 0f;
        }

        /// <summary>
        /// 动画上下文 - 保存动画的状态
        /// </summary>
        private class AnimationContext
        {
            // 要动画的对象
            public object Target { get; set; }

            // 对象的图像属性
            public string ImageProperty { get; set; }

            // 效果类型
            public MouseMoveActionEnum EffectType { get; set; }

            // 动画帧列表
            public AnimationFrame[] Frames { get; set; }

            // 当前帧索引
            public int FrameIndex { get; set; }

            // 原始图像
            public Image OriginalImage { get; set; }
        }

        /// <summary>
        /// 图像动画辅助类
        /// </summary>
        private static class CustomImageAnimator
        {
            // 动画定时器
            private static Timer _animationTimer;

            // 当前动画上下文
            private static AnimationContext _currentAnimation;

            // 动画中的图像列表（用于释放资源）
            private static List<Image> _animationImages = new List<Image>();

            /// <summary>
            /// 开始一个图片动画效果
            /// </summary>
            public static void StartAnimation(object sender, string imageProperty, Image originalImage, MouseMoveActionEnum effectType)
            {
                // 将对象添加到正在处理的集合中
                lock (_animatingObjects)
                {
                    _animatingObjects.Add(sender);
                }

                // 停止当前动画并清理资源
                StopAnimation();

                // 创建新的动画上下文
                _currentAnimation = new AnimationContext
                {
                    Target = sender,
                    ImageProperty = imageProperty,
                    EffectType = effectType,
                    OriginalImage = originalImage,
                    FrameIndex = 0,
                    Frames = CreateFrames(effectType)
                };

                // 开始动画
                StartAnimationTimer();
            }

            /// <summary>
            /// 停止当前动画并释放资源
            /// </summary>
            public static void StopAnimation()
            {
                // 停止定时器
                _animationTimer?.Stop();

                // 恢复原始图像
                if (_currentAnimation != null)
                {
                    try
                    {
                        // 检查当前图片是否在外部被更改
                        var currentImage = _currentAnimation.Target.GetType().GetProperty(_currentAnimation.ImageProperty)?.GetValue(_currentAnimation.Target) as Image;
                        if (currentImage != null && _animationImages.Contains(currentImage))
                        {
                            _currentAnimation.Target.GetType().GetProperty(_currentAnimation.ImageProperty)
                                ?.SetValue(_currentAnimation.Target, _currentAnimation.OriginalImage);

                            // 强制更新UI
                            if (_currentAnimation.Target is Control ctrl)
                            {
                                ctrl.Update();
                            }
                            else
                            {
                                Application.DoEvents();
                            }
                        }
                    }
                    catch { }

                    // 从正在处理的集合中移除对象
                    lock (_animatingObjects)
                    {
                        _animatingObjects.Remove(_currentAnimation.Target);
                    }
                }

                // 释放中间生成的图像
                CleanupImages();

                // 清空当前动画上下文
                _currentAnimation = null;
            }

            /// <summary>
            /// 清理所有中间生成的图像
            /// </summary>
            private static void CleanupImages()
            {
                //foreach (var img in _animationImages)
                //{
                //    try { img?.Dispose(); } catch { }
                //}
                _animationImages.Clear();
            }

            /// <summary>
            /// 创建动画帧
            /// </summary>
            private static AnimationFrame[] CreateFrames(MouseMoveActionEnum effectType)
            {
                switch (effectType)
                {
                    case MouseMoveActionEnum.跳动:
                        // 恢复原来跳动效果的参数，确保正常工作
                        return new[]
                        {
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 0.20f },
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 0.5f },
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 0.85f },
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 1.25f },
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 0.85f },
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 0.50f },
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 0.20f },
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 0.5f },
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 0.85f },
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 1.25f },
                            new AnimationFrame { Type = FrameType.Scale, ScaleFactor = 1.00f }
                        };
                    case MouseMoveActionEnum.抖动:
                        // 使用老版本的代码，角度使用300度和90度
                        return new[]
                        {
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 300f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 90f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 300f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 90f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 300f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 90f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 300f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 90f },
                        };
                    case MouseMoveActionEnum.旋转:
                        // 优化旋转效果：减少为一整圈(360度)，使动画更快完成
                        return new[]
                        {
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 0f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 45f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 90f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 135f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 180f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 225f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 270f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 315f },
                            new AnimationFrame { Type = FrameType.Rotate, Angle = 360f }
                        };
                    default:
                        return new AnimationFrame[0];
                }
            }

            /// <summary>
            /// 创建并启动动画定时器
            /// </summary>
            private static void StartAnimationTimer()
            {
                // 创建定时器
                _animationTimer = new Timer();

                // 根据效果类型设置定时器间隔 - 更流畅的帧率
                switch (_currentAnimation.EffectType)
                {
                    case MouseMoveActionEnum.跳动:
                        _animationTimer.Interval = 35; // 提高帧率到约45fps，使跳动效果更加流畅
                        break;
                    case MouseMoveActionEnum.抖动:
                        _animationTimer.Interval = 60; // 提高帧率到约50fps，使抖动效果更加明显
                        break;
                    case MouseMoveActionEnum.旋转:
                        _animationTimer.Interval = 30; // 约40fps，平衡的旋转动画帧率
                        break;
                    default:
                        _animationTimer.Interval = 33; // 默认约30fps
                        break;
                }

                // 设置定时器处理函数
                _animationTimer.Tick += AnimationTimerTick;

                // 启动定时器
                _animationTimer.Start();
            }

            /// <summary>
            /// 定时器每次触发时处理下一帧
            /// </summary>
            private static void AnimationTimerTick(object sender, EventArgs e)
            {
                if (_currentAnimation == null || _currentAnimation.FrameIndex >= _currentAnimation.Frames.Length)
                {
                    // 已完成一个周期，停止动画
                    StopAnimation();
                    return;
                }

                // 检查当前图片是否在外部被更改
                var currentImage = _currentAnimation.Target.GetType().GetProperty(_currentAnimation.ImageProperty)?.GetValue(_currentAnimation.Target) as Image;
                if (currentImage == null || (!_animationImages.Contains(currentImage) && currentImage != _currentAnimation.OriginalImage))
                {
                    // 图片已被外部修改，停止动画
                    StopAnimation();
                    return;
                }

                // 获取当前帧
                var frame = _currentAnimation.Frames[_currentAnimation.FrameIndex++];

                try
                {
                    // 应用当前帧效果
                    Image newImage = null;

                    switch (frame.Type)
                    {
                        case FrameType.Scale:
                            newImage = ImageProcessHelper.ZoomAndCropImage(_currentAnimation.OriginalImage, frame.ScaleFactor);
                            break;
                        case FrameType.Rotate:
                            // 注意旋转模式下使用当前图像，而非原始图像
                            if (currentImage != null)
                            {
                                newImage = ImageProcessHelper.RotateImage(currentImage, frame.Angle);
                            }
                            break;
                    }

                    if (newImage != null)
                    {
                        // 将新图像添加到列表以便后续释放
                        _animationImages.Add(newImage);

                        // 应用新图像到控件
                        _currentAnimation.Target.GetType().GetProperty(_currentAnimation.ImageProperty)
                            ?.SetValue(_currentAnimation.Target, newImage);

                        // 强制更新UI
                        if (_currentAnimation.Target is Control ctrl)
                        {
                            ctrl.Update();
                        }
                        else
                        {
                            Application.DoEvents();
                        }
                    }
                }
                catch
                {
                    // 发生错误，停止动画
                    StopAnimation();
                }
            }
        }
    }
}
