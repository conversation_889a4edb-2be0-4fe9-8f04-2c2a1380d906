﻿using System.Collections.Generic;
using System.Drawing;

namespace MetroFramework
{
    public sealed class MetroBrushes
    {
        private static Dictionary<string, SolidBrush> _metroBrushes;

        public static SolidBrush GetSaveBrush(string key, Color color)
        {
            if (_metroBrushes == null)
                _metroBrushes = new Dictionary<string, SolidBrush>();

            if (!_metroBrushes.ContainsKey(key))
                _metroBrushes.Add(key, new SolidBrush(color));

            return _metroBrushes[key].Clone() as SolidBrush;
        }
    }
}