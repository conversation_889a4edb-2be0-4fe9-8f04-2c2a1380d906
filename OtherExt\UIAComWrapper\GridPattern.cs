using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class GridPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = GridPatternIdentifiers.Pattern;


        protected GridPattern(AutomationElement el, IUIAutomationGridPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new GridPattern(el, (IUIAutomationGridPattern)pattern, cached);
        }
    }

    public class GridItemPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = GridItemPatternIdentifiers.Pattern;

        protected GridItemPattern(AutomationElement el, IUIAutomationGridItemPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new GridItemPattern(el, (IUIAutomationGridItemPattern)pattern, cached);
        }
    }
}