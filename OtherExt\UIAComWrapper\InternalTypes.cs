using System;
using System.Runtime.InteropServices;
using System.Security;

namespace UIAComWrapperInternal
{
    // It would be nice to do without these, but the AutomationIdentifiers allow you to query for GUIDs, and 
    // so we end up needing them.
    public static class AutomationIdentifierGuids
    {
        //public static readonly Guid AcceleratorKey_Property =
        //    new Guid(0x514865df, 0x2557, 0x4cb9, 0xae, 0xed, 0x6c, 0xed, 8, 0x4c, 0xe5, 0x2c);

        //public static readonly Guid AccessKey_Property =
        //    new Guid(0x6827b12, 0xa7f9, 0x4a15, 0x91, 0x7c, 0xff, 0xa5, 0xad, 0x3e, 0xb0, 0xa7);

        //public static readonly Guid AsyncContentLoaded_Event =
        //    new Guid(0x5fdee11c, 0xd2fa, 0x4fb9, 0x90, 0x4e, 0x5c, 190, 0xe8, 0x94, 0xd5, 0xef);

        //public static readonly Guid AutomationFocusChanged_Event =
        //    new Guid(0xb68a1f17, 0xf60d, 0x41a7, 0xa3, 0xcc, 0xb0, 0x52, 0x92, 0x15, 0x5f, 0xe0);

        //public static readonly Guid AutomationId_Property =
        //    new Guid(0xc82c0500, 0xb60e, 0x4310, 0xa2, 0x67, 0x30, 60, 0x53, 0x1f, 0x8e, 0xe5);

        //public static readonly Guid AutomationPropertyChanged_Event =
        //    new Guid(0x2527fba1, 0x8d7a, 0x4630, 0xa4, 0xcc, 230, 0x63, 0x15, 0x94, 0x2f, 0x52);

        public static readonly Guid BoundingRectangle_Property =
            new Guid(0x7bbfe8b2, 0x3bfc, 0x48dd, 0xb7, 0x29, 0xc7, 0x94, 0xb8, 70, 0xe9, 0xa1);

        public static readonly Guid Button_Control =
            new Guid(0x5a78e369, 0xc6a1, 0x4f33, 0xa9, 0xd7, 0x79, 0xf2, 13, 12, 120, 0x8e);

        public static readonly Guid ClassName_Property =
            new Guid(0x157b7215, 0x894f, 0x4b65, 0x84, 0xe2, 170, 0xc0, 0xda, 8, 0xb1, 0x6b);

        public static readonly Guid ClickablePoint_Property =
            new Guid(0x196903b, 0xb203, 0x4818, 0xa9, 0xf3, 240, 0x8e, 0x67, 0x5f, 0x23, 0x41);

        public static readonly Guid ControlType_Property =
            new Guid(0xca774fea, 0x28ac, 0x4bc2, 0x94, 0xca, 0xac, 0xec, 0x6d, 0x6c, 0x10, 0xa3);

        public static readonly Guid Dock_Pattern =
            new Guid(0x9cbaa846, 0x83c8, 0x428d, 130, 0x7f, 0x7e, 0x60, 0x63, 0xfe, 6, 0x20);

        public static readonly Guid Dock_Position_Property =
            new Guid(0x6d67f02e, 0xc0b0, 0x4b10, 0xb5, 0xb9, 0x18, 0xd6, 0xec, 0xf9, 0x87, 0x60);

        public static readonly Guid Edit_Control =
            new Guid(0x6504a5c8, 0x2c86, 0x4f87, 0xae, 0x7b, 0x1a, 0xbd, 220, 0x81, 12, 0xf9);

        public static readonly Guid ExpandCollapse_Pattern =
            new Guid(0xae05efa2, 0xf9d1, 0x428a, 0x83, 0x4c, 0x53, 0xa5, 0xc5, 0x2f, 0x9b, 0x8b);

        public static readonly Guid ExpandCollapse_State_Property =
            new Guid(0x275a4c48, 0x85a7, 0x4f69, 0xab, 160, 0xaf, 0x15, 0x76, 0x10, 0, 0x2b);

        //public static readonly Guid FrameworkId_Property =
        //    new Guid(0xdbfd9900, 0x7e1a, 0x4f58, 0xb6, 0x1b, 0x70, 0x63, 0x12, 15, 0x77, 0x3b);

        public static readonly Guid Grid_Pattern =
            new Guid(0x260a2ccb, 0x93a8, 0x4e44, 0xa4, 0xc1, 0x3d, 0xf3, 0x97, 0xf2, 0xb0, 0x2b);

        public static readonly Guid GridItem_Pattern =
            new Guid(0xf2d5c877, 0xa462, 0x4957, 0xa2, 0xa5, 0x2c, 150, 0xb3, 3, 0xbc, 0x63);

        public static readonly Guid Group_Control =
            new Guid(0xad50aa1c, 0xe8c8, 0x4774, 0xae, 0x1b, 0xdd, 0x86, 0xdf, 11, 0x3b, 220);

        public static readonly Guid HasKeyboardFocus_Property =
            new Guid(0xcf8afd39, 0x3f46, 0x4800, 150, 0x56, 0xb2, 0xbf, 0x12, 0x52, 0x99, 5);

        public static readonly Guid Header_Control =
            new Guid(0x5b90cbce, 0x78fb, 0x4614, 130, 0xb6, 0x55, 0x4d, 0x74, 0x71, 0x8e, 0x67);

        public static readonly Guid Image_Control =
            new Guid(0x2d3736e4, 0x6b16, 0x4c57, 0xa9, 0x62, 0xf9, 50, 0x60, 0xa7, 0x52, 0x43);

        public static readonly Guid Invoke_Pattern =
            new Guid(0xd976c2fc, 0x66ea, 0x4a6e, 0xb2, 0x8f, 0xc2, 0x4c, 0x75, 70, 0xad, 0x37);

        public static readonly Guid IsContentElement_Property =
            new Guid(0x4bda64a8, 0xf5d8, 0x480b, 0x81, 0x55, 0xef, 0x2e, 0x89, 0xad, 0xb6, 0x72);

        public static readonly Guid IsControlElement_Property =
            new Guid(0x95f35085, 0xabcc, 0x4afd, 0xa5, 0xf4, 0xdb, 180, 0x6c, 0x23, 15, 0xdb);

        public static readonly Guid IsKeyboardFocusable_Property =
            new Guid(0xf7b8552a, 0x859, 0x4b37, 0xb9, 0xcb, 0x51, 0xe7, 0x20, 0x92, 0xf2, 0x9f);

        public static readonly Guid IsOffscreen_Property =
            new Guid(0x3c3d160, 0xdb79, 0x42db, 0xa2, 0xef, 0x1c, 0x23, 30, 0xed, 0xe5, 7);

        public static readonly Guid IsPassword_Property =
            new Guid(0xe8482eb1, 0x687c, 0x497b, 190, 0xbc, 3, 190, 0x53, 0xec, 20, 0x54);

        public static readonly Guid List_Control =
            new Guid(0x9b149ee1, 0x7cca, 0x4cfc, 0x9a, 0xf1, 0xca, 0xc7, 0xbd, 0xdd, 0x30, 0x31);

        //public static readonly Guid LocalizedControlType_Property =
        //    new Guid(0x8763404f, 0xa1bd, 0x452a, 0x89, 0xc4, 0x3f, 1, 0xd3, 0x83, 0x38, 6);

        public static readonly Guid Menu_Control =
            new Guid(0x2e9b1440, 0xea8, 0x41fd, 0xb3, 0x74, 0xc1, 0xea, 0x6f, 80, 60, 0xd1);

        public static readonly Guid MultipleView_Pattern =
            new Guid(0x547a6ae4, 0x113f, 0x47c4, 0x85, 15, 0xdb, 0x4d, 250, 70, 0x6b, 0x1d);

        public static readonly Guid Name_Property =
            new Guid(0xc3a6921b, 0x4a99, 0x44f1, 0xbc, 0xa6, 0x61, 0x18, 0x70, 0x52, 0xc4, 0x31);

        public static readonly Guid NewNativeWindowHandle_Property =
            new Guid(0x5196b33b, 0x380a, 0x4982, 0x95, 0xe1, 0x91, 0xf3, 0xef, 0x60, 0xe0, 0x24);

        public static readonly Guid Orientation_Property =
            new Guid(0xa01eee62, 0x3884, 0x4415, 0x88, 0x7e, 0x67, 0x8e, 0xc2, 30, 0x39, 0xba);

        public static readonly Guid RangeValue_Pattern =
            new Guid(0x18b00d87, 0xb1c9, 0x476a, 0xbf, 0xbd, 0x5f, 11, 0xdb, 0x92, 0x6f, 0x63);

        public static readonly Guid Scroll_Pattern =
            new Guid(0x895fa4b4, 0x759d, 0x4c50, 0x8e, 0x15, 3, 70, 6, 0x72, 0, 60);

        public static readonly Guid ScrollItem_Pattern =
            new Guid(0x4591d005, 0xa803, 0x4d5c, 180, 0xd5, 0x8d, 40, 0, 0xf9, 6, 0xa7);

        public static readonly Guid Selection_Pattern =
            new Guid(0x66e3b7e8, 0xd821, 0x4d25, 0x87, 0x61, 0x43, 0x5d, 0x2c, 0x8b, 0x25, 0x3f);

        public static readonly Guid SelectionItem_Pattern =
            new Guid(0x9bc64eeb, 0x87c7, 0x4b28, 0x94, 0xbb, 0x4d, 0x9f, 0xa4, 0x37, 0xb6, 0xef);

        public static readonly Guid Tab_Control =
            new Guid(0x38cd1f2d, 0x337a, 0x4bd2, 0xa5, 0xe3, 0xad, 180, 0x69, 0xe3, 11, 0xd3);

        public static readonly Guid Table_Control =
            new Guid(0x773bfa0e, 0x5bc4, 0x4deb, 0x92, 0x1b, 0xde, 0x7b, 50, 6, 0x22, 0x9e);

        public static readonly Guid Table_Pattern =
            new Guid(0xc415218e, 0xa028, 0x461e, 170, 0x92, 0x8f, 0x92, 0x5c, 0xf7, 0x93, 0x51);

        public static readonly Guid Table_RowOrColumnMajor_Property =
            new Guid(0x83be75c3, 0x29fe, 0x4a30, 0x85, 0xe1, 0x2a, 0x62, 0x77, 0xfd, 0x10, 110);

        public static readonly Guid TableItem_Pattern =
            new Guid(0xdf1343bd, 0x1888, 0x4a29, 0xa5, 12, 0xb9, 0x2e, 0x6d, 0xe3, 0x7f, 0x6f);

        public static readonly Guid Text_AnimationStyle_Attribute =
            new Guid(0x628209f0, 0x7c9a, 0x4d57, 190, 100, 0x1f, 0x18, 0x36, 0x57, 0x1f, 0xf5);

        public static readonly Guid Text_BackgroundColor_Attribute =
            new Guid(0xfdc49a07, 0x583d, 0x4f17, 0xad, 0x27, 0x77, 0xfc, 0x83, 0x2a, 60, 11);

        public static readonly Guid Text_BulletStyle_Attribute =
            new Guid(0xc1097c90, 0xd5c4, 0x4237, 0x97, 0x81, 0x3b, 0xec, 0x8b, 0xa5, 0x4e, 0x48);

        public static readonly Guid Text_CapStyle_Attribute =
            new Guid(0xfb059c50, 0x92cc, 0x49a5, 0xba, 0x8f, 10, 0xa8, 0x72, 0xbb, 0xa2, 0xf3);

        public static readonly Guid Text_Control =
            new Guid(0xae9772dc, 0xd331, 0x4f09, 190, 0x20, 0x7e, 0x6d, 250, 240, 0x7b, 10);

        public static readonly Guid Text_Culture_Attribute =
            new Guid(0xc2025af9, 0xa42d, 0x4ced, 0xa1, 0xfb, 0xc6, 0x74, 0x63, 0x15, 0x22, 0x2e);

        public static readonly Guid Text_FlowDirections_Attribute =
            new Guid(0x8bdf8739, 0xf420, 0x423e, 0xaf, 0x77, 0x20, 0xa5, 0xd9, 0x73, 0xa9, 7);

        public static readonly Guid Text_FontName_Attribute =
            new Guid(0x64e63ba8, 0xf2e5, 0x476e, 0xa4, 0x77, 0x17, 0x34, 0xfe, 170, 0xf7, 0x26);

        public static readonly Guid Text_FontSize_Attribute =
            new Guid(0xdc5eeeff, 0x506, 0x4673, 0x93, 0xf2, 0x37, 0x7e, 0x4a, 0x8e, 1, 0xf1);

        public static readonly Guid Text_FontWeight_Attribute =
            new Guid(0x6fc02359, 0xb316, 0x4f5f, 180, 1, 0xf1, 0xce, 0x55, 0x74, 0x18, 0x53);

        public static readonly Guid Text_ForegroundColor_Attribute =
            new Guid(0x72d1c95d, 0x5e60, 0x471a, 150, 0xb1, 0x6c, 0x1b, 0x3b, 0x77, 0xa4, 0x36);

        public static readonly Guid Text_HorizontalTextAlignment_Attribute =
            new Guid(0x4ea6161, 0xfba3, 0x477a, 0x95, 0x2a, 0xbb, 50, 0x6d, 2, 0x6a, 0x5b);

        public static readonly Guid Text_IndentationFirstLine_Attribute =
            new Guid(0x206f9ad5, 0xc1d3, 0x424a, 0x81, 130, 0x6d, 0xa9, 0xa7, 0xf3, 0xd6, 50);

        public static readonly Guid Text_IndentationLeading_Attribute =
            new Guid(0x5cf66bac, 0x2d45, 0x4a4b, 0xb6, 0xc9, 0xf7, 0x22, 0x1d, 40, 0x15, 0xb0);

        public static readonly Guid Text_IndentationTrailing_Attribute =
            new Guid(0x97ff6c0f, 0x1ce4, 0x408a, 0xb6, 0x7b, 0x94, 0xd8, 0x3e, 0xb6, 0x9b, 0xf2);

        public static readonly Guid Text_IsHidden_Attribute =
            new Guid(0x360182fb, 0xbdd7, 0x47f6, 0xab, 0x69, 0x19, 0xe3, 0x3f, 0x8a, 0x33, 0x44);

        public static readonly Guid Text_IsItalic_Attribute =
            new Guid(0xfce12a56, 0x1336, 0x4a34, 150, 0x63, 0x1b, 0xab, 0x47, 0x23, 0x93, 0x20);

        public static readonly Guid Text_IsReadOnly_Attribute =
            new Guid(0xa738156b, 0xca3e, 0x495e, 0x95, 20, 0x83, 60, 0x44, 15, 0xeb, 0x11);

        public static readonly Guid Text_IsSubscript_Attribute =
            new Guid(0xf0ead858, 0x8f53, 0x413c, 0x87, 0x3f, 0x1a, 0x7d, 0x7f, 0x5e, 13, 0xe4);

        public static readonly Guid Text_IsSuperscript_Attribute =
            new Guid(0xda706ee4, 0xb3aa, 0x4645, 0xa4, 0x1f, 0xcd, 0x25, 0x15, 0x7d, 0xea, 0x76);

        public static readonly Guid Text_MarginBottom_Attribute =
            new Guid(0x7ee593c4, 0x72b4, 0x4cac, 0x92, 0x71, 0x3e, 210, 0x4b, 14, 0x4d, 0x42);

        public static readonly Guid Text_MarginLeading_Attribute =
            new Guid(0x9e9242d0, 0x5ed0, 0x4900, 0x8e, 0x8a, 0xee, 0xcc, 3, 0x83, 90, 0xfc);

        public static readonly Guid Text_MarginTop_Attribute =
            new Guid(0x683d936f, 0xc9b9, 0x4a9a, 0xb3, 0xd9, 210, 13, 0x33, 0x31, 30, 0x2a);

        public static readonly Guid Text_MarginTrailing_Attribute =
            new Guid(0xaf522f98, 0x999d, 0x40af, 0xa5, 0xb2, 1, 0x69, 0xd0, 0x34, 0x20, 2);

        public static readonly Guid Text_OutlineStyles_Attribute =
            new Guid(0x5b675b27, 0xdb89, 0x46fe, 0x97, 12, 0x61, 0x4d, 0x52, 0x3b, 0xb9, 0x7d);

        public static readonly Guid Text_OverlineColor_Attribute =
            new Guid(0x83ab383a, 0xfd43, 0x40da, 0xab, 0x3e, 0xec, 0xf8, 0x16, 0x5c, 0xbb, 0x6d);

        public static readonly Guid Text_OverlineStyle_Attribute =
            new Guid(0xa234d66, 0x617e, 0x427f, 0x87, 0x1d, 0xe1, 0xff, 30, 12, 0x21, 0x3f);

        public static readonly Guid Text_Pattern =
            new Guid(0x8615f05d, 0x7de5, 0x44fd, 0xa6, 0x79, 0x2c, 0xa4, 180, 0x60, 0x33, 0xa8);

        public static readonly Guid Text_StrikethroughColor_Attribute =
            new Guid(0xbfe15a18, 0x8c41, 0x4c5a, 0x9a, 11, 4, 0xaf, 14, 7, 0xf4, 0x87);

        public static readonly Guid Text_StrikethroughStyle_Attribute =
            new Guid(0x72913ef1, 0xda00, 0x4f01, 0x89, 0x9c, 0xac, 90, 0x85, 0x77, 0xa3, 7);

        public static readonly Guid Text_Tabs_Attribute =
            new Guid(0x2e68d00b, 0x92fe, 0x42d8, 0x89, 0x9a, 0xa7, 0x84, 170, 0x44, 0x54, 0xa1);

        public static readonly Guid Text_UnderlineColor_Attribute =
            new Guid(0xbfa12c73, 0xfde2, 0x4473, 0xbf, 100, 0x10, 0x36, 0xd6, 170, 15, 0x45);

        public static readonly Guid Text_UnderlineStyle_Attribute =
            new Guid(0x5f3b21c0, 0xede4, 0x44bd, 0x9c, 0x36, 0x38, 0x53, 3, 140, 0xbf, 0xeb);

        public static readonly Guid Toggle_Pattern =
            new Guid(0xb419760, 0xe2f4, 0x43ff, 140, 0x5f, 0x94, 0x57, 200, 0x2b, 0x56, 0xe9);

        public static readonly Guid Toggle_State_Property =
            new Guid(0xb23cdc52, 0x22c2, 0x4c6c, 0x9d, 0xed, 0xf5, 0xc4, 0x22, 0x47, 0x9e, 0xde);

        public static readonly Guid Transform_Pattern =
            new Guid(0x24b46fdb, 0x587e, 0x49f1, 0x9c, 0x4a, 0xd8, 0xe9, 0x8b, 0x66, 0x4b, 0x7b);

        public static readonly Guid Tree_Control =
            new Guid(0x7561349c, 0xd241, 0x43f4, 0x99, 8, 0xb5, 240, 0x91, 190, 230, 0x11);

        public static readonly Guid Value_Pattern =
            new Guid(0x17faad9e, 0xc877, 0x475b, 0xb9, 0x33, 0x77, 0x33, 0x27, 0x79, 0xb6, 0x37);

        public static readonly Guid Window_Control =
            new Guid(0xe13a7242, 0xf462, 0x4f4d, 0xae, 0xc1, 0x53, 0xb2, 0x8d, 0x6c, 50, 0x90);

        public static readonly Guid Window_InteractionState_Property =
            new Guid(0x4fed26a4, 0x455, 0x4fa2, 0xb2, 0x1c, 0xc4, 0xda, 0x2d, 0xb1, 0xff, 0x9c);

        public static readonly Guid Window_Pattern =
            new Guid(0x27901735, 0xc760, 0x4994, 0xad, 0x11, 0x59, 0x19, 230, 6, 0xb1, 0x10);

        public static readonly Guid Window_VisualState_Property =
            new Guid(0x4ab7905f, 0xe860, 0x453e, 0xa3, 10, 0xf6, 0x43, 30, 0x5d, 170, 0xd5);

        public static readonly Guid LegacyIAccessible_Pattern =
            new Guid(0x54cc0a9f, 0x3395, 0x48af, 0xba, 0x8d, 0x73, 0xf8, 0x56, 0x90, 0xf3, 0xe0);

        public static readonly Guid ItemContainer_Pattern =
            new Guid(0x3d13da0f, 0x8b9a, 0x4a99, 0x85, 0xfa, 0xc5, 0xc9, 0xa6, 0x9f, 0x1e, 0xd4);

        public static readonly Guid VirtualizedItem_Pattern =
            new Guid(0xf510173e, 0x2e71, 0x45e9, 0xa6, 0xe5, 0x62, 0xf6, 0xed, 0x82, 0x89, 0xd5);

        public static readonly Guid SynchronizedInput_Pattern =
            new Guid(0x05c288a6, 0xc47b, 0x488b, 0xb6, 0x53, 0x33, 0x97, 0x7a, 0x55, 0x1b, 0x8b);

        public static readonly Guid Annotation_AnnotationTypeId_Property =
            new Guid(0x20ae484f, 0x69ef, 0x4c48, 0x8f, 0x5b, 0xc4, 0x93, 0x8b, 0x20, 0x6a, 0xc7);

        public static readonly Guid Styles_StyleId_Property =
            new Guid(0xda82852f, 0x3817, 0x4233, 0x82, 0xaf, 0x02, 0x27, 0x9e, 0x72, 0xcc, 0x77);

        public static readonly Guid ObjectModel_Pattern =
            new Guid(0x3e04acfe, 0x08fc, 0x47ec, 0x96, 0xbc, 0x35, 0x3f, 0xa3, 0xb3, 0x4a, 0xa7);

        public static readonly Guid Annotation_Pattern =
            new Guid(0xf6c72ad7, 0x356c, 0x4850, 0x92, 0x91, 0x31, 0x6f, 0x60, 0x8a, 0x8c, 0x84);

        public static readonly Guid Text_Pattern2 =
            new Guid(0x498479a2, 0x5b22, 0x448d, 0xb6, 0xe4, 0x64, 0x74, 0x90, 0x86, 0x06, 0x98);

        public static readonly Guid Styles_Pattern =
            new Guid(0x1ae62655, 0xda72, 0x4d60, 0xa1, 0x53, 0xe5, 0xaa, 0x69, 0x88, 0xe3, 0xbf);

        public static readonly Guid Spreadsheet_Pattern =
            new Guid(0x6a5b24c9, 0x9d1e, 0x4b85, 0x9e, 0x44, 0xc0, 0x2e, 0x31, 0x69, 0xb1, 0x0b);

        public static readonly Guid SpreadsheetItem_Pattern =
            new Guid(0x32cf83ff, 0xf1a8, 0x4a8c, 0x86, 0x58, 0xd4, 0x7b, 0xa7, 0x4e, 0x20, 0xba);

        public static readonly Guid Tranform_Pattern2 =
            new Guid(0x8afcfd07, 0xa369, 0x44de, 0x98, 0x8b, 0x2f, 0x7f, 0xf4, 0x9f, 0xb8, 0xa8);

        public static readonly Guid TextChild_Pattern =
            new Guid(0x7533cab7, 0x3bfe, 0x41ef, 0x9e, 0x85, 0xe2, 0x63, 0x8c, 0xbe, 0x16, 0x9e);

        public static readonly Guid Drag_Pattern =
            new Guid(0xc0bee21f, 0xccb3, 0x4fed, 0x99, 0x5b, 0x11, 0x4f, 0x6e, 0x3d, 0x27, 0x28);

        public static readonly Guid DropTarget_Pattern =
            new Guid(0xbcbec56, 0xbd34, 0x4b7b, 0x9f, 0xd5, 0x26, 0x59, 0x90, 0x5e, 0xa3, 0xdc);

        public static readonly Guid Text_AnnotationTypes_Attribute =
            new Guid(0xad2eb431, 0xee4e, 0x4be1, 0xa7, 0xba, 0x55, 0x59, 0x15, 0x5a, 0x73, 0xef);

        public static readonly Guid Text_AnnotationObjects_Attribute =
            new Guid(0xff41cf68, 0xe7ab, 0x40b9, 0x8c, 0x72, 0x72, 0xa8, 0xed, 0x94, 0x01, 0x7d);

        public static readonly Guid Text_StyleName_Attribute =
            new Guid(0x22c9e091, 0x4d66, 0x45d8, 0xa8, 0x28, 0x73, 0x7b, 0xab, 0x4c, 0x98, 0xa7);

        public static readonly Guid Text_StyleId_Attribute =
            new Guid(0x14c300de, 0xc32b, 0x449b, 0xab, 0x7c, 0xb0, 0xe0, 0x78, 0x9a, 0xea, 0x5d);

        public static readonly Guid Text_Link_Attribute =
            new Guid(0xb38ef51d, 0x9e8d, 0x4e46, 0x91, 0x44, 0x56, 0xeb, 0xe1, 0x77, 0x32, 0x9b);

        public static readonly Guid Text_IsActive_Attribute =
            new Guid(0xf5a4e533, 0xe1b8, 0x436b, 0x93, 0x5d, 0xb5, 0x7a, 0xa3, 0xf5, 0x58, 0xc4);

        public static readonly Guid Text_SelectionActiveEnd_Attribute =
            new Guid(0x1f668cc3, 0x9bbf, 0x416b, 0xb0, 0xa2, 0xf8, 0x9f, 0x86, 0xf6, 0x61, 0x2c);

        public static readonly Guid Text_CaretPosition_Attribute =
            new Guid(0xb227b131, 0x9889, 0x4752, 0xa9, 0x1b, 0x73, 0x3e, 0xfd, 0xc5, 0xc5, 0xa0);

        public static readonly Guid Text_CaretBidiMode_Attribute =
            new Guid(0x929ee7a6, 0x51d3, 0x4715, 0x96, 0xdc, 0xb6, 0x94, 0xfa, 0x24, 0xa1, 0x68);
    }

    public static class UiaCoreIds
    {
        internal const int UIA_E_ELEMENTNOTAVAILABLE = -2147220991;
        internal const int UIA_E_ELEMENTNOTENABLED = -2147220992;
        internal const int UIA_E_NOCLICKABLEPOINT = -2147220990;
        internal const int UIA_E_PROXYASSEMBLYNOTLOADED = -2147220989;

        [SuppressUnmanagedCodeSecurity]
        [SecurityCritical]
        [DllImport("UIAutomationCore.dll", EntryPoint = "UiaLookupId", CharSet = CharSet.Unicode)]
        private static extern int RawUiaLookupId(AutomationIdType type, ref Guid guid);

        [SecurityCritical]
        [SecuritySafeCritical]
        public static int UiaLookupId(AutomationIdType type, ref Guid guid)
        {
            return RawUiaLookupId(type, ref guid);
        }

        public enum AutomationIdType
        {
            Property,
            Pattern,
            Event,
            ControlType,
            TextAttribute
        }
    }
}