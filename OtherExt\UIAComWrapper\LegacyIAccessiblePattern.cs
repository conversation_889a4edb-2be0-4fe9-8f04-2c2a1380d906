using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class LegacyIAccessiblePattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = LegacyIAccessiblePatternIdentifiers.Pattern;

        private LegacyIAccessiblePattern(AutomationElement el, IUIAutomationLegacyIAccessiblePattern pattern,
            bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new LegacyIAccessiblePattern(el, (IUIAutomationLegacyIAccessiblePattern)pattern, cached);
        }
    }
}