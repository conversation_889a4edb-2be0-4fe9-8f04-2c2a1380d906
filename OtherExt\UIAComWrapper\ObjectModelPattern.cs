using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class ObjectModelPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = ObjectModelPatternIdentifiers.Pattern;

        private ObjectModelPattern(AutomationElement el, IUIAutomationObjectModelPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new ObjectModelPattern(el, (IUIAutomationObjectModelPattern)pattern, cached);
        }
    }
}