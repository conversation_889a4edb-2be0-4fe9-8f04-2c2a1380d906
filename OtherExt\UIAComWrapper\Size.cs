using System.Runtime.InteropServices;

namespace System.Windows
{
    [Serializable]
    [StructLayout(LayoutKind.Sequential)]
    public struct Size
    {
        internal double _width;
        internal double _height;

        public static bool operator ==(Size size1, Size size2)
        {
            return size1.Width == size2.Width && size1.Height == size2.Height;
        }

        public static bool operator !=(Size size1, Size size2)
        {
            return !(size1 == size2);
        }

        public static bool Equals(Size size1, Size size2)
        {
            if (size1.IsEmpty) return size2.IsEmpty;
            return size1.Width.Equals(size2.Width) && size1.Height.Equals(size2.Height);
        }

        public override bool Equals(object o)
        {
            if (o == null || !(o is Size size)) return false;
            return Equals(this, size);
        }

        public bool Equals(Size value)
        {
            return Equals(this, value);
        }

        public override int GetHashCode()
        {
            if (IsEmpty) return 0;
            return Width.GetHashCode() ^ Height.GetHashCode();
        }

        public Size(double width, double height)
        {
            if (width < 0.0 || height < 0.0) throw new ArgumentException("Size_WidthAndHeightCannotBeNegative");
            _width = width;
            _height = height;
        }

        public static Size Empty { get; } = CreateEmptySize();

        public bool IsEmpty => _width < 0.0;

        public double Width
        {
            get => _width;
            set
            {
                if (IsEmpty) throw new InvalidOperationException("Size_CannotModifyEmptySize");
                if (value < 0.0) throw new ArgumentException("Size_WidthCannotBeNegative");
                _width = value;
            }
        }

        public double Height
        {
            get => _height;
            set
            {
                if (IsEmpty) throw new InvalidOperationException("Size_CannotModifyEmptySize");
                if (value < 0.0) throw new ArgumentException("Size_HeightCannotBeNegative");
                _height = value;
            }
        }

        public static explicit operator Point(Size size)
        {
            return new Point(size._width, size._height);
        }

        private static Size CreateEmptySize()
        {
            var size = new Size
            {
                _width = double.NegativeInfinity,
                _height = double.NegativeInfinity
            };
            return size;
        }
    }
}