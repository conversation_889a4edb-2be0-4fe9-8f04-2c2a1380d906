using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class SpreadsheetPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SpreadsheetPatternIdentifiers.Pattern;

        private SpreadsheetPattern(AutomationElement el, IUIAutomationSpreadsheetPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new SpreadsheetPattern(el, (IUIAutomationSpreadsheetPattern)pattern, cached);
        }
    }

    public class SpreadsheetItemPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SpreadsheetItemPatternIdentifiers.Pattern;

        private SpreadsheetItemPattern(AutomationElement el, IUIAutomationSpreadsheetItemPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new SpreadsheetItemPattern(el, (IUIAutomationSpreadsheetItemPattern)pattern, cached);
        }
    }
}