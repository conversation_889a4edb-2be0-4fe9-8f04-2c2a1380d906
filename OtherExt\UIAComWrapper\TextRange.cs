using System.Diagnostics;
using System.Runtime.InteropServices;
using UIAComWrapperInternal;
using UIAutomationClient;

namespace System.Windows.Automation.Text
{
    public class TextPatternRange
    {
        internal TextPatternRange(IUIAutomationTextRange range, TextPattern pattern)
        {
            Debug.Assert(range != null);
            Debug.Assert(pattern != null);
            NativeRange = range;
            TextPattern = pattern;
        }


        internal IUIAutomationTextRange NativeRange { get; }

        public TextPattern TextPattern { get; }

        public static TextPatternRange Wrap(IUIAutomationTextRange range, TextPattern pattern)
        {
            Debug.Assert(pattern != null);
            if (range == null)
                return null;
            return new TextPatternRange(range, pattern);
        }

        public TextPatternRange Clone()
        {
            try
            {
                return Wrap(NativeRange.Clone(), TextPattern);
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        public string GetText(int maxLength)
        {
            try
            {
                return NativeRange.GetText(maxLength);
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        public int Move(TextUnit unit, int count)
        {
            try
            {
                return NativeRange.Move((UIAutomationClient.TextUnit)unit, count);
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        public void Select()
        {
            try
            {
                NativeRange.Select();
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        public static TextPatternRange[] Wrap(IUIAutomationTextRangeArray ranges, TextPattern pattern)
        {
            if (ranges == null) return null;
            var rangeArray = new TextPatternRange[ranges.Length];
            for (var i = 0; i < ranges.Length; i++) rangeArray[i] = new TextPatternRange(ranges.GetElement(i), pattern);
            return rangeArray;
        }
    }
}