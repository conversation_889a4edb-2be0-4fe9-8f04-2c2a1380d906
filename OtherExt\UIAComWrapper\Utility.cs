using System;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Automation;
using UIAutomationClient;

namespace UIAComWrapperInternal
{
    public class Utility
    {
        public static AutomationElement[] ConvertToElementArray(IUIAutomationElementArray array)
        {
            AutomationElement[] elementArray;
            if (array != null)
            {
                elementArray = new AutomationElement[array.Length];
                for (var i = 0; i < array.Length; i++) elementArray[i] = AutomationElement.Wrap(array.GetElement(i));
            }
            else
            {
                elementArray = null;
            }

            return elementArray;
        }

        public static bool ConvertException(COMException e, out Exception uiaException)
        {
            var handled = true;
            switch (e.ErrorCode)
            {
                case UiaCoreIds.UIA_E_ELEMENTNOTAVAILABLE:
                    uiaException = new ElementNotAvailableException(e);
                    break;

                case UiaCoreIds.UIA_E_ELEMENTNOTENABLED:
                    uiaException = new ElementNotEnabledException(e);
                    break;

                case UiaCoreIds.UIA_E_NOCLICKABLEPOINT:
                    uiaException = new NoClickablePointException(e);
                    break;

                case UiaCoreIds.UIA_E_PROXYASSEMBLYNOTLOADED:
                    uiaException = new ProxyAssemblyNotLoadedException(e);
                    break;

                default:
                    uiaException = null;
                    handled = false;
                    break;
            }

            return handled;
        }

        public static tagPOINT PointManagedToNative(Point pt)
        {
            var nativePoint = new tagPOINT
            {
                x = (int)pt.X,
                y = (int)pt.Y
            };
            return nativePoint;
        }

        public static void ValidateArgument(bool cond, string reason)
        {
            if (!cond) throw new ArgumentException(reason);
        }

        public static void ValidateArgumentNonNull(object obj, string argName)
        {
            if (obj == null) throw new ArgumentNullException(argName);
        }

        public static object WrapObjectAsPattern(AutomationElement el, object nativePattern,
            AutomationPattern pattern, bool cached)
        {
            if (!Schema.GetPatternInfo(pattern, out var info)) throw new ArgumentException("Unsupported pattern");
            if (info.ClientSideWrapper == null) return null;
            return info.ClientSideWrapper(el, nativePattern, cached);
        }

        public static object WrapObjectAsProperty(AutomationProperty property, object obj)
        {
            // Handle the cases that we know.
            if (obj == AutomationElement.NotSupported)
            {
                // No-op
            }
            else if (obj is IUIAutomationElement element)
            {
                obj = AutomationElement.Wrap(element);
            }
            else if (obj is IUIAutomationElementArray array)
            {
                obj = ConvertToElementArray(array);
            }
            else if (Schema.GetPropertyTypeInfo(property, out var info))
            {
                // Well known properties
                if (obj != null && info.ObjectConverter != null) obj = info.ObjectConverter(obj);
            }

            return obj;
        }

        // Unwrap an object from API representationt to what the native client will expect
        public static object UnwrapObject(object val)
        {
            if (val != null)
            {
                if (val is ControlType type)
                    val = type.Id;
                else if (val is Rect rect)
                    val = new[] { rect.Left, rect.Top, rect.Width, rect.Height };
                else if (val is Point point)
                    val = new[] { point.X, point.Y };
                else if (val is CultureInfo info)
                    val = info.LCID;
                else if (val is AutomationElement element) val = element.NativeElement;
            }

            return val;
        }
    }
}