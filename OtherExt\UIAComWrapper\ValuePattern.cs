using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class RangeValuePattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = RangeValuePatternIdentifiers.Pattern;


        private RangeValuePattern(AutomationElement el, IUIAutomationRangeValuePattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new RangeValuePattern(el, (IUIAutomationRangeValuePattern)pattern, cached);
        }
    }

    public class ValuePattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = ValuePatternIdentifiers.Pattern;

        private ValuePattern(AutomationElement el, IUIAutomationValuePattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new ValuePattern(el, (IUIAutomationValuePattern)pattern, cached);
        }
    }
}