using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class ItemContainerPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = ItemContainerPatternIdentifiers.Pattern;

        private ItemContainerPattern(AutomationElement el, IUIAutomationItemContainerPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new ItemContainerPattern(el, (IUIAutomationItemContainerPattern)pattern, cached);
        }
    }

    public class VirtualizedItemPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = VirtualizedItemPatternIdentifiers.Pattern;

        private VirtualizedItemPattern(AutomationElement el, IUIAutomationVirtualizedItemPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new VirtualizedItemPattern(el, (IUIAutomationVirtualizedItemPattern)pattern, cached);
        }
    }
}