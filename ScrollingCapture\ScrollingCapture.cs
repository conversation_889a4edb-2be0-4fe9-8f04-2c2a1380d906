using System;

namespace OCRTools
{
    public class ScrollingCaptureOptions
    {
        /// <summary>
        /// 左边缘裁剪像素数
        /// </summary>
        public int TrimLeftEdge { get; set; } = 0;

        /// <summary>
        /// 顶部边缘裁剪像素数
        /// </summary>
        public int TrimTopEdge { get; set; } = 0;

        /// <summary>
        /// 右边缘裁剪像素数
        /// </summary>
        public int TrimRightEdge { get; set; } = 0;

        /// <summary>
        /// 底部边缘裁剪像素数
        /// </summary>
        public int TrimBottomEdge { get; set; } = 0;

        /// <summary>
        /// 垂直合并调整像素数（除最后一张图片外）
        /// </summary>
        public int CombineAdjustmentVertical { get; set; } = 0;

        /// <summary>
        /// 最后一张图片的垂直合并调整像素数
        /// </summary>
        public int CombineAdjustmentLastVertical { get; set; } = 0;

        /// <summary>
        /// 忽略最后几张图片
        /// </summary>
        public int IgnoreLast { get; set; } = 0;

        /// <summary>
        /// 开始截图前休眠时间
        /// </summary>
        public int StartDelay { get; set; } = 500;

        /// <summary>
        /// 滚动延时
        /// </summary>
        public int ScrollDelay { get; set; } = 500;

        /// <summary>
        /// 倍速滚动
        /// </summary>
        public int ScrollAmount { get; set; } = 1;
    }

    public enum ScrollingWhenCapture
    {
        切换区域,
        放大镜
    }

    public enum ScrollingCaptureScrollMethod // Localized
    {
        自动尝试所有方法直到某方法生效,
        发送滚动消息至窗口或控件,
        模拟按下Page_Down按钮,
        模拟鼠标滚轮滚动,
        模拟按下Down按键
    }

    public enum ScrollingCaptureScrollTopMethod // Localized
    {
        自动尝试所有方法直到某方法生效,
        发送滚动消息至顶部,
        模拟按下Home按键,
        模拟鼠标滚轮滚动,
        不自动滚动至顶部
    }

    [Flags]
    public enum ScrollInfoMask : uint
    {
        SIF_RANGE = 0x1,
        SIF_PAGE = 0x2,
        SIF_POS = 0x4,
        SIF_DISABLENOSCROLL = 0x8,
        SIF_TRACKPOS = 0x10,
        SIF_ALL = SIF_RANGE | SIF_PAGE | SIF_POS | SIF_TRACKPOS
    }

    public enum SBOrientation
    {
        SB_HORZ = 0x0,
        SB_VERT = 0x1,
        SB_CTL = 0x2,
        SB_BOTH = 0x3
    }
}