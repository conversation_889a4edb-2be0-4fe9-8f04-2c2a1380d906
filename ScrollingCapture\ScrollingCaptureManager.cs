using OCRTools.Common;
using OCRTools.ScreenCaptureLib;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools.ScrollingCapture
{
    /// <summary>
    /// 重叠检测参数
    /// </summary>
    internal struct OverlapParameters
    {
        public int MatchLimit;
        public int IgnoreSideOffset;
        public Rectangle CompareRect;
    }

    /// <summary>
    /// 匹配结果
    /// </summary>
    internal struct MatchResult
    {
        public int MatchCount;
        public int MatchIndex;
    }

    /// <summary>
    /// 滚动截图管理器 - 负责智能滚动截图和图片拼接
    /// </summary>
    internal class ScrollingCaptureManager : IDisposable
    {
        #region 常量定义

        /// <summary>
        /// 默认最大滚动重试次数
        /// </summary>
        private const int DEFAULT_MAX_SCROLL_RETRY_TIMES = 50;

        /// <summary>
        /// 连续失败的最大容忍次数
        /// </summary>
        private const int MAX_CONSECUTIVE_FAILURES = 5;

        /// <summary>
        /// 默认边缘忽略的最小像素数
        /// </summary>
        private const int MIN_IGNORE_SIDE_OFFSET = 50;

        /// <summary>
        /// 边缘忽略像素数的计算比例
        /// </summary>
        private const int IGNORE_SIDE_RATIO = 20;

        /// <summary>
        /// 边缘忽略的最小宽度倍数
        /// </summary>
        private const int MIN_WIDTH_MULTIPLIER = 3;

        /// <summary>
        /// 重复内容检测的最小相似度阈值
        /// </summary>
        private const double DUPLICATE_SIMILARITY_THRESHOLD = 0.95;

        /// <summary>
        /// 重复内容检测的最小高度
        /// </summary>
        private const int MIN_DUPLICATE_CHECK_HEIGHT = 30;

        #endregion

        #region 属性和字段

        public ScrollingCaptureOptions Options { get; private set; }
        public Bitmap Result { get; private set; }

        private int bestMatchCount, bestMatchIndex;
        private WindowInfo selectedWindow;
        private Rectangle selectedRectangle;
        private readonly int maxScrollRetryTimes;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化滚动截图管理器
        /// </summary>
        /// <param name="options">滚动截图选项</param>
        /// <param name="window">目标窗口信息</param>
        /// <param name="rectangle">截图区域</param>
        public ScrollingCaptureManager(ScrollingCaptureOptions options, WindowInfo window, Rectangle rectangle)
        {
            Options = options ?? throw new ArgumentNullException(nameof(options));
            selectedWindow = window ?? throw new ArgumentNullException(nameof(window));
            selectedRectangle = rectangle;
            maxScrollRetryTimes = DEFAULT_MAX_SCROLL_RETRY_TIMES;

            // 验证截图区域
            if (rectangle.IsEmpty || rectangle.Width <= 0 || rectangle.Height <= 0)
            {
                throw new ArgumentException("截图区域无效", nameof(rectangle));
            }
        }

        #endregion

        /// <summary>
        /// 释放所有资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                Result?.Dispose();
                Result = null;

                // 重置状态
                bestMatchCount = 0;
                bestMatchIndex = 0;
                selectedWindow = null;
                selectedRectangle = Rectangle.Empty;
            }
            catch (Exception ex)
            {
                Log.WriteError("释放ScrollingCaptureManager资源时发生错误", ex);
            }
        }



        public void StartCapture()
        {
            //var files = new System.Collections.Generic.List<string>(Directory.GetFiles(Application.StartupPath, "*.png")).OrderBy(p => p).ToList();

            //Result = new Bitmap(Image.FromFile(files[0]));
            //for (int i = 1; i < files.Count; i++)
            //{
            //    Result = CombineImages(Result, new Bitmap(Image.FromFile(files[i])));
            //}
            //Result.SaveFileWithOutConfirm("Result_" + DateTime.Now.ToString("HHmmssfff") + ".png");

            //return;
            if (selectedWindow != null && !selectedRectangle.IsEmpty)
            {
                bestMatchCount = 0;
                bestMatchIndex = 0;

                selectedWindow.Activate();

                Thread.Sleep(Options.StartDelay);

                if (CommonSetting.ScrollTopMethodBeforeCapture != ScrollingCaptureScrollTopMethod.不自动滚动至顶部.ToString())
                {
                    var _currentScrollToTopMethod = CommonSetting.ConvertToEnum(CommonSetting.ScrollTopMethodBeforeCapture, ScrollingCaptureScrollTopMethod.发送滚动消息至顶部);
                    if (Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.模拟按下Home按键)
                        || Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.自动尝试所有方法直到某方法生效))
                    {
                        //发送Home键
                        InputHelpers.SendKeyPress(VirtualKeyCode.Home);
                        SendKeys.SendWait("{HOME}");
                        if (Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.模拟按下Home按键))
                            Thread.Sleep(Options.ScrollDelay);
                    }

                    if (Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.发送滚动消息至顶部)
                        || Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.自动尝试所有方法直到某方法生效))
                    {
                        //发消息到顶部
                        NativeMethods.SendMessage(selectedWindow.IsParentHandleCreated ? selectedWindow.ParentHandle : selectedWindow.Handle, 0x0115, (int)ScrollBarCommands.SB_TOP, 0);
                        if (Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.发送滚动消息至顶部))
                            Thread.Sleep(Options.ScrollDelay);
                    }

                    if (Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.模拟鼠标滚轮滚动)
                        || Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.自动尝试所有方法直到某方法生效))
                    {
                        //模拟鼠标向上滚动
                        Bitmap firstImage = null;
                        var maxTime = 10;
                        var nTime = 0;
                        while (nTime < maxTime)
                        {
                            for (int i = 0; i < 10; i++)
                            {
                                InputHelpers.SendMouseWheel(120);
                            }
                            Thread.Sleep(300);

                            var bmp = Screenshot.CaptureRectangle(selectedRectangle, selectedRectangle);

                            if (CompareLastTwoImages(firstImage, bmp))
                            {
                                break;
                            }
                            firstImage = bmp;
                            nTime++;
                        }
                    }

                }

                var _autoChangeScroll = CommonSetting.ScrollMethod == ScrollingCaptureScrollMethod.自动尝试所有方法直到某方法生效.ToString();
                ScrollingCaptureScrollMethod _currentScrollToEndMethod = ScrollingCaptureScrollMethod.发送滚动消息至窗口或控件;
                if (!_autoChangeScroll)
                    _currentScrollToEndMethod = CommonSetting.ConvertToEnum(CommonSetting.ScrollMethod, _currentScrollToEndMethod);

                var _currentScrollCount = 0;
                Bitmap lastImage = null;

                while (_currentScrollCount < maxScrollRetryTimes)
                {
                    try
                    {
                        Thread.Sleep(Options.ScrollDelay);

                        var bmp = Screenshot.CaptureRectangle(selectedRectangle, selectedRectangle);

                        // 验证截图是否有效
                        if (bmp == null || bmp.Width <= 0 || bmp.Height <= 0)
                        {
                            Log.WriteLog($"截图无效，跳过第{_currentScrollCount}次滚动");
                            _currentScrollCount++;
                            continue;
                        }

                        //bmp.SaveFileWithOutConfirm(_currentScrollCount + "Begin_" + DateTime.Now.ToString("HHmmssfff") + ".png");

                        if (CompareLastTwoImages(lastImage, bmp))
                        {
                            //自动尝试所有方法直到某方法生效
                            if (_autoChangeScroll && _currentScrollToEndMethod != ScrollingCaptureScrollMethod.模拟按下Down按键)
                            {
                                _currentScrollToEndMethod = (ScrollingCaptureScrollMethod)(_currentScrollToEndMethod.GetHashCode() + 1);
                            }
                            else
                            {
                                // 释放最后一张图片的内存
                                bmp?.Dispose();
                                break;
                            }
                        }

                        // 执行滚动操作
                        if (!ExecuteScrollAction(_currentScrollToEndMethod))
                        {
                            Log.WriteLog($"滚动操作失败，方法：{_currentScrollToEndMethod}");
                        }

                        _currentScrollCount++;

                        // 合并图片
                        var previousResult = Result;
                        Result = CombineImages(Result, bmp);

                        // 如果合并成功且结果不同，释放之前的结果
                        if (Result != previousResult && previousResult != null)
                        {
                            previousResult.Dispose();
                        }

                        //Result.SaveFileWithOutConfirm(_currentScrollCount + "Result_" + DateTime.Now.ToString("HHmmssfff") + ".png");

                        // 释放上一张图片的内存
                        lastImage?.Dispose();
                        lastImage = bmp;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"滚动截图第{_currentScrollCount}次时发生错误", ex);
                        _currentScrollCount++;

                        // 如果连续失败次数过多，退出循环
                        if (_currentScrollCount > MAX_CONSECUTIVE_FAILURES)
                        {
                            break;
                        }
                    }
                }

                // 清理最后一张图片
                lastImage?.Dispose();
            }
        }

        /// <summary>
        /// 执行滚动操作
        /// </summary>
        /// <param name="scrollMethod">滚动方法</param>
        /// <returns>操作是否成功</returns>
        private bool ExecuteScrollAction(ScrollingCaptureScrollMethod scrollMethod)
        {
            try
            {
                switch (scrollMethod)
                {
                    case ScrollingCaptureScrollMethod.发送滚动消息至窗口或控件:
                        var handle = selectedWindow.IsParentHandleCreated ? selectedWindow.ParentHandle : selectedWindow.Handle;
                        if (handle != IntPtr.Zero)
                        {
                            NativeMethods.SendMessage(handle, 0x0115, (int)ScrollBarCommands.SB_PAGEDOWN, 0);
                            return true;
                        }
                        break;

                    case ScrollingCaptureScrollMethod.模拟按下Page_Down按钮:
                        if (!InputHelpers.SendKeyPress(VirtualKeyCode.Next))
                        {
                            SendKeys.SendWait("{PGDN}");
                        }
                        return true;

                    case ScrollingCaptureScrollMethod.模拟按下Down按键:
                        if (!InputHelpers.SendKeyPress(VirtualKeyCode.Down, Options.ScrollAmount))
                        {
                            SendKeys.SendWait("{DOWN}");
                        }
                        return true;

                    case ScrollingCaptureScrollMethod.模拟鼠标滚轮滚动:
                        InputHelpers.SendMouseWheel(-120 * Options.ScrollAmount);
                        return true;
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"执行滚动操作失败：{scrollMethod}", ex);
            }

            return false;
        }

        /// <summary>
        /// 比较两张图片是否相同
        /// </summary>
        /// <param name="lastImage">上一张图片</param>
        /// <param name="currentImg">当前图片</param>
        /// <returns>如果相同返回true</returns>
        private bool CompareLastTwoImages(Bitmap lastImage, Bitmap currentImg)
        {
            if (lastImage == null || currentImg == null)
                return false;

            try
            {
                return ImageProcessHelper.IsImagesEqual(lastImage, currentImg);
            }
            catch (Exception ex)
            {
                Log.WriteError("比较图片时发生错误", ex);
                return false;
            }
        }

        /// <summary>
        /// 智能合并两张图片，通过像素级重叠检测实现无缝拼接
        /// </summary>
        /// <param name="result">已有的结果图片</param>
        /// <param name="currentImage">当前要合并的图片</param>
        /// <param name="isGuess">是否启用边缘猜测模式</param>
        /// <returns>合并后的图片</returns>
        private Bitmap CombineImages(Bitmap result, Bitmap currentImage, bool isGuess = true)
        {
            // 输入参数验证
            if (currentImage == null)
                return result;

            if (result == null)
                return (Bitmap)currentImage.Clone();

            try
            {
                // 预计算重叠检测参数
                var overlapParams = CalculateOverlapParameters(result, currentImage);

                // 执行像素级重叠检测
                var matchResult = FindBestOverlapMatch(result, currentImage, overlapParams);

                // 处理检测结果
                if (matchResult.MatchCount > 0)
                {
                    return CreateCombinedImage(result, currentImage, matchResult);
                }
                else if (isGuess)
                {
                    // 使用边缘猜测进行递归处理
                    return HandleGuessMode(result, currentImage);
                }
                else
                {
                    // 没有找到匹配且不使用猜测模式，直接拼接
                    Log.WriteLog("未找到重叠区域，直接拼接图片");
                    return DirectCombineImages(result, currentImage);
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.WriteError("图片合并时发生错误", ex);
                return result; // 返回原图片，避免程序崩溃
            }
        }

        /// <summary>
        /// 计算重叠检测的参数
        /// </summary>
        private OverlapParameters CalculateOverlapParameters(Bitmap result, Bitmap currentImage)
        {
            var matchLimit = currentImage.Height / 2;
            var ignoreSideOffset = Math.Max(MIN_IGNORE_SIDE_OFFSET, currentImage.Width / IGNORE_SIDE_RATIO);

            // 如果图片太窄，不忽略边缘
            if (currentImage.Width < ignoreSideOffset * MIN_WIDTH_MULTIPLIER)
            {
                ignoreSideOffset = 0;
            }

            return new OverlapParameters
            {
                MatchLimit = matchLimit,
                IgnoreSideOffset = ignoreSideOffset,
                CompareRect = new Rectangle(
                    ignoreSideOffset,
                    result.Height - currentImage.Height,
                    currentImage.Width - ignoreSideOffset * 2,
                    currentImage.Height)
            };
        }

        /// <summary>
        /// 查找最佳重叠匹配点 - 改进版本，支持模糊匹配
        /// </summary>
        private MatchResult FindBestOverlapMatch(Bitmap result, Bitmap currentImage, OverlapParameters parameters)
        {
            var matchResult = new MatchResult();

            // 首先尝试精确匹配
            var exactMatch = FindExactMatch(result, currentImage, parameters);
            if (exactMatch.MatchCount > 0)
            {
                return exactMatch;
            }

            // 如果精确匹配失败，尝试模糊匹配
            var fuzzyMatch = FindFuzzyMatch(result, currentImage, parameters);
            if (fuzzyMatch.MatchCount > 0)
            {
                return fuzzyMatch;
            }

            // 如果都失败，尝试使用历史最佳匹配
            if (bestMatchCount > 0)
            {
                matchResult.MatchCount = bestMatchCount;
                matchResult.MatchIndex = bestMatchIndex;
            }

            return matchResult;
        }

        /// <summary>
        /// 精确像素匹配
        /// </summary>
        private MatchResult FindExactMatch(Bitmap result, Bitmap currentImage, OverlapParameters parameters)
        {
            var matchResult = new MatchResult();

            var resultData = result.LockBits(new Rectangle(0, 0, result.Width, result.Height),
                ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);
            var currentImageData = currentImage.LockBits(new Rectangle(0, 0, currentImage.Width, currentImage.Height),
                ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);

            try
            {
                var stride = resultData.Stride;
                var pixelSize = stride / result.Width;
                var resultScan0 = resultData.Scan0 + pixelSize * parameters.IgnoreSideOffset;
                var currentImageScan0 = currentImageData.Scan0 + pixelSize * parameters.IgnoreSideOffset;
                var rectBottom = parameters.CompareRect.Bottom - 1;
                var compareLength = pixelSize * parameters.CompareRect.Width;

                // 从下往上扫描，寻找最佳匹配点
                for (int currentImageY = currentImage.Height - 1;
                     currentImageY >= 0 && matchResult.MatchCount < parameters.MatchLimit;
                     currentImageY--)
                {
                    var currentMatchCount = CountConsecutiveMatches(
                        resultScan0, currentImageScan0, stride, compareLength,
                        rectBottom, currentImageY, parameters.MatchLimit);

                    if (currentMatchCount > matchResult.MatchCount)
                    {
                        matchResult.MatchCount = currentMatchCount;
                        matchResult.MatchIndex = currentImageY;
                    }
                }
            }
            finally
            {
                result.UnlockBits(resultData);
                currentImage.UnlockBits(currentImageData);
            }

            return matchResult;
        }

        /// <summary>
        /// 模糊匹配 - 允许一定的像素差异，适用于有时间戳或滚动条变化的情况
        /// </summary>
        private MatchResult FindFuzzyMatch(Bitmap result, Bitmap currentImage, OverlapParameters parameters)
        {
            var matchResult = new MatchResult();
            const double SIMILARITY_THRESHOLD = 0.85; // 85% 相似度阈值
            const int MIN_MATCH_HEIGHT = 20; // 最小匹配高度

            try
            {
                // 在结果图片的底部区域寻找与当前图片顶部最相似的区域
                var searchHeight = Math.Min(currentImage.Height, result.Height / 2);
                var searchStartY = Math.Max(0, result.Height - searchHeight);

                for (int resultY = searchStartY; resultY < result.Height - MIN_MATCH_HEIGHT; resultY++)
                {
                    var maxSimilarHeight = Math.Min(result.Height - resultY, currentImage.Height);

                    for (int matchHeight = MIN_MATCH_HEIGHT; matchHeight <= maxSimilarHeight; matchHeight++)
                    {
                        var similarity = CalculateRegionSimilarity(
                            result, new Rectangle(parameters.IgnoreSideOffset, resultY,
                                                parameters.CompareRect.Width, matchHeight),
                            currentImage, new Rectangle(parameters.IgnoreSideOffset, 0,
                                                      parameters.CompareRect.Width, matchHeight));

                        if (similarity >= SIMILARITY_THRESHOLD && matchHeight > matchResult.MatchCount)
                        {
                            matchResult.MatchCount = matchHeight;
                            matchResult.MatchIndex = matchHeight - 1; // 转换为当前图片的索引
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteLog($"模糊匹配时发生错误: {ex.Message}");
            }

            return matchResult;
        }

        /// <summary>
        /// 直接拼接两张图片，不进行重叠检测
        /// </summary>
        private Bitmap DirectCombineImages(Bitmap result, Bitmap currentImage)
        {
            var newHeight = result.Height + currentImage.Height;
            var newResult = new Bitmap(result.Width, newHeight);
            newResult.SetResolution(result.HorizontalResolution, result.VerticalResolution);

            using (var graphics = Graphics.FromImage(newResult))
            {
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.DrawImage(result, 0, 0);
                graphics.DrawImage(currentImage, 0, result.Height);
            }

            result.Dispose();
            return newResult;
        }

        /// <summary>
        /// 计算两个区域的相似度
        /// </summary>
        private double CalculateRegionSimilarity(Bitmap img1, Rectangle rect1, Bitmap img2, Rectangle rect2)
        {
            if (rect1.Width != rect2.Width || rect1.Height != rect2.Height)
                return 0.0;

            var totalPixels = rect1.Width * rect1.Height;
            var matchingPixels = 0;
            const int PIXEL_TOLERANCE = 30; // 像素值容差

            var data1 = img1.LockBits(rect1, ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);
            var data2 = img2.LockBits(rect2, ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);

            try
            {
                unsafe
                {
                    var ptr1 = (byte*)data1.Scan0;
                    var ptr2 = (byte*)data2.Scan0;
                    var stride1 = data1.Stride;
                    var stride2 = data2.Stride;

                    for (int y = 0; y < rect1.Height; y++)
                    {
                        var row1 = ptr1 + y * stride1;
                        var row2 = ptr2 + y * stride2;

                        for (int x = 0; x < rect1.Width; x++)
                        {
                            var pixel1 = row1 + x * 4;
                            var pixel2 = row2 + x * 4;

                            // 比较 RGB 值（忽略 Alpha）
                            var rDiff = Math.Abs(pixel1[2] - pixel2[2]);
                            var gDiff = Math.Abs(pixel1[1] - pixel2[1]);
                            var bDiff = Math.Abs(pixel1[0] - pixel2[0]);

                            if (rDiff <= PIXEL_TOLERANCE && gDiff <= PIXEL_TOLERANCE && bDiff <= PIXEL_TOLERANCE)
                            {
                                matchingPixels++;
                            }
                        }
                    }
                }
            }
            finally
            {
                img1.UnlockBits(data1);
                img2.UnlockBits(data2);
            }

            return (double)matchingPixels / totalPixels;
        }

        /// <summary>
        /// 计算连续匹配的行数
        /// </summary>
        private int CountConsecutiveMatches(IntPtr resultScan0, IntPtr currentImageScan0,
            int stride, int compareLength, int rectBottom, int currentImageY, int matchLimit)
        {
            int currentMatchCount = 0;

            for (int y = 0; currentImageY - y >= 0 && currentMatchCount < matchLimit; y++)
            {
                if (NativeMethods.memcmp(
                    resultScan0 + ((rectBottom - y) * stride),
                    currentImageScan0 + ((currentImageY - y) * stride),
                    compareLength) == 0)
                {
                    currentMatchCount++;
                }
                else
                {
                    break;
                }
            }

            return currentMatchCount;
        }

        /// <summary>
        /// 验证匹配结果的有效性，防止错误合并
        /// </summary>
        private bool IsValidMatch(Bitmap result, Bitmap currentImage, MatchResult matchResult, OverlapParameters parameters)
        {
            try
            {
                // 基本有效性检查
                if (matchResult.MatchCount <= 0 || matchResult.MatchIndex < 0 ||
                    matchResult.MatchIndex >= currentImage.Height)
                    return false;

                // 检查匹配高度是否合理（不应该超过图片高度的80%）
                var maxReasonableMatch = Math.Min(currentImage.Height, result.Height) * 0.8;
                if (matchResult.MatchCount > maxReasonableMatch)
                {
                    Log.WriteLog($"匹配高度过大，可能是错误匹配: {matchResult.MatchCount} > {maxReasonableMatch}");
                    return false;
                }

                // 检查是否会产生有意义的新内容（至少要有10像素的新内容）
                var newContentHeight = currentImage.Height - matchResult.MatchIndex - 1;
                if (newContentHeight < 10)
                {
                    Log.WriteLog($"新内容高度太小: {newContentHeight}");
                    return false;
                }

                // 进行区域相似度验证
                var similarity = CalculateRegionSimilarity(
                    result, new Rectangle(parameters.IgnoreSideOffset, result.Height - matchResult.MatchCount,
                                        parameters.CompareRect.Width, matchResult.MatchCount),
                    currentImage, new Rectangle(parameters.IgnoreSideOffset, matchResult.MatchIndex - matchResult.MatchCount + 1,
                                              parameters.CompareRect.Width, matchResult.MatchCount));

                // 相似度应该大于70%才认为是有效匹配
                if (similarity < 0.7)
                {
                    Log.WriteLog($"区域相似度过低: {similarity:P2}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.WriteLog($"匹配验证时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建合并后的图片 - 改进版本，包含重复内容检测
        /// </summary>
        private Bitmap CreateCombinedImage(Bitmap result, Bitmap currentImage, MatchResult matchResult)
        {
            var matchHeight = currentImage.Height - matchResult.MatchIndex - 1;

            if (matchHeight <= 0)
            {
                Log.WriteLog("计算的新内容高度为0，返回原图片");
                return result;
            }

            // 验证匹配的有效性
            var parameters = CalculateOverlapParameters(result, currentImage);
            if (!IsValidMatch(result, currentImage, matchResult, parameters))
            {
                Log.WriteLog("匹配验证失败，直接拼接图片");
                return DirectCombineImages(result, currentImage);
            }

            // 更新最佳匹配记录
            if (matchResult.MatchCount > bestMatchCount)
            {
                bestMatchCount = matchResult.MatchCount;
                bestMatchIndex = matchResult.MatchIndex;
            }

            // 创建新的合并图片
            var newResult = new Bitmap(result.Width, result.Height + matchHeight);
            newResult.SetResolution(result.HorizontalResolution, result.VerticalResolution);

            using (var graphics = Graphics.FromImage(newResult))
            {
                // 设置高质量渲染
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

                // 绘制原有图片
                graphics.DrawImage(result,
                    new Rectangle(0, 0, result.Width, result.Height),
                    new Rectangle(0, 0, result.Width, result.Height),
                    GraphicsUnit.Pixel);

                // 绘制新增部分
                graphics.DrawImage(currentImage,
                    new Rectangle(0, result.Height, currentImage.Width, matchHeight),
                    new Rectangle(0, matchResult.MatchIndex + 1, currentImage.Width, matchHeight),
                    GraphicsUnit.Pixel);
            }

            // 释放原图片内存
            result.Dispose();
            return newResult;
        }

        /// <summary>
        /// 处理边缘猜测模式
        /// </summary>
        private Bitmap HandleGuessMode(Bitmap result, Bitmap currentImage)
        {
            var notSameRect = GuessEdges(result, currentImage);

            if (notSameRect.IsEmpty)
                return result;

            try
            {
                var cropRect = new Rectangle(
                    notSameRect.X,
                    notSameRect.Y,
                    notSameRect.Width - notSameRect.X,
                    notSameRect.Height - notSameRect.Y);

                // 验证裁剪区域有效性
                if (!IsValidCropRectangle(cropRect, result) || !IsValidCropRectangle(cropRect, currentImage))
                    return result;

                using (var croppedResult = ImageProcessHelper.CropBitmap(result, cropRect))
                using (var croppedCurrent = ImageProcessHelper.CropBitmap(currentImage, cropRect))
                {
                    if (croppedResult == null || croppedCurrent == null)
                        return result;

                    // 递归合并裁剪后的图片
                    var combinedResult = CombineImages(croppedResult, croppedCurrent, false);

                    // 更新选择区域
                    UpdateSelectedRectangle(notSameRect);

                    return combinedResult;
                }
            }
            catch (Exception ex)
            {
                Log.WriteError("边缘猜测模式处理失败", ex);
                return result;
            }
        }

        /// <summary>
        /// 验证裁剪矩形是否有效
        /// </summary>
        private bool IsValidCropRectangle(Rectangle cropRect, Bitmap bitmap)
        {
            return cropRect.X >= 0 && cropRect.Y >= 0 &&
                   cropRect.Width > 0 && cropRect.Height > 0 &&
                   cropRect.Right <= bitmap.Width && cropRect.Bottom <= bitmap.Height;
        }

        /// <summary>
        /// 更新选择区域
        /// </summary>
        private void UpdateSelectedRectangle(Rectangle notSameRect)
        {
            selectedRectangle = new Rectangle(
                notSameRect.X + selectedRectangle.X,
                notSameRect.Y + selectedRectangle.Y,
                Math.Min(selectedRectangle.Width - notSameRect.X, Result?.Width ?? selectedRectangle.Width),
                Math.Min(selectedRectangle.Height - notSameRect.Y, Result?.Height ?? selectedRectangle.Height));
        }

        /// <summary>
        /// 智能猜测两张图片的不同边缘区域
        /// </summary>
        /// <param name="img1">第一张图片</param>
        /// <param name="img2">第二张图片</param>
        /// <returns>不同区域的矩形，如果无法确定则返回空矩形</returns>
        private Rectangle GuessEdges(Bitmap img1, Bitmap img2)
        {
            // 输入验证
            if (img1 == null || img2 == null)
                return Rectangle.Empty;

            if (img1.Width != img2.Width || img1.Height != img2.Height)
                return Rectangle.Empty;

            try
            {
                var rect = new Rectangle(0, 0, img1.Width, img1.Height);

                using (var bmp1 = new UnsafeBitmap(img1, true, ImageLockMode.ReadOnly))
                using (var bmp2 = new UnsafeBitmap(img2, true, ImageLockMode.ReadOnly))
                {
                    // 优化：使用更高效的边缘检测算法
                    rect.X = FindLeftEdge(bmp1, bmp2, rect);
                    rect.Y = FindTopEdge(bmp1, bmp2, rect);
                    rect.Width = FindRightEdge(bmp1, bmp2, rect);
                    rect.Height = FindBottomEdge(bmp1, bmp2, rect);

                    // 验证结果的有效性
                    if (rect.Width <= rect.X || rect.Height <= rect.Y)
                        return Rectangle.Empty;

                    return rect;
                }
            }
            catch (Exception ex)
            {
                Log.WriteError("边缘猜测时发生错误", ex);
                return Rectangle.Empty;
            }
        }

        /// <summary>
        /// 查找左边缘差异
        /// </summary>
        private int FindLeftEdge(UnsafeBitmap bmp1, UnsafeBitmap bmp2, Rectangle searchRect)
        {
            for (var x = searchRect.X; x < searchRect.Width; x++)
            {
                for (var y = searchRect.Y; y < searchRect.Height; y++)
                {
                    if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        return x;
                }
            }
            return searchRect.X;
        }

        /// <summary>
        /// 查找顶部边缘差异
        /// </summary>
        private int FindTopEdge(UnsafeBitmap bmp1, UnsafeBitmap bmp2, Rectangle searchRect)
        {
            for (var y = searchRect.Y; y < searchRect.Height; y++)
            {
                for (var x = searchRect.X; x < searchRect.Width; x++)
                {
                    if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        return y;
                }
            }
            return searchRect.Y;
        }

        /// <summary>
        /// 查找右边缘差异
        /// </summary>
        private int FindRightEdge(UnsafeBitmap bmp1, UnsafeBitmap bmp2, Rectangle searchRect)
        {
            for (var x = searchRect.Width - 1; x >= searchRect.X; x--)
            {
                for (var y = searchRect.Y; y < searchRect.Height; y++)
                {
                    if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        return x + 1;
                }
            }
            return searchRect.Width;
        }

        /// <summary>
        /// 查找底部边缘差异
        /// </summary>
        private int FindBottomEdge(UnsafeBitmap bmp1, UnsafeBitmap bmp2, Rectangle searchRect)
        {
            for (var y = searchRect.Height - 1; y >= searchRect.Y; y--)
            {
                for (var x = searchRect.X; x < searchRect.Width; x++)
                {
                    if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        return y + 1;
                }
            }
            return searchRect.Height;
        }

    }
}
