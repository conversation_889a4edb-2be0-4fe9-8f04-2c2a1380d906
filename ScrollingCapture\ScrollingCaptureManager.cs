using OCRTools.Common;
using OCRTools.ScreenCaptureLib;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools.ScrollingCapture
{
    /// <summary>
    /// 重叠检测参数
    /// </summary>
    internal struct OverlapParameters
    {
        public int MatchLimit;
        public int IgnoreSideOffset;
        public Rectangle CompareRect;
    }

    /// <summary>
    /// 匹配结果
    /// </summary>
    internal struct MatchResult
    {
        public int MatchCount;
        public int MatchIndex;
    }
}

namespace OCRTools.ScrollingCapture
{
    /// <summary>
    /// 滚动截图管理器 - 负责智能滚动截图和图片拼接
    /// </summary>
    internal class ScrollingCaptureManager : IDisposable
    {
        #region 常量定义

        /// <summary>
        /// 默认最大滚动重试次数
        /// </summary>
        private const int DEFAULT_MAX_SCROLL_RETRY_TIMES = 50;

        /// <summary>
        /// 连续失败的最大容忍次数
        /// </summary>
        private const int MAX_CONSECUTIVE_FAILURES = 5;

        /// <summary>
        /// 默认边缘忽略的最小像素数
        /// </summary>
        private const int MIN_IGNORE_SIDE_OFFSET = 50;

        /// <summary>
        /// 边缘忽略像素数的计算比例
        /// </summary>
        private const int IGNORE_SIDE_RATIO = 20;

        /// <summary>
        /// 边缘忽略的最小宽度倍数
        /// </summary>
        private const int MIN_WIDTH_MULTIPLIER = 3;

        #endregion

        #region 属性和字段

        public ScrollingCaptureOptions Options { get; private set; }
        public Bitmap Result { get; private set; }

        private int bestMatchCount, bestMatchIndex;
        private WindowInfo selectedWindow;
        private Rectangle selectedRectangle;
        private readonly int maxScrollRetryTimes;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化滚动截图管理器
        /// </summary>
        /// <param name="options">滚动截图选项</param>
        /// <param name="window">目标窗口信息</param>
        /// <param name="rectangle">截图区域</param>
        public ScrollingCaptureManager(ScrollingCaptureOptions options, WindowInfo window, Rectangle rectangle)
        {
            Options = options ?? throw new ArgumentNullException(nameof(options));
            selectedWindow = window ?? throw new ArgumentNullException(nameof(window));
            selectedRectangle = rectangle;
            maxScrollRetryTimes = DEFAULT_MAX_SCROLL_RETRY_TIMES;

            // 验证截图区域
            if (rectangle.IsEmpty || rectangle.Width <= 0 || rectangle.Height <= 0)
            {
                throw new ArgumentException("截图区域无效", nameof(rectangle));
            }
        }

        #endregion

        /// <summary>
        /// 释放所有资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                Result?.Dispose();
                Result = null;

                // 重置状态
                bestMatchCount = 0;
                bestMatchIndex = 0;
                selectedWindow = null;
                selectedRectangle = Rectangle.Empty;
            }
            catch (Exception ex)
            {
                Log.WriteError("释放ScrollingCaptureManager资源时发生错误", ex);
            }
        }



        public void StartCapture()
        {
            //var files = new System.Collections.Generic.List<string>(Directory.GetFiles(Application.StartupPath, "*.png")).OrderBy(p => p).ToList();

            //Result = new Bitmap(Image.FromFile(files[0]));
            //for (int i = 1; i < files.Count; i++)
            //{
            //    Result = CombineImages(Result, new Bitmap(Image.FromFile(files[i])));
            //}
            //Result.SaveFileWithOutConfirm("Result_" + DateTime.Now.ToString("HHmmssfff") + ".png");

            //return;
            if (selectedWindow != null && !selectedRectangle.IsEmpty)
            {
                bestMatchCount = 0;
                bestMatchIndex = 0;

                selectedWindow.Activate();

                Thread.Sleep(Options.StartDelay);

                if (CommonSetting.ScrollTopMethodBeforeCapture != ScrollingCaptureScrollTopMethod.不自动滚动至顶部.ToString())
                {
                    var _currentScrollToTopMethod = CommonSetting.ConvertToEnum(CommonSetting.ScrollTopMethodBeforeCapture, ScrollingCaptureScrollTopMethod.发送滚动消息至顶部);
                    if (Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.模拟按下Home按键)
                        || Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.自动尝试所有方法直到某方法生效))
                    {
                        //发送Home键
                        InputHelpers.SendKeyPress(VirtualKeyCode.Home);
                        SendKeys.SendWait("{HOME}");
                        if (Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.模拟按下Home按键))
                            Thread.Sleep(Options.ScrollDelay);
                    }

                    if (Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.发送滚动消息至顶部)
                        || Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.自动尝试所有方法直到某方法生效))
                    {
                        //发消息到顶部
                        NativeMethods.SendMessage(selectedWindow.IsParentHandleCreated ? selectedWindow.ParentHandle : selectedWindow.Handle, 0x0115, (int)ScrollBarCommands.SB_TOP, 0);
                        if (Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.发送滚动消息至顶部))
                            Thread.Sleep(Options.ScrollDelay);
                    }

                    if (Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.模拟鼠标滚轮滚动)
                        || Equals(_currentScrollToTopMethod, ScrollingCaptureScrollTopMethod.自动尝试所有方法直到某方法生效))
                    {
                        //模拟鼠标向上滚动
                        Bitmap firstImage = null;
                        var maxTime = 10;
                        var nTime = 0;
                        while (nTime < maxTime)
                        {
                            for (int i = 0; i < 10; i++)
                            {
                                InputHelpers.SendMouseWheel(120);
                            }
                            Thread.Sleep(300);

                            var bmp = Screenshot.CaptureRectangle(selectedRectangle, selectedRectangle);

                            if (CompareLastTwoImages(firstImage, bmp))
                            {
                                break;
                            }
                            firstImage = bmp;
                            nTime++;
                        }
                    }

                }

                var _autoChangeScroll = CommonSetting.ScrollMethod == ScrollingCaptureScrollMethod.自动尝试所有方法直到某方法生效.ToString();
                ScrollingCaptureScrollMethod _currentScrollToEndMethod = ScrollingCaptureScrollMethod.发送滚动消息至窗口或控件;
                if (!_autoChangeScroll)
                    _currentScrollToEndMethod = CommonSetting.ConvertToEnum(CommonSetting.ScrollMethod, _currentScrollToEndMethod);

                var _currentScrollCount = 0;
                Bitmap lastImage = null;

                while (_currentScrollCount < maxScrollRetryTimes)
                {
                    try
                    {
                        Thread.Sleep(Options.ScrollDelay);

                        var bmp = Screenshot.CaptureRectangle(selectedRectangle, selectedRectangle);

                        // 验证截图是否有效
                        if (bmp == null || bmp.Width <= 0 || bmp.Height <= 0)
                        {
                            Log.WriteLog($"截图无效，跳过第{_currentScrollCount}次滚动");
                            _currentScrollCount++;
                            continue;
                        }

                        //bmp.SaveFileWithOutConfirm(_currentScrollCount + "Begin_" + DateTime.Now.ToString("HHmmssfff") + ".png");

                        if (CompareLastTwoImages(lastImage, bmp))
                        {
                            //自动尝试所有方法直到某方法生效
                            if (_autoChangeScroll && _currentScrollToEndMethod != ScrollingCaptureScrollMethod.模拟按下Down按键)
                            {
                                _currentScrollToEndMethod = (ScrollingCaptureScrollMethod)(_currentScrollToEndMethod.GetHashCode() + 1);
                            }
                            else
                            {
                                // 释放最后一张图片的内存
                                bmp?.Dispose();
                                break;
                            }
                        }

                        // 执行滚动操作
                        if (!ExecuteScrollAction(_currentScrollToEndMethod))
                        {
                            Log.WriteLog($"滚动操作失败，方法：{_currentScrollToEndMethod}");
                        }

                        _currentScrollCount++;

                        // 合并图片
                        var previousResult = Result;
                        Result = CombineImages(Result, bmp);

                        // 如果合并成功且结果不同，释放之前的结果
                        if (Result != previousResult && previousResult != null)
                        {
                            previousResult.Dispose();
                        }

                        //Result.SaveFileWithOutConfirm(_currentScrollCount + "Result_" + DateTime.Now.ToString("HHmmssfff") + ".png");

                        // 释放上一张图片的内存
                        lastImage?.Dispose();
                        lastImage = bmp;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"滚动截图第{_currentScrollCount}次时发生错误", ex);
                        _currentScrollCount++;

                        // 如果连续失败次数过多，退出循环
                        if (_currentScrollCount > MAX_CONSECUTIVE_FAILURES)
                        {
                            break;
                        }
                    }
                }

                // 清理最后一张图片
                lastImage?.Dispose();
            }
        }

        /// <summary>
        /// 执行滚动操作
        /// </summary>
        /// <param name="scrollMethod">滚动方法</param>
        /// <returns>操作是否成功</returns>
        private bool ExecuteScrollAction(ScrollingCaptureScrollMethod scrollMethod)
        {
            try
            {
                switch (scrollMethod)
                {
                    case ScrollingCaptureScrollMethod.发送滚动消息至窗口或控件:
                        var handle = selectedWindow.IsParentHandleCreated ? selectedWindow.ParentHandle : selectedWindow.Handle;
                        if (handle != IntPtr.Zero)
                        {
                            NativeMethods.SendMessage(handle, 0x0115, (int)ScrollBarCommands.SB_PAGEDOWN, 0);
                            return true;
                        }
                        break;

                    case ScrollingCaptureScrollMethod.模拟按下Page_Down按钮:
                        if (!InputHelpers.SendKeyPress(VirtualKeyCode.Next))
                        {
                            SendKeys.SendWait("{PGDN}");
                        }
                        return true;

                    case ScrollingCaptureScrollMethod.模拟按下Down按键:
                        if (!InputHelpers.SendKeyPress(VirtualKeyCode.Down, Options.ScrollAmount))
                        {
                            SendKeys.SendWait("{DOWN}");
                        }
                        return true;

                    case ScrollingCaptureScrollMethod.模拟鼠标滚轮滚动:
                        InputHelpers.SendMouseWheel(-120 * Options.ScrollAmount);
                        return true;
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"执行滚动操作失败：{scrollMethod}", ex);
            }

            return false;
        }

        /// <summary>
        /// 比较两张图片是否相同
        /// </summary>
        /// <param name="lastImage">上一张图片</param>
        /// <param name="currentImg">当前图片</param>
        /// <returns>如果相同返回true</returns>
        private bool CompareLastTwoImages(Bitmap lastImage, Bitmap currentImg)
        {
            if (lastImage == null || currentImg == null)
                return false;

            try
            {
                return ImageProcessHelper.IsImagesEqual(lastImage, currentImg);
            }
            catch (Exception ex)
            {
                Log.WriteError("比较图片时发生错误", ex);
                return false;
            }
        }

        /// <summary>
        /// 智能合并两张图片，通过像素级重叠检测实现无缝拼接
        /// </summary>
        /// <param name="result">已有的结果图片</param>
        /// <param name="currentImage">当前要合并的图片</param>
        /// <param name="isGuess">是否启用边缘猜测模式</param>
        /// <returns>合并后的图片</returns>
        private Bitmap CombineImages(Bitmap result, Bitmap currentImage, bool isGuess = true)
        {
            // 输入参数验证
            if (currentImage == null)
                return result;

            if (result == null)
                return (Bitmap)currentImage.Clone();

            try
            {
                // 预计算重叠检测参数
                var overlapParams = CalculateOverlapParameters(result, currentImage);

                // 执行像素级重叠检测
                var matchResult = FindBestOverlapMatch(result, currentImage, overlapParams);

                // 处理检测结果
                if (matchResult.MatchCount > 0)
                {
                    return CreateCombinedImage(result, currentImage, matchResult);
                }
                else if (isGuess)
                {
                    // 使用边缘猜测进行递归处理
                    return HandleGuessMode(result, currentImage);
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.WriteError("图片合并时发生错误", ex);
                return result; // 返回原图片，避免程序崩溃
            }
        }

        /// <summary>
        /// 计算重叠检测的参数
        /// </summary>
        private OverlapParameters CalculateOverlapParameters(Bitmap result, Bitmap currentImage)
        {
            var matchLimit = currentImage.Height / 2;
            var ignoreSideOffset = Math.Max(MIN_IGNORE_SIDE_OFFSET, currentImage.Width / IGNORE_SIDE_RATIO);

            // 如果图片太窄，不忽略边缘
            if (currentImage.Width < ignoreSideOffset * MIN_WIDTH_MULTIPLIER)
            {
                ignoreSideOffset = 0;
            }

            return new OverlapParameters
            {
                MatchLimit = matchLimit,
                IgnoreSideOffset = ignoreSideOffset,
                CompareRect = new Rectangle(
                    ignoreSideOffset,
                    result.Height - currentImage.Height,
                    currentImage.Width - ignoreSideOffset * 2,
                    currentImage.Height)
            };
        }

        /// <summary>
        /// 查找最佳重叠匹配点
        /// </summary>
        private MatchResult FindBestOverlapMatch(Bitmap result, Bitmap currentImage, OverlapParameters parameters)
        {
            var matchResult = new MatchResult();

            // 锁定位图数据进行高效像素比较
            var resultData = result.LockBits(new Rectangle(0, 0, result.Width, result.Height),
                ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);
            var currentImageData = currentImage.LockBits(new Rectangle(0, 0, currentImage.Width, currentImage.Height),
                ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);

            try
            {
                var stride = resultData.Stride;
                var pixelSize = stride / result.Width;
                var resultScan0 = resultData.Scan0 + pixelSize * parameters.IgnoreSideOffset;
                var currentImageScan0 = currentImageData.Scan0 + pixelSize * parameters.IgnoreSideOffset;
                var rectBottom = parameters.CompareRect.Bottom - 1;
                var compareLength = pixelSize * parameters.CompareRect.Width;

                // 从下往上扫描，寻找最佳匹配点
                for (int currentImageY = currentImage.Height - 1;
                     currentImageY >= 0 && matchResult.MatchCount < parameters.MatchLimit;
                     currentImageY--)
                {
                    var currentMatchCount = CountConsecutiveMatches(
                        resultScan0, currentImageScan0, stride, compareLength,
                        rectBottom, currentImageY, parameters.MatchLimit);

                    if (currentMatchCount > matchResult.MatchCount)
                    {
                        matchResult.MatchCount = currentMatchCount;
                        matchResult.MatchIndex = currentImageY;
                    }
                }
            }
            finally
            {
                result.UnlockBits(resultData);
                currentImage.UnlockBits(currentImageData);
            }

            // 如果没有找到匹配，尝试使用历史最佳匹配
            if (matchResult.MatchCount == 0 && bestMatchCount > 0)
            {
                matchResult.MatchCount = bestMatchCount;
                matchResult.MatchIndex = bestMatchIndex;
            }

            return matchResult;
        }

        /// <summary>
        /// 计算连续匹配的行数
        /// </summary>
        private int CountConsecutiveMatches(IntPtr resultScan0, IntPtr currentImageScan0,
            int stride, int compareLength, int rectBottom, int currentImageY, int matchLimit)
        {
            int currentMatchCount = 0;

            for (int y = 0; currentImageY - y >= 0 && currentMatchCount < matchLimit; y++)
            {
                if (NativeMethods.memcmp(
                    resultScan0 + ((rectBottom - y) * stride),
                    currentImageScan0 + ((currentImageY - y) * stride),
                    compareLength) == 0)
                {
                    currentMatchCount++;
                }
                else
                {
                    break;
                }
            }

            return currentMatchCount;
        }

        /// <summary>
        /// 创建合并后的图片
        /// </summary>
        private Bitmap CreateCombinedImage(Bitmap result, Bitmap currentImage, MatchResult matchResult)
        {
            var matchHeight = currentImage.Height - matchResult.MatchIndex - 1;

            if (matchHeight <= 0)
                return result;

            // 更新最佳匹配记录
            if (matchResult.MatchCount > bestMatchCount)
            {
                bestMatchCount = matchResult.MatchCount;
                bestMatchIndex = matchResult.MatchIndex;
            }

            // 创建新的合并图片
            var newResult = new Bitmap(result.Width, result.Height + matchHeight);
            newResult.SetResolution(result.HorizontalResolution, result.VerticalResolution);

            using (var graphics = Graphics.FromImage(newResult))
            {
                // 设置高质量渲染
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

                // 绘制原有图片
                graphics.DrawImage(result,
                    new Rectangle(0, 0, result.Width, result.Height),
                    new Rectangle(0, 0, result.Width, result.Height),
                    GraphicsUnit.Pixel);

                // 绘制新增部分
                graphics.DrawImage(currentImage,
                    new Rectangle(0, result.Height, currentImage.Width, matchHeight),
                    new Rectangle(0, matchResult.MatchIndex + 1, currentImage.Width, matchHeight),
                    GraphicsUnit.Pixel);
            }

            // 释放原图片内存
            result.Dispose();
            return newResult;
        }

        /// <summary>
        /// 处理边缘猜测模式
        /// </summary>
        private Bitmap HandleGuessMode(Bitmap result, Bitmap currentImage)
        {
            var notSameRect = GuessEdges(result, currentImage);

            if (notSameRect.IsEmpty)
                return result;

            try
            {
                var cropRect = new Rectangle(
                    notSameRect.X,
                    notSameRect.Y,
                    notSameRect.Width - notSameRect.X,
                    notSameRect.Height - notSameRect.Y);

                // 验证裁剪区域有效性
                if (!IsValidCropRectangle(cropRect, result) || !IsValidCropRectangle(cropRect, currentImage))
                    return result;

                using (var croppedResult = ImageProcessHelper.CropBitmap(result, cropRect))
                using (var croppedCurrent = ImageProcessHelper.CropBitmap(currentImage, cropRect))
                {
                    if (croppedResult == null || croppedCurrent == null)
                        return result;

                    // 递归合并裁剪后的图片
                    var combinedResult = CombineImages(croppedResult, croppedCurrent, false);

                    // 更新选择区域
                    UpdateSelectedRectangle(notSameRect);

                    return combinedResult;
                }
            }
            catch (Exception ex)
            {
                Log.WriteError("边缘猜测模式处理失败", ex);
                return result;
            }
        }

        /// <summary>
        /// 验证裁剪矩形是否有效
        /// </summary>
        private bool IsValidCropRectangle(Rectangle cropRect, Bitmap bitmap)
        {
            return cropRect.X >= 0 && cropRect.Y >= 0 &&
                   cropRect.Width > 0 && cropRect.Height > 0 &&
                   cropRect.Right <= bitmap.Width && cropRect.Bottom <= bitmap.Height;
        }

        /// <summary>
        /// 更新选择区域
        /// </summary>
        private void UpdateSelectedRectangle(Rectangle notSameRect)
        {
            selectedRectangle = new Rectangle(
                notSameRect.X + selectedRectangle.X,
                notSameRect.Y + selectedRectangle.Y,
                Math.Min(selectedRectangle.Width - notSameRect.X, Result?.Width ?? selectedRectangle.Width),
                Math.Min(selectedRectangle.Height - notSameRect.Y, Result?.Height ?? selectedRectangle.Height));
        }

        /// <summary>
        /// 智能猜测两张图片的不同边缘区域
        /// </summary>
        /// <param name="img1">第一张图片</param>
        /// <param name="img2">第二张图片</param>
        /// <returns>不同区域的矩形，如果无法确定则返回空矩形</returns>
        private Rectangle GuessEdges(Bitmap img1, Bitmap img2)
        {
            // 输入验证
            if (img1 == null || img2 == null)
                return Rectangle.Empty;

            if (img1.Width != img2.Width || img1.Height != img2.Height)
                return Rectangle.Empty;

            try
            {
                var rect = new Rectangle(0, 0, img1.Width, img1.Height);

                using (var bmp1 = new UnsafeBitmap(img1, true, ImageLockMode.ReadOnly))
                using (var bmp2 = new UnsafeBitmap(img2, true, ImageLockMode.ReadOnly))
                {
                    // 优化：使用更高效的边缘检测算法
                    rect.X = FindLeftEdge(bmp1, bmp2, rect);
                    rect.Y = FindTopEdge(bmp1, bmp2, rect);
                    rect.Width = FindRightEdge(bmp1, bmp2, rect);
                    rect.Height = FindBottomEdge(bmp1, bmp2, rect);

                    // 验证结果的有效性
                    if (rect.Width <= rect.X || rect.Height <= rect.Y)
                        return Rectangle.Empty;

                    return rect;
                }
            }
            catch (Exception ex)
            {
                Log.WriteError("边缘猜测时发生错误", ex);
                return Rectangle.Empty;
            }
        }

        /// <summary>
        /// 查找左边缘差异
        /// </summary>
        private int FindLeftEdge(UnsafeBitmap bmp1, UnsafeBitmap bmp2, Rectangle searchRect)
        {
            for (var x = searchRect.X; x < searchRect.Width; x++)
            {
                for (var y = searchRect.Y; y < searchRect.Height; y++)
                {
                    if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        return x;
                }
            }
            return searchRect.X;
        }

        /// <summary>
        /// 查找顶部边缘差异
        /// </summary>
        private int FindTopEdge(UnsafeBitmap bmp1, UnsafeBitmap bmp2, Rectangle searchRect)
        {
            for (var y = searchRect.Y; y < searchRect.Height; y++)
            {
                for (var x = searchRect.X; x < searchRect.Width; x++)
                {
                    if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        return y;
                }
            }
            return searchRect.Y;
        }

        /// <summary>
        /// 查找右边缘差异
        /// </summary>
        private int FindRightEdge(UnsafeBitmap bmp1, UnsafeBitmap bmp2, Rectangle searchRect)
        {
            for (var x = searchRect.Width - 1; x >= searchRect.X; x--)
            {
                for (var y = searchRect.Y; y < searchRect.Height; y++)
                {
                    if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        return x + 1;
                }
            }
            return searchRect.Width;
        }

        /// <summary>
        /// 查找底部边缘差异
        /// </summary>
        private int FindBottomEdge(UnsafeBitmap bmp1, UnsafeBitmap bmp2, Rectangle searchRect)
        {
            for (var y = searchRect.Height - 1; y >= searchRect.Y; y--)
            {
                for (var x = searchRect.X; x < searchRect.Width; x++)
                {
                    if (bmp1.GetPixel(x, y) != bmp2.GetPixel(x, y))
                        return y + 1;
                }
            }
            return searchRect.Height;
        }

    }
}
