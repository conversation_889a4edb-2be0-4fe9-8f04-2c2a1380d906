﻿<?xml version="1.0" encoding="utf-8"?>
<ApplicationInsights xmlns="http://schemas.microsoft.com/ApplicationInsights/2013/Settings">
  <!-- 
    Learn more about Application Insights configuration with ApplicationInsights.config here: 
    http://go.microsoft.com/fwlink/?LinkID=513840
    
    Note: If not present, please add <InstrumentationKey>Your Key</InstrumentationKey> to the top of this file.
  -->
  <TelemetryInitializers>
    <Add Type="Microsoft.ApplicationInsights.WindowsServer.AzureRoleEnvironmentTelemetryInitializer, Microsoft.AI.WindowsServer" />
    <Add Type="Microsoft.ApplicationInsights.WindowsServer.BuildInfoConfigComponentVersionTelemetryInitializer, Microsoft.AI.WindowsServer" />
  </TelemetryInitializers>
  <TelemetryModules>
    <Add Type="Microsoft.ApplicationInsights.WindowsServer.AppServicesHeartbeatTelemetryModule, Microsoft.AI.WindowsServer" />
    <Add Type="Microsoft.ApplicationInsights.WindowsServer.AzureInstanceMetadataTelemetryModule, Microsoft.AI.WindowsServer">
      <!--
      Remove individual fields collected here by adding them to the ApplicationInsights.HeartbeatProvider 
      with the following syntax:
      
      <Add Type="Microsoft.ApplicationInsights.Extensibility.Implementation.Tracing.DiagnosticsTelemetryModule, Microsoft.ApplicationInsights">
        <ExcludedHeartbeatProperties>
          <Add>osType</Add>
          <Add>location</Add>
          <Add>name</Add>
          <Add>offer</Add>
          <Add>platformFaultDomain</Add>
          <Add>platformUpdateDomain</Add>
          <Add>publisher</Add>
          <Add>sku</Add>
          <Add>version</Add>
          <Add>vmId</Add>
          <Add>vmSize</Add>
          <Add>subscriptionId</Add>
          <Add>resourceGroupName</Add>
          <Add>placementGroupId</Add>
          <Add>tags</Add>
          <Add>vmScaleSetName</Add>
        </ExcludedHeartbeatProperties>
      </Add>
            
      NOTE: exclusions will be lost upon upgrade.
      -->
    </Add>
    <Add Type="Microsoft.ApplicationInsights.WindowsServer.DeveloperModeWithDebuggerAttachedTelemetryModule, Microsoft.AI.WindowsServer" />
    <Add Type="Microsoft.ApplicationInsights.WindowsServer.UnhandledExceptionTelemetryModule, Microsoft.AI.WindowsServer" />
    <Add Type="Microsoft.ApplicationInsights.WindowsServer.UnobservedExceptionTelemetryModule, Microsoft.AI.WindowsServer">
      <!--</Add>
    <Add Type="Microsoft.ApplicationInsights.WindowsServer.FirstChanceExceptionStatisticsTelemetryModule, Microsoft.AI.WindowsServer">-->
    </Add>
  </TelemetryModules>
</ApplicationInsights>