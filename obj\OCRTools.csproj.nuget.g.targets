﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.runtime.windowsruntime\4.6.0\build\net45\System.Runtime.WindowsRuntime.targets" Condition="Exists('$(NuGetPackageRoot)system.runtime.windowsruntime\4.6.0\build\net45\System.Runtime.WindowsRuntime.targets')" />
    <Import Project="$(NuGetPackageRoot)system.runtime.windowsruntime.ui.xaml\4.6.0\build\net45\System.Runtime.WindowsRuntime.UI.Xaml.targets" Condition="Exists('$(NuGetPackageRoot)system.runtime.windowsruntime.ui.xaml\4.6.0\build\net45\System.Runtime.WindowsRuntime.UI.Xaml.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windows.sdk.contracts\10.0.22621.755\build\Microsoft.Windows.SDK.Contracts.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windows.sdk.contracts\10.0.22621.755\build\Microsoft.Windows.SDK.Contracts.targets')" />
  </ImportGroup>
</Project>