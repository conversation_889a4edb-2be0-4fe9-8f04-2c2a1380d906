{"version": 3, "targets": {".NETFramework,Version=v4.5": {"Microsoft.Windows.SDK.Contracts/10.0.22621.755": {"type": "package", "dependencies": {"System.Runtime.InteropServices.WindowsRuntime": "4.3.0", "System.Runtime.WindowsRuntime": "4.6.0", "System.Runtime.WindowsRuntime.UI.Xaml": "4.6.0"}, "build": {"build/Microsoft.Windows.SDK.Contracts.props": {}, "build/Microsoft.Windows.SDK.Contracts.targets": {}}}, "System.Runtime.InteropServices.WindowsRuntime/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.WindowsRuntime/4.6.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net45/System.Runtime.WindowsRuntime.targets": {}}}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"type": "package", "dependencies": {"System.Runtime.WindowsRuntime": "4.6.0"}, "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net45/System.Runtime.WindowsRuntime.UI.Xaml.targets": {}}}}, ".NETFramework,Version=v4.5/win": {"Microsoft.Windows.SDK.Contracts/10.0.22621.755": {"type": "package", "dependencies": {"System.Runtime.InteropServices.WindowsRuntime": "4.3.0", "System.Runtime.WindowsRuntime": "4.6.0", "System.Runtime.WindowsRuntime.UI.Xaml": "4.6.0"}, "build": {"build/Microsoft.Windows.SDK.Contracts.props": {}, "build/Microsoft.Windows.SDK.Contracts.targets": {}}}, "System.Runtime.InteropServices.WindowsRuntime/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.WindowsRuntime/4.6.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net45/System.Runtime.WindowsRuntime.targets": {}}}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"type": "package", "dependencies": {"System.Runtime.WindowsRuntime": "4.6.0"}, "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net45/System.Runtime.WindowsRuntime.UI.Xaml.targets": {}}}}, ".NETFramework,Version=v4.5/win-x64": {"Microsoft.Windows.SDK.Contracts/10.0.22621.755": {"type": "package", "dependencies": {"System.Runtime.InteropServices.WindowsRuntime": "4.3.0", "System.Runtime.WindowsRuntime": "4.6.0", "System.Runtime.WindowsRuntime.UI.Xaml": "4.6.0"}, "build": {"build/Microsoft.Windows.SDK.Contracts.props": {}, "build/Microsoft.Windows.SDK.Contracts.targets": {}}}, "System.Runtime.InteropServices.WindowsRuntime/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.WindowsRuntime/4.6.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net45/System.Runtime.WindowsRuntime.targets": {}}}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"type": "package", "dependencies": {"System.Runtime.WindowsRuntime": "4.6.0"}, "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net45/System.Runtime.WindowsRuntime.UI.Xaml.targets": {}}}}, ".NETFramework,Version=v4.5/win-x86": {"Microsoft.Windows.SDK.Contracts/10.0.22621.755": {"type": "package", "dependencies": {"System.Runtime.InteropServices.WindowsRuntime": "4.3.0", "System.Runtime.WindowsRuntime": "4.6.0", "System.Runtime.WindowsRuntime.UI.Xaml": "4.6.0"}, "build": {"build/Microsoft.Windows.SDK.Contracts.props": {}, "build/Microsoft.Windows.SDK.Contracts.targets": {}}}, "System.Runtime.InteropServices.WindowsRuntime/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.WindowsRuntime/4.6.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net45/System.Runtime.WindowsRuntime.targets": {}}}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"type": "package", "dependencies": {"System.Runtime.WindowsRuntime": "4.6.0"}, "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net45/System.Runtime.WindowsRuntime.UI.Xaml.targets": {}}}}}, "libraries": {"Microsoft.Windows.SDK.Contracts/10.0.22621.755": {"sha512": "J1mpz9jekt87ZcbsHxU+iMvJIVJSMkTpeAftudx5AYsucPSxaQxlq3ZM2X8lVyRSdlMphtlfHfoTDKcMReIzdA==", "type": "package", "path": "microsoft.windows.sdk.contracts/10.0.22621.755", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.Windows.SDK.Contracts.props", "build/Microsoft.Windows.SDK.Contracts.targets", "c/Catalogs/cat353be8f91891a6a5761b9ac157fa2ff1.cat", "c/Catalogs/cat4ec14c5368b7642563c070cd168960a8.cat", "c/Catalogs/cate59830bab4961666e8d8c2af1e5fa771.cat", "c/Catalogs/catf105a73f98cfc88c7b64d8f7b39a474c.cat", "microsoft.windows.sdk.contracts.10.0.22621.755.nupkg.sha512", "microsoft.windows.sdk.contracts.nuspec", "ref/netstandard2.0/Windows.AI.MachineLearning.MachineLearningContract.winmd", "ref/netstandard2.0/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsPhoneContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsVoipContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.LockScreenCallContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.FullTrustAppContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Search.SearchContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.StartupTaskContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Wallet.WalletContract.winmd", "ref/netstandard2.0/Windows.Devices.Custom.CustomDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.DevicesLowLevelContract.winmd", "ref/netstandard2.0/Windows.Devices.Portable.PortableDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.Printers.Extensions.ExtensionsContract.winmd", "ref/netstandard2.0/Windows.Devices.Printers.PrintersContract.winmd", "ref/netstandard2.0/Windows.Devices.Scanners.ScannerDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd", "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd", "ref/netstandard2.0/Windows.Devices.Sms.LegacySmsApiContract.winmd", "ref/netstandard2.0/Windows.Embedded.DeviceLockdown.DeviceLockdownContract.winmd", "ref/netstandard2.0/Windows.Foundation.FoundationContract.winmd", "ref/netstandard2.0/Windows.Foundation.UniversalApiContract.winmd", "ref/netstandard2.0/Windows.Gaming.Input.GamingInputPreviewContract.winmd", "ref/netstandard2.0/Windows.Gaming.Preview.GamesEnumerationContract.winmd", "ref/netstandard2.0/Windows.Gaming.UI.GameChatOverlayContract.winmd", "ref/netstandard2.0/Windows.Gaming.UI.GamingUIProviderContract.winmd", "ref/netstandard2.0/Windows.Gaming.XboxLive.StorageApiContract.winmd", "ref/netstandard2.0/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd", "ref/netstandard2.0/Windows.Graphics.Printing3D.Printing3DContract.winmd", "ref/netstandard2.0/Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd", "ref/netstandard2.0/Windows.Management.Deployment.SharedPackageContainerContract.winmd", "ref/netstandard2.0/Windows.Management.Update.WindowsUpdateContract.winmd", "ref/netstandard2.0/Windows.Management.Workplace.WorkplaceSettingsContract.winmd", "ref/netstandard2.0/Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd", "ref/netstandard2.0/Windows.Media.AppRecording.AppRecordingContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppBroadcastContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppCaptureContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppCaptureMetadataContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.CameraCaptureUIContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.GameBarContract.winmd", "ref/netstandard2.0/Windows.Media.Devices.CallControlContract.winmd", "ref/netstandard2.0/Windows.Media.MediaControlContract.winmd", "ref/netstandard2.0/Windows.Media.Playlists.PlaylistsContract.winmd", "ref/netstandard2.0/Windows.Media.Protection.ProtectionRenewalContract.winmd", "ref/netstandard2.0/Windows.Networking.Connectivity.WwanContract.winmd", "ref/netstandard2.0/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd", "ref/netstandard2.0/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.WinMD", "ref/netstandard2.0/Windows.Networking.Sockets.ControlChannelTriggerContract.winmd", "ref/netstandard2.0/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd", "ref/netstandard2.0/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd", "ref/netstandard2.0/Windows.Phone.PhoneContract.winmd", "ref/netstandard2.0/Windows.Phone.StartScreen.DualSimTileContract.WinMD", "ref/netstandard2.0/Windows.Security.EnterpriseData.EnterpriseDataContract.winmd", "ref/netstandard2.0/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd", "ref/netstandard2.0/Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd", "ref/netstandard2.0/Windows.Services.Maps.GuidanceContract.winmd", "ref/netstandard2.0/Windows.Services.Maps.LocalSearchContract.winmd", "ref/netstandard2.0/Windows.Services.Store.StoreContract.winmd", "ref/netstandard2.0/Windows.Services.TargetedContent.TargetedContentContract.winmd", "ref/netstandard2.0/Windows.Storage.Provider.CloudFilesContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileHardwareTokenContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileRetailInfoContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileSharedModeContract.winmd", "ref/netstandard2.0/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd", "ref/netstandard2.0/Windows.System.SystemManagementContract.winmd", "ref/netstandard2.0/Windows.System.UserProfile.UserProfileContract.winmd", "ref/netstandard2.0/Windows.System.UserProfile.UserProfileLockScreenContract.winmd", "ref/netstandard2.0/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd", "ref/netstandard2.0/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd", "ref/netstandard2.0/Windows.UI.Core.CoreWindowDialogsContract.winmd", "ref/netstandard2.0/Windows.UI.Shell.SecurityAppManagerContract.winmd", "ref/netstandard2.0/Windows.UI.UIAutomation.UIAutomationContract.winmd", "ref/netstandard2.0/Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd", "ref/netstandard2.0/Windows.UI.WebUI.Core.WebUICommandBarContract.winmd", "ref/netstandard2.0/Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd", "ref/netstandard2.0/Windows.UI.Xaml.Hosting.HostingContract.winmd", "ref/netstandard2.0/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd", "ref/netstandard2.0/Windows.WinMD", "ref/netstandard2.0/en/Windows.AI.MachineLearning.MachineLearningContract.xml", "ref/netstandard2.0/en/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.CallsPhoneContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.CallsVoipContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.LockScreenCallContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.FullTrustAppContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Search.Core.SearchCoreContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Search.SearchContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.SocialInfo.SocialInfoContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.StartupTaskContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Wallet.WalletContract.xml", "ref/netstandard2.0/en/Windows.Devices.Custom.CustomDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.DevicesLowLevelContract.xml", "ref/netstandard2.0/en/Windows.Devices.Portable.PortableDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.Printers.Extensions.ExtensionsContract.xml", "ref/netstandard2.0/en/Windows.Devices.Printers.PrintersContract.xml", "ref/netstandard2.0/en/Windows.Devices.Scanners.ScannerDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.xml", "ref/netstandard2.0/en/Windows.Devices.SmartCards.SmartCardEmulatorContract.xml", "ref/netstandard2.0/en/Windows.Devices.Sms.LegacySmsApiContract.xml", "ref/netstandard2.0/en/Windows.Foundation.FoundationContract.xml", "ref/netstandard2.0/en/Windows.Foundation.UniversalApiContract.xml", "ref/netstandard2.0/en/Windows.Gaming.Input.GamingInputPreviewContract.xml", "ref/netstandard2.0/en/Windows.Gaming.Preview.GamesEnumerationContract.xml", "ref/netstandard2.0/en/Windows.Gaming.UI.GameChatOverlayContract.xml", "ref/netstandard2.0/en/Windows.Gaming.UI.GamingUIProviderContract.xml", "ref/netstandard2.0/en/Windows.Gaming.XboxLive.StorageApiContract.xml", "ref/netstandard2.0/en/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.xml", "ref/netstandard2.0/en/Windows.Graphics.Printing3D.Printing3DContract.xml", "ref/netstandard2.0/en/Windows.Management.Deployment.Preview.DeploymentPreviewContract.xml", "ref/netstandard2.0/en/Windows.Management.Workplace.WorkplaceSettingsContract.xml", "ref/netstandard2.0/en/Windows.Media.AppBroadcasting.AppBroadcastingContract.xml", "ref/netstandard2.0/en/Windows.Media.AppRecording.AppRecordingContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppBroadcastContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppCaptureContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppCaptureMetadataContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.CameraCaptureUIContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.GameBarContract.xml", "ref/netstandard2.0/en/Windows.Media.Devices.CallControlContract.xml", "ref/netstandard2.0/en/Windows.Media.MediaControlContract.xml", "ref/netstandard2.0/en/Windows.Media.Playlists.PlaylistsContract.xml", "ref/netstandard2.0/en/Windows.Media.Protection.ProtectionRenewalContract.xml", "ref/netstandard2.0/en/Windows.Networking.Connectivity.WwanContract.xml", "ref/netstandard2.0/en/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.xml", "ref/netstandard2.0/en/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.xml", "ref/netstandard2.0/en/Windows.Networking.Sockets.ControlChannelTriggerContract.xml", "ref/netstandard2.0/en/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.xml", "ref/netstandard2.0/en/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.xml", "ref/netstandard2.0/en/Windows.Phone.PhoneContract.xml", "ref/netstandard2.0/en/Windows.Phone.StartScreen.DualSimTileContract.xml", "ref/netstandard2.0/en/Windows.Security.EnterpriseData.EnterpriseDataContract.xml", "ref/netstandard2.0/en/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.xml", "ref/netstandard2.0/en/Windows.Security.Isolation.IsolatedWindowsEnvironmentContract.xml", "ref/netstandard2.0/en/Windows.Services.Maps.GuidanceContract.xml", "ref/netstandard2.0/en/Windows.Services.Maps.LocalSearchContract.xml", "ref/netstandard2.0/en/Windows.Services.Store.StoreContract.xml", "ref/netstandard2.0/en/Windows.Services.TargetedContent.TargetedContentContract.xml", "ref/netstandard2.0/en/Windows.Storage.Provider.CloudFilesContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileHardwareTokenContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileRetailInfoContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileSharedModeContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.xml", "ref/netstandard2.0/en/Windows.System.SystemManagementContract.xml", "ref/netstandard2.0/en/Windows.System.UserProfile.UserProfileContract.xml", "ref/netstandard2.0/en/Windows.System.UserProfile.UserProfileLockScreenContract.xml", "ref/netstandard2.0/en/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.xml", "ref/netstandard2.0/en/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.xml", "ref/netstandard2.0/en/Windows.UI.Core.CoreWindowDialogsContract.xml", "ref/netstandard2.0/en/Windows.UI.Shell.SecurityAppManagerContract.xml", "ref/netstandard2.0/en/Windows.UI.UIAutomation.UIAutomationContract.xml", "ref/netstandard2.0/en/Windows.UI.ViewManagement.ViewManagementViewScalingContract.xml", "ref/netstandard2.0/en/Windows.UI.WebUI.Core.WebUICommandBarContract.xml", "ref/netstandard2.0/en/Windows.UI.Xaml.Core.Direct.XamlDirectContract.xml", "ref/netstandard2.0/en/Windows.UI.Xaml.Hosting.HostingContract.xml", "ref/netstandard2.0/en/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.xml"]}, "System.Runtime.InteropServices.WindowsRuntime/4.3.0": {"sha512": "J4GUi3xZQLUBasNwZnjrffN8i5wpHrBtZoLG+OhRyGo/+YunMRWWtwoMDlUAIdmX0uRfpHIBDSV6zyr3yf00TA==", "type": "package", "path": "system.runtime.interopservices.windowsruntime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.InteropServices.WindowsRuntime.dll", "lib/netstandard1.3/System.Runtime.InteropServices.WindowsRuntime.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios1/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.InteropServices.WindowsRuntime.dll", "ref/netcore50/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/de/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/es/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/fr/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/it/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/ja/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/ko/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/ru/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/System.Runtime.InteropServices.WindowsRuntime.dll", "ref/netstandard1.0/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/de/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/es/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/fr/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/it/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/ja/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/ko/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/ru/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.WindowsRuntime.dll", "system.runtime.interopservices.windowsruntime.4.3.0.nupkg.sha512", "system.runtime.interopservices.windowsruntime.nuspec"]}, "System.Runtime.WindowsRuntime/4.6.0": {"sha512": "IWrs1TmbxP65ZZjIglNyvDkFNoV5q2Pofg5WO7I8RKQOpLdFprQSh3xesOoClBqR4JHr4nEB1Xk1MqLPW1jPuQ==", "type": "package", "path": "system.runtime.windowsruntime/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/net45/System.Runtime.WindowsRuntime.targets", "build/net451/System.Runtime.WindowsRuntime.targets", "build/net461/System.Runtime.WindowsRuntime.targets", "buildTransitive/net45/System.Runtime.WindowsRuntime.targets", "buildTransitive/net451/System.Runtime.WindowsRuntime.targets", "buildTransitive/net461/System.Runtime.WindowsRuntime.targets", "lib/net45/_._", "lib/netstandard1.0/System.Runtime.WindowsRuntime.dll", "lib/netstandard1.0/System.Runtime.WindowsRuntime.xml", "lib/netstandard1.2/System.Runtime.WindowsRuntime.dll", "lib/netstandard1.2/System.Runtime.WindowsRuntime.xml", "lib/netstandard2.0/System.Runtime.WindowsRuntime.dll", "lib/netstandard2.0/System.Runtime.WindowsRuntime.xml", "lib/portable-win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.WindowsRuntime.dll", "ref/netcore50/System.Runtime.WindowsRuntime.xml", "ref/netcore50/de/System.Runtime.WindowsRuntime.xml", "ref/netcore50/es/System.Runtime.WindowsRuntime.xml", "ref/netcore50/fr/System.Runtime.WindowsRuntime.xml", "ref/netcore50/it/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ja/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ko/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ru/System.Runtime.WindowsRuntime.xml", "ref/netcore50/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netcore50/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/System.Runtime.WindowsRuntime.dll", "ref/netstandard1.0/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/de/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/es/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/fr/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/it/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ja/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ko/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ru/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/System.Runtime.WindowsRuntime.dll", "ref/netstandard1.2/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/de/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/es/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/fr/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/it/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ja/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ko/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ru/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard2.0/System.Runtime.WindowsRuntime.dll", "ref/netstandard2.0/System.Runtime.WindowsRuntime.xml", "ref/portable-win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "runtimes/win-aot/lib/netcore50/System.Runtime.WindowsRuntime.dll", "runtimes/win-aot/lib/uap10.0.16299/_._", "runtimes/win/lib/netcore50/System.Runtime.WindowsRuntime.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.xml", "runtimes/win/lib/uap10.0.16299/_._", "system.runtime.windowsruntime.4.6.0.nupkg.sha512", "system.runtime.windowsruntime.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"sha512": "r4tNw5v5kqRJ9HikWpcyNf3suGw7DjX93svj9iBjtdeLqL8jt9Z+7f+s4wrKZJr84u8IMsrIjt8K6jYvkRqMSg==", "type": "package", "path": "system.runtime.windowsruntime.ui.xaml/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/net45/System.Runtime.WindowsRuntime.UI.Xaml.targets", "build/net461/System.Runtime.WindowsRuntime.UI.Xaml.targets", "lib/net45/_._", "lib/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.dll", "lib/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.xml", "lib/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "lib/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "lib/portable-win8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wpa81/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/de/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/es/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/fr/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/it/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ja/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ko/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ru/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/zh-hans/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/zh-hant/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/de/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/es/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/fr/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/it/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ja/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ko/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ru/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/zh-hans/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/zh-hant/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/portable-win8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wpa81/_._", "runtimes/win-aot/lib/uap10.0.16299/_._", "runtimes/win/lib/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "runtimes/win/lib/uap10.0.16299/_._", "system.runtime.windowsruntime.ui.xaml.4.6.0.nupkg.sha512", "system.runtime.windowsruntime.ui.xaml.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.5": ["Microsoft.Windows.SDK.Contracts >= 10.0.22621.755"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Code\\CatchTools\\OCRTools.csproj", "projectName": "OCRTools", "projectPath": "D:\\Code\\CatchTools\\OCRTools.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Code\\CatchTools\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net45"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://mirrors.cloud.tencent.com/nuget/": {}, "https://repo.huaweicloud.com/repository/nuget/v3/index.json": {}}, "frameworks": {"net45": {"projectReferences": {}}}}, "frameworks": {"net45": {"dependencies": {"Microsoft.Windows.SDK.Contracts": {"target": "Package", "version": "[10.0.22621.755, )"}}}}, "runtimes": {"win": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}